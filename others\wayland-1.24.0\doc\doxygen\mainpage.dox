/**
 * @mainpage
 * Wayland protocol API documentation.
 *
 * This documentation is available for the Server- and the Client-side APIs.
 *
 * - <a href="../Server/index.html">Server-side API</a>
 * - <a href="../Client/index.html">Client-side API</a>
 * - <a href="../Cursor/index.html">Cursor helper library API</a>
 *
 * Further documentation about the architecture and principles of Wayland is
 * available in the
 * <a href="https://wayland.freedesktop.org/docs/html">Wayland Book</a>
 *
 * @section ifaces Interfaces
 * For the list of available interfaces, please see the
 * <a href="modules.html">modules</a> list.
 *
 * @section protocols Protocols
 * For the list of protocols, please see the
 * <a href="pages.html">Related Pages</a>.
 *
 */
