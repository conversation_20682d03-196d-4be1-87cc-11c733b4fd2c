# WebGPU后端替换设计

## 设计目标

**保留**: VSG/OSG的完整场景图系统
**替换**: 仅替换底层渲染后端 (Vulkan/OpenGL → WebGPU)

## 核心设计策略

### 1. 接口适配器模式
通过适配器模式，让VSG/OSG的现有接口调用转发到WebGPU后端，而不修改场景图逻辑。

```cpp
// VSG原有接口保持不变
namespace vsg {
    class Device : public Object {
    public:
        // 原有接口签名保持不变
        VkResult createBuffer(const VkBufferCreateInfo* pCreateInfo, VkBuffer* pBuffer);
        VkResult createImage(const VkImageCreateInfo* pCreateInfo, VkImage* pImage);
        
    private:
        // 内部使用WebGPU后端
        std::unique_ptr<WebGPUBackend> _webgpuBackend;
    };
}
```

### 2. 后端抽象层
创建一个抽象的渲染后端接口，支持多种实现。

```cpp
// 渲染后端抽象接口
class RenderBackend {
public:
    virtual ~RenderBackend() = default;
    
    // 设备管理
    virtual bool initialize() = 0;
    virtual void cleanup() = 0;
    
    // 资源创建
    virtual BackendBuffer* createBuffer(size_t size, BufferUsage usage) = 0;
    virtual BackendTexture* createTexture(const TextureDesc& desc) = 0;
    virtual BackendPipeline* createPipeline(const PipelineDesc& desc) = 0;
    
    // 命令记录
    virtual BackendCommandBuffer* createCommandBuffer() = 0;
    virtual void submitCommands(BackendCommandBuffer* cmdBuffer) = 0;
    
    // 渲染操作
    virtual void beginRenderPass(const RenderPassDesc& desc) = 0;
    virtual void endRenderPass() = 0;
    virtual void draw(uint32_t vertexCount, uint32_t instanceCount = 1) = 0;
};

// WebGPU后端实现
class WebGPUBackend : public RenderBackend {
public:
    bool initialize() override;
    BackendBuffer* createBuffer(size_t size, BufferUsage usage) override;
    // ... 其他接口实现
    
private:
    wgpu::Device _device;
    wgpu::Queue _queue;
    wgpu::Instance _instance;
};

// Vulkan后端实现 (保持兼容)
class VulkanBackend : public RenderBackend {
public:
    bool initialize() override;
    BackendBuffer* createBuffer(size_t size, BufferUsage usage) override;
    // ... 其他接口实现
    
private:
    VkDevice _device;
    VkQueue _queue;
    VkInstance _instance;
};
```

## VSG接口适配实现

### 1. 设备适配
```cpp
namespace vsg {
    class Device : public Object {
    public:
        // 构造时选择后端
        Device(RenderBackendType backendType = RenderBackendType::WebGPU) {
            switch (backendType) {
                case RenderBackendType::WebGPU:
                    _backend = std::make_unique<WebGPUBackend>();
                    break;
                case RenderBackendType::Vulkan:
                    _backend = std::make_unique<VulkanBackend>();
                    break;
            }
            _backend->initialize();
        }
        
        // VSG原有接口，内部转发到后端
        VkResult createBuffer(const VkBufferCreateInfo* pCreateInfo, VkBuffer* pBuffer) {
            std::cout << "[VSG_ADAPTER] Device::createBuffer() -> WebGPU backend" << std::endl;
            
            // 转换Vulkan参数到通用参数
            BufferUsage usage = convertVkBufferUsage(pCreateInfo->usage);
            
            // 调用后端接口
            auto* backendBuffer = _backend->createBuffer(pCreateInfo->size, usage);
            
            // 将后端句柄包装为Vulkan句柄 (适配器模式)
            *pBuffer = reinterpret_cast<VkBuffer>(backendBuffer);
            
            return VK_SUCCESS;
        }
        
        VkResult createImage(const VkImageCreateInfo* pCreateInfo, VkImage* pImage) {
            std::cout << "[VSG_ADAPTER] Device::createImage() -> WebGPU backend" << std::endl;
            
            // 转换参数
            TextureDesc desc = convertVkImageCreateInfo(pCreateInfo);
            
            // 调用后端
            auto* backendTexture = _backend->createTexture(desc);
            
            // 包装句柄
            *pImage = reinterpret_cast<VkImage>(backendTexture);
            
            return VK_SUCCESS;
        }
        
    private:
        std::unique_ptr<RenderBackend> _backend;
        
        // 参数转换函数
        BufferUsage convertVkBufferUsage(VkBufferUsageFlags vkUsage) {
            BufferUsage usage = BufferUsage::None;
            if (vkUsage & VK_BUFFER_USAGE_VERTEX_BUFFER_BIT) usage |= BufferUsage::Vertex;
            if (vkUsage & VK_BUFFER_USAGE_INDEX_BUFFER_BIT) usage |= BufferUsage::Index;
            if (vkUsage & VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT) usage |= BufferUsage::Uniform;
            return usage;
        }
        
        TextureDesc convertVkImageCreateInfo(const VkImageCreateInfo* pCreateInfo) {
            TextureDesc desc;
            desc.width = pCreateInfo->extent.width;
            desc.height = pCreateInfo->extent.height;
            desc.depth = pCreateInfo->extent.depth;
            desc.format = convertVkFormat(pCreateInfo->format);
            desc.usage = convertVkImageUsage(pCreateInfo->usage);
            return desc;
        }
    };
}
```

### 2. 命令缓冲区适配
```cpp
namespace vsg {
    class CommandBuffer : public Object {
    public:
        CommandBuffer(ref_ptr<Device> device) 
            : _device(device) {
            _backendCmdBuffer = device->getBackend()->createCommandBuffer();
        }
        
        void begin(VkCommandBufferBeginInfo* beginInfo) {
            std::cout << "[VSG_ADAPTER] CommandBuffer::begin() -> WebGPU backend" << std::endl;
            _backendCmdBuffer->begin();
        }
        
        void beginRenderPass(VkRenderPassBeginInfo* renderPassBegin) {
            std::cout << "[VSG_ADAPTER] CommandBuffer::beginRenderPass() -> WebGPU backend" << std::endl;
            
            // 转换渲染通道参数
            RenderPassDesc desc = convertVkRenderPassBeginInfo(renderPassBegin);
            _backendCmdBuffer->beginRenderPass(desc);
        }
        
        void bindPipeline(VkPipelineBindPoint pipelineBindPoint, VkPipeline pipeline) {
            std::cout << "[VSG_ADAPTER] CommandBuffer::bindPipeline() -> WebGPU backend" << std::endl;
            
            // 从Vulkan句柄恢复后端对象
            auto* backendPipeline = reinterpret_cast<BackendPipeline*>(pipeline);
            _backendCmdBuffer->bindPipeline(backendPipeline);
        }
        
        void draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance) {
            std::cout << "[VSG_ADAPTER] CommandBuffer::draw() -> WebGPU backend - vertices: " 
                      << vertexCount << ", instances: " << instanceCount << std::endl;
            
            _backendCmdBuffer->draw(vertexCount, instanceCount, firstVertex, firstInstance);
        }
        
        void endRenderPass() {
            std::cout << "[VSG_ADAPTER] CommandBuffer::endRenderPass() -> WebGPU backend" << std::endl;
            _backendCmdBuffer->endRenderPass();
        }
        
        void end() {
            std::cout << "[VSG_ADAPTER] CommandBuffer::end() -> WebGPU backend" << std::endl;
            _backendCmdBuffer->end();
        }
        
    private:
        ref_ptr<Device> _device;
        std::unique_ptr<BackendCommandBuffer> _backendCmdBuffer;
    };
}
```

## WebGPU后端具体实现

### 1. WebGPU设备管理
```cpp
class WebGPUBackend : public RenderBackend {
public:
    bool initialize() override {
        std::cout << "[WEBGPU_BACKEND] Initializing WebGPU device..." << std::endl;
        
        // 创建WebGPU实例
        wgpu::InstanceDescriptor instanceDesc{};
        _instance = wgpu::createInstance(instanceDesc);
        
        // 请求适配器
        wgpu::RequestAdapterOptions adapterOpts{};
        _adapter = _instance.requestAdapter(adapterOpts);
        
        // 请求设备
        wgpu::DeviceDescriptor deviceDesc{};
        _device = _adapter.requestDevice(deviceDesc);
        _queue = _device.getQueue();
        
        std::cout << "[WEBGPU_BACKEND] WebGPU device initialized successfully" << std::endl;
        return true;
    }
    
    BackendBuffer* createBuffer(size_t size, BufferUsage usage) override {
        std::cout << "[WEBGPU_BACKEND] Creating buffer - size: " << size << std::endl;
        
        wgpu::BufferUsage wgpuUsage = wgpu::BufferUsage::None;
        if (usage & BufferUsage::Vertex) wgpuUsage |= wgpu::BufferUsage::Vertex;
        if (usage & BufferUsage::Index) wgpuUsage |= wgpu::BufferUsage::Index;
        if (usage & BufferUsage::Uniform) wgpuUsage |= wgpu::BufferUsage::Uniform;
        
        wgpu::BufferDescriptor bufferDesc{};
        bufferDesc.size = size;
        bufferDesc.usage = wgpuUsage;
        
        wgpu::Buffer wgpuBuffer = _device.createBuffer(bufferDesc);
        
        return new WebGPUBuffer(wgpuBuffer);
    }
    
    BackendTexture* createTexture(const TextureDesc& desc) override {
        std::cout << "[WEBGPU_BACKEND] Creating texture - " << desc.width << "x" << desc.height << std::endl;
        
        wgpu::TextureDescriptor textureDesc{};
        textureDesc.size = {desc.width, desc.height, desc.depth};
        textureDesc.format = convertToWGPUFormat(desc.format);
        textureDesc.usage = convertToWGPUTextureUsage(desc.usage);
        
        wgpu::Texture wgpuTexture = _device.createTexture(textureDesc);
        
        return new WebGPUTexture(wgpuTexture);
    }
    
private:
    wgpu::Instance _instance;
    wgpu::Adapter _adapter;
    wgpu::Device _device;
    wgpu::Queue _queue;
};
```

### 2. WebGPU命令缓冲区
```cpp
class WebGPUCommandBuffer : public BackendCommandBuffer {
public:
    WebGPUCommandBuffer(wgpu::Device device) : _device(device) {
        wgpu::CommandEncoderDescriptor encoderDesc{};
        _encoder = _device.createCommandEncoder(encoderDesc);
    }
    
    void begin() override {
        std::cout << "[WEBGPU_BACKEND] Command buffer begin" << std::endl;
        // WebGPU不需要显式begin
    }
    
    void beginRenderPass(const RenderPassDesc& desc) override {
        std::cout << "[WEBGPU_BACKEND] Begin render pass" << std::endl;
        
        wgpu::RenderPassDescriptor renderPassDesc{};
        // 设置颜色附件
        wgpu::RenderPassColorAttachment colorAttachment{};
        colorAttachment.view = desc.colorTarget;
        colorAttachment.loadOp = wgpu::LoadOp::Clear;
        colorAttachment.storeOp = wgpu::StoreOp::Store;
        colorAttachment.clearValue = {0.0f, 0.0f, 0.0f, 1.0f};
        
        renderPassDesc.colorAttachmentCount = 1;
        renderPassDesc.colorAttachments = &colorAttachment;
        
        _renderPass = _encoder.beginRenderPass(renderPassDesc);
    }
    
    void draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance) override {
        std::cout << "[WEBGPU_BACKEND] Draw call - vertices: " << vertexCount << std::endl;
        _renderPass.draw(vertexCount, instanceCount, firstVertex, firstInstance);
    }
    
    void endRenderPass() override {
        std::cout << "[WEBGPU_BACKEND] End render pass" << std::endl;
        _renderPass.end();
    }
    
    void end() override {
        std::cout << "[WEBGPU_BACKEND] Command buffer end" << std::endl;
        wgpu::CommandBufferDescriptor cmdBufferDesc{};
        _commandBuffer = _encoder.finish(cmdBufferDesc);
    }
    
private:
    wgpu::Device _device;
    wgpu::CommandEncoder _encoder;
    wgpu::RenderPassEncoder _renderPass;
    wgpu::CommandBuffer _commandBuffer;
};
```

## 使用示例

### VSG应用无需修改
```cpp
// VSG应用代码保持完全不变
int main() {
    // 创建VSG设备 (内部使用WebGPU后端)
    auto device = vsg::Device::create(vsg::RenderBackendType::WebGPU);
    
    // 创建VSG场景图 (完全不变)
    auto scene = vsg::Group::create();
    auto transform = vsg::Transform::create();
    auto geometry = vsg::Geometry::create();
    
    scene->addChild(transform);
    transform->addChild(geometry);
    
    // VSG渲染循环 (完全不变)
    while (running) {
        scene->accept(cullVisitor);
        scene->accept(renderVisitor);
    }
    
    return 0;
}
```

### 控制台输出示例
```
[VSG_ADAPTER] Device::createBuffer() -> WebGPU backend
[WEBGPU_BACKEND] Creating buffer - size: 1024
[VSG_ADAPTER] CommandBuffer::begin() -> WebGPU backend
[WEBGPU_BACKEND] Command buffer begin
[VSG_ADAPTER] CommandBuffer::beginRenderPass() -> WebGPU backend
[WEBGPU_BACKEND] Begin render pass
[VSG_ADAPTER] CommandBuffer::draw() -> WebGPU backend - vertices: 36, instances: 1
[WEBGPU_BACKEND] Draw call - vertices: 36
[VSG_ADAPTER] CommandBuffer::endRenderPass() -> WebGPU backend
[WEBGPU_BACKEND] End render pass
```

这种设计确保了：
1. **VSG/OSG场景图系统完全保留**
2. **应用代码无需修改**
3. **仅替换底层渲染后端**
4. **支持多种后端切换**
5. **完整的调用跟踪和调试**
