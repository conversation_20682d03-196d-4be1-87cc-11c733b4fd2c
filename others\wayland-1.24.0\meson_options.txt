option('libraries',
  description: 'Compile Wayland libraries',
  type: 'boolean',
  value: true)
option('scanner',
  description: 'Compile wayland-scanner binary',
  type: 'boolean',
  value: true)
option('tests',
  description: 'Compile Wayland tests',
  type: 'boolean',
  value: true)
option('documentation',
  description: 'Build the documentation (requires Doxygen, dot, xmlto, xsltproc)',
  type: 'boolean',
  value: true)
option('dtd_validation',
  description: 'Validate the protocol DTD (requires libxml2)',
  type: 'boolean',
  value: true)
option('icon_directory',
  description: 'Location used to look for cursors (defaults to ${datadir}/icons if unset)',
  type: 'string',
  value: '')
