#include "BackendSwitcher.h"
#include <USG_Backend/BackendFactory.h>
#include <iostream>
#include <chrono>
#include <algorithm>

#ifdef USG_HAS_WEBGPU
#include "../../src/WebGPU/WebGPUBackend.h"
#endif

#ifdef USG_HAS_VULKAN
// Vulkan后端头文件
#endif

namespace USG
{

    BackendSwitcher::BackendSwitcher()
        : _factory(BackendFactory::getInstance())
    {
    }

    BackendSwitcher::~BackendSwitcher()
    {
        cleanup();
    }

    bool BackendSwitcher::initialize(const BackendConfig &config)
    {
        if (_initialized)
        {
            return true;
        }

        _backendConfig = config;

        // 扫描可用后端
        int availableCount = scanAvailableBackends();
        if (availableCount == 0)
        {
            if (_errorCallback)
            {
                _errorCallback("No available backends found");
            }
            return false;
        }

        std::cout << "[BackendSwitcher] Initialized with " << availableCount << " available backends" << std::endl;

        _initialized = true;
        return true;
    }

    void BackendSwitcher::cleanup()
    {
        if (!_initialized)
        {
            return;
        }

        // 清理所有后端
        for (auto &backend : _backends)
        {
            if (backend.initialized)
            {
                cleanupBackend(backend);
            }
        }

        _backends.clear();
        _currentBackendType = BackendType::Unknown;
        _currentBackendIndex = -1;
        _initialized = false;

        std::cout << "[BackendSwitcher] Cleaned up" << std::endl;
    }

    int BackendSwitcher::scanAvailableBackends()
    {
        _backends.clear();

        // 检查WebGPU后端
#ifdef USG_HAS_WEBGPU
        BackendInfo webgpuInfo;
        webgpuInfo.type = BackendType::WebGPU;
        webgpuInfo.name = "WebGPU";
        webgpuInfo.description = "WebGPU渲染后端 - 现代跨平台图形API";
        webgpuInfo.available = _factory.isBackendAvailable(BackendType::WebGPU);
        _backends.push_back(std::move(webgpuInfo));
#endif

        // 检查OpenGL后端
        BackendInfo openglInfo;
        openglInfo.type = BackendType::OpenGL;
        openglInfo.name = "OpenGL";
        openglInfo.description = "OpenGL渲染后端 - 成熟稳定的图形API";
        openglInfo.available = _factory.isBackendAvailable(BackendType::OpenGL);
        _backends.push_back(std::move(openglInfo));

        // 检查Vulkan后端
#ifdef USG_HAS_VULKAN
        BackendInfo vulkanInfo;
        vulkanInfo.type = BackendType::Vulkan;
        vulkanInfo.name = "Vulkan";
        vulkanInfo.description = "Vulkan渲染后端 - 高性能低级图形API";
        vulkanInfo.available = _factory.isBackendAvailable(BackendType::Vulkan);
        _backends.push_back(std::move(vulkanInfo));
#endif

        // 统计可用后端数量
        int availableCount = 0;
        for (const auto &backend : _backends)
        {
            if (backend.available)
            {
                availableCount++;
                std::cout << "[BackendSwitcher] Found available backend: " << backend.name << std::endl;
            }
        }

        return availableCount;
    }

    RenderBackend *BackendSwitcher::getCurrentBackend() const
    {
        if (_currentBackendIndex >= 0 && _currentBackendIndex < static_cast<int>(_backends.size()))
        {
            return _backends[_currentBackendIndex].instance.get();
        }
        return nullptr;
    }

    std::string BackendSwitcher::getCurrentBackendName() const
    {
        if (_currentBackendIndex >= 0 && _currentBackendIndex < static_cast<int>(_backends.size()))
        {
            return _backends[_currentBackendIndex].name;
        }
        return "None";
    }

    BackendSwitcher::SwitchResult BackendSwitcher::switchTo(BackendType backendType, const SwitchConfig &config)
    {
        auto startTime = getCurrentTimeMs();

        SwitchResult result;
        result.fromBackend = _currentBackendType;
        result.toBackend = backendType;

        // 查找目标后端
        BackendInfo *targetBackend = findBackendInfo(backendType);
        if (!targetBackend)
        {
            result.errorMessage = "Backend not found";
            _lastSwitchResult = result;
            return result;
        }

        if (!targetBackend->available)
        {
            result.errorMessage = "Backend not available";
            _lastSwitchResult = result;
            return result;
        }

        // 如果已经是当前后端，直接返回成功
        if (_currentBackendType == backendType && _currentBackendIndex >= 0)
        {
            result.success = true;
            result.switchTimeMs = getCurrentTimeMs() - startTime;
            _lastSwitchResult = result;
            return result;
        }

        std::cout << "[BackendSwitcher] Switching from " << getCurrentBackendName()
                  << " to " << targetBackend->name << std::endl;

        // 初始化目标后端（如果尚未初始化）
        if (!targetBackend->initialized)
        {
            if (!initializeBackend(*targetBackend))
            {
                result.errorMessage = "Failed to initialize target backend";
                _lastSwitchResult = result;
                return result;
            }
        }

        // 保存当前后端资源（如果需要）
        RenderBackend *oldBackend = getCurrentBackend();
        if (config.preserveResources && oldBackend)
        {
            if (!transferResources(oldBackend, targetBackend->instance.get()))
            {
                std::cout << "[BackendSwitcher] Warning: Failed to transfer resources" << std::endl;
            }
        }

        // 切换到新后端
        _currentBackendType = backendType;
        _currentBackendIndex = static_cast<int>(targetBackend - _backends.data());

        // 验证切换结果（如果需要）
        if (config.validateAfterSwitch)
        {
            if (!validateBackend(targetBackend->instance.get()))
            {
                result.errorMessage = "Backend validation failed after switch";
                _lastSwitchResult = result;
                return result;
            }
        }

        result.success = true;
        result.switchTimeMs = getCurrentTimeMs() - startTime;

        std::cout << "[BackendSwitcher] Successfully switched to " << targetBackend->name
                  << " in " << result.switchTimeMs << "ms" << std::endl;

        // 调用切换回调
        if (_switchCallback)
        {
            _switchCallback(result);
        }

        // 记录切换历史
        _switchHistory.push_back(result);
        _lastSwitchResult = result;

        return result;
    }

    BackendSwitcher::SwitchResult BackendSwitcher::switchTo(const std::string &backendName, const SwitchConfig &config)
    {
        BackendInfo *targetBackend = findBackendInfo(backendName);
        if (!targetBackend)
        {
            SwitchResult result;
            result.errorMessage = "Backend '" + backendName + "' not found";
            _lastSwitchResult = result;
            return result;
        }

        return switchTo(targetBackend->type, config);
    }

    bool BackendSwitcher::isBackendAvailable(BackendType backendType) const
    {
        const BackendInfo *info = const_cast<BackendSwitcher *>(this)->findBackendInfo(backendType);
        return info && info->available;
    }

    bool BackendSwitcher::isBackendInitialized(BackendType backendType) const
    {
        const BackendInfo *info = const_cast<BackendSwitcher *>(this)->findBackendInfo(backendType);
        return info && info->initialized;
    }

    bool BackendSwitcher::preInitializeBackend(BackendType backendType)
    {
        BackendInfo *info = findBackendInfo(backendType);
        if (!info || !info->available || info->initialized)
        {
            return false;
        }

        return initializeBackend(*info);
    }

    int BackendSwitcher::preInitializeAllBackends()
    {
        int successCount = 0;

        for (auto &backend : _backends)
        {
            if (backend.available && !backend.initialized)
            {
                if (initializeBackend(backend))
                {
                    successCount++;
                }
            }
        }

        std::cout << "[BackendSwitcher] Pre-initialized " << successCount << " backends" << std::endl;
        return successCount;
    }

    BackendType BackendSwitcher::getRecommendedBackend() const
    {
        // 优先级：WebGPU > Vulkan > OpenGL
        for (BackendType type : {BackendType::WebGPU, BackendType::Vulkan, BackendType::OpenGL})
        {
            if (isBackendAvailable(type))
            {
                return type;
            }
        }
        return BackendType::Unknown;
    }

    void BackendSwitcher::setSwitchCallback(std::function<void(const SwitchResult &)> callback)
    {
        _switchCallback = callback;
    }

    void BackendSwitcher::setErrorCallback(std::function<void(const std::string &)> callback)
    {
        _errorCallback = callback;
    }

    // 私有方法实现
    BackendSwitcher::BackendInfo *BackendSwitcher::findBackendInfo(BackendType type)
    {
        auto it = std::find_if(_backends.begin(), _backends.end(),
                               [type](const BackendInfo &info)
                               { return info.type == type; });
        return (it != _backends.end()) ? &(*it) : nullptr;
    }

    BackendSwitcher::BackendInfo *BackendSwitcher::findBackendInfo(const std::string &name)
    {
        auto it = std::find_if(_backends.begin(), _backends.end(),
                               [&name](const BackendInfo &info)
                               { return info.name == name; });
        return (it != _backends.end()) ? &(*it) : nullptr;
    }

    bool BackendSwitcher::initializeBackend(BackendInfo &info)
    {
        if (info.initialized)
        {
            return true;
        }

        std::cout << "[BackendSwitcher] Initializing " << info.name << " backend..." << std::endl;

        // 创建后端实例 - 通过名称而不是类型，避免Mock后端覆盖真实后端
        info.instance = _factory.createBackend(info.name);
        if (!info.instance)
        {
            std::cout << "[BackendSwitcher] Failed to create " << info.name << " backend instance" << std::endl;
            return false;
        }

        // 初始化后端
        if (!info.instance->initialize(_backendConfig))
        {
            std::cout << "[BackendSwitcher] Failed to initialize " << info.name << " backend" << std::endl;
            info.instance.reset();
            return false;
        }

        info.initialized = true;
        std::cout << "[BackendSwitcher] Successfully initialized " << info.name << " backend" << std::endl;

        return true;
    }

    bool BackendSwitcher::cleanupBackend(BackendInfo &info)
    {
        if (!info.initialized)
        {
            return true;
        }

        std::cout << "[BackendSwitcher] Cleaning up " << info.name << " backend..." << std::endl;

        if (info.instance)
        {
            info.instance->cleanup();
            info.instance.reset();
        }

        info.initialized = false;
        return true;
    }

    bool BackendSwitcher::transferResources(RenderBackend *from, RenderBackend *to)
    {
        // 简化实现：实际应用中需要转移缓冲区、纹理等资源
        // 这里只是一个占位符
        return true;
    }

    bool BackendSwitcher::validateBackend(RenderBackend *backend) const
    {
        if (!backend)
        {
            return false;
        }

        // 简单验证：检查后端是否响应
        try
        {
            BackendType type = backend->getBackendType();
            return type != BackendType::Unknown;
        }
        catch (...)
        {
            return false;
        }
    }

    double BackendSwitcher::getCurrentTimeMs() const
    {
        auto now = std::chrono::high_resolution_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration<double, std::milli>(duration).count();
    }

} // namespace USG
