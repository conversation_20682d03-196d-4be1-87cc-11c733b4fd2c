#pragma once

#include <USG_Backend/RenderBackend.h>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <atomic>

namespace USG {

/**
 * @brief 资源管理器类
 * 
 * 负责管理VSG句柄和USG Backend资源之间的映射关系
 */
class ResourceManager {
public:
    ResourceManager();
    ~ResourceManager();
    
    // 句柄生成
    template<typename T>
    T generateHandle() {
        return reinterpret_cast<T>(static_cast<uintptr_t>(++_handleCounter));
    }
    
    // 缓冲区管理
    void registerBuffer(void* handle, BackendBuffer* buffer);
    void unregisterBuffer(void* handle);
    BackendBuffer* getBuffer(void* handle);
    
    // 纹理管理
    void registerTexture(void* handle, BackendTexture* texture);
    void unregisterTexture(void* handle);
    BackendTexture* getTexture(void* handle);
    
    // 管线管理
    void registerPipeline(void* handle, BackendPipeline* pipeline);
    void unregisterPipeline(void* handle);
    BackendPipeline* getPipeline(void* handle);
    
    // 着色器管理
    void registerShader(void* handle, BackendShader* shader);
    void unregisterShader(void* handle);
    BackendShader* getShader(void* handle);
    
    // 描述符集管理
    void registerDescriptorSet(void* handle, BackendDescriptorSet* descriptorSet);
    void unregisterDescriptorSet(void* handle);
    BackendDescriptorSet* getDescriptorSet(void* handle);
    
    // 同步对象管理
    void registerFence(void* handle, BackendFence* fence);
    void unregisterFence(void* handle);
    BackendFence* getFence(void* handle);
    
    void registerSemaphore(void* handle, BackendSemaphore* semaphore);
    void unregisterSemaphore(void* handle);
    BackendSemaphore* getSemaphore(void* handle);
    
    // 命令列表管理
    void registerCommandList(void* handle, BackendCommandList* commandList);
    void unregisterCommandList(void* handle);
    BackendCommandList* getCommandList(void* handle);
    
    // 统计信息
    size_t getBufferCount() const;
    size_t getTextureCount() const;
    size_t getPipelineCount() const;
    size_t getShaderCount() const;
    size_t getDescriptorSetCount() const;
    size_t getFenceCount() const;
    size_t getSemaphoreCount() const;
    size_t getCommandListCount() const;
    
    // 清理所有资源
    void cleanup();
    
    // 调试信息
    void printStatistics() const;
    
private:
    mutable std::mutex _mutex;
    std::atomic<uint64_t> _handleCounter{0x1000};
    
    // 资源映射表
    std::unordered_map<void*, BackendBuffer*> _buffers;
    std::unordered_map<void*, BackendTexture*> _textures;
    std::unordered_map<void*, BackendPipeline*> _pipelines;
    std::unordered_map<void*, BackendShader*> _shaders;
    std::unordered_map<void*, BackendDescriptorSet*> _descriptorSets;
    std::unordered_map<void*, BackendFence*> _fences;
    std::unordered_map<void*, BackendSemaphore*> _semaphores;
    std::unordered_map<void*, BackendCommandList*> _commandLists;
    
    // 辅助方法
    template<typename T>
    void registerResource(std::unordered_map<void*, T*>& map, void* handle, T* resource);
    
    template<typename T>
    void unregisterResource(std::unordered_map<void*, T*>& map, void* handle);
    
    template<typename T>
    T* getResource(const std::unordered_map<void*, T*>& map, void* handle) const;
};

// 模板方法实现
template<typename T>
void ResourceManager::registerResource(std::unordered_map<void*, T*>& map, void* handle, T* resource) {
    std::lock_guard<std::mutex> lock(_mutex);
    map[handle] = resource;
}

template<typename T>
void ResourceManager::unregisterResource(std::unordered_map<void*, T*>& map, void* handle) {
    std::lock_guard<std::mutex> lock(_mutex);
    map.erase(handle);
}

template<typename T>
T* ResourceManager::getResource(const std::unordered_map<void*, T*>& map, void* handle) const {
    std::lock_guard<std::mutex> lock(_mutex);
    auto it = map.find(handle);
    return (it != map.end()) ? it->second : nullptr;
}

} // namespace USG
