#include "WebGPUBackend.h"
#include <iostream>
#include <cassert>
#include <cstring>

#ifdef USG_PLATFORM_DESKTOP
    #include <GLFW/glfw3.h>
    #ifdef USG_USE_WGPU_NATIVE
        #include <glfw3webgpu.h>
    #endif
#endif

namespace USG {

// WebGPUBuffer实现
WebGPUBuffer::WebGPUBuffer(WGPUBuffer buffer, const BufferDesc& desc)
    : _buffer(buffer), _desc(desc) {
}

WebGPUBuffer::~WebGPUBuffer() {
    if (_mappedData) {
        wgpuBufferUnmap(_buffer);
        _mappedData = nullptr;
    }
    if (_buffer) {
        wgpuBufferRelease(_buffer);
    }
}

// WebGPUTexture实现
WebGPUTexture::WebGPUTexture(WGPUTexture texture, const TextureDesc& desc)
    : _texture(texture), _desc(desc), _textureView(nullptr) {
    // 创建默认纹理视图
    WGPUTextureViewDescriptor viewDesc = {};
    viewDesc.nextInChain = nullptr;
    viewDesc.label = "Default Texture View";
    viewDesc.format = static_cast<WGPUTextureFormat>(_desc.format);
    viewDesc.dimension = WGPUTextureViewDimension_2D;
    viewDesc.baseMipLevel = 0;
    viewDesc.mipLevelCount = _desc.mipLevels;
    viewDesc.baseArrayLayer = 0;
    viewDesc.arrayLayerCount = _desc.arrayLayers;
    viewDesc.aspect = WGPUTextureAspect_All;
    
    _textureView = wgpuTextureCreateView(_texture, &viewDesc);
}

WebGPUTexture::~WebGPUTexture() {
    if (_textureView) {
        wgpuTextureViewRelease(_textureView);
    }
    if (_texture) {
        wgpuTextureRelease(_texture);
    }
}

WGPUTextureView WebGPUTexture::createView(const WGPUTextureViewDescriptor* descriptor) {
    if (descriptor) {
        return wgpuTextureCreateView(_texture, descriptor);
    } else {
        // 返回默认视图的引用
        wgpuTextureViewReference(_textureView);
        return _textureView;
    }
}

// WebGPUBackend实现
WebGPUBackend::WebGPUBackend() 
    : _instance(nullptr)
    , _adapter(nullptr)
    , _device(nullptr)
    , _queue(nullptr)
    , _surface(nullptr)
    , _swapChain(nullptr)
    , _initialized(false) {
}

WebGPUBackend::~WebGPUBackend() {
    cleanup();
}

bool WebGPUBackend::initialize(const BackendConfig& config) {
    if (_initialized) {
        return true;
    }
    
    _config = config;
    
    std::cout << "[WebGPUBackend] Initializing WebGPU backend..." << std::endl;
    std::cout << "[WebGPUBackend] Using " << WebGPUAdapter::getImplementationName() << std::endl;
    
    // 初始化WebGPU适配器
    if (!WebGPUAdapter::initialize()) {
        std::cerr << "[WebGPUBackend] Failed to initialize WebGPU adapter" << std::endl;
        return false;
    }
    
    // 设置错误回调
    setupErrorHandling();
    
    // 创建实例
    if (!createInstance()) {
        return false;
    }
    
    // 创建表面（如果需要）
    if (config.nativeWindow && !createSurface()) {
        return false;
    }
    
    // 请求适配器
    if (!requestAdapter()) {
        return false;
    }
    
    // 请求设备
    if (!requestDevice()) {
        return false;
    }
    
    // 创建交换链（如果有表面）
    if (_surface && !createSwapChain()) {
        return false;
    }
    
    // 创建后端设备包装
    _backendDevice = std::make_unique<WebGPUDevice>(_device);
    
    _initialized = true;
    std::cout << "[WebGPUBackend] WebGPU backend initialized successfully" << std::endl;
    
    return true;
}

void WebGPUBackend::cleanup() {
    if (!_initialized) {
        return;
    }
    
    std::cout << "[WebGPUBackend] Cleaning up WebGPU backend..." << std::endl;
    
    // 等待设备空闲
    if (_device) {
        // 注意：不是所有WebGPU实现都有waitIdle
        // wgpuDeviceWaitIdle(_device);
    }
    
    // 清理后端设备
    _backendDevice.reset();
    
    // 释放WebGPU对象
    if (_swapChain) {
        wgpuSwapChainRelease(_swapChain);
        _swapChain = nullptr;
    }
    
    if (_queue) {
        wgpuQueueRelease(_queue);
        _queue = nullptr;
    }
    
    if (_device) {
        wgpuDeviceRelease(_device);
        _device = nullptr;
    }
    
    if (_adapter) {
        wgpuAdapterRelease(_adapter);
        _adapter = nullptr;
    }
    
    if (_surface) {
        wgpuSurfaceRelease(_surface);
        _surface = nullptr;
    }
    
    if (_instance) {
        wgpuInstanceRelease(_instance);
        _instance = nullptr;
    }
    
    // 清理WebGPU适配器
    WebGPUAdapter::cleanup();
    
    _initialized = false;
    std::cout << "[WebGPUBackend] WebGPU backend cleaned up" << std::endl;
}

void WebGPUBackend::beginFrame() {
    // WebGPU通常不需要显式的帧开始
}

void WebGPUBackend::endFrame() {
    // WebGPU通常不需要显式的帧结束
}

void WebGPUBackend::present() {
    if (_swapChain) {
        wgpuSwapChainPresent(_swapChain);
    }
}

void WebGPUBackend::waitIdle() {
    if (_device) {
        // 提交一个空的命令缓冲区并等待完成
        WGPUCommandEncoderDescriptor encoderDesc = {};
        encoderDesc.label = "Wait Idle Encoder";
        WGPUCommandEncoder encoder = wgpuDeviceCreateCommandEncoder(_device, &encoderDesc);
        
        WGPUCommandBufferDescriptor cmdBufferDesc = {};
        cmdBufferDesc.label = "Wait Idle Command Buffer";
        WGPUCommandBuffer cmdBuffer = wgpuCommandEncoderFinish(encoder, &cmdBufferDesc);
        
        wgpuQueueSubmit(_queue, 1, &cmdBuffer);
        
        wgpuCommandBufferRelease(cmdBuffer);
        wgpuCommandEncoderRelease(encoder);
        
        // 注意：这里应该等待队列完成，但WebGPU C API没有直接的等待方法
        // 在实际实现中可能需要使用回调或其他同步机制
    }
}

BackendBuffer* WebGPUBackend::createBuffer(const BufferDesc& desc) {
    if (!_device) {
        std::cerr << "[WebGPUBackend] Device not initialized" << std::endl;
        return nullptr;
    }
    
    WGPUBufferDescriptor bufferDesc = {};
    bufferDesc.nextInChain = nullptr;
    bufferDesc.label = desc.label.empty() ? "USG Buffer" : desc.label.c_str();
    bufferDesc.size = desc.size;
    bufferDesc.usage = convertBufferUsage(desc.usage);
    bufferDesc.mappedAtCreation = desc.mappedAtCreation;
    
    WGPUBuffer buffer = wgpuDeviceCreateBuffer(_device, &bufferDesc);
    if (!buffer) {
        std::cerr << "[WebGPUBackend] Failed to create buffer" << std::endl;
        return nullptr;
    }
    
    logMessage("Created buffer: size=" + std::to_string(desc.size) + 
               ", usage=" + std::to_string(static_cast<uint32_t>(desc.usage)));
    
    return new WebGPUBuffer(buffer, desc);
}

BackendTexture* WebGPUBackend::createTexture(const TextureDesc& desc) {
    if (!_device) {
        std::cerr << "[WebGPUBackend] Device not initialized" << std::endl;
        return nullptr;
    }
    
    WGPUTextureDescriptor textureDesc = {};
    textureDesc.nextInChain = nullptr;
    textureDesc.label = desc.label.empty() ? "USG Texture" : desc.label.c_str();
    textureDesc.size.width = desc.width;
    textureDesc.size.height = desc.height;
    textureDesc.size.depthOrArrayLayers = std::max(desc.depth, desc.arrayLayers);
    textureDesc.mipLevelCount = desc.mipLevels;
    textureDesc.sampleCount = desc.sampleCount;
    textureDesc.dimension = (desc.depth > 1) ? WGPUTextureDimension_3D : WGPUTextureDimension_2D;
    textureDesc.format = convertTextureFormat(desc.format);
    textureDesc.usage = convertTextureUsage(desc.usage);
    
    WGPUTexture texture = wgpuDeviceCreateTexture(_device, &textureDesc);
    if (!texture) {
        std::cerr << "[WebGPUBackend] Failed to create texture" << std::endl;
        return nullptr;
    }
    
    logMessage("Created texture: " + std::to_string(desc.width) + "x" + std::to_string(desc.height) +
               ", format=" + std::to_string(static_cast<uint32_t>(desc.format)));
    
    return new WebGPUTexture(texture, desc);
}

void WebGPUBackend::destroyBuffer(BackendBuffer* buffer) {
    delete buffer;
}

void WebGPUBackend::destroyTexture(BackendTexture* texture) {
    delete texture;
}

void WebGPUBackend::setErrorCallback(std::function<void(const std::string&)> callback) {
    _errorCallback = callback;
}

void WebGPUBackend::setDebugCallback(std::function<void(const std::string&)> callback) {
    _debugCallback = callback;
}

// 私有方法实现
bool WebGPUBackend::createInstance() {
    _instance = WebGPUAdapter::createInstance();
    if (!_instance) {
        logError("Failed to create WebGPU instance");
        return false;
    }
    
    logMessage("Created WebGPU instance");
    return true;
}

bool WebGPUBackend::requestAdapter() {
    std::promise<WGPUAdapter> adapterPromise;
    auto adapterFuture = adapterPromise.get_future();
    
    WGPURequestAdapterOptions options = {};
    options.powerPreference = WGPUPowerPreference_HighPerformance;
    options.compatibleSurface = _surface;
    
    WebGPUAdapter::requestAdapter(_instance, &options, 
        [&adapterPromise](WGPUAdapter adapter) {
            adapterPromise.set_value(adapter);
        });
    
    // 等待适配器
    _adapter = adapterFuture.get();
    if (!_adapter) {
        logError("Failed to request WebGPU adapter");
        return false;
    }
    
    logMessage("Requested WebGPU adapter");
    return true;
}

bool WebGPUBackend::requestDevice() {
    std::promise<WGPUDevice> devicePromise;
    auto deviceFuture = devicePromise.get_future();
    
    WGPUDeviceDescriptor deviceDesc = {};
    deviceDesc.label = "USG WebGPU Device";
    deviceDesc.defaultQueue.label = "USG Default Queue";
    
    WebGPUAdapter::requestDevice(_adapter, &deviceDesc,
        [&devicePromise](WGPUDevice device) {
            devicePromise.set_value(device);
        });
    
    // 等待设备
    _device = deviceFuture.get();
    if (!_device) {
        logError("Failed to request WebGPU device");
        return false;
    }
    
    // 获取队列
    _queue = wgpuDeviceGetQueue(_device);
    if (!_queue) {
        logError("Failed to get device queue");
        return false;
    }
    
    logMessage("Requested WebGPU device and queue");
    return true;
}

void WebGPUBackend::setupErrorHandling() {
    WebGPUAdapter::setErrorCallback([this](WGPUErrorType type, const char* message) {
        std::string errorMsg = "WebGPU Error [" + std::string(WebGPUDebug::errorTypeToString(type)) + "]: " + 
                              (message ? message : "Unknown error");
        logError(errorMsg);
    });
}

void WebGPUBackend::logMessage(const std::string& message) {
    if (_debugCallback) {
        _debugCallback("[WebGPUBackend] " + message);
    } else {
        std::cout << "[WebGPUBackend] " << message << std::endl;
    }
}

void WebGPUBackend::logError(const std::string& error) {
    if (_errorCallback) {
        _errorCallback("[WebGPUBackend] " + error);
    } else {
        std::cerr << "[WebGPUBackend] " << error << std::endl;
    }
}

} // namespace USG
