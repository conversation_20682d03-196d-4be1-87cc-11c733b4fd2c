#include "WebGPUBackend.h"
#include <iostream>
#include <cassert>
#include <cstring>

#ifdef USG_PLATFORM_DESKTOP
#include <GLFW/glfw3.h>
#ifdef USG_USE_WGPU_NATIVE
#include <glfw3webgpu.h>
#endif
#endif

namespace USG
{

    // WebGPUBuffer实现
    WebGPUBuffer::WebGPUBuffer(WGPUBuffer buffer, const BufferDesc &desc)
        : _buffer(buffer), _desc(desc)
    {
    }

    WebGPUBuffer::~WebGPUBuffer()
    {
        if (_mappedData)
        {
            wgpuBufferUnmap(_buffer);
            _mappedData = nullptr;
        }
        if (_buffer)
        {
            wgpuBufferRelease(_buffer);
        }
    }

    // WebGPUTexture实现
    WebGPUTexture::WebGPUTexture(WGPUTexture texture, const TextureDesc &desc)
        : _texture(texture), _desc(desc), _textureView(nullptr)
    {
        // 创建默认纹理视图
        WGPUTextureViewDescriptor viewDesc = {};
        viewDesc.nextInChain = nullptr;
        viewDesc.label = "Default Texture View";
        viewDesc.format = static_cast<WGPUTextureFormat>(_desc.format);
        viewDesc.dimension = WGPUTextureViewDimension_2D;
        viewDesc.baseMipLevel = 0;
        viewDesc.mipLevelCount = _desc.mipLevels;
        viewDesc.baseArrayLayer = 0;
        viewDesc.arrayLayerCount = _desc.arrayLayers;
        viewDesc.aspect = WGPUTextureAspect_All;

        _textureView = wgpuTextureCreateView(_texture, &viewDesc);
    }

    WebGPUTexture::~WebGPUTexture()
    {
        if (_textureView)
        {
            wgpuTextureViewRelease(_textureView);
        }
        if (_texture)
        {
            wgpuTextureRelease(_texture);
        }
    }

    WGPUTextureView WebGPUTexture::createView(const WGPUTextureViewDescriptor *descriptor)
    {
        if (descriptor)
        {
            return wgpuTextureCreateView(_texture, descriptor);
        }
        else
        {
            // 返回默认视图的引用
            wgpuTextureViewReference(_textureView);
            return _textureView;
        }
    }

    // WebGPUBackend实现
    WebGPUBackend::WebGPUBackend()
        : _instance(nullptr), _adapter(nullptr), _device(nullptr), _queue(nullptr), _surface(nullptr), _swapChain(nullptr), _initialized(false)
    {
    }

    WebGPUBackend::~WebGPUBackend()
    {
        cleanup();
    }

    bool WebGPUBackend::initialize(const BackendConfig &config)
    {
        if (_initialized)
        {
            return true;
        }

        _config = config;

        std::cout << "[WebGPUBackend] Initializing WebGPU backend..." << std::endl;
        std::cout << "[WebGPUBackend] Using " << WebGPUAdapter::getImplementationName() << std::endl;

        // 初始化WebGPU适配器
        if (!WebGPUAdapter::initialize())
        {
            std::cerr << "[WebGPUBackend] Failed to initialize WebGPU adapter" << std::endl;
            return false;
        }

        // 设置错误回调
        setupErrorHandling();

        // 创建实例
        if (!createInstance())
        {
            return false;
        }

        // 创建表面（如果需要）
        if (config.nativeWindow && !createSurface())
        {
            return false;
        }

        // 请求适配器
        if (!requestAdapter())
        {
            return false;
        }

        // 请求设备
        if (!requestDevice())
        {
            return false;
        }

        // 创建交换链（如果有表面）
        if (_surface && !createSwapChain())
        {
            return false;
        }

        // 创建后端设备包装
        _backendDevice = std::make_unique<WebGPUDevice>(_device);

        _initialized = true;
        std::cout << "[WebGPUBackend] WebGPU backend initialized successfully" << std::endl;

        return true;
    }

    void WebGPUBackend::cleanup()
    {
        if (!_initialized)
        {
            return;
        }

        std::cout << "[WebGPUBackend] Cleaning up WebGPU backend..." << std::endl;

        // 等待设备空闲
        if (_device)
        {
            // 注意：不是所有WebGPU实现都有waitIdle
            // wgpuDeviceWaitIdle(_device);
        }

        // 清理后端设备
        _backendDevice.reset();

        // 释放WebGPU对象
        if (_swapChain)
        {
            wgpuSwapChainRelease(_swapChain);
            _swapChain = nullptr;
        }

        if (_queue)
        {
            wgpuQueueRelease(_queue);
            _queue = nullptr;
        }

        if (_device)
        {
            wgpuDeviceRelease(_device);
            _device = nullptr;
        }

        if (_adapter)
        {
            wgpuAdapterRelease(_adapter);
            _adapter = nullptr;
        }

        if (_surface)
        {
            wgpuSurfaceRelease(_surface);
            _surface = nullptr;
        }

        if (_instance)
        {
            wgpuInstanceRelease(_instance);
            _instance = nullptr;
        }

        // 清理WebGPU适配器
        WebGPUAdapter::cleanup();

        _initialized = false;
        std::cout << "[WebGPUBackend] WebGPU backend cleaned up" << std::endl;
    }

    void WebGPUBackend::beginFrame()
    {
        // WebGPU通常不需要显式的帧开始
    }

    void WebGPUBackend::endFrame()
    {
        // WebGPU通常不需要显式的帧结束
    }

    void WebGPUBackend::present()
    {
        if (_swapChain)
        {
            wgpuSwapChainPresent(_swapChain);
        }
    }

    void WebGPUBackend::waitIdle()
    {
        if (_device)
        {
            // 提交一个空的命令缓冲区并等待完成
            WGPUCommandEncoderDescriptor encoderDesc = {};
            encoderDesc.label = "Wait Idle Encoder";
            WGPUCommandEncoder encoder = wgpuDeviceCreateCommandEncoder(_device, &encoderDesc);

            WGPUCommandBufferDescriptor cmdBufferDesc = {};
            cmdBufferDesc.label = "Wait Idle Command Buffer";
            WGPUCommandBuffer cmdBuffer = wgpuCommandEncoderFinish(encoder, &cmdBufferDesc);

            wgpuQueueSubmit(_queue, 1, &cmdBuffer);

            wgpuCommandBufferRelease(cmdBuffer);
            wgpuCommandEncoderRelease(encoder);

            // 注意：这里应该等待队列完成，但WebGPU C API没有直接的等待方法
            // 在实际实现中可能需要使用回调或其他同步机制
        }
    }

    BackendBuffer *WebGPUBackend::createBuffer(const BufferDesc &desc)
    {
        if (!_device)
        {
            std::cerr << "[WebGPUBackend] Device not initialized" << std::endl;
            return nullptr;
        }

        WGPUBufferDescriptor bufferDesc = {};
        bufferDesc.nextInChain = nullptr;
        bufferDesc.label = desc.label.empty() ? "USG Buffer" : desc.label.c_str();
        bufferDesc.size = desc.size;
        bufferDesc.usage = convertBufferUsage(desc.usage);
        bufferDesc.mappedAtCreation = desc.mappedAtCreation;

        WGPUBuffer buffer = wgpuDeviceCreateBuffer(_device, &bufferDesc);
        if (!buffer)
        {
            std::cerr << "[WebGPUBackend] Failed to create buffer" << std::endl;
            return nullptr;
        }

        logMessage("Created buffer: size=" + std::to_string(desc.size) +
                   ", usage=" + std::to_string(static_cast<uint32_t>(desc.usage)));

        return new WebGPUBuffer(buffer, desc);
    }

    BackendTexture *WebGPUBackend::createTexture(const TextureDesc &desc)
    {
        if (!_device)
        {
            std::cerr << "[WebGPUBackend] Device not initialized" << std::endl;
            return nullptr;
        }

        WGPUTextureDescriptor textureDesc = {};
        textureDesc.nextInChain = nullptr;
        textureDesc.label = desc.label.empty() ? "USG Texture" : desc.label.c_str();
        textureDesc.size.width = desc.width;
        textureDesc.size.height = desc.height;
        textureDesc.size.depthOrArrayLayers = std::max(desc.depth, desc.arrayLayers);
        textureDesc.mipLevelCount = desc.mipLevels;
        textureDesc.sampleCount = desc.sampleCount;
        textureDesc.dimension = (desc.depth > 1) ? WGPUTextureDimension_3D : WGPUTextureDimension_2D;
        textureDesc.format = convertTextureFormat(desc.format);
        textureDesc.usage = convertTextureUsage(desc.usage);

        WGPUTexture texture = wgpuDeviceCreateTexture(_device, &textureDesc);
        if (!texture)
        {
            std::cerr << "[WebGPUBackend] Failed to create texture" << std::endl;
            return nullptr;
        }

        logMessage("Created texture: " + std::to_string(desc.width) + "x" + std::to_string(desc.height) +
                   ", format=" + std::to_string(static_cast<uint32_t>(desc.format)));

        return new WebGPUTexture(texture, desc);
    }

    void WebGPUBackend::destroyBuffer(BackendBuffer *buffer)
    {
        delete buffer;
    }

    void WebGPUBackend::destroyTexture(BackendTexture *texture)
    {
        delete texture;
    }

    void WebGPUBackend::setErrorCallback(std::function<void(const std::string &)> callback)
    {
        _errorCallback = callback;
    }

    void WebGPUBackend::setDebugCallback(std::function<void(const std::string &)> callback)
    {
        _debugCallback = callback;
    }

    // 私有方法实现
    bool WebGPUBackend::createInstance()
    {
        _instance = WebGPUAdapter::createInstance();
        if (!_instance)
        {
            logError("Failed to create WebGPU instance");
            return false;
        }

        logMessage("Created WebGPU instance");
        return true;
    }

    bool WebGPUBackend::requestAdapter()
    {
        std::promise<WGPUAdapter> adapterPromise;
        auto adapterFuture = adapterPromise.get_future();

        WGPURequestAdapterOptions options = {};
        options.powerPreference = WGPUPowerPreference_HighPerformance;
        options.compatibleSurface = _surface;

        WebGPUAdapter::requestAdapter(_instance, &options,
                                      [&adapterPromise](WGPUAdapter adapter)
                                      {
                                          adapterPromise.set_value(adapter);
                                      });

        // 等待适配器
        _adapter = adapterFuture.get();
        if (!_adapter)
        {
            logError("Failed to request WebGPU adapter");
            return false;
        }

        logMessage("Requested WebGPU adapter");
        return true;
    }

    bool WebGPUBackend::requestDevice()
    {
        std::promise<WGPUDevice> devicePromise;
        auto deviceFuture = devicePromise.get_future();

        WGPUDeviceDescriptor deviceDesc = {};
        deviceDesc.label = "USG WebGPU Device";
        deviceDesc.defaultQueue.label = "USG Default Queue";

        WebGPUAdapter::requestDevice(_adapter, &deviceDesc,
                                     [&devicePromise](WGPUDevice device)
                                     {
                                         devicePromise.set_value(device);
                                     });

        // 等待设备
        _device = deviceFuture.get();
        if (!_device)
        {
            logError("Failed to request WebGPU device");
            return false;
        }

        // 获取队列
        _queue = wgpuDeviceGetQueue(_device);
        if (!_queue)
        {
            logError("Failed to get device queue");
            return false;
        }

        logMessage("Requested WebGPU device and queue");
        return true;
    }

    void WebGPUBackend::setupErrorHandling()
    {
        WebGPUAdapter::setErrorCallback([this](WGPUErrorType type, const char *message)
                                        {
        std::string errorMsg = "WebGPU Error [" + std::string(WebGPUDebug::errorTypeToString(type)) + "]: " + 
                              (message ? message : "Unknown error");
        logError(errorMsg); });
    }

    void WebGPUBackend::logMessage(const std::string &message)
    {
        if (_debugCallback)
        {
            _debugCallback("[WebGPUBackend] " + message);
        }
        else
        {
            std::cout << "[WebGPUBackend] " << message << std::endl;
        }
    }

    void WebGPUBackend::logError(const std::string &error)
    {
        if (_errorCallback)
        {
            _errorCallback("[WebGPUBackend] " + error);
        }
        else
        {
            std::cerr << "[WebGPUBackend] " << error << std::endl;
        }
    }

    bool WebGPUBackend::createSurface()
    {
        if (!_config.nativeWindow)
        {
            logMessage("No native window provided, skipping surface creation");
            return true; // 不是错误，某些用例不需要表面
        }

#ifdef USG_PLATFORM_DESKTOP
        // 桌面平台使用GLFW创建表面
        _surface = WebGPUPlatform::createSurfaceFromGLFW(_instance, _config.nativeWindow);
#elif defined(USG_PLATFORM_WASM)
        // WebAssembly平台使用Canvas创建表面
        _surface = WebGPUPlatform::createSurfaceFromCanvas(_instance, "canvas");
#endif

        if (!_surface)
        {
            logError("Failed to create surface");
            return false;
        }

        logMessage("Created surface successfully");
        return true;
    }

    bool WebGPUBackend::createSwapChain()
    {
        if (!_surface)
        {
            logMessage("No surface available, skipping swap chain creation");
            return true;
        }

        // 获取表面首选格式
        WGPUTextureFormat preferredFormat = WebGPUPlatform::getPreferredSwapChainFormat(_surface);

        // 创建交换链描述符
        WGPUSwapChainDescriptor swapChainDesc = {};
        swapChainDesc.nextInChain = nullptr;
        swapChainDesc.label = "USG SwapChain";
        swapChainDesc.usage = WGPUTextureUsage_RenderAttachment;
        swapChainDesc.format = preferredFormat;
        swapChainDesc.width = _config.windowWidth > 0 ? _config.windowWidth : 800;
        swapChainDesc.height = _config.windowHeight > 0 ? _config.windowHeight : 600;
        swapChainDesc.presentMode = WGPUPresentMode_Fifo; // VSync

        _swapChain = wgpuDeviceCreateSwapChain(_device, _surface, &swapChainDesc);
        if (!_swapChain)
        {
            logError("Failed to create swap chain");
            return false;
        }

        logMessage("Created swap chain: " + std::to_string(swapChainDesc.width) + "x" + std::to_string(swapChainDesc.height));
        return true;
    }

    BackendShader *WebGPUBackend::createShader(const ShaderDesc &desc)
    {
        if (!_device)
        {
            logError("Device not initialized");
            return nullptr;
        }

        WGPUShaderModuleDescriptor shaderDesc = {};
        shaderDesc.nextInChain = nullptr;
        shaderDesc.label = desc.label.empty() ? "USG Shader" : desc.label.c_str();

        // 设置WGSL代码
        WGPUShaderModuleWGSLDescriptor wgslDesc = {};
        wgslDesc.chain.next = nullptr;
        wgslDesc.chain.sType = WGPUSType_ShaderModuleWGSLDescriptor;

        // 如果是字节码，需要转换为WGSL
        std::string wgslCode;
        if (!desc.bytecode.empty())
        {
            // 简化实现：假设字节码已经是WGSL格式
            wgslCode = std::string(desc.bytecode.begin(), desc.bytecode.end());
        }
        else
        {
            // 默认的顶点着色器
            wgslCode = R"(
            @vertex
            fn vs_main(@builtin(vertex_index) vertexIndex: u32) -> @builtin(position) vec4<f32> {
                var pos = array<vec2<f32>, 3>(
                    vec2<f32>( 0.0,  0.5),
                    vec2<f32>(-0.5, -0.5),
                    vec2<f32>( 0.5, -0.5)
                );
                return vec4<f32>(pos[vertexIndex], 0.0, 1.0);
            }
        )";
        }

        wgslDesc.code = wgslCode.c_str();
        shaderDesc.nextInChain = &wgslDesc.chain;

        WGPUShaderModule shaderModule = wgpuDeviceCreateShaderModule(_device, &shaderDesc);
        if (!shaderModule)
        {
            logError("Failed to create shader module");
            return nullptr;
        }

        logMessage("Created shader: " + desc.label);
        return new WebGPUShader(shaderModule, desc);
    }

    BackendPipeline *WebGPUBackend::createPipeline(const PipelineDesc &desc)
    {
        if (!_device)
        {
            logError("Device not initialized");
            return nullptr;
        }

        if (desc.isComputePipeline)
        {
            return createComputePipeline(desc);
        }
        else
        {
            return createRenderPipeline(desc);
        }
    }

    BackendPipeline *WebGPUBackend::createRenderPipeline(const PipelineDesc &desc)
    {
        WGPURenderPipelineDescriptor pipelineDesc = {};
        pipelineDesc.nextInChain = nullptr;
        pipelineDesc.label = desc.label.empty() ? "USG Render Pipeline" : desc.label.c_str();

        // 顶点阶段
        WGPUVertexState vertexState = {};
        if (desc.vertexShader)
        {
            auto *webgpuShader = static_cast<WebGPUShader *>(desc.vertexShader);
            vertexState.module = webgpuShader->getWGPUShaderModule();
            vertexState.entryPoint = desc.vertexEntryPoint.empty() ? "vs_main" : desc.vertexEntryPoint.c_str();
        }
        pipelineDesc.vertex = vertexState;

        // 片段阶段
        WGPUFragmentState fragmentState = {};
        WGPUColorTargetState colorTarget = {};
        if (desc.fragmentShader)
        {
            auto *webgpuShader = static_cast<WebGPUShader *>(desc.fragmentShader);
            fragmentState.module = webgpuShader->getWGPUShaderModule();
            fragmentState.entryPoint = desc.fragmentEntryPoint.empty() ? "fs_main" : desc.fragmentEntryPoint.c_str();

            colorTarget.format = WGPUTextureFormat_BGRA8Unorm; // 默认格式
            colorTarget.writeMask = WGPUColorWriteMask_All;

            fragmentState.targetCount = 1;
            fragmentState.targets = &colorTarget;

            pipelineDesc.fragment = &fragmentState;
        }

        // 图元状态
        WGPUPrimitiveState primitiveState = {};
        primitiveState.topology = convertPrimitiveTopology(desc.primitiveTopology);
        primitiveState.stripIndexFormat = WGPUIndexFormat_Undefined;
        primitiveState.frontFace = WGPUFrontFace_CCW;
        primitiveState.cullMode = WGPUCullMode_None;
        pipelineDesc.primitive = primitiveState;

        // 多重采样状态
        WGPUMultisampleState multisampleState = {};
        multisampleState.count = desc.sampleCount;
        multisampleState.mask = 0xFFFFFFFF;
        multisampleState.alphaToCoverageEnabled = false;
        pipelineDesc.multisample = multisampleState;

        WGPURenderPipeline pipeline = wgpuDeviceCreateRenderPipeline(_device, &pipelineDesc);
        if (!pipeline)
        {
            logError("Failed to create render pipeline");
            return nullptr;
        }

        logMessage("Created render pipeline: " + desc.label);
        return new WebGPUPipeline(pipeline, desc);
    }

    BackendPipeline *WebGPUBackend::createComputePipeline(const PipelineDesc &desc)
    {
        WGPUComputePipelineDescriptor pipelineDesc = {};
        pipelineDesc.nextInChain = nullptr;
        pipelineDesc.label = desc.label.empty() ? "USG Compute Pipeline" : desc.label.c_str();

        // 计算阶段
        if (desc.computeShader)
        {
            auto *webgpuShader = static_cast<WebGPUShader *>(desc.computeShader);
            pipelineDesc.compute.module = webgpuShader->getWGPUShaderModule();
            pipelineDesc.compute.entryPoint = desc.computeEntryPoint.empty() ? "cs_main" : desc.computeEntryPoint.c_str();
        }

        WGPUComputePipeline pipeline = wgpuDeviceCreateComputePipeline(_device, &pipelineDesc);
        if (!pipeline)
        {
            logError("Failed to create compute pipeline");
            return nullptr;
        }

        logMessage("Created compute pipeline: " + desc.label);
        return new WebGPUPipeline(pipeline, desc);
    }

    BackendDescriptorSet *WebGPUBackend::createDescriptorSet()
    {
        if (!_device)
        {
            logError("Device not initialized");
            return nullptr;
        }

        // 创建空的绑定组（稍后绑定资源）
        WGPUBindGroupDescriptor bindGroupDesc = {};
        bindGroupDesc.nextInChain = nullptr;
        bindGroupDesc.label = "USG DescriptorSet";
        bindGroupDesc.layout = nullptr; // 将在绑定资源时设置
        bindGroupDesc.entryCount = 0;
        bindGroupDesc.entries = nullptr;

        // 暂时创建一个空的绑定组
        WGPUBindGroup bindGroup = nullptr; // 稍后创建

        logMessage("Created descriptor set");
        return new WebGPUDescriptorSet(bindGroup);
    }

    BackendCommandList *WebGPUBackend::createCommandList()
    {
        if (!_device)
        {
            logError("Device not initialized");
            return nullptr;
        }

        logMessage("Created command list");
        return new WebGPUCommandList(_device);
    }

    BackendFence *WebGPUBackend::createFence()
    {
        logMessage("Created fence");
        return new WebGPUFence();
    }

    BackendSemaphore *WebGPUBackend::createSemaphore()
    {
        logMessage("Created semaphore");
        return new WebGPUSemaphore();
    }

    void WebGPUBackend::executeCommandList(BackendCommandList *cmdList,
                                           const std::vector<BackendSemaphore *> &waitSemaphores,
                                           const std::vector<BackendSemaphore *> &signalSemaphores,
                                           BackendFence *fence)
    {
        if (!_device || !_queue || !cmdList)
        {
            logError("Invalid parameters for command list execution");
            return;
        }

        auto *webgpuCmdList = static_cast<WebGPUCommandList *>(cmdList);
        WGPUCommandBuffer commandBuffer = webgpuCmdList->getWGPUCommandBuffer();

        if (commandBuffer)
        {
            wgpuQueueSubmit(_queue, 1, &commandBuffer);
            logMessage("Executed command list");
        }

        // 处理同步对象
        for (auto *semaphore : signalSemaphores)
        {
            // WebGPU没有显式的信号量，使用fence模拟
            if (auto *webgpuSemaphore = static_cast<WebGPUSemaphore *>(semaphore))
            {
                // 标记信号量为已信号状态
            }
        }

        if (fence)
        {
            auto *webgpuFence = static_cast<WebGPUFence *>(fence);
            webgpuFence->signal();
        }
    }

    void WebGPUBackend::destroyShader(BackendShader *shader)
    {
        delete shader;
    }

    void WebGPUBackend::destroyPipeline(BackendPipeline *pipeline)
    {
        delete pipeline;
    }

    void WebGPUBackend::destroyDescriptorSet(BackendDescriptorSet *descriptorSet)
    {
        delete descriptorSet;
    }

    void WebGPUBackend::destroyCommandList(BackendCommandList *cmdList)
    {
        delete cmdList;
    }

    void WebGPUBackend::destroyFence(BackendFence *fence)
    {
        delete fence;
    }

    void WebGPUBackend::destroySemaphore(BackendSemaphore *semaphore)
    {
        delete semaphore;
    }

} // namespace USG
