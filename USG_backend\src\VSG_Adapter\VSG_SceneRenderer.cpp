#include <VSG_Adapter/VSG_SceneRenderer.h>
#include <VSG_Adapter/VSG_BackendBridge.h>
#include <USG_Backend/RenderBackend.h>
#include <iostream>

namespace USG {

VSG_SceneRenderer::VSG_SceneRenderer() {
}

VSG_SceneRenderer::~VSG_SceneRenderer() {
    cleanup();
}

bool VSG_SceneRenderer::initialize(RenderBackend* backend, const RenderConfig& config) {
    if (!backend) {
        std::cerr << "[VSG_SceneRenderer] Invalid backend" << std::endl;
        return false;
    }
    
    _backend = backend;
    _renderConfig = config;
    
    std::cout << "[VSG_SceneRenderer] Initializing VSG scene renderer..." << std::endl;
    
    // 创建后端桥接器
    _bridge = std::make_unique<VSG_BackendBridge>();
    if (!_bridge->initialize(backend)) {
        std::cerr << "[VSG_SceneRenderer] Failed to initialize backend bridge" << std::endl;
        return false;
    }
    
    // TODO: 创建VSG对象
    // 由于我们没有直接链接VSG库，这里使用占位符实现
    
    _initialized = true;
    
    std::cout << "[VSG_SceneRenderer] VSG scene renderer initialized successfully" << std::endl;
    return true;
}

void VSG_SceneRenderer::cleanup() {
    if (!_initialized) {
        return;
    }
    
    std::cout << "[VSG_SceneRenderer] Cleaning up VSG scene renderer..." << std::endl;
    
    // 清理VSG对象
    _sceneNodes.clear();
    
    // 清理桥接器
    if (_bridge) {
        _bridge->cleanup();
        _bridge.reset();
    }
    
    _backend = nullptr;
    _initialized = false;
    
    std::cout << "[VSG_SceneRenderer] VSG scene renderer cleaned up" << std::endl;
}

void VSG_SceneRenderer::setSceneRoot(std::shared_ptr<vsg::Node> root) {
    // TODO: 实现VSG场景根节点设置
    std::cout << "[VSG_SceneRenderer] Set scene root (placeholder)" << std::endl;
}

void VSG_SceneRenderer::addSceneNode(std::shared_ptr<vsg::Node> node, const std::string& name) {
    SceneNode sceneNode;
    sceneNode.node = node;
    sceneNode.name = name.empty() ? ("Node_" + std::to_string(_sceneNodes.size())) : name;
    sceneNode.visible = true;
    
    _sceneNodes.push_back(sceneNode);
    
    std::cout << "[VSG_SceneRenderer] Added scene node: " << sceneNode.name << std::endl;
}

void VSG_SceneRenderer::removeSceneNode(const std::string& name) {
    auto it = std::find_if(_sceneNodes.begin(), _sceneNodes.end(),
        [&name](const SceneNode& node) { return node.name == name; });
    
    if (it != _sceneNodes.end()) {
        std::cout << "[VSG_SceneRenderer] Removed scene node: " << name << std::endl;
        _sceneNodes.erase(it);
    }
}

void VSG_SceneRenderer::setCameraConfig(const CameraConfig& config) {
    _cameraConfig = config;
    
    // TODO: 更新VSG相机
    std::cout << "[VSG_SceneRenderer] Updated camera config" << std::endl;
}

void VSG_SceneRenderer::setRenderConfig(const RenderConfig& config) {
    _renderConfig = config;
    
    // TODO: 更新渲染配置
    std::cout << "[VSG_SceneRenderer] Updated render config" << std::endl;
}

bool VSG_SceneRenderer::renderFrame() {
    if (!_initialized || !_backend) {
        return false;
    }
    
    // 简化的渲染实现
    // TODO: 实现完整的VSG场景渲染
    
    // 更新统计信息
    _renderStats.frameCount++;
    
    return true;
}

void VSG_SceneRenderer::resize(uint32_t width, uint32_t height) {
    _renderConfig.windowWidth = width;
    _renderConfig.windowHeight = height;
    
    // 更新相机宽高比
    _cameraConfig.aspectRatio = static_cast<float>(width) / static_cast<float>(height);
    
    std::cout << "[VSG_SceneRenderer] Resized to " << width << "x" << height << std::endl;
}

BackendType VSG_SceneRenderer::getCurrentBackendType() const {
    return _backend ? _backend->getBackendType() : BackendType::Unknown;
}

bool VSG_SceneRenderer::switchBackend(RenderBackend* newBackend) {
    if (!newBackend) {
        return false;
    }
    
    std::cout << "[VSG_SceneRenderer] Switching backend..." << std::endl;
    
    // 保存当前状态
    auto oldConfig = _renderConfig;
    auto oldCameraConfig = _cameraConfig;
    auto oldNodes = _sceneNodes;
    
    // 清理当前资源
    cleanup();
    
    // 使用新后端重新初始化
    if (!initialize(newBackend, oldConfig)) {
        std::cerr << "[VSG_SceneRenderer] Failed to reinitialize with new backend" << std::endl;
        return false;
    }
    
    // 恢复状态
    _cameraConfig = oldCameraConfig;
    _sceneNodes = oldNodes;
    
    std::cout << "[VSG_SceneRenderer] Backend switch completed" << std::endl;
    return true;
}

void VSG_SceneRenderer::setFrameCallback(std::function<void(double deltaTime)> callback) {
    _frameCallback = callback;
}

} // namespace USG
