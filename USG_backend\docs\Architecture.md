# USG Backend 架构设计

## 1. 整体架构概览

USG Backend采用分层插件化架构，通过适配器模式实现对现有图形引擎的透明替换。

```mermaid
graph TB
    subgraph "应用层 (Application Layer)"
        App[VSG Application]
        Scene[Scene Graph]
        Nodes[VSG Nodes]
    end
    
    subgraph "VSG API层 (VSG API Layer)"
        VSGDevice[vsg::Device]
        VSGCmd[vsg::CommandBuffer]
        VSGPipeline[vsg::Pipeline]
        VSGBuffer[vsg::Buffer]
    end
    
    subgraph "USG适配器层 (USG Adapter Layer)"
        AdapterDevice[VSG_Device Adapter]
        AdapterCmd[VSG_CommandBuffer Adapter]
        AdapterPipeline[VSG_Pipeline Adapter]
        AdapterBuffer[VSG_Buffer Adapter]
    end
    
    subgraph "USG抽象层 (USG Abstract Layer)"
        RenderBackend[RenderBackend Interface]
        BackendDevice[BackendDevice Interface]
        BackendCmd[BackendCommandList Interface]
        BackendPipeline[BackendPipeline Interface]
    end
    
    subgraph "USG实现层 (USG Implementation Layer)"
        WebGPUBackend[WebGPU Backend]
        VulkanBackend[Vulkan Backend]
        OpenGLBackend[OpenGL Backend]
    end
    
    App --> Scene
    Scene --> Nodes
    Nodes --> VSGDevice
    VSGDevice --> AdapterDevice
    AdapterDevice --> RenderBackend
    RenderBackend --> WebGPUBackend
    
    VSGCmd --> AdapterCmd
    AdapterCmd --> BackendCmd
    BackendCmd --> WebGPUBackend
```

## 2. 核心设计原则

### 2.1 零侵入原则
- **不修改VSG源码**: 通过动态链接和符号替换实现
- **不修改应用代码**: 应用层API调用完全不变
- **运行时替换**: 支持运行时动态切换后端

### 2.2 透明替换原则
- **API兼容**: 保持VSG API的完全兼容性
- **行为一致**: 渲染结果与原VSG完全一致
- **性能对等**: 性能不低于原VSG实现

### 2.3 面向接口设计
- **抽象接口**: 定义统一的后端抽象接口
- **插件化**: 支持动态加载不同后端实现
- **可扩展**: 易于添加新的后端和适配器

## 3. 关键组件设计

### 3.1 后端抽象接口

```cpp
namespace USG {

// 渲染后端抽象接口
class RenderBackend {
public:
    virtual ~RenderBackend() = default;
    
    // 生命周期管理
    virtual bool initialize(const BackendConfig& config) = 0;
    virtual void cleanup() = 0;
    virtual void present() = 0;
    
    // 设备管理
    virtual BackendDevice* getDevice() = 0;
    virtual BackendQueue* getQueue() = 0;
    
    // 资源创建
    virtual BackendBuffer* createBuffer(const BufferDesc& desc) = 0;
    virtual BackendTexture* createTexture(const TextureDesc& desc) = 0;
    virtual BackendShader* createShader(const ShaderDesc& desc) = 0;
    virtual BackendPipeline* createPipeline(const PipelineDesc& desc) = 0;
    
    // 命令记录
    virtual BackendCommandList* createCommandList() = 0;
    virtual void executeCommandList(BackendCommandList* cmdList) = 0;
    
    // 同步操作
    virtual void waitIdle() = 0;
    virtual BackendFence* createFence() = 0;
    virtual void waitForFence(BackendFence* fence) = 0;
};

// 后端设备接口
class BackendDevice {
public:
    virtual ~BackendDevice() = default;
    
    // 设备信息
    virtual std::string getDeviceName() const = 0;
    virtual BackendType getBackendType() const = 0;
    virtual DeviceCapabilities getCapabilities() const = 0;
    
    // 内存管理
    virtual void* mapBuffer(BackendBuffer* buffer, size_t offset, size_t size) = 0;
    virtual void unmapBuffer(BackendBuffer* buffer) = 0;
    virtual void updateBuffer(BackendBuffer* buffer, const void* data, size_t size, size_t offset = 0) = 0;
    
    // 纹理操作
    virtual void updateTexture(BackendTexture* texture, const void* data, const TextureRegion& region) = 0;
    virtual void generateMipmaps(BackendTexture* texture) = 0;
};

// 命令列表接口
class BackendCommandList {
public:
    virtual ~BackendCommandList() = default;
    
    // 命令记录控制
    virtual void begin() = 0;
    virtual void end() = 0;
    virtual void reset() = 0;
    
    // 渲染通道
    virtual void beginRenderPass(const RenderPassDesc& desc) = 0;
    virtual void endRenderPass() = 0;
    
    // 管线和状态
    virtual void setPipeline(BackendPipeline* pipeline) = 0;
    virtual void setVertexBuffer(BackendBuffer* buffer, uint32_t slot, size_t offset = 0) = 0;
    virtual void setIndexBuffer(BackendBuffer* buffer, IndexFormat format, size_t offset = 0) = 0;
    virtual void setDescriptorSet(BackendDescriptorSet* descriptorSet, uint32_t slot) = 0;
    
    // 绘制命令
    virtual void draw(uint32_t vertexCount, uint32_t instanceCount = 1, uint32_t firstVertex = 0, uint32_t firstInstance = 0) = 0;
    virtual void drawIndexed(uint32_t indexCount, uint32_t instanceCount = 1, uint32_t firstIndex = 0, int32_t vertexOffset = 0, uint32_t firstInstance = 0) = 0;
    
    // 计算命令
    virtual void dispatch(uint32_t groupCountX, uint32_t groupCountY = 1, uint32_t groupCountZ = 1) = 0;
    
    // 资源屏障
    virtual void barrier(const BarrierDesc& barrier) = 0;
    
    // 调试标记
    virtual void pushDebugGroup(const std::string& name) = 0;
    virtual void popDebugGroup() = 0;
    virtual void insertDebugMarker(const std::string& name) = 0;
};

} // namespace USG
```

### 3.2 VSG适配器设计

```cpp
namespace USG {

// VSG Device适配器
class VSG_Device {
public:
    VSG_Device(BackendType backendType = BackendType::WebGPU);
    ~VSG_Device();
    
    // VSG API兼容接口
    VkResult createBuffer(const VkBufferCreateInfo* pCreateInfo, VkBuffer* pBuffer);
    VkResult createImage(const VkImageCreateInfo* pCreateInfo, VkImage* pImage);
    VkResult createImageView(const VkImageViewCreateInfo* pCreateInfo, VkImageView* pImageView);
    VkResult createSampler(const VkSamplerCreateInfo* pCreateInfo, VkSampler* pSampler);
    VkResult createShaderModule(const VkShaderModuleCreateInfo* pCreateInfo, VkShaderModule* pShaderModule);
    VkResult createPipelineLayout(const VkPipelineLayoutCreateInfo* pCreateInfo, VkPipelineLayout* pPipelineLayout);
    VkResult createRenderPass(const VkRenderPassCreateInfo* pCreateInfo, VkRenderPass* pRenderPass);
    VkResult createGraphicsPipelines(uint32_t createInfoCount, const VkGraphicsPipelineCreateInfo* pCreateInfos, VkPipeline* pPipelines);
    VkResult createDescriptorSetLayout(const VkDescriptorSetLayoutCreateInfo* pCreateInfo, VkDescriptorSetLayout* pSetLayout);
    VkResult createDescriptorPool(const VkDescriptorPoolCreateInfo* pCreateInfo, VkDescriptorPool* pDescriptorPool);
    VkResult allocateDescriptorSets(const VkDescriptorSetAllocateInfo* pAllocateInfo, VkDescriptorSet* pDescriptorSets);
    
    // 资源销毁
    void destroyBuffer(VkBuffer buffer);
    void destroyImage(VkImage image);
    void destroyImageView(VkImageView imageView);
    void destroySampler(VkSampler sampler);
    void destroyShaderModule(VkShaderModule shaderModule);
    void destroyPipelineLayout(VkPipelineLayout pipelineLayout);
    void destroyRenderPass(VkRenderPass renderPass);
    void destroyPipeline(VkPipeline pipeline);
    void destroyDescriptorSetLayout(VkDescriptorSetLayout descriptorSetLayout);
    void destroyDescriptorPool(VkDescriptorPool descriptorPool);
    
    // 内存管理
    VkResult allocateMemory(const VkMemoryAllocateInfo* pAllocateInfo, VkDeviceMemory* pMemory);
    void freeMemory(VkDeviceMemory memory);
    VkResult bindBufferMemory(VkBuffer buffer, VkDeviceMemory memory, VkDeviceSize memoryOffset);
    VkResult bindImageMemory(VkImage image, VkDeviceMemory memory, VkDeviceSize memoryOffset);
    VkResult mapMemory(VkDeviceMemory memory, VkDeviceSize offset, VkDeviceSize size, void** ppData);
    void unmapMemory(VkDeviceMemory memory);
    
    // 队列操作
    void getDeviceQueue(uint32_t queueFamilyIndex, uint32_t queueIndex, VkQueue* pQueue);
    VkResult queueSubmit(VkQueue queue, uint32_t submitCount, const VkSubmitInfo* pSubmits, VkFence fence);
    VkResult queueWaitIdle(VkQueue queue);
    VkResult deviceWaitIdle();
    
    // 同步对象
    VkResult createSemaphore(const VkSemaphoreCreateInfo* pCreateInfo, VkSemaphore* pSemaphore);
    VkResult createFence(const VkFenceCreateInfo* pCreateInfo, VkFence* pFence);
    void destroySemaphore(VkSemaphore semaphore);
    void destroyFence(VkFence fence);
    VkResult waitForFences(uint32_t fenceCount, const VkFence* pFences, VkBool32 waitAll, uint64_t timeout);
    VkResult resetFences(uint32_t fenceCount, const VkFence* pFences);
    
    // 获取后端实例
    RenderBackend* getBackend() const { return _backend.get(); }
    
private:
    std::unique_ptr<RenderBackend> _backend;
    std::unique_ptr<TypeConverter> _converter;
    std::unique_ptr<ResourceManager> _resourceManager;
    
    // 内部辅助方法
    BackendBuffer* getBackendBuffer(VkBuffer buffer);
    BackendTexture* getBackendTexture(VkImage image);
    BackendPipeline* getBackendPipeline(VkPipeline pipeline);
    
    // 类型转换方法
    BufferDesc convertBufferCreateInfo(const VkBufferCreateInfo* pCreateInfo);
    TextureDesc convertImageCreateInfo(const VkImageCreateInfo* pCreateInfo);
    PipelineDesc convertGraphicsPipelineCreateInfo(const VkGraphicsPipelineCreateInfo* pCreateInfo);
};

// VSG CommandBuffer适配器
class VSG_CommandBuffer {
public:
    VSG_CommandBuffer(VSG_Device* device);
    ~VSG_CommandBuffer();
    
    // VSG API兼容接口
    VkResult begin(const VkCommandBufferBeginInfo* pBeginInfo);
    VkResult end();
    VkResult reset(VkCommandBufferResetFlags flags);
    
    // 渲染通道
    void beginRenderPass(const VkRenderPassBeginInfo* pRenderPassBegin, VkSubpassContents contents);
    void nextSubpass(VkSubpassContents contents);
    void endRenderPass();
    
    // 管线绑定
    void bindPipeline(VkPipelineBindPoint pipelineBindPoint, VkPipeline pipeline);
    void bindDescriptorSets(VkPipelineBindPoint pipelineBindPoint, VkPipelineLayout layout, 
                           uint32_t firstSet, uint32_t descriptorSetCount, 
                           const VkDescriptorSet* pDescriptorSets, 
                           uint32_t dynamicOffsetCount = 0, 
                           const uint32_t* pDynamicOffsets = nullptr);
    
    // 顶点和索引缓冲区
    void bindVertexBuffers(uint32_t firstBinding, uint32_t bindingCount, 
                          const VkBuffer* pBuffers, const VkDeviceSize* pOffsets);
    void bindIndexBuffer(VkBuffer buffer, VkDeviceSize offset, VkIndexType indexType);
    
    // 绘制命令
    void draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance);
    void drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t vertexOffset, uint32_t firstInstance);
    void drawIndirect(VkBuffer buffer, VkDeviceSize offset, uint32_t drawCount, uint32_t stride);
    void drawIndexedIndirect(VkBuffer buffer, VkDeviceSize offset, uint32_t drawCount, uint32_t stride);
    
    // 计算命令
    void dispatch(uint32_t groupCountX, uint32_t groupCountY, uint32_t groupCountZ);
    void dispatchIndirect(VkBuffer buffer, VkDeviceSize offset);
    
    // 内存屏障
    void pipelineBarrier(VkPipelineStageFlags srcStageMask, VkPipelineStageFlags dstStageMask,
                        VkDependencyFlags dependencyFlags, uint32_t memoryBarrierCount,
                        const VkMemoryBarrier* pMemoryBarriers, uint32_t bufferMemoryBarrierCount,
                        const VkBufferMemoryBarrier* pBufferMemoryBarriers, uint32_t imageMemoryBarrierCount,
                        const VkImageMemoryBarrier* pImageMemoryBarriers);
    
    // 资源拷贝
    void copyBuffer(VkBuffer srcBuffer, VkBuffer dstBuffer, uint32_t regionCount, const VkBufferCopy* pRegions);
    void copyImage(VkImage srcImage, VkImageLayout srcImageLayout, VkImage dstImage, VkImageLayout dstImageLayout,
                   uint32_t regionCount, const VkImageCopy* pRegions);
    void copyBufferToImage(VkBuffer srcBuffer, VkImage dstImage, VkImageLayout dstImageLayout,
                          uint32_t regionCount, const VkBufferImageCopy* pRegions);
    void copyImageToBuffer(VkImage srcImage, VkImageLayout srcImageLayout, VkBuffer dstBuffer,
                          uint32_t regionCount, const VkBufferImageCopy* pRegions);
    
    // 查询操作
    void beginQuery(VkQueryPool queryPool, uint32_t query, VkQueryControlFlags flags);
    void endQuery(VkQueryPool queryPool, uint32_t query);
    void resetQueryPool(VkQueryPool queryPool, uint32_t firstQuery, uint32_t queryCount);
    void writeTimestamp(VkPipelineStageFlagBits pipelineStage, VkQueryPool queryPool, uint32_t query);
    
    // 调试标记
    void pushDebugGroup(const std::string& name);
    void popDebugGroup();
    void insertDebugMarker(const std::string& name);
    
    // 获取后端命令列表
    BackendCommandList* getBackendCommandList() const { return _backendCmdList.get(); }
    
private:
    VSG_Device* _device;
    std::unique_ptr<BackendCommandList> _backendCmdList;
    std::unique_ptr<TypeConverter> _converter;
    
    // 状态跟踪
    VkPipeline _currentPipeline = VK_NULL_HANDLE;
    VkRenderPass _currentRenderPass = VK_NULL_HANDLE;
    uint32_t _currentSubpass = 0;
    
    // 内部辅助方法
    void updatePipelineState(VkPipeline pipeline);
    void validateRenderPassState();
};

} // namespace USG
```

## 4. 集成策略

### 4.1 动态链接替换
通过LD_PRELOAD或DLL替换机制，在运行时替换VSG库：

```bash
# Linux
export LD_PRELOAD=/path/to/libUSG_Backend_VSG.so
./your_vsg_application

# Windows
# 将USG_Backend_VSG.dll重命名为vsg.dll并放在应用程序目录
```

### 4.2 静态链接集成
在编译时链接USG Backend库：

```cmake
find_package(USG_Backend REQUIRED)
target_link_libraries(your_app USG_Backend::VSG_Adapter)
```

### 4.3 插件模式
通过插件接口动态加载：

```cpp
// 在应用启动时
USG::BackendManager::loadPlugin("USG_Backend_WebGPU");
USG::BackendManager::setDefaultBackend(USG::BackendType::WebGPU);
```

## 5. 性能优化策略

### 5.1 零开销抽象
- 使用模板和内联函数避免虚函数调用开销
- 编译期类型检查和优化
- 避免不必要的内存分配和拷贝

### 5.2 批处理优化
- 自动合并相似的绘制调用
- 状态变化最小化
- 命令缓冲区重用

### 5.3 异步执行
- 多线程命令记录
- 异步资源加载
- GPU/CPU并行执行

## 6. 错误处理和调试

### 6.1 错误处理策略
- 统一的错误码系统
- 异常安全保证
- 资源泄漏检测

### 6.2 调试支持
- 详细的调用跟踪
- 性能分析接口
- 可视化调试工具

这个架构设计确保了USG Backend能够实现零侵入、透明替换的目标，同时提供了高性能和良好的可扩展性。
