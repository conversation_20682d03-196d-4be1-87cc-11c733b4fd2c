#include <iostream>
#include <vector>
#include <chrono>
#include <thread>

#ifdef _WIN32
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#define GLFW_EXPOSE_NATIVE_WIN32
#include <GLFW/glfw3.h>
#include <GLFW/glfw3native.h>
#else
#include <GLFW/glfw3.h>
#endif

struct PixelColor
{
    int r, g, b;
    PixelColor(int red = 0, int green = 0, int blue = 0) : r(red), g(green), b(blue) {}
    
    bool isBlack() const
    {
        return r < 10 && g < 10 && b < 10;
    }
    
    bool isBackground() const
    {
        // 检查是否是背景色 (0.2, 0.3, 0.4) -> (51, 76, 102)
        return (r >= 45 && r <= 60) && (g >= 70 && g <= 85) && (b >= 95 && b <= 110);
    }
    
    bool isTriangleColor() const
    {
        // 检查是否是三角形颜色 (通常是红色或其他明亮颜色)
        return r > 200 || g > 200 || b > 200;
    }
    
    void print() const
    {
        std::cout << "RGB(" << r << ", " << g << ", " << b << ")";
    }
};

class PixelTestTool {
private:
    GLFWwindow* window;
#ifdef _WIN32
    HWND hwnd;
    HDC hdc;
#endif
    
public:
    PixelTestTool(GLFWwindow* win) : window(win) {
#ifdef _WIN32
        hwnd = glfwGetWin32Window(window);
        hdc = GetDC(hwnd);
#endif
    }
    
    ~PixelTestTool() {
#ifdef _WIN32
        if (hdc) {
            ReleaseDC(hwnd, hdc);
        }
#endif
    }

    PixelColor getPixelColor(int x, int y)
    {
#ifdef _WIN32
        COLORREF color = GetPixel(hdc, x, y);
        if (color == CLR_INVALID)
        {
            std::cout << "[PixelTest] Failed to get pixel at (" << x << ", " << y << ")" << std::endl;
            return PixelColor(0, 0, 0);
        }

        int r = GetRValue(color);
        int g = GetGValue(color);
        int b = GetBValue(color);

        return PixelColor(r, g, b);
#else
        // 非Windows平台暂时返回黑色
        return PixelColor(0, 0, 0);
#endif
    }
    
    void sampleWindowPixels() {
        int width, height;
        glfwGetWindowSize(window, &width, &height);
        
        std::cout << "\n[PixelTest] *** WINDOW PIXEL SAMPLING ***" << std::endl;
        std::cout << "[PixelTest] Window size: " << width << "x" << height << std::endl;
        
        // 采样关键位置的像素
        std::vector<std::pair<int, int>> samplePoints = {
            {width/2, height/2},     // 中心
            {width/4, height/4},     // 左上
            {3*width/4, height/4},   // 右上
            {width/4, 3*height/4},   // 左下
            {3*width/4, 3*height/4}, // 右下
            {10, 10},                // 左上角
            {width-10, 10},          // 右上角
            {10, height-10},         // 左下角
            {width-10, height-10}    // 右下角
        };
        
        int blackPixels = 0;
        int backgroundPixels = 0;
        int coloredPixels = 0;
        
        for (auto& point : samplePoints) {
            PixelColor color = getPixelColor(point.first, point.second);
            std::cout << "[PixelTest] Pixel at (" << point.first << ", " << point.second << "): ";
            color.print();
            
            if (color.isBlack()) {
                std::cout << " [BLACK]";
                blackPixels++;
            } else if (color.isBackground()) {
                std::cout << " [BACKGROUND]";
                backgroundPixels++;
            } else if (color.isTriangleColor()) {
                std::cout << " [GEOMETRY]";
                coloredPixels++;
            } else {
                std::cout << " [OTHER]";
            }
            std::cout << std::endl;
        }
        
        std::cout << "\n[PixelTest] *** PIXEL ANALYSIS ***" << std::endl;
        std::cout << "[PixelTest] Black pixels: " << blackPixels << "/" << samplePoints.size() << std::endl;
        std::cout << "[PixelTest] Background pixels: " << backgroundPixels << "/" << samplePoints.size() << std::endl;
        std::cout << "[PixelTest] Colored pixels: " << coloredPixels << "/" << samplePoints.size() << std::endl;
        
        if (blackPixels == samplePoints.size()) {
            std::cout << "[PixelTest] ❌ COMPLETE BLACK SCREEN DETECTED!" << std::endl;
        } else if (backgroundPixels > 0) {
            std::cout << "[PixelTest] ✅ BACKGROUND COLOR DETECTED - Rendering working!" << std::endl;
        } else if (coloredPixels > 0) {
            std::cout << "[PixelTest] ✅ GEOMETRY COLORS DETECTED - Full rendering working!" << std::endl;
        } else {
            std::cout << "[PixelTest] ⚠️ UNKNOWN PIXEL PATTERN" << std::endl;
        }
    }
    
    bool waitForNonBlackPixels(int timeoutSeconds = 10) {
        std::cout << "\n[PixelTest] Waiting for non-black pixels (timeout: " << timeoutSeconds << "s)..." << std::endl;
        
        auto startTime = std::chrono::steady_clock::now();
        auto timeout = std::chrono::seconds(timeoutSeconds);
        
        while (std::chrono::steady_clock::now() - startTime < timeout) {
            int width, height;
            glfwGetWindowSize(window, &width, &height);
            
            // 检查中心像素
            PixelColor centerPixel = getPixelColor(width/2, height/2);
            if (!centerPixel.isBlack()) {
                std::cout << "[PixelTest] ✅ Non-black pixel detected at center: ";
                centerPixel.print();
                std::cout << std::endl;
                return true;
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        std::cout << "[PixelTest] ❌ Timeout: Still black screen after " << timeoutSeconds << " seconds" << std::endl;
        return false;
    }
};

// 导出C接口供测试应用使用
extern "C" {
    void* createPixelTestTool(GLFWwindow* window) {
        return new PixelTestTool(window);
    }
    
    void destroyPixelTestTool(void* tool) {
        delete static_cast<PixelTestTool*>(tool);
    }
    
    void sampleWindowPixels(void* tool) {
        static_cast<PixelTestTool*>(tool)->sampleWindowPixels();
    }
    
    int waitForNonBlackPixels(void* tool, int timeoutSeconds) {
        return static_cast<PixelTestTool*>(tool)->waitForNonBlackPixels(timeoutSeconds) ? 1 : 0;
    }
}
