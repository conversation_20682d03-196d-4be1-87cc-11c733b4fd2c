#!/bin/sh -eu

generate() {
	"$WAYLAND_SCANNER" $1 < "$TEST_DATA_DIR/$2" > "$TEST_DATA_DIR/$3"
	"$SED" -i -e 's/Generated by wayland-scanner [0-9.]*/SCANNER TEST/' \
		"$TEST_DATA_DIR/$3"
}

generate "code" "example.xml" "example-code.c"
generate "client-header" "example.xml" "example-client.h"
generate "server-header" "example.xml" "example-server.h"
generate "enum-header" "example.xml" "example-enum.h"

generate "code" "small.xml" "small-code.c"
generate "client-header" "small.xml" "small-client.h"
generate "server-header" "small.xml" "small-server.h"

generate "-c code" "small.xml" "small-code-core.c"
generate "-c client-header" "small.xml" "small-client-core.h"
generate "-c server-header" "small.xml" "small-server-core.h"

generate "private-code" "small.xml" "small-private-code.c"

generate "code" "empty.xml" "empty-code.c"
generate "client-header" "empty.xml" "empty-client.h"
generate "server-header" "empty.xml" "empty-server.h"
