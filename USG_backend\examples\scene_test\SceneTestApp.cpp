#include "SceneTestApp.h"
#include "BackendSwitcher.h"
#include "SimpleScene.h"
#include <USG_Backend/BackendFactory.h>
#include <iostream>
#include <chrono>

#ifdef USG_PLATFORM_DESKTOP
#include <GLFW/glfw3.h>
#endif

namespace USG
{

    SceneTestApp::SceneTestApp()
    {
    }

    SceneTestApp::~SceneTestApp()
    {
        cleanup();
    }

    bool SceneTestApp::initialize(const AppConfig &config)
    {
        _appConfig = config;

        std::cout << "[SceneTestApp] Initializing application..." << std::endl;

        // 初始化GLFW
        if (!createWindow())
        {
            return false;
        }

        // 创建后端切换器
        _backendSwitcher = std::make_unique<BackendSwitcher>();

        BackendConfig backendConfig;
        backendConfig.nativeWindow = _window;
        backendConfig.swapchainWidth = _appConfig.windowWidth;
        backendConfig.swapchainHeight = _appConfig.windowHeight;
        backendConfig.vsyncEnabled = _appConfig.enableVSync;
        backendConfig.applicationName = _appConfig.title;

        if (!_backendSwitcher->initialize(backendConfig))
        {
            std::cerr << "[SceneTestApp] Failed to initialize backend switcher" << std::endl;
            return false;
        }

        // 切换到默认后端
        auto switchResult = _backendSwitcher->switchTo(_appConfig.defaultBackend);
        if (!switchResult.success)
        {
            // 尝试推荐的后端
            BackendType recommended = _backendSwitcher->getRecommendedBackend();
            if (recommended != BackendType::Unknown)
            {
                switchResult = _backendSwitcher->switchTo(recommended);
            }

            if (!switchResult.success)
            {
                std::cerr << "[SceneTestApp] Failed to initialize any backend" << std::endl;
                return false;
            }
        }

        _appState.currentBackend = _backendSwitcher->getCurrentBackendType();
        _appState.currentBackendName = _backendSwitcher->getCurrentBackendName();

        // 创建场景
        if (!createScene())
        {
            return false;
        }

        // 设置回调
        _backendSwitcher->setSwitchCallback([this](const BackendSwitcher::SwitchResult &result)
                                            {
        if (result.success) {
            _appState.currentBackend = result.toBackend;
            _appState.currentBackendName = _backendSwitcher->getCurrentBackendName();
            std::cout << "[SceneTestApp] Switched to " << _appState.currentBackendName << std::endl;
        } });

        _appState.running = true;

        std::cout << "[SceneTestApp] Application initialized successfully" << std::endl;
        std::cout << "[SceneTestApp] Using backend: " << _appState.currentBackendName << std::endl;

        return true;
    }

    int SceneTestApp::run()
    {
        if (!_appState.running)
        {
            return -1;
        }

        std::cout << "[SceneTestApp] Starting main loop..." << std::endl;

#ifdef USG_PLATFORM_DESKTOP
        _lastFrameTime = glfwGetTime();

        while (_appState.running && !glfwWindowShouldClose(_window))
        {
            // 处理事件
            glfwPollEvents();

            // 更新帧
            updateFrame();

            // 渲染帧
            if (!_appState.paused)
            {
                renderFrame();
            }

            // 交换缓冲区
            glfwSwapBuffers(_window);
        }
#endif

        std::cout << "[SceneTestApp] Main loop ended" << std::endl;
        return 0;
    }

    void SceneTestApp::cleanup()
    {
        std::cout << "[SceneTestApp] Cleaning up application..." << std::endl;

        _scene.reset();
        _backendSwitcher.reset();

#ifdef USG_PLATFORM_DESKTOP
        if (_window)
        {
            glfwDestroyWindow(_window);
            _window = nullptr;
        }

        glfwTerminate();
#endif

        _appState.running = false;

        std::cout << "[SceneTestApp] Application cleaned up" << std::endl;
    }

    bool SceneTestApp::switchBackend(BackendType backendType)
    {
        if (!_backendSwitcher)
        {
            return false;
        }

        auto result = _backendSwitcher->switchTo(backendType);
        if (result.success)
        {
            // 更新场景的后端
            if (_scene)
            {
                _scene->switchBackend(_backendSwitcher->getCurrentBackend());
            }
        }

        return result.success;
    }

    bool SceneTestApp::switchBackend(const std::string &backendName)
    {
        if (!_backendSwitcher)
        {
            return false;
        }

        auto result = _backendSwitcher->switchTo(backendName);
        if (result.success)
        {
            // 更新场景的后端
            if (_scene)
            {
                _scene->switchBackend(_backendSwitcher->getCurrentBackend());
            }
        }

        return result.success;
    }

    std::vector<BackendType> SceneTestApp::getAvailableBackends() const
    {
        std::vector<BackendType> backends;
        if (_backendSwitcher)
        {
            for (const auto &info : _backendSwitcher->getAvailableBackends())
            {
                if (info.available)
                {
                    backends.push_back(info.type);
                }
            }
        }
        return backends;
    }

    std::vector<std::string> SceneTestApp::getAvailableBackendNames() const
    {
        std::vector<std::string> names;
        if (_backendSwitcher)
        {
            for (const auto &info : _backendSwitcher->getAvailableBackends())
            {
                if (info.available)
                {
                    names.push_back(info.name);
                }
            }
        }
        return names;
    }

    void SceneTestApp::setErrorCallback(std::function<void(const std::string &)> callback)
    {
        _errorCallback = callback;
        if (_backendSwitcher)
        {
            _backendSwitcher->setErrorCallback(callback);
        }
    }

    void SceneTestApp::setPaused(bool paused)
    {
        _appState.paused = paused;
    }

    void SceneTestApp::requestExit()
    {
        _appState.running = false;
#ifdef USG_PLATFORM_DESKTOP
        if (_window)
        {
            glfwSetWindowShouldClose(_window, GLFW_TRUE);
        }
#endif
    }

    // 私有方法实现
    bool SceneTestApp::createWindow()
    {
#ifdef USG_PLATFORM_DESKTOP
        // 初始化GLFW
        glfwSetErrorCallback(glfwErrorCallback);

        if (!glfwInit())
        {
            std::cerr << "[SceneTestApp] Failed to initialize GLFW" << std::endl;
            return false;
        }

        // 设置窗口提示
        glfwWindowHint(GLFW_CLIENT_API, GLFW_NO_API); // 不使用OpenGL上下文
        glfwWindowHint(GLFW_RESIZABLE, GLFW_TRUE);

        // 创建窗口
        GLFWmonitor *monitor = _appConfig.fullscreen ? glfwGetPrimaryMonitor() : nullptr;
        _window = glfwCreateWindow(
            _appConfig.windowWidth,
            _appConfig.windowHeight,
            _appConfig.title.c_str(),
            monitor,
            nullptr);

        if (!_window)
        {
            std::cerr << "[SceneTestApp] Failed to create GLFW window" << std::endl;
            glfwTerminate();
            return false;
        }

        // 设置用户指针
        glfwSetWindowUserPointer(_window, this);

        // 设置回调
        glfwSetWindowSizeCallback(_window, glfwWindowSizeCallback);
        glfwSetKeyCallback(_window, glfwKeyCallback);
        glfwSetMouseButtonCallback(_window, glfwMouseButtonCallback);
        glfwSetCursorPosCallback(_window, glfwCursorPosCallback);

        std::cout << "[SceneTestApp] Created window: " << _appConfig.windowWidth
                  << "x" << _appConfig.windowHeight << std::endl;

        return true;
#else
        std::cerr << "[SceneTestApp] Window creation not supported on this platform" << std::endl;
        return false;
#endif
    }

    bool SceneTestApp::createScene()
    {
        _scene = std::make_unique<SimpleScene>();

        SimpleScene::SceneConfig sceneConfig;
        sceneConfig.enableLighting = true;
        sceneConfig.enableTextures = false; // 简化，暂不使用纹理

        if (!_scene->initialize(_backendSwitcher->getCurrentBackend(), sceneConfig))
        {
            std::cerr << "[SceneTestApp] Failed to initialize scene" << std::endl;
            return false;
        }

        // 添加一些测试对象
        _scene->addObject(SimpleScene::ObjectType::Cube, "TestCube");

        std::cout << "[SceneTestApp] Scene created successfully" << std::endl;
        return true;
    }

    void SceneTestApp::updateFrame()
    {
#ifdef USG_PLATFORM_DESKTOP
        double currentTime = glfwGetTime();
        _appState.deltaTime = currentTime - _lastFrameTime;
        _lastFrameTime = currentTime;

        _appState.totalTime += _appState.deltaTime;
        _appState.frameCount++;

        // 更新统计信息
        updateStats();

        // 处理输入
        handleInput();

        // 更新场景
        if (_scene && !_appState.paused)
        {
            _scene->update(_appState.deltaTime);
        }
#endif
    }

    void SceneTestApp::renderFrame()
    {
        // 简化的渲染实现
        if (_scene)
        {
            // 这里应该创建命令列表并渲染场景
            // 由于我们还没有完整实现所有后端，这里只是占位符

            // 更新渲染统计
            const auto &sceneStats = _scene->getRenderStats();
            _appState.triangleCount = sceneStats.triangleCount;
            _appState.drawCalls = sceneStats.drawCalls;
        }
    }

    void SceneTestApp::handleInput()
    {
#ifdef USG_PLATFORM_DESKTOP
        // 检查按键
        if (glfwGetKey(_window, GLFW_KEY_ESCAPE) == GLFW_PRESS)
        {
            requestExit();
        }

        if (glfwGetKey(_window, GLFW_KEY_SPACE) == GLFW_PRESS)
        {
            static bool spacePressed = false;
            if (!spacePressed)
            {
                setPaused(!_appState.paused);
                spacePressed = true;
            }
        }
        else
        {
            static bool spacePressed = false;
            spacePressed = false;
        }

        // 后端切换快捷键
        if (glfwGetKey(_window, GLFW_KEY_1) == GLFW_PRESS)
        {
            switchBackend(BackendType::WebGPU);
        }
        if (glfwGetKey(_window, GLFW_KEY_2) == GLFW_PRESS)
        {
            switchBackend(BackendType::Vulkan);
        }
        if (glfwGetKey(_window, GLFW_KEY_3) == GLFW_PRESS)
        {
            switchBackend(BackendType::OpenGL);
        }
#endif
    }

    void SceneTestApp::updateStats()
    {
        // 每秒更新一次FPS
        _fpsFrameCount++;
        double currentTime = _appState.totalTime;

        if (currentTime - _fpsUpdateTime >= 1.0)
        {
            _appState.fps = _fpsFrameCount / (currentTime - _fpsUpdateTime);
            _appState.frameTime = (currentTime - _fpsUpdateTime) * 1000.0 / _fpsFrameCount;

            _fpsFrameCount = 0;
            _fpsUpdateTime = currentTime;

            // 打印统计信息
            if (_appConfig.showStats)
            {
                std::cout << "[Stats] FPS: " << static_cast<int>(_appState.fps)
                          << ", Frame Time: " << _appState.frameTime << "ms"
                          << ", Backend: " << _appState.currentBackendName << std::endl;
            }
        }
    }

    // GLFW回调函数
    void SceneTestApp::glfwErrorCallback(int error, const char *description)
    {
        std::cerr << "[GLFW Error] " << error << ": " << description << std::endl;
    }

    void SceneTestApp::glfwWindowSizeCallback(GLFWwindow *window, int width, int height)
    {
        SceneTestApp *app = static_cast<SceneTestApp *>(glfwGetWindowUserPointer(window));
        if (app)
        {
            app->onWindowResize(width, height);
        }
    }

    void SceneTestApp::glfwKeyCallback(GLFWwindow *window, int key, int scancode, int action, int mods)
    {
        SceneTestApp *app = static_cast<SceneTestApp *>(glfwGetWindowUserPointer(window));
        if (app)
        {
            app->onKeyPress(key, action);
        }
    }

    void SceneTestApp::glfwMouseButtonCallback(GLFWwindow *window, int button, int action, int mods)
    {
        SceneTestApp *app = static_cast<SceneTestApp *>(glfwGetWindowUserPointer(window));
        if (app)
        {
            app->onMouseButton(button, action);
        }
    }

    void SceneTestApp::glfwCursorPosCallback(GLFWwindow *window, double x, double y)
    {
        SceneTestApp *app = static_cast<SceneTestApp *>(glfwGetWindowUserPointer(window));
        if (app)
        {
            app->onMouseMove(x, y);
        }
    }

    void SceneTestApp::onWindowResize(int width, int height)
    {
        _appConfig.windowWidth = width;
        _appConfig.windowHeight = height;

        // 通知场景窗口大小变化
        // 这里应该更新投影矩阵等
    }

    void SceneTestApp::onKeyPress(int key, int action)
    {
        // 处理按键事件
    }

    void SceneTestApp::onMouseButton(int button, int action)
    {
        // 处理鼠标按键事件
    }

    void SceneTestApp::onMouseMove(double x, double y)
    {
        // 处理鼠标移动事件
        _mouseX = x;
        _mouseY = y;
    }

} // namespace USG
