#include "SceneTestApp.h"
#include "BackendSwitcher.h"
#include "SimpleScene.h"
#include "VSGScene.h"
#include <USG_Backend/BackendFactory.h>
#include <iostream>
#include <chrono>
#include <thread>

#ifdef USG_PLATFORM_DESKTOP
#include <GLFW/glfw3.h>
#endif

// 像素测试工具C接口声明
extern "C"
{
    void *createPixelTestTool(GLFWwindow *window);
    void destroyPixelTestTool(void *tool);
    void sampleWindowPixels(void *tool);
    int waitForNonBlackPixels(void *tool, int timeoutSeconds);
}

namespace USG
{

    SceneTestApp::SceneTestApp()
    {
    }

    SceneTestApp::~SceneTestApp()
    {
        cleanup();
    }

    bool SceneTestApp::initialize(const AppConfig &config)
    {
        _appConfig = config;

        std::cout << "[SceneTestApp] Initializing application..." << std::endl;

        // 初始化GLFW
        if (!createWindow())
        {
            return false;
        }

        // 创建后端切换器
        _backendSwitcher = std::make_unique<BackendSwitcher>();

        BackendConfig backendConfig;
        backendConfig.nativeWindow = _window;
        backendConfig.swapchainWidth = _appConfig.windowWidth;
        backendConfig.swapchainHeight = _appConfig.windowHeight;
        backendConfig.vsyncEnabled = _appConfig.enableVSync;
        backendConfig.applicationName = _appConfig.title;

        if (!_backendSwitcher->initialize(backendConfig))
        {
            std::cerr << "[SceneTestApp] Failed to initialize backend switcher" << std::endl;
            return false;
        }

        // 尝试初始化后端 - 按优先级尝试所有可用后端
        bool backendInitialized = false;
        BackendSwitcher::SwitchResult switchResult;

        // 定义后端优先级列表（跳过WebGPU，因为它还未完全实现）
        std::vector<BackendType> backendPriority = {
            BackendType::Vulkan, // 优先使用Vulkan
            BackendType::OpenGL  // 然后OpenGL
        };

        // 首先尝试默认后端
        if (_appConfig.defaultBackend != BackendType::Unknown)
        {
            std::cout << "[SceneTestApp] Trying default backend: " << static_cast<int>(_appConfig.defaultBackend) << std::endl;
            switchResult = _backendSwitcher->switchTo(_appConfig.defaultBackend);
            if (switchResult.success)
            {
                backendInitialized = true;
                std::cout << "[SceneTestApp] Successfully initialized default backend" << std::endl;
            }
            else
            {
                std::cout << "[SceneTestApp] Failed to initialize default backend, trying alternatives..." << std::endl;
            }
        }

        // 如果默认后端失败，按优先级尝试其他后端
        if (!backendInitialized)
        {
            for (BackendType backendType : backendPriority)
            {
                if (_backendSwitcher->isBackendAvailable(backendType))
                {
                    std::cout << "[SceneTestApp] Trying backend: " << static_cast<int>(backendType) << std::endl;
                    switchResult = _backendSwitcher->switchTo(backendType);
                    if (switchResult.success)
                    {
                        backendInitialized = true;
                        std::cout << "[SceneTestApp] Successfully initialized backend: " << static_cast<int>(backendType) << std::endl;
                        break;
                    }
                    else
                    {
                        std::cout << "[SceneTestApp] Failed to initialize backend: " << static_cast<int>(backendType) << std::endl;
                    }
                }
                else
                {
                    std::cout << "[SceneTestApp] Backend not available: " << static_cast<int>(backendType) << std::endl;
                }
            }
        }

        if (!backendInitialized)
        {
            std::cerr << "[SceneTestApp] Failed to initialize any backend" << std::endl;
            return false;
        }

        _appState.currentBackend = _backendSwitcher->getCurrentBackendType();
        _appState.currentBackendName = _backendSwitcher->getCurrentBackendName();

        // *** 关键修复：立即更新窗口指针 ***
        auto currentConfig = _backendSwitcher->getCurrentConfig();
        if (currentConfig.nativeWindow != _window)
        {
            std::cout << "[SceneTestApp] *** CRITICAL FIX *** Updating window pointer after initial backend switch" << std::endl;
            std::cout << "[SceneTestApp] Old window: " << _window << ", New window: " << currentConfig.nativeWindow << std::endl;
            _window = static_cast<GLFWwindow *>(currentConfig.nativeWindow);
        }

        // *** 检查窗口显示状态 ***
        int windowVisible = glfwGetWindowAttrib(_window, GLFW_VISIBLE);
        int windowIconified = glfwGetWindowAttrib(_window, GLFW_ICONIFIED);
        int windowFocused = glfwGetWindowAttrib(_window, GLFW_FOCUSED);

        std::cout << "[SceneTestApp] *** WINDOW DISPLAY DEBUG ***" << std::endl;
        std::cout << "[SceneTestApp] Window visible: " << windowVisible << std::endl;
        std::cout << "[SceneTestApp] Window iconified: " << windowIconified << std::endl;
        std::cout << "[SceneTestApp] Window focused: " << windowFocused << std::endl;

        // 强制显示和聚焦窗口
        if (!windowVisible)
        {
            std::cout << "[SceneTestApp] *** FORCING WINDOW VISIBLE ***" << std::endl;
            glfwShowWindow(_window);
        }
        if (windowIconified)
        {
            std::cout << "[SceneTestApp] *** RESTORING WINDOW ***" << std::endl;
            glfwRestoreWindow(_window);
        }
        if (!windowFocused)
        {
            std::cout << "[SceneTestApp] *** FOCUSING WINDOW ***" << std::endl;
            glfwFocusWindow(_window);
        }

        // *** 额外的窗口显示确保 ***
        glfwShowWindow(_window);
        glfwRequestWindowAttention(_window);

        // 获取窗口位置和大小
        int windowX, windowY, windowWidth, windowHeight;
        glfwGetWindowPos(_window, &windowX, &windowY);
        glfwGetWindowSize(_window, &windowWidth, &windowHeight);

        std::cout << "[SceneTestApp] *** WINDOW GEOMETRY ***" << std::endl;
        std::cout << "[SceneTestApp] Position: (" << windowX << ", " << windowY << ")" << std::endl;
        std::cout << "[SceneTestApp] Size: " << windowWidth << "x" << windowHeight << std::endl;

        // 检查窗口系统级别状态
        std::cout << "[SceneTestApp] *** WINDOW SYSTEM DEBUG ***" << std::endl;
        int framebufferWidth, framebufferHeight;
        glfwGetFramebufferSize(_window, &framebufferWidth, &framebufferHeight);
        std::cout << "[SceneTestApp] Framebuffer size: " << framebufferWidth << "x" << framebufferHeight << std::endl;

        float xscale, yscale;
        glfwGetWindowContentScale(_window, &xscale, &yscale);
        std::cout << "[SceneTestApp] Content scale: " << xscale << "x" << yscale << std::endl;

        std::cout << "[SceneTestApp] Window opacity: " << glfwGetWindowOpacity(_window) << std::endl;

        // 检查窗口是否在屏幕范围内
        const GLFWvidmode *mode = glfwGetVideoMode(glfwGetPrimaryMonitor());
        if (mode)
        {
            std::cout << "[SceneTestApp] Screen size: " << mode->width << "x" << mode->height << std::endl;

            if (windowX < 0 || windowY < 0 || windowX >= mode->width || windowY >= mode->height)
            {
                std::cout << "[SceneTestApp] *** WINDOW OFF-SCREEN, REPOSITIONING ***" << std::endl;
                glfwSetWindowPos(_window, 100, 100);
            }
        }

        // 创建场景
        if (!createScene())
        {
            return false;
        }

        // 设置回调
        _backendSwitcher->setSwitchCallback([this](const BackendSwitcher::SwitchResult &result)
                                            {
        if (result.success) {
            _appState.currentBackend = result.toBackend;
            _appState.currentBackendName = _backendSwitcher->getCurrentBackendName();

            // 更新窗口指针（如果窗口被重新创建）
            auto currentConfig = _backendSwitcher->getCurrentConfig();
            if (currentConfig.nativeWindow != _window) {
                std::cout << "[SceneTestApp] Updating window pointer after backend switch" << std::endl;
                _window = static_cast<GLFWwindow*>(currentConfig.nativeWindow);
            }

            std::cout << "[SceneTestApp] Switched to " << _appState.currentBackendName << std::endl;
        } });

        _appState.running = true;

        // 初始化像素测试
        if (_appConfig.enablePixelTest)
        {
            initPixelTest();
        }

        std::cout << "[SceneTestApp] Application initialized successfully" << std::endl;
        std::cout << "[SceneTestApp] Using backend: " << _appState.currentBackendName << std::endl;

        return true;
    }

    int SceneTestApp::run()
    {
        if (!_appState.running)
        {
            return -1;
        }

        std::cout << "[SceneTestApp] Starting main loop..." << std::endl;

#ifdef USG_PLATFORM_DESKTOP
        _lastFrameTime = glfwGetTime();

        std::cout << "[SceneTestApp] Entering main loop - running: " << _appState.running
                  << ", window should close: " << glfwWindowShouldClose(_window) << std::endl;

        // 运行像素测试
        if (_pixelTestEnabled)
        {
            std::cout << "[SceneTestApp] Running initial pixel test..." << std::endl;
            runPixelTest();
        }

        while (_appState.running && !glfwWindowShouldClose(_window))
        {
            // 处理事件
            glfwPollEvents();

            // 更新帧
            updateFrame();

            // 渲染帧
            if (!_appState.paused)
            {
                renderFrame();
            }

            // 交换缓冲区 (仅对OpenGL后端)
            if (_appState.currentBackend == BackendType::OpenGL)
            {
                glfwSwapBuffers(_window);
            }

            // 添加小延迟以便观察
            std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
        }
#endif

        std::cout << "[SceneTestApp] Main loop ended" << std::endl;
        return 0;
    }

    void SceneTestApp::cleanup()
    {
        std::cout << "[SceneTestApp] Cleaning up application..." << std::endl;

        // 清理像素测试
        cleanupPixelTest();

        _scene.reset();
        _backendSwitcher.reset();

#ifdef USG_PLATFORM_DESKTOP
        if (_window)
        {
            glfwDestroyWindow(_window);
            _window = nullptr;
        }

        glfwTerminate();
#endif

        _appState.running = false;

        std::cout << "[SceneTestApp] Application cleaned up" << std::endl;
    }

    bool SceneTestApp::switchBackend(BackendType backendType)
    {
        if (!_backendSwitcher)
        {
            return false;
        }

        auto result = _backendSwitcher->switchTo(backendType);
        if (result.success)
        {
            // 更新场景的后端
            if (_vsgScene)
            {
                _vsgScene->switchBackend(_backendSwitcher->getCurrentBackend());
            }
            else if (_scene)
            {
                _scene->switchBackend(_backendSwitcher->getCurrentBackend());
            }
        }

        return result.success;
    }

    bool SceneTestApp::switchBackend(const std::string &backendName)
    {
        if (!_backendSwitcher)
        {
            return false;
        }

        auto result = _backendSwitcher->switchTo(backendName);
        if (result.success)
        {
            // 更新场景的后端
            if (_vsgScene)
            {
                _vsgScene->switchBackend(_backendSwitcher->getCurrentBackend());
            }
            else if (_scene)
            {
                _scene->switchBackend(_backendSwitcher->getCurrentBackend());
            }
        }

        return result.success;
    }

    std::vector<BackendType> SceneTestApp::getAvailableBackends() const
    {
        std::vector<BackendType> backends;
        if (_backendSwitcher)
        {
            for (const auto &info : _backendSwitcher->getAvailableBackends())
            {
                if (info.available)
                {
                    backends.push_back(info.type);
                }
            }
        }
        return backends;
    }

    std::vector<std::string> SceneTestApp::getAvailableBackendNames() const
    {
        std::vector<std::string> names;
        if (_backendSwitcher)
        {
            for (const auto &info : _backendSwitcher->getAvailableBackends())
            {
                if (info.available)
                {
                    names.push_back(info.name);
                }
            }
        }
        return names;
    }

    void SceneTestApp::setErrorCallback(std::function<void(const std::string &)> callback)
    {
        _errorCallback = callback;
        if (_backendSwitcher)
        {
            _backendSwitcher->setErrorCallback(callback);
        }
    }

    void SceneTestApp::setPaused(bool paused)
    {
        _appState.paused = paused;
    }

    void SceneTestApp::requestExit()
    {
        _appState.running = false;
#ifdef USG_PLATFORM_DESKTOP
        if (_window)
        {
            glfwSetWindowShouldClose(_window, GLFW_TRUE);
        }
#endif
    }

    // 私有方法实现
    bool SceneTestApp::createWindow()
    {
#ifdef USG_PLATFORM_DESKTOP
        // 初始化GLFW
        glfwSetErrorCallback(glfwErrorCallback);

        if (!glfwInit())
        {
            std::cerr << "[SceneTestApp] Failed to initialize GLFW" << std::endl;
            return false;
        }

        // 设置窗口提示
        // 默认创建OpenGL上下文，其他后端需要时会重建窗口
        glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
        glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
        glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_COMPAT_PROFILE);
        glfwWindowHint(GLFW_RESIZABLE, GLFW_TRUE);

        // 启用双缓冲（解决闪烁问题）
        glfwWindowHint(GLFW_DOUBLEBUFFER, GLFW_TRUE);

        // 设置其他窗口属性
        glfwWindowHint(GLFW_VISIBLE, GLFW_TRUE);

        // 创建窗口
        GLFWmonitor *monitor = _appConfig.fullscreen ? glfwGetPrimaryMonitor() : nullptr;
        _window = glfwCreateWindow(
            _appConfig.windowWidth,
            _appConfig.windowHeight,
            _appConfig.title.c_str(),
            monitor,
            nullptr);

        if (!_window)
        {
            std::cerr << "[SceneTestApp] Failed to create GLFW window" << std::endl;
            glfwTerminate();
            return false;
        }

        // 激活OpenGL上下文
        glfwMakeContextCurrent(_window);

        // 启用垂直同步
        glfwSwapInterval(1);

        // 设置用户指针
        glfwSetWindowUserPointer(_window, this);

        // 设置回调
        glfwSetWindowSizeCallback(_window, glfwWindowSizeCallback);
        glfwSetKeyCallback(_window, glfwKeyCallback);
        glfwSetMouseButtonCallback(_window, glfwMouseButtonCallback);
        glfwSetCursorPosCallback(_window, glfwCursorPosCallback);

        std::cout << "[SceneTestApp] Created window: " << _appConfig.windowWidth
                  << "x" << _appConfig.windowHeight << std::endl;

        return true;
#else
        std::cerr << "[SceneTestApp] Window creation not supported on this platform" << std::endl;
        return false;
#endif
    }

    bool SceneTestApp::createScene()
    {
        // 创建VSG场景（优先使用）
        _vsgScene = std::make_unique<VSGScene>();
        if (_vsgScene->initialize(_backendSwitcher->getCurrentBackend()))
        {
            std::cout << "[SceneTestApp] VSG scene created successfully" << std::endl;
            return true;
        }
        else
        {
            std::cerr << "[SceneTestApp] Failed to initialize VSG scene, falling back to SimpleScene" << std::endl;
            _vsgScene.reset();
        }

        // 回退到SimpleScene
        _scene = std::make_unique<SimpleScene>();

        SimpleScene::SceneConfig sceneConfig;
        sceneConfig.enableLighting = true;
        sceneConfig.enableTextures = false; // 简化，暂不使用纹理

        if (!_scene->initialize(_backendSwitcher->getCurrentBackend(), sceneConfig))
        {
            std::cerr << "[SceneTestApp] Failed to initialize scene" << std::endl;
            return false;
        }

        // 添加一些测试对象
        _scene->addObject(SimpleScene::ObjectType::Cube, "TestCube");

        std::cout << "[SceneTestApp] SimpleScene created successfully" << std::endl;
        return true;
    }

    void SceneTestApp::updateFrame()
    {
#ifdef USG_PLATFORM_DESKTOP
        double currentTime = glfwGetTime();
        _appState.deltaTime = currentTime - _lastFrameTime;
        _lastFrameTime = currentTime;

        _appState.totalTime += _appState.deltaTime;
        _appState.frameCount++;

        // 更新统计信息
        updateStats();

        // 处理输入
        handleInput();

        // 更新场景
        if (_vsgScene && !_appState.paused)
        {
            _vsgScene->update(_appState.deltaTime);
        }
        else if (_scene && !_appState.paused)
        {
            _scene->update(_appState.deltaTime);
        }
#endif
    }

    void SceneTestApp::renderFrame()
    {
        if ((!_vsgScene && !_scene) || !_backendSwitcher)
        {
            return;
        }

        auto backend = _backendSwitcher->getCurrentBackend();
        if (!backend)
        {
            return;
        }

        // 开始帧渲染
        backend->beginFrame();

        // 创建命令列表
        auto commandList = std::unique_ptr<BackendCommandList>(backend->createCommandList());
        if (commandList)
        {
            // 简单的视图和投影矩阵（单位矩阵）
            float viewMatrix[16] = {
                1.0f, 0.0f, 0.0f, 0.0f,
                0.0f, 1.0f, 0.0f, 0.0f,
                0.0f, 0.0f, 1.0f, 0.0f,
                0.0f, 0.0f, 0.0f, 1.0f};

            float projMatrix[16] = {
                1.0f, 0.0f, 0.0f, 0.0f,
                0.0f, 1.0f, 0.0f, 0.0f,
                0.0f, 0.0f, 1.0f, 0.0f,
                0.0f, 0.0f, 0.0f, 1.0f};

            // 渲染场景（优先使用VSG场景）
            if (_vsgScene)
            {
                _vsgScene->render(commandList.get(), viewMatrix, projMatrix);
            }
            else if (_scene)
            {
                _scene->render(commandList.get(), viewMatrix, projMatrix);
            }

            // 执行命令列表
            backend->executeCommandList(commandList.get());

            // 销毁命令列表
            backend->destroyCommandList(commandList.release());
        }

        // 结束帧渲染
        backend->endFrame();

        // 呈现渲染结果
        backend->present();

        // 更新渲染统计
        if (_vsgScene)
        {
            const auto &sceneStats = _vsgScene->getRenderStats();
            _appState.triangleCount = sceneStats.triangleCount;
            _appState.drawCalls = sceneStats.drawCalls;
        }
        else if (_scene)
        {
            const auto &sceneStats = _scene->getRenderStats();
            _appState.triangleCount = sceneStats.triangleCount;
            _appState.drawCalls = sceneStats.drawCalls;
        }

        // 注意：缓冲区交换在主循环中进行，这里不需要重复交换
    }

    void SceneTestApp::handleInput()
    {
#ifdef USG_PLATFORM_DESKTOP
        // 添加调试输出（每60帧输出一次，避免日志过多）
        static int debugCounter = 0;
        if (++debugCounter % 60 == 0)
        {
            std::cout << "[SceneTestApp] handleInput called (frame " << debugCounter << ")" << std::endl;
        }
        // 检查按键
        if (glfwGetKey(_window, GLFW_KEY_ESCAPE) == GLFW_PRESS)
        {
            std::cout << "[SceneTestApp] ESC key pressed, requesting exit..." << std::endl;
            requestExit();
        }

        if (glfwGetKey(_window, GLFW_KEY_SPACE) == GLFW_PRESS)
        {
            static bool spacePressed = false;
            if (!spacePressed)
            {
                setPaused(!_appState.paused);
                spacePressed = true;
            }
        }
        else
        {
            static bool spacePressed = false;
            spacePressed = false;
        }

        // 后端切换快捷键 (添加防抖动)
        static bool key1Pressed = false;
        static bool key2Pressed = false;
        static bool key3Pressed = false;

        // 添加调试信息（复用已有的debugCounter）
        if (debugCounter % 60 == 0) // 每1秒打印一次调试信息
        {
            int key1State = glfwGetKey(_window, GLFW_KEY_1);
            int key2State = glfwGetKey(_window, GLFW_KEY_2);
            int windowFocused = glfwGetWindowAttrib(_window, GLFW_FOCUSED);
            std::cout << "[SceneTestApp] *** KEY DEBUG *** - 1: " << key1State << ", 2: " << key2State
                      << ", Window: " << _window << ", Focused: " << windowFocused << std::endl;

            // 如果窗口失去焦点，尝试重新获取焦点
            if (!windowFocused)
            {
                std::cout << "[SceneTestApp] Window lost focus, attempting to regain focus..." << std::endl;
                glfwFocusWindow(_window);
                glfwRequestWindowAttention(_window);
            }
        }

        if (glfwGetKey(_window, GLFW_KEY_1) == GLFW_PRESS && !key1Pressed)
        {
            key1Pressed = true;
            std::cout << "[SceneTestApp] KEY 1 PRESSED! Switching to WebGPU backend..." << std::endl;
            switchBackend(BackendType::WebGPU);
        }
        else if (glfwGetKey(_window, GLFW_KEY_1) == GLFW_RELEASE)
        {
            key1Pressed = false;
        }

        if (glfwGetKey(_window, GLFW_KEY_2) == GLFW_PRESS && !key2Pressed)
        {
            key2Pressed = true;
            std::cout << "[SceneTestApp] KEY 2 PRESSED! Switching to Vulkan backend..." << std::endl;
            switchBackend(BackendType::Vulkan);
        }
        else if (glfwGetKey(_window, GLFW_KEY_2) == GLFW_RELEASE)
        {
            key2Pressed = false;
        }

        if (glfwGetKey(_window, GLFW_KEY_3) == GLFW_PRESS && !key3Pressed)
        {
            key3Pressed = true;
            std::cout << "[SceneTestApp] Switching to OpenGL backend..." << std::endl;
            switchBackend(BackendType::OpenGL);
        }
        else if (glfwGetKey(_window, GLFW_KEY_3) == GLFW_RELEASE)
        {
            key3Pressed = false;
        }
#endif
    }

    void SceneTestApp::updateStats()
    {
        // 每秒更新一次FPS
        _fpsFrameCount++;
        double currentTime = _appState.totalTime;

        if (currentTime - _fpsUpdateTime >= 1.0)
        {
            _appState.fps = _fpsFrameCount / (currentTime - _fpsUpdateTime);
            _appState.frameTime = (currentTime - _fpsUpdateTime) * 1000.0 / _fpsFrameCount;

            _fpsFrameCount = 0;
            _fpsUpdateTime = currentTime;

            // 打印统计信息
            if (_appConfig.showStats)
            {
                std::cout << "[Stats] FPS: " << static_cast<int>(_appState.fps)
                          << ", Frame Time: " << _appState.frameTime << "ms"
                          << ", Backend: " << _appState.currentBackendName << std::endl;
            }
        }
    }

    // GLFW回调函数
    void SceneTestApp::glfwErrorCallback(int error, const char *description)
    {
        std::cerr << "[GLFW Error] " << error << ": " << description << std::endl;
    }

    void SceneTestApp::glfwWindowSizeCallback(GLFWwindow *window, int width, int height)
    {
        SceneTestApp *app = static_cast<SceneTestApp *>(glfwGetWindowUserPointer(window));
        if (app)
        {
            app->onWindowResize(width, height);
        }
    }

    void SceneTestApp::glfwKeyCallback(GLFWwindow *window, int key, int scancode, int action, int mods)
    {
        SceneTestApp *app = static_cast<SceneTestApp *>(glfwGetWindowUserPointer(window));
        if (app)
        {
            app->onKeyPress(key, action);
        }
    }

    void SceneTestApp::glfwMouseButtonCallback(GLFWwindow *window, int button, int action, int mods)
    {
        SceneTestApp *app = static_cast<SceneTestApp *>(glfwGetWindowUserPointer(window));
        if (app)
        {
            app->onMouseButton(button, action);
        }
    }

    void SceneTestApp::glfwCursorPosCallback(GLFWwindow *window, double x, double y)
    {
        SceneTestApp *app = static_cast<SceneTestApp *>(glfwGetWindowUserPointer(window));
        if (app)
        {
            app->onMouseMove(x, y);
        }
    }

    void SceneTestApp::onWindowResize(int width, int height)
    {
        _appConfig.windowWidth = width;
        _appConfig.windowHeight = height;

        // 通知场景窗口大小变化
        // 这里应该更新投影矩阵等
    }

    void SceneTestApp::onKeyPress(int key, int action)
    {
        // 处理按键事件
    }

    void SceneTestApp::onMouseButton(int button, int action)
    {
        // 处理鼠标按键事件
    }

    void SceneTestApp::onMouseMove(double x, double y)
    {
        // 处理鼠标移动事件
        _mouseX = x;
        _mouseY = y;
    }

    // 像素测试方法实现
    void SceneTestApp::initPixelTest()
    {
        if (_window && _appConfig.enablePixelTest)
        {
            std::cout << "[SceneTestApp] Initializing pixel test tool..." << std::endl;
            _pixelTestTool = createPixelTestTool(_window);
            _pixelTestEnabled = (_pixelTestTool != nullptr);

            if (_pixelTestEnabled)
            {
                std::cout << "[SceneTestApp] ✅ Pixel test tool initialized successfully" << std::endl;
            }
            else
            {
                std::cout << "[SceneTestApp] ❌ Failed to initialize pixel test tool" << std::endl;
            }
        }
    }

    void SceneTestApp::cleanupPixelTest()
    {
        if (_pixelTestTool)
        {
            std::cout << "[SceneTestApp] Cleaning up pixel test tool..." << std::endl;
            destroyPixelTestTool(_pixelTestTool);
            _pixelTestTool = nullptr;
            _pixelTestEnabled = false;
        }
    }

    void SceneTestApp::runPixelTest()
    {
        if (!_pixelTestEnabled || !_pixelTestTool)
        {
            return;
        }

        std::cout << "\n[SceneTestApp] *** RUNNING PIXEL TEST ***" << std::endl;

        // 等待几帧让渲染稳定
        for (int i = 0; i < 10; i++)
        {
            glfwPollEvents();
            if (!_appState.paused)
            {
                renderFrame();
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }

        // 采样窗口像素
        sampleWindowPixels(_pixelTestTool);

        // 等待非黑色像素
        bool success = waitForValidPixels(_appConfig.pixelTestTimeoutSeconds);

        if (success)
        {
            std::cout << "[SceneTestApp] ✅ PIXEL TEST PASSED - Rendering is working!" << std::endl;
        }
        else
        {
            std::cout << "[SceneTestApp] ❌ PIXEL TEST FAILED - Black screen detected!" << std::endl;
            std::cout << "[SceneTestApp] This indicates a rendering problem that needs to be fixed." << std::endl;
        }
    }

    bool SceneTestApp::waitForValidPixels(int timeoutSeconds)
    {
        if (!_pixelTestEnabled || !_pixelTestTool)
        {
            return false;
        }

        return waitForNonBlackPixels(_pixelTestTool, timeoutSeconds) == 1;
    }

} // namespace USG
