<!doctypehtml><html lang=zh-CN><head><meta charset=utf-8><meta content="width=device-width,initial-scale=1"name=viewport><title>WebGPU 三维查看器</title><style>*{margin:0;padding:0;box-sizing:border-box}body{font-family:'Segoe UI',Tahoma,Geneva,Verdana,sans-serif;background:linear-gradient(135deg,#667eea 0,#764ba2 100%);color:#333;overflow:hidden}.container{display:flex;flex-direction:column;height:100vh}.header{background:rgba(255,255,255,.95);backdrop-filter:blur(10px);padding:15px 20px;box-shadow:0 2px 10px rgba(0,0,0,.1);z-index:1000}.header h1{color:#2c3e50;font-size:24px;font-weight:600;margin:0}.header .subtitle{color:#7f8c8d;font-size:14px;margin-top:5px}.main-content{display:flex;flex:1;overflow:hidden}.viewport-container{flex:1;display:flex;flex-direction:column;padding:20px;padding-right:10px}.viewport{flex:1;background:#000;border-radius:12px;overflow:hidden;box-shadow:0 8px 32px rgba(0,0,0,.3);position:relative}#canvas{width:100%;height:100%;display:block;border-radius:12px}.info-panel{width:300px;background:rgba(255,255,255,.95);backdrop-filter:blur(10px);padding:20px;overflow-y:auto;box-shadow:-2px 0 10px rgba(0,0,0,.1)}.info-section{margin-bottom:25px}.info-section h3{color:#2c3e50;font-size:16px;margin-bottom:12px;padding-bottom:8px;border-bottom:2px solid #3498db}.info-item{display:flex;justify-content:space-between;margin-bottom:8px;font-size:14px}.info-label{color:#7f8c8d;font-weight:500}.info-value{color:#2c3e50;font-weight:600}.toolbar{background:rgba(255,255,255,.95);backdrop-filter:blur(10px);padding:15px 20px;box-shadow:0 -2px 10px rgba(0,0,0,.1);display:flex;align-items:center;gap:20px}.btn{background:linear-gradient(135deg,#3498db,#2980b9);color:#fff;border:none;padding:10px 20px;border-radius:8px;cursor:pointer;font-size:14px;font-weight:500;transition:all .3s ease;box-shadow:0 4px 15px rgba(52,152,219,.3)}.btn:hover{transform:translateY(-2px);box-shadow:0 6px 20px rgba(52,152,219,.4)}.btn:active{transform:translateY(0)}.btn.active{background:linear-gradient(135deg,#e74c3c,#c0392b);box-shadow:0 4px 15px rgba(231,76,60,.3)}.btn.earth{background:linear-gradient(135deg,#27ae60,#229954);box-shadow:0 4px 15px rgba(39,174,96,.3)}.btn.earth:hover{box-shadow:0 6px 20px rgba(39,174,96,.4)}.toolbar-group{display:flex;align-items:center;gap:10px}.toolbar-separator{width:1px;height:30px;background:#bdc3c7}.loading{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:#fff;font-size:18px;z-index:100}.spinner{border:3px solid rgba(255,255,255,.3);border-top:3px solid #3498db;border-radius:50%;width:40px;height:40px;animation:spin 1s linear infinite;margin:0 auto 15px}@keyframes spin{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}@media (max-width:1024px){.info-panel{width:250px}}@media (max-width:768px){.main-content{flex-direction:column}.info-panel{width:100%;height:200px;order:2}.viewport-container{padding:10px;order:1}}</style></head><body><div class=container><header class=header><h1>WebGPU 三维查看器</h1><div class=subtitle>基于现代WebGPU技术的高性能三维渲染引擎</div></header><div class=main-content><div class=viewport-container><div class=viewport><div class=loading id=loading><div class=spinner></div><div>正在加载WebGPU引擎...</div></div><canvas class=emscripten id=canvas style=display:none></canvas></div></div><div class=info-panel><div class=info-section><h3>渲染信息</h3><div class=info-item><span class=info-label>渲染器:</span> <span class=info-value id=renderer>WebGPU</span></div><div class=info-item><span class=info-label>帧率:</span> <span class=info-value id=fps>-- FPS</span></div><div class=info-item><span class=info-label>三角形数:</span> <span class=info-value id=triangles>--</span></div><div class=info-item><span class=info-label>顶点数:</span> <span class=info-value id=vertices>--</span></div></div><div class=info-section><h3>相机信息</h3><div class=info-item><span class=info-label>位置 X:</span> <span class=info-value id=cam-x>0.00</span></div><div class=info-item><span class=info-label>位置 Y:</span> <span class=info-value id=cam-y>0.00</span></div><div class=info-item><span class=info-label>位置 Z:</span> <span class=info-value id=cam-z>0.00</span></div><div class=info-item><span class=info-label>视角:</span> <span class=info-value id=fov>45°</span></div></div><div class=info-section><h3>场景控制</h3><div class=info-item><span class=info-label>当前模式:</span> <span class=info-value id=current-mode>模型查看</span></div><div class=info-item><span class=info-label>光照:</span> <span class=info-value>启用</span></div><div class=info-item><span class=info-label>纹理:</span> <span class=info-value>启用</span></div></div></div></div><div class=toolbar><div class=toolbar-group><button class="btn active"onclick=switchToModel() id=btn-model>📦 模型查看</button> <button class="btn earth"onclick=switchToEarth() id=btn-earth>🌍 数字地球</button></div><div class=toolbar-separator></div><div class=toolbar-group><button class=btn onclick=resetCamera()>🎯 重置视角</button> <button class=btn onclick=toggleWireframe()>🔲 线框模式</button></div><div class=toolbar-separator></div><div class=toolbar-group><button class=btn onclick=captureScreen()>📷 截图</button> <button class=btn onclick=toggleFullscreen()>⛶ 全屏</button></div></div></div><script>var Module={canvas:function(){var e=document.getElementById("canvas");return e.addEventListener("webglcontextlost",(function(e){alert("WebGL context lost. You will need to reload the page."),e.preventDefault()}),!1),e}(),setStatus:function(e){e?console.log("Status: "+e):(console.log("Application ready"),document.getElementById("loading").style.display="none",document.getElementById("canvas").style.display="block")},totalDependencies:0,monitorRunDependencies:function(e){this.totalDependencies=Math.max(this.totalDependencies,e),Module.setStatus(e?"Preparing... ("+(this.totalDependencies-e)+"/"+this.totalDependencies+")":"All downloads complete.")}};let currentMode="model",frameCount=0,lastTime=performance.now();function switchToModel(){currentMode="model",document.getElementById("btn-model").classList.add("active"),document.getElementById("btn-earth").classList.remove("active"),document.getElementById("current-mode").textContent="模型查看",void 0!==Module&&Module.ccall&&Module.ccall("switchToModelMode",null,[],[])}function switchToEarth(){currentMode="earth",document.getElementById("btn-earth").classList.add("active"),document.getElementById("btn-model").classList.remove("active"),document.getElementById("current-mode").textContent="数字地球",void 0!==Module&&Module.ccall&&Module.ccall("switchToEarthMode",null,[],[])}function resetCamera(){void 0!==Module&&Module.ccall&&Module.ccall("resetCamera",null,[],[])}function toggleWireframe(){void 0!==Module&&Module.ccall&&Module.ccall("toggleWireframe",null,[],[])}function captureScreen(){const e=document.getElementById("canvas"),t=document.createElement("a");t.download="webgpu_screenshot.png",t.href=e.toDataURL(),t.click()}function toggleFullscreen(){document.fullscreenElement?document.exitFullscreen():document.documentElement.requestFullscreen()}function updateInfo(){frameCount++;const e=performance.now();if(e-lastTime>=1e3){const t=Math.round(1e3*frameCount/(e-lastTime));document.getElementById("fps").textContent=t+" FPS",frameCount=0,lastTime=e}}setInterval(updateInfo,16),window.onModuleReady=function(){document.getElementById("loading").style.display="none",document.getElementById("canvas").style.display="block",console.log("WebGPU模块加载完成")}</script><script async src=App.js></script></body></html>