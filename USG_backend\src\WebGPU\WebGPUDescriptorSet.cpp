#include "WebGPUBackend.h"

namespace USG {

// WebGPUDescriptorSet实现
WebGPUDescriptorSet::WebGPUDescriptorSet(WGPUBindGroup bindGroup)
    : _bindGroup(bindGroup), _needsUpdate(false) {
}

WebGPUDescriptorSet::~WebGPUDescriptorSet() {
    if (_bindGroup) {
        wgpuBindGroupRelease(_bindGroup);
    }
}

void WebGPUDescriptorSet::bindBuffer(uint32_t binding, BackendBuffer* buffer, size_t offset, size_t size) {
    if (!buffer) {
        return;
    }
    
    auto* webgpuBuffer = static_cast<WebGPUBuffer*>(buffer);
    
    WGPUBindGroupEntry entry = {};
    entry.binding = binding;
    entry.buffer = webgpuBuffer->getWGPUBuffer();
    entry.offset = offset;
    entry.size = (size == 0) ? webgpuBuffer->getSize() : size;
    
    // 查找是否已存在相同绑定点的条目
    bool found = false;
    for (auto& existingEntry : _entries) {
        if (existingEntry.binding == binding) {
            existingEntry = entry;
            found = true;
            break;
        }
    }
    
    if (!found) {
        _entries.push_back(entry);
    }
    
    _needsUpdate = true;
}

void WebGPUDescriptorSet::bindTexture(uint32_t binding, BackendTexture* texture) {
    if (!texture) {
        return;
    }
    
    auto* webgpuTexture = static_cast<WebGPUTexture*>(texture);
    
    WGPUBindGroupEntry entry = {};
    entry.binding = binding;
    entry.textureView = webgpuTexture->getWGPUTextureView();
    
    // 查找是否已存在相同绑定点的条目
    bool found = false;
    for (auto& existingEntry : _entries) {
        if (existingEntry.binding == binding) {
            existingEntry = entry;
            found = true;
            break;
        }
    }
    
    if (!found) {
        _entries.push_back(entry);
    }
    
    _needsUpdate = true;
}

void WebGPUDescriptorSet::update() {
    if (!_needsUpdate || _entries.empty()) {
        return;
    }
    
    // 注意：WebGPU的BindGroup是不可变的，需要重新创建
    // 这里简化实现，实际应该缓存BindGroupLayout并重新创建BindGroup
    _needsUpdate = false;
}

} // namespace USG
