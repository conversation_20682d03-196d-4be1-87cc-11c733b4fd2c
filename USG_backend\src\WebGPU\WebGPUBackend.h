#pragma once

#include <USG_Backend/RenderBackend.h>
#include "WebGPUAdapter.h"
#include <memory>
#include <vector>
#include <unordered_map>
#include <atomic>
#include <mutex>

namespace USG
{

    // WebGPU后端资源实现
    class WebGPUBuffer : public BackendBuffer
    {
    public:
        WebGPUBuffer(WGPUBuffer buffer, const BufferDesc &desc);
        ~WebGPUBuffer() override;

        uint64_t getSize() const override { return _desc.size; }
        BufferUsage getUsage() const override { return _desc.usage; }
        MemoryProperty getMemoryProperties() const override { return _desc.memoryProperties; }

        WGPUBuffer getWGPUBuffer() const { return _buffer; }

        // 映射相关
        void *getMappedData() const { return _mappedData; }
        void setMappedData(void *data) { _mappedData = data; }
        bool isMapped() const { return _mappedData != nullptr; }

    private:
        WGPUBuffer _buffer;
        BufferDesc _desc;
        void *_mappedData = nullptr;
    };

    class WebGPUTexture : public BackendTexture
    {
    public:
        WebGPUTexture(WGPUTexture texture, const TextureDesc &desc);
        ~WebGPUTexture() override;

        uint32_t getWidth() const override { return _desc.width; }
        uint32_t getHeight() const override { return _desc.height; }
        uint32_t getDepth() const override { return _desc.depth; }
        uint32_t getMipLevels() const override { return _desc.mipLevels; }
        uint32_t getArrayLayers() const override { return _desc.arrayLayers; }
        TextureFormat getFormat() const override { return _desc.format; }
        TextureUsage getUsage() const override { return _desc.usage; }

        WGPUTexture getWGPUTexture() const { return _texture; }
        WGPUTextureView getWGPUTextureView() const { return _textureView; }

        // 创建纹理视图
        WGPUTextureView createView(const WGPUTextureViewDescriptor *descriptor = nullptr);

    private:
        WGPUTexture _texture;
        WGPUTextureView _textureView;
        TextureDesc _desc;
    };

    class WebGPUShader : public BackendShader
    {
    public:
        WebGPUShader(wgpu::ShaderModule shaderModule, const ShaderDesc &desc);
        ~WebGPUShader() override = default;

        ShaderStage getStage() const override { return _desc.stage; }
        std::string getEntryPoint() const override { return _desc.entryPoint; }

        wgpu::ShaderModule getWGPUShaderModule() const { return _shaderModule; }

    private:
        wgpu::ShaderModule _shaderModule;
        ShaderDesc _desc;
    };

    class WebGPUPipeline : public BackendPipeline
    {
    public:
        WebGPUPipeline(wgpu::RenderPipeline pipeline, const PipelineDesc &desc);
        WebGPUPipeline(wgpu::ComputePipeline pipeline, const PipelineDesc &desc);
        ~WebGPUPipeline() override = default;

        bool isComputePipeline() const override { return _isComputePipeline; }

        wgpu::RenderPipeline getRenderPipeline() const { return _renderPipeline; }
        wgpu::ComputePipeline getComputePipeline() const { return _computePipeline; }

    private:
        wgpu::RenderPipeline _renderPipeline;
        wgpu::ComputePipeline _computePipeline;
        bool _isComputePipeline;
        PipelineDesc _desc;
    };

    class WebGPUDescriptorSet : public BackendDescriptorSet
    {
    public:
        WebGPUDescriptorSet(wgpu::BindGroup bindGroup);
        ~WebGPUDescriptorSet() override = default;

        void bindBuffer(uint32_t binding, BackendBuffer *buffer, size_t offset = 0, size_t size = 0) override;
        void bindTexture(uint32_t binding, BackendTexture *texture) override;
        void update() override;

        wgpu::BindGroup getWGPUBindGroup() const { return _bindGroup; }

    private:
        wgpu::BindGroup _bindGroup;
        std::vector<wgpu::BindGroupEntry> _entries;
        bool _needsUpdate = false;
    };

    class WebGPUCommandList : public BackendCommandList
    {
    public:
        WebGPUCommandList(wgpu::Device device);
        ~WebGPUCommandList() override = default;

        void begin() override;
        void end() override;
        void reset() override;

        void beginRenderPass(const RenderPassDesc &desc) override;
        void endRenderPass() override;

        void setPipeline(BackendPipeline *pipeline) override;
        void setVertexBuffer(BackendBuffer *buffer, uint32_t slot, size_t offset = 0) override;
        void setIndexBuffer(BackendBuffer *buffer, IndexFormat format, size_t offset = 0) override;
        void setDescriptorSet(BackendDescriptorSet *descriptorSet, uint32_t slot) override;

        void draw(uint32_t vertexCount, uint32_t instanceCount = 1, uint32_t firstVertex = 0, uint32_t firstInstance = 0) override;
        void drawIndexed(uint32_t indexCount, uint32_t instanceCount = 1, uint32_t firstIndex = 0, int32_t vertexOffset = 0, uint32_t firstInstance = 0) override;
        void dispatch(uint32_t groupCountX, uint32_t groupCountY = 1, uint32_t groupCountZ = 1) override;

        void barrier(const BarrierDesc &barrier) override;

        void pushDebugGroup(const std::string &name) override;
        void popDebugGroup() override;
        void insertDebugMarker(const std::string &name) override;

        wgpu::CommandBuffer getWGPUCommandBuffer() const { return _commandBuffer; }

    private:
        wgpu::Device _device;
        wgpu::CommandEncoder _encoder;
        wgpu::RenderPassEncoder _renderPassEncoder;
        wgpu::ComputePassEncoder _computePassEncoder;
        wgpu::CommandBuffer _commandBuffer;

        bool _inRenderPass = false;
        bool _inComputePass = false;
        bool _recording = false;
    };

    class WebGPUFence : public BackendFence
    {
    public:
        WebGPUFence();
        ~WebGPUFence() override = default;

        bool wait(uint64_t timeoutNs = UINT64_MAX) override;
        void reset() override;
        bool isSignaled() override;

        void signal();

    private:
        std::atomic<bool> _signaled{false};
    };

    class WebGPUSemaphore : public BackendSemaphore
    {
    public:
        WebGPUSemaphore();
        ~WebGPUSemaphore() override = default;

    private:
        // WebGPU doesn't have explicit semaphores, we'll use fences for synchronization
        std::unique_ptr<WebGPUFence> _fence;
    };

    class WebGPUDevice : public BackendDevice
    {
    public:
        WebGPUDevice(wgpu::Device device);
        ~WebGPUDevice() override = default;

        std::string getDeviceName() const override;
        DeviceCapabilities getCapabilities() const override;

        void *mapBuffer(BackendBuffer *buffer, size_t offset, size_t size) override;
        void unmapBuffer(BackendBuffer *buffer) override;
        void updateBuffer(BackendBuffer *buffer, const void *data, size_t size, size_t offset = 0) override;
        void updateTexture(BackendTexture *texture, const void *data, const TextureRegion &region) override;
        void generateMipmaps(BackendTexture *texture) override;

        wgpu::Device getWGPUDevice() const { return _device; }

    private:
        wgpu::Device _device;
        DeviceCapabilities _capabilities;

        void queryCapabilities();
    };

    /**
     * @brief WebGPU渲染后端实现
     */
    class WebGPUBackend : public RenderBackend
    {
    public:
        WebGPUBackend();
        ~WebGPUBackend() override;

        // RenderBackend接口实现
        BackendType getBackendType() const override { return BackendType::WebGPU; }
        std::string getBackendName() const override { return "WebGPU"; }

        bool initialize(const BackendConfig &config) override;
        void cleanup() override;

        BackendDevice *getDevice() override { return _backendDevice.get(); }

        void beginFrame() override;
        void endFrame() override;
        void present() override;
        void waitIdle() override;

        BackendBuffer *createBuffer(const BufferDesc &desc) override;
        BackendTexture *createTexture(const TextureDesc &desc) override;
        BackendShader *createShader(const ShaderDesc &desc) override;
        BackendPipeline *createPipeline(const PipelineDesc &desc) override;
        BackendDescriptorSet *createDescriptorSet() override;
        BackendCommandList *createCommandList() override;
        BackendFence *createFence() override;
        BackendSemaphore *createSemaphore() override;

        void executeCommandList(BackendCommandList *cmdList,
                                const std::vector<BackendSemaphore *> &waitSemaphores = {},
                                const std::vector<BackendSemaphore *> &signalSemaphores = {},
                                BackendFence *fence = nullptr) override;

        void destroyBuffer(BackendBuffer *buffer) override;
        void destroyTexture(BackendTexture *texture) override;
        void destroyShader(BackendShader *shader) override;
        void destroyPipeline(BackendPipeline *pipeline) override;
        void destroyDescriptorSet(BackendDescriptorSet *descriptorSet) override;
        void destroyCommandList(BackendCommandList *cmdList) override;
        void destroyFence(BackendFence *fence) override;
        void destroySemaphore(BackendSemaphore *semaphore) override;

        void setErrorCallback(std::function<void(const std::string &)> callback) override;
        void setDebugCallback(std::function<void(const std::string &)> callback) override;

        // WebGPU特定接口
        WGPUDevice getWGPUDevice() const { return _device; }
        WGPUQueue getWGPUQueue() const { return _queue; }
        WGPUSurface getWGPUSurface() const { return _surface; }

    private:
        // WebGPU核心对象
        WGPUInstance _instance;
        WGPUAdapter _adapter;
        WGPUDevice _device;
        WGPUQueue _queue;
        WGPUSurface _surface;
        WGPUSwapChain _swapChain;

        // 后端设备包装
        std::unique_ptr<WebGPUDevice> _backendDevice;

        // 配置和状态
        BackendConfig _config;
        bool _initialized = false;

        // 回调函数
        std::function<void(const std::string &)> _errorCallback;
        std::function<void(const std::string &)> _debugCallback;

        // 内部方法
        bool createInstance();
        bool requestAdapter();
        bool requestDevice();
        bool createSurface();
        bool createSwapChain();

        // 管线创建辅助方法
        BackendPipeline *createRenderPipeline(const PipelineDesc &desc);
        BackendPipeline *createComputePipeline(const PipelineDesc &desc);

        void setupErrorHandling();
        void logMessage(const std::string &message);
        void logError(const std::string &error);

        // 类型转换方法
        WGPUBufferUsage convertBufferUsage(BufferUsage usage);
        WGPUTextureUsage convertTextureUsage(TextureUsage usage);
        WGPUTextureFormat convertTextureFormat(TextureFormat format);
        WGPUIndexFormat convertIndexFormat(IndexFormat format);
        WGPUPrimitiveTopology convertPrimitiveTopology(PrimitiveTopology topology);
        WGPUCompareFunction convertCompareFunction(CompareFunction func);
        WGPUBlendOperation convertBlendOperation(BlendOperation op);
        WGPUBlendFactor convertBlendFactor(BlendFactor factor);

        BufferUsage convertWGPUBufferUsage(WGPUBufferUsage usage);
        TextureUsage convertWGPUTextureUsage(WGPUTextureUsage usage);
        TextureFormat convertWGPUTextureFormat(WGPUTextureFormat format);
    };

} // namespace USG
