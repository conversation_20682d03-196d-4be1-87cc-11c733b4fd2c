#include "TypeConverter.h"
#include <algorithm>
#include <cmath>
#include <iostream>

namespace USG {

BufferDesc TypeConverter::convertVkBufferCreateInfo(const VkBufferCreateInfo* pCreateInfo) {
    BufferDesc desc;
    
    desc.size = pCreateInfo->size;
    desc.usage = convertVkBufferUsage(pCreateInfo->usage);
    desc.memoryProperties = MemoryProperty::DeviceLocal; // 默认设备本地
    desc.mappedAtCreation = false;
    desc.label = "VSG Buffer";
    
    return desc;
}

TextureDesc TypeConverter::convertVkImageCreateInfo(const VkImageCreateInfo* pCreateInfo) {
    TextureDesc desc;
    
    desc.width = pCreateInfo->extent.width;
    desc.height = pCreateInfo->extent.height;
    desc.depth = pCreateInfo->extent.depth;
    desc.mipLevels = pCreateInfo->mipLevels;
    desc.arrayLayers = pCreateInfo->arrayLayers;
    desc.format = convertVkFormat(pCreateInfo->format);
    desc.usage = convertVkImageUsage(pCreateInfo->usage);
    desc.sampleCount = pCreateInfo->samples;
    desc.label = "VSG Texture";
    
    return desc;
}

ShaderDesc TypeConverter::convertVkShaderModuleCreateInfo(const VkShaderModuleCreateInfo* pCreateInfo, ShaderStage stage) {
    ShaderDesc desc;
    
    desc.stage = stage;
    desc.entryPoint = "main"; // VSG通常使用main作为入口点
    desc.label = "VSG Shader";
    
    // 复制字节码
    const uint8_t* codePtr = reinterpret_cast<const uint8_t*>(pCreateInfo->pCode);
    desc.bytecode.assign(codePtr, codePtr + pCreateInfo->codeSize);
    
    return desc;
}

BufferUsage TypeConverter::convertVkBufferUsage(VkBufferUsageFlags usage) {
    BufferUsage result = BufferUsage::None;
    
    if (usage & VK_BUFFER_USAGE_VERTEX_BUFFER_BIT) {
        result = result | BufferUsage::Vertex;
    }
    if (usage & VK_BUFFER_USAGE_INDEX_BUFFER_BIT) {
        result = result | BufferUsage::Index;
    }
    if (usage & VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT) {
        result = result | BufferUsage::Uniform;
    }
    if (usage & VK_BUFFER_USAGE_STORAGE_BUFFER_BIT) {
        result = result | BufferUsage::Storage;
    }
    if (usage & VK_BUFFER_USAGE_INDIRECT_BUFFER_BIT) {
        result = result | BufferUsage::Indirect;
    }
    if (usage & VK_BUFFER_USAGE_TRANSFER_SRC_BIT) {
        result = result | BufferUsage::TransferSrc;
    }
    if (usage & VK_BUFFER_USAGE_TRANSFER_DST_BIT) {
        result = result | BufferUsage::TransferDst;
    }
    
    return result;
}

TextureUsage TypeConverter::convertVkImageUsage(VkImageUsageFlags usage) {
    TextureUsage result = TextureUsage::None;
    
    if (usage & VK_IMAGE_USAGE_SAMPLED_BIT) {
        result = result | TextureUsage::Sampled;
    }
    if (usage & VK_IMAGE_USAGE_STORAGE_BIT) {
        result = result | TextureUsage::Storage;
    }
    if (usage & VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT) {
        result = result | TextureUsage::RenderTarget;
    }
    if (usage & VK_IMAGE_USAGE_DEPTH_STENCIL_ATTACHMENT_BIT) {
        result = result | TextureUsage::DepthStencil;
    }
    if (usage & VK_IMAGE_USAGE_TRANSFER_SRC_BIT) {
        result = result | TextureUsage::TransferSrc;
    }
    if (usage & VK_IMAGE_USAGE_TRANSFER_DST_BIT) {
        result = result | TextureUsage::TransferDst;
    }
    
    return result;
}

TextureFormat TypeConverter::convertVkFormat(uint32_t format) {
    // Vulkan格式常量定义（简化版）
    const uint32_t VK_FORMAT_R8_UNORM = 9;
    const uint32_t VK_FORMAT_R8_SNORM = 10;
    const uint32_t VK_FORMAT_R8_UINT = 13;
    const uint32_t VK_FORMAT_R8_SINT = 14;
    const uint32_t VK_FORMAT_R8G8_UNORM = 16;
    const uint32_t VK_FORMAT_R8G8_SNORM = 17;
    const uint32_t VK_FORMAT_R8G8_UINT = 20;
    const uint32_t VK_FORMAT_R8G8_SINT = 21;
    const uint32_t VK_FORMAT_R8G8B8A8_UNORM = 37;
    const uint32_t VK_FORMAT_R8G8B8A8_SNORM = 38;
    const uint32_t VK_FORMAT_R8G8B8A8_UINT = 41;
    const uint32_t VK_FORMAT_R8G8B8A8_SINT = 42;
    const uint32_t VK_FORMAT_R8G8B8A8_SRGB = 43;
    const uint32_t VK_FORMAT_B8G8R8A8_UNORM = 44;
    const uint32_t VK_FORMAT_B8G8R8A8_SRGB = 50;
    const uint32_t VK_FORMAT_R16_UNORM = 70;
    const uint32_t VK_FORMAT_R16_SNORM = 71;
    const uint32_t VK_FORMAT_R16_UINT = 74;
    const uint32_t VK_FORMAT_R16_SINT = 75;
    const uint32_t VK_FORMAT_R16_SFLOAT = 76;
    const uint32_t VK_FORMAT_R16G16_UNORM = 77;
    const uint32_t VK_FORMAT_R16G16_SNORM = 78;
    const uint32_t VK_FORMAT_R16G16_UINT = 81;
    const uint32_t VK_FORMAT_R16G16_SINT = 82;
    const uint32_t VK_FORMAT_R16G16_SFLOAT = 83;
    const uint32_t VK_FORMAT_R16G16B16A16_UNORM = 91;
    const uint32_t VK_FORMAT_R16G16B16A16_SNORM = 92;
    const uint32_t VK_FORMAT_R16G16B16A16_UINT = 95;
    const uint32_t VK_FORMAT_R16G16B16A16_SINT = 96;
    const uint32_t VK_FORMAT_R16G16B16A16_SFLOAT = 97;
    const uint32_t VK_FORMAT_R32_UINT = 98;
    const uint32_t VK_FORMAT_R32_SINT = 99;
    const uint32_t VK_FORMAT_R32_SFLOAT = 100;
    const uint32_t VK_FORMAT_R32G32_UINT = 101;
    const uint32_t VK_FORMAT_R32G32_SINT = 102;
    const uint32_t VK_FORMAT_R32G32_SFLOAT = 103;
    const uint32_t VK_FORMAT_R32G32B32A32_UINT = 107;
    const uint32_t VK_FORMAT_R32G32B32A32_SINT = 108;
    const uint32_t VK_FORMAT_R32G32B32A32_SFLOAT = 109;
    const uint32_t VK_FORMAT_D16_UNORM = 124;
    const uint32_t VK_FORMAT_D32_SFLOAT = 126;
    const uint32_t VK_FORMAT_D24_UNORM_S8_UINT = 129;
    const uint32_t VK_FORMAT_D32_SFLOAT_S8_UINT = 130;
    
    switch (format) {
        // 8位格式
        case VK_FORMAT_R8_UNORM: return TextureFormat::R8_UNorm;
        case VK_FORMAT_R8_SNORM: return TextureFormat::R8_SNorm;
        case VK_FORMAT_R8_UINT: return TextureFormat::R8_UInt;
        case VK_FORMAT_R8_SINT: return TextureFormat::R8_SInt;
        
        // 16位格式
        case VK_FORMAT_R8G8_UNORM: return TextureFormat::RG8_UNorm;
        case VK_FORMAT_R8G8_SNORM: return TextureFormat::RG8_SNorm;
        case VK_FORMAT_R8G8_UINT: return TextureFormat::RG8_UInt;
        case VK_FORMAT_R8G8_SINT: return TextureFormat::RG8_SInt;
        case VK_FORMAT_R16_UNORM: return TextureFormat::R16_UNorm;
        case VK_FORMAT_R16_SNORM: return TextureFormat::R16_SNorm;
        case VK_FORMAT_R16_UINT: return TextureFormat::R16_UInt;
        case VK_FORMAT_R16_SINT: return TextureFormat::R16_SInt;
        case VK_FORMAT_R16_SFLOAT: return TextureFormat::R16_Float;
        
        // 32位格式
        case VK_FORMAT_R8G8B8A8_UNORM: return TextureFormat::RGBA8_UNorm;
        case VK_FORMAT_R8G8B8A8_SNORM: return TextureFormat::RGBA8_SNorm;
        case VK_FORMAT_R8G8B8A8_UINT: return TextureFormat::RGBA8_UInt;
        case VK_FORMAT_R8G8B8A8_SINT: return TextureFormat::RGBA8_SInt;
        case VK_FORMAT_R8G8B8A8_SRGB: return TextureFormat::RGBA8_UNorm_sRGB;
        case VK_FORMAT_B8G8R8A8_UNORM: return TextureFormat::BGRA8_UNorm;
        case VK_FORMAT_B8G8R8A8_SRGB: return TextureFormat::BGRA8_UNorm_sRGB;
        case VK_FORMAT_R16G16_UNORM: return TextureFormat::RG16_UNorm;
        case VK_FORMAT_R16G16_SNORM: return TextureFormat::RG16_SNorm;
        case VK_FORMAT_R16G16_UINT: return TextureFormat::RG16_UInt;
        case VK_FORMAT_R16G16_SINT: return TextureFormat::RG16_SInt;
        case VK_FORMAT_R16G16_SFLOAT: return TextureFormat::RG16_Float;
        case VK_FORMAT_R32_UINT: return TextureFormat::R32_UInt;
        case VK_FORMAT_R32_SINT: return TextureFormat::R32_SInt;
        case VK_FORMAT_R32_SFLOAT: return TextureFormat::R32_Float;
        
        // 64位格式
        case VK_FORMAT_R16G16B16A16_UNORM: return TextureFormat::RGBA16_UNorm;
        case VK_FORMAT_R16G16B16A16_SNORM: return TextureFormat::RGBA16_SNorm;
        case VK_FORMAT_R16G16B16A16_UINT: return TextureFormat::RGBA16_UInt;
        case VK_FORMAT_R16G16B16A16_SINT: return TextureFormat::RGBA16_SInt;
        case VK_FORMAT_R16G16B16A16_SFLOAT: return TextureFormat::RGBA16_Float;
        case VK_FORMAT_R32G32_UINT: return TextureFormat::RG32_UInt;
        case VK_FORMAT_R32G32_SINT: return TextureFormat::RG32_SInt;
        case VK_FORMAT_R32G32_SFLOAT: return TextureFormat::RG32_Float;
        
        // 128位格式
        case VK_FORMAT_R32G32B32A32_UINT: return TextureFormat::RGBA32_UInt;
        case VK_FORMAT_R32G32B32A32_SINT: return TextureFormat::RGBA32_SInt;
        case VK_FORMAT_R32G32B32A32_SFLOAT: return TextureFormat::RGBA32_Float;
        
        // 深度/模板格式
        case VK_FORMAT_D16_UNORM: return TextureFormat::Depth16_UNorm;
        case VK_FORMAT_D24_UNORM_S8_UINT: return TextureFormat::Depth24_UNorm_Stencil8_UInt;
        case VK_FORMAT_D32_SFLOAT: return TextureFormat::Depth32_Float;
        case VK_FORMAT_D32_SFLOAT_S8_UINT: return TextureFormat::Depth32_Float_Stencil8_UInt;
        
        default:
            std::cerr << "[TypeConverter] Unsupported Vulkan format: " << format << std::endl;
            return TextureFormat::RGBA8_UNorm; // 默认格式
    }
}

IndexFormat TypeConverter::convertVkIndexType(VkIndexType indexType) {
    switch (indexType) {
        case VK_INDEX_TYPE_UINT16: return IndexFormat::UInt16;
        case VK_INDEX_TYPE_UINT32: return IndexFormat::UInt32;
        default:
            std::cerr << "[TypeConverter] Unsupported index type: " << indexType << std::endl;
            return IndexFormat::UInt16;
    }
}

PrimitiveTopology TypeConverter::convertVkPrimitiveTopology(uint32_t topology) {
    // Vulkan拓扑常量
    const uint32_t VK_PRIMITIVE_TOPOLOGY_POINT_LIST = 0;
    const uint32_t VK_PRIMITIVE_TOPOLOGY_LINE_LIST = 1;
    const uint32_t VK_PRIMITIVE_TOPOLOGY_LINE_STRIP = 2;
    const uint32_t VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST = 3;
    const uint32_t VK_PRIMITIVE_TOPOLOGY_TRIANGLE_STRIP = 4;
    
    switch (topology) {
        case VK_PRIMITIVE_TOPOLOGY_POINT_LIST: return PrimitiveTopology::PointList;
        case VK_PRIMITIVE_TOPOLOGY_LINE_LIST: return PrimitiveTopology::LineList;
        case VK_PRIMITIVE_TOPOLOGY_LINE_STRIP: return PrimitiveTopology::LineStrip;
        case VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST: return PrimitiveTopology::TriangleList;
        case VK_PRIMITIVE_TOPOLOGY_TRIANGLE_STRIP: return PrimitiveTopology::TriangleStrip;
        default:
            std::cerr << "[TypeConverter] Unsupported primitive topology: " << topology << std::endl;
            return PrimitiveTopology::TriangleList;
    }
}

MemoryProperty TypeConverter::convertVkMemoryPropertyFlags(VkMemoryPropertyFlags flags) {
    MemoryProperty result = static_cast<MemoryProperty>(0);
    
    const VkMemoryPropertyFlags VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT = 0x00000001;
    const VkMemoryPropertyFlags VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT = 0x00000002;
    const VkMemoryPropertyFlags VK_MEMORY_PROPERTY_HOST_COHERENT_BIT = 0x00000004;
    const VkMemoryPropertyFlags VK_MEMORY_PROPERTY_HOST_CACHED_BIT = 0x00000008;
    const VkMemoryPropertyFlags VK_MEMORY_PROPERTY_LAZILY_ALLOCATED_BIT = 0x00000010;
    
    if (flags & VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT) {
        result = result | MemoryProperty::DeviceLocal;
    }
    if (flags & VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT) {
        result = result | MemoryProperty::HostVisible;
    }
    if (flags & VK_MEMORY_PROPERTY_HOST_COHERENT_BIT) {
        result = result | MemoryProperty::HostCoherent;
    }
    if (flags & VK_MEMORY_PROPERTY_HOST_CACHED_BIT) {
        result = result | MemoryProperty::HostCached;
    }
    if (flags & VK_MEMORY_PROPERTY_LAZILY_ALLOCATED_BIT) {
        result = result | MemoryProperty::LazilyAllocated;
    }
    
    return result;
}

// 反向转换方法
VkBufferUsageFlags TypeConverter::convertToVkBufferUsage(BufferUsage usage) {
    VkBufferUsageFlags result = 0;
    
    if (usage & BufferUsage::Vertex) {
        result |= VK_BUFFER_USAGE_VERTEX_BUFFER_BIT;
    }
    if (usage & BufferUsage::Index) {
        result |= VK_BUFFER_USAGE_INDEX_BUFFER_BIT;
    }
    if (usage & BufferUsage::Uniform) {
        result |= VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT;
    }
    if (usage & BufferUsage::Storage) {
        result |= VK_BUFFER_USAGE_STORAGE_BUFFER_BIT;
    }
    if (usage & BufferUsage::Indirect) {
        result |= VK_BUFFER_USAGE_INDIRECT_BUFFER_BIT;
    }
    if (usage & BufferUsage::TransferSrc) {
        result |= VK_BUFFER_USAGE_TRANSFER_SRC_BIT;
    }
    if (usage & BufferUsage::TransferDst) {
        result |= VK_BUFFER_USAGE_TRANSFER_DST_BIT;
    }
    
    return result;
}

VkImageUsageFlags TypeConverter::convertToVkImageUsage(TextureUsage usage) {
    VkImageUsageFlags result = 0;
    
    if (usage & TextureUsage::Sampled) {
        result |= VK_IMAGE_USAGE_SAMPLED_BIT;
    }
    if (usage & TextureUsage::Storage) {
        result |= VK_IMAGE_USAGE_STORAGE_BIT;
    }
    if (usage & TextureUsage::RenderTarget) {
        result |= VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT;
    }
    if (usage & TextureUsage::DepthStencil) {
        result |= VK_IMAGE_USAGE_DEPTH_STENCIL_ATTACHMENT_BIT;
    }
    if (usage & TextureUsage::TransferSrc) {
        result |= VK_IMAGE_USAGE_TRANSFER_SRC_BIT;
    }
    if (usage & TextureUsage::TransferDst) {
        result |= VK_IMAGE_USAGE_TRANSFER_DST_BIT;
    }
    
    return result;
}

// 辅助方法
uint32_t TypeConverter::calculateMipLevels(uint32_t width, uint32_t height, uint32_t depth) {
    uint32_t maxDimension = std::max({width, height, depth});
    return static_cast<uint32_t>(std::floor(std::log2(maxDimension))) + 1;
}

bool TypeConverter::isDepthStencilFormat(uint32_t format) {
    const uint32_t VK_FORMAT_D16_UNORM = 124;
    const uint32_t VK_FORMAT_D32_SFLOAT = 126;
    const uint32_t VK_FORMAT_D24_UNORM_S8_UINT = 129;
    const uint32_t VK_FORMAT_D32_SFLOAT_S8_UINT = 130;
    
    return format == VK_FORMAT_D16_UNORM ||
           format == VK_FORMAT_D32_SFLOAT ||
           format == VK_FORMAT_D24_UNORM_S8_UINT ||
           format == VK_FORMAT_D32_SFLOAT_S8_UINT;
}

bool TypeConverter::isCompressedFormat(uint32_t format) {
    // 简化实现，实际应该检查所有压缩格式
    return format >= 131 && format <= 184; // BC格式范围
}

uint32_t TypeConverter::getFormatBlockSize(uint32_t format) {
    // 简化实现，返回每像素字节数
    // 实际实现应该根据具体格式返回正确的块大小
    return 4; // 默认RGBA8格式
}

} // namespace USG
