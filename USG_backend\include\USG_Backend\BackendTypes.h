#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <memory>

namespace USG {

// 前向声明
class RenderBackend;
class BackendDevice;
class BackendCommandList;
class BackendBuffer;
class BackendTexture;
class BackendPipeline;
class BackendShader;
class BackendDescriptorSet;
class BackendFence;
class BackendSemaphore;

// 后端类型枚举
enum class BackendType : uint32_t {
    Unknown = 0,
    WebGPU = 1,
    Vulkan = 2,
    OpenGL = 3,
    DirectX12 = 4,
    Metal = 5
};

// 缓冲区用途标志
enum class BufferUsage : uint32_t {
    None = 0,
    Vertex = 1 << 0,
    Index = 1 << 1,
    Uniform = 1 << 2,
    Storage = 1 << 3,
    Indirect = 1 << 4,
    TransferSrc = 1 << 5,
    TransferDst = 1 << 6,
    QueryBuffer = 1 << 7
};

// 纹理用途标志
enum class TextureUsage : uint32_t {
    None = 0,
    Sampled = 1 << 0,
    Storage = 1 << 1,
    RenderTarget = 1 << 2,
    DepthStencil = 1 << 3,
    TransferSrc = 1 << 4,
    TransferDst = 1 << 5
};

// 纹理格式
enum class TextureFormat : uint32_t {
    Unknown = 0,
    
    // 8位格式
    R8_UNorm,
    R8_SNorm,
    R8_UInt,
    R8_SInt,
    
    // 16位格式
    R16_UNorm,
    R16_SNorm,
    R16_UInt,
    R16_SInt,
    R16_Float,
    RG8_UNorm,
    RG8_SNorm,
    RG8_UInt,
    RG8_SInt,
    
    // 32位格式
    R32_UInt,
    R32_SInt,
    R32_Float,
    RG16_UNorm,
    RG16_SNorm,
    RG16_UInt,
    RG16_SInt,
    RG16_Float,
    RGBA8_UNorm,
    RGBA8_UNorm_sRGB,
    RGBA8_SNorm,
    RGBA8_UInt,
    RGBA8_SInt,
    BGRA8_UNorm,
    BGRA8_UNorm_sRGB,
    
    // 64位格式
    RG32_UInt,
    RG32_SInt,
    RG32_Float,
    RGBA16_UNorm,
    RGBA16_SNorm,
    RGBA16_UInt,
    RGBA16_SInt,
    RGBA16_Float,
    
    // 128位格式
    RGBA32_UInt,
    RGBA32_SInt,
    RGBA32_Float,
    
    // 深度/模板格式
    Depth16_UNorm,
    Depth24_UNorm_Stencil8_UInt,
    Depth32_Float,
    Depth32_Float_Stencil8_UInt,
    
    // 压缩格式
    BC1_RGBA_UNorm,
    BC1_RGBA_UNorm_sRGB,
    BC2_RGBA_UNorm,
    BC2_RGBA_UNorm_sRGB,
    BC3_RGBA_UNorm,
    BC3_RGBA_UNorm_sRGB,
    BC4_R_UNorm,
    BC4_R_SNorm,
    BC5_RG_UNorm,
    BC5_RG_SNorm,
    BC6H_RGB_UFloat,
    BC6H_RGB_SFloat,
    BC7_RGBA_UNorm,
    BC7_RGBA_UNorm_sRGB
};

// 索引格式
enum class IndexFormat : uint32_t {
    UInt16 = 0,
    UInt32 = 1
};

// 图元拓扑
enum class PrimitiveTopology : uint32_t {
    PointList = 0,
    LineList = 1,
    LineStrip = 2,
    TriangleList = 3,
    TriangleStrip = 4
};

// 比较函数
enum class CompareFunction : uint32_t {
    Never = 0,
    Less = 1,
    Equal = 2,
    LessEqual = 3,
    Greater = 4,
    NotEqual = 5,
    GreaterEqual = 6,
    Always = 7
};

// 混合操作
enum class BlendOperation : uint32_t {
    Add = 0,
    Subtract = 1,
    ReverseSubtract = 2,
    Min = 3,
    Max = 4
};

// 混合因子
enum class BlendFactor : uint32_t {
    Zero = 0,
    One = 1,
    Src = 2,
    OneMinusSrc = 3,
    SrcAlpha = 4,
    OneMinusSrcAlpha = 5,
    Dst = 6,
    OneMinusDst = 7,
    DstAlpha = 8,
    OneMinusDstAlpha = 9,
    SrcAlphaSaturated = 10,
    Constant = 11,
    OneMinusConstant = 12
};

// 着色器阶段
enum class ShaderStage : uint32_t {
    Vertex = 1 << 0,
    Fragment = 1 << 1,
    Compute = 1 << 2,
    Geometry = 1 << 3,
    TessellationControl = 1 << 4,
    TessellationEvaluation = 1 << 5
};

// 内存属性
enum class MemoryProperty : uint32_t {
    DeviceLocal = 1 << 0,
    HostVisible = 1 << 1,
    HostCoherent = 1 << 2,
    HostCached = 1 << 3,
    LazilyAllocated = 1 << 4
};

// 缓冲区描述符
struct BufferDesc {
    uint64_t size = 0;
    BufferUsage usage = BufferUsage::None;
    MemoryProperty memoryProperties = MemoryProperty::DeviceLocal;
    bool mappedAtCreation = false;
    std::string label;
};

// 纹理描述符
struct TextureDesc {
    uint32_t width = 1;
    uint32_t height = 1;
    uint32_t depth = 1;
    uint32_t mipLevels = 1;
    uint32_t arrayLayers = 1;
    TextureFormat format = TextureFormat::RGBA8_UNorm;
    TextureUsage usage = TextureUsage::Sampled;
    uint32_t sampleCount = 1;
    std::string label;
};

// 纹理区域
struct TextureRegion {
    uint32_t x = 0;
    uint32_t y = 0;
    uint32_t z = 0;
    uint32_t width = 1;
    uint32_t height = 1;
    uint32_t depth = 1;
    uint32_t mipLevel = 0;
    uint32_t arrayLayer = 0;
};

// 着色器描述符
struct ShaderDesc {
    ShaderStage stage;
    std::vector<uint8_t> bytecode;
    std::string entryPoint = "main";
    std::string label;
};

// 顶点属性描述
struct VertexAttribute {
    uint32_t location;
    uint32_t binding;
    TextureFormat format;
    uint32_t offset;
};

// 顶点绑定描述
struct VertexBinding {
    uint32_t binding;
    uint32_t stride;
    bool perInstance = false;
};

// 混合状态
struct BlendState {
    bool enabled = false;
    BlendFactor srcColorFactor = BlendFactor::One;
    BlendFactor dstColorFactor = BlendFactor::Zero;
    BlendOperation colorOperation = BlendOperation::Add;
    BlendFactor srcAlphaFactor = BlendFactor::One;
    BlendFactor dstAlphaFactor = BlendFactor::Zero;
    BlendOperation alphaOperation = BlendOperation::Add;
    uint8_t writeMask = 0xF;
};

// 深度模板状态
struct DepthStencilState {
    bool depthTestEnabled = false;
    bool depthWriteEnabled = false;
    CompareFunction depthCompareFunction = CompareFunction::Less;
    bool stencilTestEnabled = false;
    uint8_t stencilReadMask = 0xFF;
    uint8_t stencilWriteMask = 0xFF;
    // 简化的模板状态，可以根据需要扩展
};

// 光栅化状态
struct RasterizationState {
    PrimitiveTopology topology = PrimitiveTopology::TriangleList;
    bool cullEnabled = false;
    bool frontCounterClockwise = false;
    float depthBias = 0.0f;
    float depthBiasSlopeScale = 0.0f;
    float depthBiasClamp = 0.0f;
};

// 管线描述符
struct PipelineDesc {
    BackendShader* vertexShader = nullptr;
    BackendShader* fragmentShader = nullptr;
    std::vector<VertexAttribute> vertexAttributes;
    std::vector<VertexBinding> vertexBindings;
    RasterizationState rasterizationState;
    DepthStencilState depthStencilState;
    std::vector<BlendState> blendStates;
    std::vector<TextureFormat> colorTargetFormats;
    TextureFormat depthStencilFormat = TextureFormat::Unknown;
    uint32_t sampleCount = 1;
    std::string label;
};

// 渲染通道描述符
struct RenderPassDesc {
    struct ColorAttachment {
        BackendTexture* texture = nullptr;
        uint32_t mipLevel = 0;
        uint32_t arrayLayer = 0;
        float clearColor[4] = {0.0f, 0.0f, 0.0f, 1.0f};
        bool loadClear = true;
        bool storeResult = true;
    };
    
    struct DepthStencilAttachment {
        BackendTexture* texture = nullptr;
        uint32_t mipLevel = 0;
        uint32_t arrayLayer = 0;
        float clearDepth = 1.0f;
        uint32_t clearStencil = 0;
        bool loadClearDepth = true;
        bool loadClearStencil = true;
        bool storeDepth = true;
        bool storeStencil = true;
    };
    
    std::vector<ColorAttachment> colorAttachments;
    DepthStencilAttachment depthStencilAttachment;
    std::string label;
};

// 屏障描述符
struct BarrierDesc {
    enum Type {
        Memory,
        Buffer,
        Texture
    } type;
    
    // 缓冲区屏障
    struct BufferBarrier {
        BackendBuffer* buffer;
        uint64_t offset;
        uint64_t size;
    } bufferBarrier;
    
    // 纹理屏障
    struct TextureBarrier {
        BackendTexture* texture;
        uint32_t mipLevel;
        uint32_t mipLevelCount;
        uint32_t arrayLayer;
        uint32_t arrayLayerCount;
    } textureBarrier;
};

// 清除值
struct ClearValue {
    union {
        float color[4];
        struct {
            float depth;
            uint32_t stencil;
        } depthStencil;
    };
};

// 设备能力
struct DeviceCapabilities {
    uint32_t maxTextureSize1D;
    uint32_t maxTextureSize2D;
    uint32_t maxTextureSize3D;
    uint32_t maxTextureArrayLayers;
    uint32_t maxColorAttachments;
    uint32_t maxVertexAttributes;
    uint32_t maxVertexBindings;
    uint64_t maxUniformBufferSize;
    uint64_t maxStorageBufferSize;
    uint32_t maxSampledTexturesPerStage;
    uint32_t maxSamplersPerStage;
    uint32_t maxStorageTexturesPerStage;
    uint32_t maxUniformBuffersPerStage;
    uint32_t maxStorageBuffersPerStage;
    bool supportsComputeShaders;
    bool supportsGeometryShaders;
    bool supportsTessellationShaders;
    bool supportsMultiDrawIndirect;
    bool supportsTimestampQueries;
    bool supportsPipelineStatisticsQueries;
};

// 后端配置
struct BackendConfig {
    bool enableValidation = false;
    bool enableDebugMarkers = false;
    bool enableGPUBasedValidation = false;
    std::string applicationName = "USG Backend Application";
    uint32_t applicationVersion = 1;
    void* nativeWindow = nullptr;
    uint32_t swapchainWidth = 1024;
    uint32_t swapchainHeight = 768;
    TextureFormat swapchainFormat = TextureFormat::BGRA8_UNorm;
    uint32_t swapchainBufferCount = 2;
    bool vsyncEnabled = true;
};

// 位运算操作符重载
inline BufferUsage operator|(BufferUsage a, BufferUsage b) {
    return static_cast<BufferUsage>(static_cast<uint32_t>(a) | static_cast<uint32_t>(b));
}

inline BufferUsage operator&(BufferUsage a, BufferUsage b) {
    return static_cast<BufferUsage>(static_cast<uint32_t>(a) & static_cast<uint32_t>(b));
}

inline TextureUsage operator|(TextureUsage a, TextureUsage b) {
    return static_cast<TextureUsage>(static_cast<uint32_t>(a) | static_cast<uint32_t>(b));
}

inline TextureUsage operator&(TextureUsage a, TextureUsage b) {
    return static_cast<TextureUsage>(static_cast<uint32_t>(a) & static_cast<uint32_t>(b));
}

inline ShaderStage operator|(ShaderStage a, ShaderStage b) {
    return static_cast<ShaderStage>(static_cast<uint32_t>(a) | static_cast<uint32_t>(b));
}

inline ShaderStage operator&(ShaderStage a, ShaderStage b) {
    return static_cast<ShaderStage>(static_cast<uint32_t>(a) & static_cast<uint32_t>(b));
}

inline MemoryProperty operator|(MemoryProperty a, MemoryProperty b) {
    return static_cast<MemoryProperty>(static_cast<uint32_t>(a) | static_cast<uint32_t>(b));
}

inline MemoryProperty operator&(MemoryProperty a, MemoryProperty b) {
    return static_cast<MemoryProperty>(static_cast<uint32_t>(a) & static_cast<uint32_t>(b));
}

// 辅助函数
const char* backendTypeToString(BackendType type);
const char* textureFormatToString(TextureFormat format);
uint32_t getTextureFormatBytesPerPixel(TextureFormat format);
bool isDepthFormat(TextureFormat format);
bool isStencilFormat(TextureFormat format);
bool isCompressedFormat(TextureFormat format);

} // namespace USG
