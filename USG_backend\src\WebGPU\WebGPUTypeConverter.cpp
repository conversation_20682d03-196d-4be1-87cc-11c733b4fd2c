#include "WebGPUBackend.h"
#include <iostream>

namespace USG {

// 缓冲区用途转换
WGPUBufferUsage WebGPUBackend::convertBufferUsage(BufferUsage usage) {
    WGPUBufferUsage wgpuUsage = WGPUBufferUsage_None;
    
    if (usage & BufferUsage::Vertex) {
        wgpuUsage |= WGPUBufferUsage_Vertex;
    }
    if (usage & BufferUsage::Index) {
        wgpuUsage |= WGPUBufferUsage_Index;
    }
    if (usage & BufferUsage::Uniform) {
        wgpuUsage |= WGPUBufferUsage_Uniform;
    }
    if (usage & BufferUsage::Storage) {
        wgpuUsage |= WGPUBufferUsage_Storage;
    }
    if (usage & BufferUsage::Indirect) {
        wgpuUsage |= WGPUBufferUsage_Indirect;
    }
    if (usage & BufferUsage::TransferSrc) {
        wgpuUsage |= WGPUBufferUsage_CopySrc;
    }
    if (usage & BufferUsage::TransferDst) {
        wgpuUsage |= WGPUBufferUsage_CopyDst;
    }
    if (usage & BufferUsage::QueryBuffer) {
        wgpuUsage |= WGPUBufferUsage_QueryResolve;
    }
    
    return wgpuUsage;
}

// 纹理用途转换
WGPUTextureUsage WebGPUBackend::convertTextureUsage(TextureUsage usage) {
    WGPUTextureUsage wgpuUsage = WGPUTextureUsage_None;
    
    if (usage & TextureUsage::Sampled) {
        wgpuUsage |= WGPUTextureUsage_TextureBinding;
    }
    if (usage & TextureUsage::Storage) {
        wgpuUsage |= WGPUTextureUsage_StorageBinding;
    }
    if (usage & TextureUsage::RenderTarget) {
        wgpuUsage |= WGPUTextureUsage_RenderAttachment;
    }
    if (usage & TextureUsage::DepthStencil) {
        wgpuUsage |= WGPUTextureUsage_RenderAttachment;
    }
    if (usage & TextureUsage::TransferSrc) {
        wgpuUsage |= WGPUTextureUsage_CopySrc;
    }
    if (usage & TextureUsage::TransferDst) {
        wgpuUsage |= WGPUTextureUsage_CopyDst;
    }
    
    return wgpuUsage;
}

// 纹理格式转换
WGPUTextureFormat WebGPUBackend::convertTextureFormat(TextureFormat format) {
    switch (format) {
        // 8位格式
        case TextureFormat::R8_UNorm: return WGPUTextureFormat_R8Unorm;
        case TextureFormat::R8_SNorm: return WGPUTextureFormat_R8Snorm;
        case TextureFormat::R8_UInt: return WGPUTextureFormat_R8Uint;
        case TextureFormat::R8_SInt: return WGPUTextureFormat_R8Sint;
        
        // 16位格式
        case TextureFormat::R16_UNorm: return WGPUTextureFormat_R16Unorm;
        case TextureFormat::R16_SNorm: return WGPUTextureFormat_R16Snorm;
        case TextureFormat::R16_UInt: return WGPUTextureFormat_R16Uint;
        case TextureFormat::R16_SInt: return WGPUTextureFormat_R16Sint;
        case TextureFormat::R16_Float: return WGPUTextureFormat_R16Float;
        case TextureFormat::RG8_UNorm: return WGPUTextureFormat_RG8Unorm;
        case TextureFormat::RG8_SNorm: return WGPUTextureFormat_RG8Snorm;
        case TextureFormat::RG8_UInt: return WGPUTextureFormat_RG8Uint;
        case TextureFormat::RG8_SInt: return WGPUTextureFormat_RG8Sint;
        
        // 32位格式
        case TextureFormat::R32_UInt: return WGPUTextureFormat_R32Uint;
        case TextureFormat::R32_SInt: return WGPUTextureFormat_R32Sint;
        case TextureFormat::R32_Float: return WGPUTextureFormat_R32Float;
        case TextureFormat::RG16_UNorm: return WGPUTextureFormat_RG16Unorm;
        case TextureFormat::RG16_SNorm: return WGPUTextureFormat_RG16Snorm;
        case TextureFormat::RG16_UInt: return WGPUTextureFormat_RG16Uint;
        case TextureFormat::RG16_SInt: return WGPUTextureFormat_RG16Sint;
        case TextureFormat::RG16_Float: return WGPUTextureFormat_RG16Float;
        case TextureFormat::RGBA8_UNorm: return WGPUTextureFormat_RGBA8Unorm;
        case TextureFormat::RGBA8_UNorm_sRGB: return WGPUTextureFormat_RGBA8UnormSrgb;
        case TextureFormat::RGBA8_SNorm: return WGPUTextureFormat_RGBA8Snorm;
        case TextureFormat::RGBA8_UInt: return WGPUTextureFormat_RGBA8Uint;
        case TextureFormat::RGBA8_SInt: return WGPUTextureFormat_RGBA8Sint;
        case TextureFormat::BGRA8_UNorm: return WGPUTextureFormat_BGRA8Unorm;
        case TextureFormat::BGRA8_UNorm_sRGB: return WGPUTextureFormat_BGRA8UnormSrgb;
        
        // 64位格式
        case TextureFormat::RG32_UInt: return WGPUTextureFormat_RG32Uint;
        case TextureFormat::RG32_SInt: return WGPUTextureFormat_RG32Sint;
        case TextureFormat::RG32_Float: return WGPUTextureFormat_RG32Float;
        case TextureFormat::RGBA16_UNorm: return WGPUTextureFormat_RGBA16Unorm;
        case TextureFormat::RGBA16_SNorm: return WGPUTextureFormat_RGBA16Snorm;
        case TextureFormat::RGBA16_UInt: return WGPUTextureFormat_RGBA16Uint;
        case TextureFormat::RGBA16_SInt: return WGPUTextureFormat_RGBA16Sint;
        case TextureFormat::RGBA16_Float: return WGPUTextureFormat_RGBA16Float;
        
        // 128位格式
        case TextureFormat::RGBA32_UInt: return WGPUTextureFormat_RGBA32Uint;
        case TextureFormat::RGBA32_SInt: return WGPUTextureFormat_RGBA32Sint;
        case TextureFormat::RGBA32_Float: return WGPUTextureFormat_RGBA32Float;
        
        // 深度/模板格式
        case TextureFormat::Depth16_UNorm: return WGPUTextureFormat_Depth16Unorm;
        case TextureFormat::Depth24_UNorm_Stencil8_UInt: return WGPUTextureFormat_Depth24PlusStencil8;
        case TextureFormat::Depth32_Float: return WGPUTextureFormat_Depth32Float;
        case TextureFormat::Depth32_Float_Stencil8_UInt: return WGPUTextureFormat_Depth32FloatStencil8;
        
        // 压缩格式
        case TextureFormat::BC1_RGBA_UNorm: return WGPUTextureFormat_BC1RGBAUnorm;
        case TextureFormat::BC1_RGBA_UNorm_sRGB: return WGPUTextureFormat_BC1RGBAUnormSrgb;
        case TextureFormat::BC2_RGBA_UNorm: return WGPUTextureFormat_BC2RGBAUnorm;
        case TextureFormat::BC2_RGBA_UNorm_sRGB: return WGPUTextureFormat_BC2RGBAUnormSrgb;
        case TextureFormat::BC3_RGBA_UNorm: return WGPUTextureFormat_BC3RGBAUnorm;
        case TextureFormat::BC3_RGBA_UNorm_sRGB: return WGPUTextureFormat_BC3RGBAUnormSrgb;
        case TextureFormat::BC4_R_UNorm: return WGPUTextureFormat_BC4RUnorm;
        case TextureFormat::BC4_R_SNorm: return WGPUTextureFormat_BC4RSnorm;
        case TextureFormat::BC5_RG_UNorm: return WGPUTextureFormat_BC5RGUnorm;
        case TextureFormat::BC5_RG_SNorm: return WGPUTextureFormat_BC5RGSnorm;
        case TextureFormat::BC6H_RGB_UFloat: return WGPUTextureFormat_BC6HRGBUfloat;
        case TextureFormat::BC6H_RGB_SFloat: return WGPUTextureFormat_BC6HRGBFloat;
        case TextureFormat::BC7_RGBA_UNorm: return WGPUTextureFormat_BC7RGBAUnorm;
        case TextureFormat::BC7_RGBA_UNorm_sRGB: return WGPUTextureFormat_BC7RGBAUnormSrgb;
        
        default:
            std::cerr << "[WebGPUBackend] Unsupported texture format: " << static_cast<int>(format) << std::endl;
            return WGPUTextureFormat_RGBA8Unorm; // 默认格式
    }
}

// 索引格式转换
WGPUIndexFormat WebGPUBackend::convertIndexFormat(IndexFormat format) {
    switch (format) {
        case IndexFormat::UInt16: return WGPUIndexFormat_Uint16;
        case IndexFormat::UInt32: return WGPUIndexFormat_Uint32;
        default:
            std::cerr << "[WebGPUBackend] Unsupported index format: " << static_cast<int>(format) << std::endl;
            return WGPUIndexFormat_Uint16;
    }
}

// 图元拓扑转换
WGPUPrimitiveTopology WebGPUBackend::convertPrimitiveTopology(PrimitiveTopology topology) {
    switch (topology) {
        case PrimitiveTopology::PointList: return WGPUPrimitiveTopology_PointList;
        case PrimitiveTopology::LineList: return WGPUPrimitiveTopology_LineList;
        case PrimitiveTopology::LineStrip: return WGPUPrimitiveTopology_LineStrip;
        case PrimitiveTopology::TriangleList: return WGPUPrimitiveTopology_TriangleList;
        case PrimitiveTopology::TriangleStrip: return WGPUPrimitiveTopology_TriangleStrip;
        default:
            std::cerr << "[WebGPUBackend] Unsupported primitive topology: " << static_cast<int>(topology) << std::endl;
            return WGPUPrimitiveTopology_TriangleList;
    }
}

// 比较函数转换
WGPUCompareFunction WebGPUBackend::convertCompareFunction(CompareFunction func) {
    switch (func) {
        case CompareFunction::Never: return WGPUCompareFunction_Never;
        case CompareFunction::Less: return WGPUCompareFunction_Less;
        case CompareFunction::Equal: return WGPUCompareFunction_Equal;
        case CompareFunction::LessEqual: return WGPUCompareFunction_LessEqual;
        case CompareFunction::Greater: return WGPUCompareFunction_Greater;
        case CompareFunction::NotEqual: return WGPUCompareFunction_NotEqual;
        case CompareFunction::GreaterEqual: return WGPUCompareFunction_GreaterEqual;
        case CompareFunction::Always: return WGPUCompareFunction_Always;
        default:
            std::cerr << "[WebGPUBackend] Unsupported compare function: " << static_cast<int>(func) << std::endl;
            return WGPUCompareFunction_Always;
    }
}

// 混合操作转换
WGPUBlendOperation WebGPUBackend::convertBlendOperation(BlendOperation op) {
    switch (op) {
        case BlendOperation::Add: return WGPUBlendOperation_Add;
        case BlendOperation::Subtract: return WGPUBlendOperation_Subtract;
        case BlendOperation::ReverseSubtract: return WGPUBlendOperation_ReverseSubtract;
        case BlendOperation::Min: return WGPUBlendOperation_Min;
        case BlendOperation::Max: return WGPUBlendOperation_Max;
        default:
            std::cerr << "[WebGPUBackend] Unsupported blend operation: " << static_cast<int>(op) << std::endl;
            return WGPUBlendOperation_Add;
    }
}

// 混合因子转换
WGPUBlendFactor WebGPUBackend::convertBlendFactor(BlendFactor factor) {
    switch (factor) {
        case BlendFactor::Zero: return WGPUBlendFactor_Zero;
        case BlendFactor::One: return WGPUBlendFactor_One;
        case BlendFactor::Src: return WGPUBlendFactor_Src;
        case BlendFactor::OneMinusSrc: return WGPUBlendFactor_OneMinusSrc;
        case BlendFactor::SrcAlpha: return WGPUBlendFactor_SrcAlpha;
        case BlendFactor::OneMinusSrcAlpha: return WGPUBlendFactor_OneMinusSrcAlpha;
        case BlendFactor::Dst: return WGPUBlendFactor_Dst;
        case BlendFactor::OneMinusDst: return WGPUBlendFactor_OneMinusDst;
        case BlendFactor::DstAlpha: return WGPUBlendFactor_DstAlpha;
        case BlendFactor::OneMinusDstAlpha: return WGPUBlendFactor_OneMinusDstAlpha;
        case BlendFactor::SrcAlphaSaturated: return WGPUBlendFactor_SrcAlphaSaturated;
        case BlendFactor::Constant: return WGPUBlendFactor_Constant;
        case BlendFactor::OneMinusConstant: return WGPUBlendFactor_OneMinusConstant;
        default:
            std::cerr << "[WebGPUBackend] Unsupported blend factor: " << static_cast<int>(factor) << std::endl;
            return WGPUBlendFactor_One;
    }
}

// 反向转换：WebGPU到USG
BufferUsage WebGPUBackend::convertWGPUBufferUsage(WGPUBufferUsage usage) {
    BufferUsage usgUsage = BufferUsage::None;
    
    if (usage & WGPUBufferUsage_Vertex) {
        usgUsage = usgUsage | BufferUsage::Vertex;
    }
    if (usage & WGPUBufferUsage_Index) {
        usgUsage = usgUsage | BufferUsage::Index;
    }
    if (usage & WGPUBufferUsage_Uniform) {
        usgUsage = usgUsage | BufferUsage::Uniform;
    }
    if (usage & WGPUBufferUsage_Storage) {
        usgUsage = usgUsage | BufferUsage::Storage;
    }
    if (usage & WGPUBufferUsage_Indirect) {
        usgUsage = usgUsage | BufferUsage::Indirect;
    }
    if (usage & WGPUBufferUsage_CopySrc) {
        usgUsage = usgUsage | BufferUsage::TransferSrc;
    }
    if (usage & WGPUBufferUsage_CopyDst) {
        usgUsage = usgUsage | BufferUsage::TransferDst;
    }
    if (usage & WGPUBufferUsage_QueryResolve) {
        usgUsage = usgUsage | BufferUsage::QueryBuffer;
    }
    
    return usgUsage;
}

TextureUsage WebGPUBackend::convertWGPUTextureUsage(WGPUTextureUsage usage) {
    TextureUsage usgUsage = TextureUsage::None;
    
    if (usage & WGPUTextureUsage_TextureBinding) {
        usgUsage = usgUsage | TextureUsage::Sampled;
    }
    if (usage & WGPUTextureUsage_StorageBinding) {
        usgUsage = usgUsage | TextureUsage::Storage;
    }
    if (usage & WGPUTextureUsage_RenderAttachment) {
        usgUsage = usgUsage | TextureUsage::RenderTarget;
    }
    if (usage & WGPUTextureUsage_CopySrc) {
        usgUsage = usgUsage | TextureUsage::TransferSrc;
    }
    if (usage & WGPUTextureUsage_CopyDst) {
        usgUsage = usgUsage | TextureUsage::TransferDst;
    }
    
    return usgUsage;
}

TextureFormat WebGPUBackend::convertWGPUTextureFormat(WGPUTextureFormat format) {
    switch (format) {
        case WGPUTextureFormat_R8Unorm: return TextureFormat::R8_UNorm;
        case WGPUTextureFormat_R8Snorm: return TextureFormat::R8_SNorm;
        case WGPUTextureFormat_R8Uint: return TextureFormat::R8_UInt;
        case WGPUTextureFormat_R8Sint: return TextureFormat::R8_SInt;
        case WGPUTextureFormat_RGBA8Unorm: return TextureFormat::RGBA8_UNorm;
        case WGPUTextureFormat_RGBA8UnormSrgb: return TextureFormat::RGBA8_UNorm_sRGB;
        case WGPUTextureFormat_BGRA8Unorm: return TextureFormat::BGRA8_UNorm;
        case WGPUTextureFormat_BGRA8UnormSrgb: return TextureFormat::BGRA8_UNorm_sRGB;
        case WGPUTextureFormat_Depth16Unorm: return TextureFormat::Depth16_UNorm;
        case WGPUTextureFormat_Depth24PlusStencil8: return TextureFormat::Depth24_UNorm_Stencil8_UInt;
        case WGPUTextureFormat_Depth32Float: return TextureFormat::Depth32_Float;
        case WGPUTextureFormat_Depth32FloatStencil8: return TextureFormat::Depth32_Float_Stencil8_UInt;
        // 添加更多格式映射...
        default:
            std::cerr << "[WebGPUBackend] Unsupported WebGPU texture format: " << static_cast<int>(format) << std::endl;
            return TextureFormat::RGBA8_UNorm;
    }
}

} // namespace USG
