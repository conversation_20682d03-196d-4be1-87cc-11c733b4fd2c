# VSG Vulkan接口分析

## 目标
分析VSG库中调用Vulkan API的关键位置，为WebGPU后端替换做准备。

## VSG核心渲染接口分析

### 1. 设备和上下文管理
```cpp
// VSG中的Vulkan设备管理
namespace vsg {
    class Device : public Object {
        VkDevice _device;
        VkPhysicalDevice _physicalDevice;
        VkInstance _instance;
        
        // 关键接口 - 需要WebGPU替换
        VkResult createBuffer(const VkBufferCreateInfo* pCreateInfo, VkBuffer* pBuffer);
        VkResult createImage(const VkImageCreateInfo* pCreateInfo, VkImage* pImage);
        VkResult createRenderPass(const VkRenderPassCreateInfo* pCreateInfo, VkRenderPass* pRenderPass);
        VkResult createGraphicsPipeline(const VkGraphicsPipelineCreateInfo* pCreateInfo, VkPipeline* pPipeline);
    };
}

// 跟踪点1: 设备创建和管理
// 在VSG源码中添加调试输出
void Device::create() {
    std::cout << "[VSG_TRACE] Device::create() - Vulkan device creation" << std::endl;
    // 原有Vulkan代码...
}
```

### 2. 命令缓冲区和渲染通道
```cpp
// VSG中的命令记录
namespace vsg {
    class CommandBuffer : public Object {
        VkCommandBuffer _commandBuffer;
        
        // 关键接口 - 需要WebGPU替换
        void begin(VkCommandBufferBeginInfo* beginInfo);
        void beginRenderPass(VkRenderPassBeginInfo* renderPassBegin);
        void bindPipeline(VkPipelineBindPoint pipelineBindPoint, VkPipeline pipeline);
        void bindDescriptorSets(VkPipelineBindPoint pipelineBindPoint, VkPipelineLayout layout, 
                               uint32_t firstSet, uint32_t descriptorSetCount, 
                               const VkDescriptorSet* pDescriptorSets);
        void draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance);
        void endRenderPass();
        void end();
    };
}

// 跟踪点2: 命令记录
// 在VSG源码中添加调试输出
void CommandBuffer::draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance) {
    std::cout << "[VSG_TRACE] CommandBuffer::draw() - vertices: " << vertexCount 
              << ", instances: " << instanceCount << std::endl;
    vkCmdDraw(_commandBuffer, vertexCount, instanceCount, firstVertex, firstInstance);
}
```

### 3. 资源管理
```cpp
// VSG中的缓冲区管理
namespace vsg {
    class Buffer : public Object {
        VkBuffer _buffer;
        VkDeviceMemory _deviceMemory;
        
        // 关键接口 - 需要WebGPU替换
        VkResult create(VkDeviceSize size, VkBufferUsageFlags usage, VkSharingMode sharingMode);
        VkResult map(VkDeviceSize offset, VkDeviceSize size, void** ppData);
        void unmap();
    };
    
    class Image : public Object {
        VkImage _image;
        VkDeviceMemory _deviceMemory;
        VkImageView _imageView;
        
        // 关键接口 - 需要WebGPU替换
        VkResult create(const VkImageCreateInfo* pCreateInfo);
        VkResult createImageView(const VkImageViewCreateInfo* pCreateInfo);
    };
}

// 跟踪点3: 资源创建
// 在VSG源码中添加调试输出
VkResult Buffer::create(VkDeviceSize size, VkBufferUsageFlags usage, VkSharingMode sharingMode) {
    std::cout << "[VSG_TRACE] Buffer::create() - size: " << size << ", usage: " << usage << std::endl;
    // 原有Vulkan代码...
}
```

### 4. 渲染管线
```cpp
// VSG中的管线管理
namespace vsg {
    class GraphicsPipeline : public Object {
        VkPipeline _pipeline;
        VkPipelineLayout _pipelineLayout;
        
        // 关键接口 - 需要WebGPU替换
        VkResult create(const VkGraphicsPipelineCreateInfo* pCreateInfo);
    };
    
    class ShaderModule : public Object {
        VkShaderModule _shaderModule;
        
        // 关键接口 - 需要WebGPU替换
        VkResult create(const VkShaderModuleCreateInfo* pCreateInfo);
    };
}

// 跟踪点4: 管线创建
// 在VSG源码中添加调试输出
VkResult GraphicsPipeline::create(const VkGraphicsPipelineCreateInfo* pCreateInfo) {
    std::cout << "[VSG_TRACE] GraphicsPipeline::create() - stages: " << pCreateInfo->stageCount << std::endl;
    // 原有Vulkan代码...
}
```

## VSG渲染流程跟踪

### 关键调用链分析
```cpp
// VSG典型渲染流程
void VSGRenderer::render() {
    // 1. 场景遍历 (保留)
    scene->accept(cullVisitor);
    
    // 2. 命令记录开始 (需要替换)
    std::cout << "[VSG_TRACE] Begin command recording" << std::endl;
    commandBuffer->begin();
    
    // 3. 渲染通道开始 (需要替换)
    std::cout << "[VSG_TRACE] Begin render pass" << std::endl;
    commandBuffer->beginRenderPass();
    
    // 4. 绘制调用 (需要替换)
    for (auto& drawable : drawables) {
        std::cout << "[VSG_TRACE] Draw call for: " << drawable->getName() << std::endl;
        commandBuffer->bindPipeline(drawable->getPipeline());
        commandBuffer->bindDescriptorSets(drawable->getDescriptorSets());
        commandBuffer->draw(drawable->getVertexCount());
    }
    
    // 5. 渲染通道结束 (需要替换)
    std::cout << "[VSG_TRACE] End render pass" << std::endl;
    commandBuffer->endRenderPass();
    
    // 6. 命令记录结束 (需要替换)
    std::cout << "[VSG_TRACE] End command recording" << std::endl;
    commandBuffer->end();
    
    // 7. 提交命令 (需要替换)
    std::cout << "[VSG_TRACE] Submit commands" << std::endl;
    queue->submit(commandBuffer);
}
```

## 需要替换的核心接口

### 1. 设备层接口
- `VkDevice` → `wgpu::Device`
- `VkQueue` → `wgpu::Queue`
- `VkInstance` → `wgpu::Instance`

### 2. 资源层接口
- `VkBuffer` → `wgpu::Buffer`
- `VkImage/VkImageView` → `wgpu::Texture/wgpu::TextureView`
- `VkSampler` → `wgpu::Sampler`

### 3. 管线层接口
- `VkPipeline` → `wgpu::RenderPipeline`
- `VkShaderModule` → `wgpu::ShaderModule`
- `VkDescriptorSet` → `wgpu::BindGroup`

### 4. 命令层接口
- `VkCommandBuffer` → `wgpu::CommandEncoder`
- `VkRenderPass` → `wgpu::RenderPassEncoder`

## 跟踪方法

### 1. 编译时跟踪
```cpp
// 在VSG源码中添加宏定义
#define VSG_TRACE_VULKAN_CALLS 1

#ifdef VSG_TRACE_VULKAN_CALLS
#define VSG_VULKAN_CALL(func, ...) \
    do { \
        std::cout << "[VSG_VULKAN] Calling: " << #func << std::endl; \
        auto result = func(__VA_ARGS__); \
        std::cout << "[VSG_VULKAN] Result: " << result << std::endl; \
        return result; \
    } while(0)
#else
#define VSG_VULKAN_CALL(func, ...) func(__VA_ARGS__)
#endif
```

### 2. 运行时跟踪
```cpp
// 在关键函数中添加日志输出
class VulkanTracer {
public:
    static void logCall(const std::string& function, const std::string& details = "") {
        std::cout << "[VSG_TRACE] " << function;
        if (!details.empty()) {
            std::cout << " - " << details;
        }
        std::cout << std::endl;
    }
};

// 使用示例
void Device::createBuffer(...) {
    VulkanTracer::logCall("Device::createBuffer", "size: " + std::to_string(size));
    // 原有代码...
}
```

## 下一步行动

1. **在VSG源码中添加跟踪代码**
2. **运行典型VSG应用，收集调用日志**
3. **分析调用模式和频率**
4. **设计WebGPU替换接口**
5. **实现渐进式替换**
