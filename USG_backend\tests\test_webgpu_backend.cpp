/**
 * @file test_webgpu_backend.cpp
 * @brief WebGPU后端基础测试
 */

#include <USG_Backend/BackendFactory.h>
#include <USG_Backend/RenderBackend.h>
#include <VSG_Adapter/VSG_Device.h>
#include <iostream>
#include <cassert>

using namespace USG;

void testBackendFactory() {
    std::cout << "=== 测试后端工厂 ===" << std::endl;
    
    auto& factory = BackendFactory::getInstance();
    
    // 检查WebGPU后端是否可用
    bool webgpuAvailable = factory.isBackendAvailable(BackendType::WebGPU);
    std::cout << "WebGPU后端可用: " << (webgpuAvailable ? "是" : "否") << std::endl;
    
    // 获取所有可用后端
    auto availableBackends = factory.getAvailableBackends();
    std::cout << "可用后端数量: " << availableBackends.size() << std::endl;
    
    for (auto backend : availableBackends) {
        std::cout << "  - 后端类型: " << static_cast<int>(backend) << std::endl;
    }
    
    std::cout << "后端工厂测试完成" << std::endl << std::endl;
}

void testWebGPUBackend() {
    std::cout << "=== 测试WebGPU后端 ===" << std::endl;
    
    // 创建WebGPU后端
    auto& factory = BackendFactory::getInstance();
    auto backend = factory.createBackend(BackendType::WebGPU);
    
    if (!backend) {
        std::cerr << "无法创建WebGPU后端" << std::endl;
        return;
    }
    
    std::cout << "后端名称: " << backend->getBackendName() << std::endl;
    std::cout << "后端类型: " << static_cast<int>(backend->getBackendType()) << std::endl;
    
    // 初始化后端
    BackendConfig config;
    config.enableValidation = true;
    config.enableDebugMarkers = true;
    config.applicationName = "USG Backend Test";
    
    bool initialized = backend->initialize(config);
    std::cout << "后端初始化: " << (initialized ? "成功" : "失败") << std::endl;
    
    if (initialized) {
        // 测试设备获取
        auto* device = backend->getDevice();
        if (device) {
            std::cout << "设备名称: " << device->getDeviceName() << std::endl;
            
            auto capabilities = device->getCapabilities();
            std::cout << "设备能力:" << std::endl;
            std::cout << "  最大2D纹理尺寸: " << capabilities.maxTextureSize2D << std::endl;
            std::cout << "  支持计算着色器: " << (capabilities.supportsComputeShaders ? "是" : "否") << std::endl;
        }
        
        // 测试缓冲区创建
        BufferDesc bufferDesc;
        bufferDesc.size = 1024;
        bufferDesc.usage = BufferUsage::Vertex | BufferUsage::TransferDst;
        bufferDesc.label = "Test Buffer";
        
        auto* buffer = backend->createBuffer(bufferDesc);
        if (buffer) {
            std::cout << "缓冲区创建成功, 大小: " << buffer->getSize() << " 字节" << std::endl;
            backend->destroyBuffer(buffer);
            std::cout << "缓冲区销毁成功" << std::endl;
        } else {
            std::cerr << "缓冲区创建失败" << std::endl;
        }
        
        // 测试纹理创建
        TextureDesc textureDesc;
        textureDesc.width = 256;
        textureDesc.height = 256;
        textureDesc.format = TextureFormat::RGBA8_UNorm;
        textureDesc.usage = TextureUsage::Sampled | TextureUsage::TransferDst;
        textureDesc.label = "Test Texture";
        
        auto* texture = backend->createTexture(textureDesc);
        if (texture) {
            std::cout << "纹理创建成功, 尺寸: " << texture->getWidth() << "x" << texture->getHeight() << std::endl;
            backend->destroyTexture(texture);
            std::cout << "纹理销毁成功" << std::endl;
        } else {
            std::cerr << "纹理创建失败" << std::endl;
        }
        
        // 清理后端
        backend->cleanup();
        std::cout << "后端清理完成" << std::endl;
    }
    
    std::cout << "WebGPU后端测试完成" << std::endl << std::endl;
}

void testVSGAdapter() {
    std::cout << "=== 测试VSG适配器 ===" << std::endl;
    
    // 创建VSG设备适配器
    VSG_Device vsgDevice(BackendType::WebGPU);
    
    // 初始化
    BackendConfig config;
    config.enableValidation = true;
    config.applicationName = "VSG Adapter Test";
    
    bool initialized = vsgDevice.initialize(config);
    std::cout << "VSG设备初始化: " << (initialized ? "成功" : "失败") << std::endl;
    
    if (initialized) {
        std::cout << "后端类型: " << static_cast<int>(vsgDevice.getBackendType()) << std::endl;
        
        // 测试缓冲区创建（Vulkan API）
        VkBufferCreateInfo bufferCreateInfo = {};
        bufferCreateInfo.sType = 12; // VK_STRUCTURE_TYPE_BUFFER_CREATE_INFO
        bufferCreateInfo.size = 2048;
        bufferCreateInfo.usage = VK_BUFFER_USAGE_VERTEX_BUFFER_BIT | VK_BUFFER_USAGE_TRANSFER_DST_BIT;
        bufferCreateInfo.sharingMode = VK_SHARING_MODE_EXCLUSIVE;
        
        VkBuffer buffer;
        VkResult result = vsgDevice.createBuffer(&bufferCreateInfo, &buffer);
        std::cout << "Vulkan缓冲区创建: " << (result == VK_SUCCESS ? "成功" : "失败") << std::endl;
        
        if (result == VK_SUCCESS) {
            std::cout << "缓冲区句柄: " << buffer << std::endl;
            vsgDevice.destroyBuffer(buffer);
            std::cout << "缓冲区销毁完成" << std::endl;
        }
        
        // 测试图像创建（Vulkan API）
        VkImageCreateInfo imageCreateInfo = {};
        imageCreateInfo.sType = 14; // VK_STRUCTURE_TYPE_IMAGE_CREATE_INFO
        imageCreateInfo.imageType = 1; // VK_IMAGE_TYPE_2D
        imageCreateInfo.format = 37; // VK_FORMAT_R8G8B8A8_UNORM
        imageCreateInfo.extent.width = 512;
        imageCreateInfo.extent.height = 512;
        imageCreateInfo.extent.depth = 1;
        imageCreateInfo.mipLevels = 1;
        imageCreateInfo.arrayLayers = 1;
        imageCreateInfo.samples = 1; // VK_SAMPLE_COUNT_1_BIT
        imageCreateInfo.tiling = 1; // VK_IMAGE_TILING_OPTIMAL
        imageCreateInfo.usage = VK_IMAGE_USAGE_SAMPLED_BIT | VK_IMAGE_USAGE_TRANSFER_DST_BIT;
        imageCreateInfo.sharingMode = VK_SHARING_MODE_EXCLUSIVE;
        imageCreateInfo.initialLayout = 0; // VK_IMAGE_LAYOUT_UNDEFINED
        
        VkImage image;
        result = vsgDevice.createImage(&imageCreateInfo, &image);
        std::cout << "Vulkan图像创建: " << (result == VK_SUCCESS ? "成功" : "失败") << std::endl;
        
        if (result == VK_SUCCESS) {
            std::cout << "图像句柄: " << image << std::endl;
            vsgDevice.destroyImage(image);
            std::cout << "图像销毁完成" << std::endl;
        }
        
        // 测试内存分配（Vulkan API）
        VkMemoryAllocateInfo allocInfo = {};
        allocInfo.sType = 5; // VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_INFO
        allocInfo.allocationSize = 4096;
        allocInfo.memoryTypeIndex = 0;
        
        VkDeviceMemory memory;
        result = vsgDevice.allocateMemory(&allocInfo, &memory);
        std::cout << "Vulkan内存分配: " << (result == VK_SUCCESS ? "成功" : "失败") << std::endl;
        
        if (result == VK_SUCCESS) {
            std::cout << "内存句柄: " << memory << std::endl;
            vsgDevice.freeMemory(memory);
            std::cout << "内存释放完成" << std::endl;
        }
        
        // 测试设备等待空闲
        result = vsgDevice.deviceWaitIdle();
        std::cout << "设备等待空闲: " << (result == VK_SUCCESS ? "成功" : "失败") << std::endl;
        
        // 清理
        vsgDevice.cleanup();
        std::cout << "VSG设备清理完成" << std::endl;
    }
    
    std::cout << "VSG适配器测试完成" << std::endl << std::endl;
}

void testBackendManager() {
    std::cout << "=== 测试后端管理器 ===" << std::endl;
    
    auto& manager = BackendManager::getInstance();
    
    // 初始化管理器
    bool initialized = manager.initialize(BackendType::WebGPU);
    std::cout << "后端管理器初始化: " << (initialized ? "成功" : "失败") << std::endl;
    
    if (initialized) {
        std::cout << "当前后端类型: " << static_cast<int>(manager.getCurrentBackendType()) << std::endl;
        std::cout << "当前后端名称: " << manager.getCurrentBackendName() << std::endl;
        
        auto* backend = manager.getCurrentBackend();
        if (backend) {
            std::cout << "获取当前后端成功" << std::endl;
        }
        
        // 清理管理器
        manager.cleanup();
        std::cout << "后端管理器清理完成" << std::endl;
    }
    
    std::cout << "后端管理器测试完成" << std::endl << std::endl;
}

int main() {
    std::cout << "开始USG Backend测试..." << std::endl << std::endl;
    
    try {
        // 运行各项测试
        testBackendFactory();
        testWebGPUBackend();
        testVSGAdapter();
        testBackendManager();
        
        std::cout << "所有测试完成!" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "测试异常: " << e.what() << std::endl;
        return -1;
    } catch (...) {
        std::cerr << "未知测试异常" << std::endl;
        return -1;
    }
}

/**
 * 编译和运行说明:
 * 
 * 1. 确保已安装wgpu-native库
 * 2. 编译:
 *    mkdir build && cd build
 *    cmake .. -DUSG_BUILD_WEBGPU_BACKEND=ON -DUSG_BUILD_VSG_ADAPTER=ON -DUSG_BUILD_TESTS=ON
 *    make -j8
 * 
 * 3. 运行测试:
 *    ./tests/test_webgpu_backend
 * 
 * 4. 预期输出:
 *    - 后端工厂测试通过
 *    - WebGPU后端初始化成功
 *    - 缓冲区和纹理创建成功
 *    - VSG适配器Vulkan API调用成功
 *    - 后端管理器工作正常
 */
