#include <USG_Backend/BackendTypes.h>
#include <cmath>

namespace USG {

const char* backendTypeToString(BackendType type) {
    switch (type) {
        case BackendType::Unknown: return "Unknown";
        case BackendType::WebGPU: return "WebGPU";
        case BackendType::Vulkan: return "Vulkan";
        case BackendType::OpenGL: return "OpenGL";
        case BackendType::DirectX12: return "DirectX12";
        case BackendType::Metal: return "Metal";
        default: return "Invalid";
    }
}

const char* textureFormatToString(TextureFormat format) {
    switch (format) {
        case TextureFormat::Unknown: return "Unknown";
        
        // 8位格式
        case TextureFormat::R8_UNorm: return "R8_UNorm";
        case TextureFormat::R8_SNorm: return "R8_SNorm";
        case TextureFormat::R8_UInt: return "R8_UInt";
        case TextureFormat::R8_SInt: return "R8_SInt";
        
        // 16位格式
        case TextureFormat::R16_UNorm: return "R16_UNorm";
        case TextureFormat::R16_SNorm: return "R16_SNorm";
        case TextureFormat::R16_UInt: return "R16_UInt";
        case TextureFormat::R16_SInt: return "R16_SInt";
        case TextureFormat::R16_Float: return "R16_Float";
        case TextureFormat::RG8_UNorm: return "RG8_UNorm";
        case TextureFormat::RG8_SNorm: return "RG8_SNorm";
        case TextureFormat::RG8_UInt: return "RG8_UInt";
        case TextureFormat::RG8_SInt: return "RG8_SInt";
        
        // 32位格式
        case TextureFormat::R32_UInt: return "R32_UInt";
        case TextureFormat::R32_SInt: return "R32_SInt";
        case TextureFormat::R32_Float: return "R32_Float";
        case TextureFormat::RG16_UNorm: return "RG16_UNorm";
        case TextureFormat::RG16_SNorm: return "RG16_SNorm";
        case TextureFormat::RG16_UInt: return "RG16_UInt";
        case TextureFormat::RG16_SInt: return "RG16_SInt";
        case TextureFormat::RG16_Float: return "RG16_Float";
        case TextureFormat::RGBA8_UNorm: return "RGBA8_UNorm";
        case TextureFormat::RGBA8_UNorm_sRGB: return "RGBA8_UNorm_sRGB";
        case TextureFormat::RGBA8_SNorm: return "RGBA8_SNorm";
        case TextureFormat::RGBA8_UInt: return "RGBA8_UInt";
        case TextureFormat::RGBA8_SInt: return "RGBA8_SInt";
        case TextureFormat::BGRA8_UNorm: return "BGRA8_UNorm";
        case TextureFormat::BGRA8_UNorm_sRGB: return "BGRA8_UNorm_sRGB";
        
        // 64位格式
        case TextureFormat::RG32_UInt: return "RG32_UInt";
        case TextureFormat::RG32_SInt: return "RG32_SInt";
        case TextureFormat::RG32_Float: return "RG32_Float";
        case TextureFormat::RGBA16_UNorm: return "RGBA16_UNorm";
        case TextureFormat::RGBA16_SNorm: return "RGBA16_SNorm";
        case TextureFormat::RGBA16_UInt: return "RGBA16_UInt";
        case TextureFormat::RGBA16_SInt: return "RGBA16_SInt";
        case TextureFormat::RGBA16_Float: return "RGBA16_Float";
        
        // 128位格式
        case TextureFormat::RGBA32_UInt: return "RGBA32_UInt";
        case TextureFormat::RGBA32_SInt: return "RGBA32_SInt";
        case TextureFormat::RGBA32_Float: return "RGBA32_Float";
        
        // 深度/模板格式
        case TextureFormat::Depth16_UNorm: return "Depth16_UNorm";
        case TextureFormat::Depth24_UNorm_Stencil8_UInt: return "Depth24_UNorm_Stencil8_UInt";
        case TextureFormat::Depth32_Float: return "Depth32_Float";
        case TextureFormat::Depth32_Float_Stencil8_UInt: return "Depth32_Float_Stencil8_UInt";
        
        // 压缩格式
        case TextureFormat::BC1_RGBA_UNorm: return "BC1_RGBA_UNorm";
        case TextureFormat::BC1_RGBA_UNorm_sRGB: return "BC1_RGBA_UNorm_sRGB";
        case TextureFormat::BC2_RGBA_UNorm: return "BC2_RGBA_UNorm";
        case TextureFormat::BC2_RGBA_UNorm_sRGB: return "BC2_RGBA_UNorm_sRGB";
        case TextureFormat::BC3_RGBA_UNorm: return "BC3_RGBA_UNorm";
        case TextureFormat::BC3_RGBA_UNorm_sRGB: return "BC3_RGBA_UNorm_sRGB";
        case TextureFormat::BC4_R_UNorm: return "BC4_R_UNorm";
        case TextureFormat::BC4_R_SNorm: return "BC4_R_SNorm";
        case TextureFormat::BC5_RG_UNorm: return "BC5_RG_UNorm";
        case TextureFormat::BC5_RG_SNorm: return "BC5_RG_SNorm";
        case TextureFormat::BC6H_RGB_UFloat: return "BC6H_RGB_UFloat";
        case TextureFormat::BC6H_RGB_SFloat: return "BC6H_RGB_SFloat";
        case TextureFormat::BC7_RGBA_UNorm: return "BC7_RGBA_UNorm";
        case TextureFormat::BC7_RGBA_UNorm_sRGB: return "BC7_RGBA_UNorm_sRGB";
        
        default: return "Invalid";
    }
}

uint32_t getTextureFormatBytesPerPixel(TextureFormat format) {
    switch (format) {
        // 8位格式 (1字节)
        case TextureFormat::R8_UNorm:
        case TextureFormat::R8_SNorm:
        case TextureFormat::R8_UInt:
        case TextureFormat::R8_SInt:
            return 1;
        
        // 16位格式 (2字节)
        case TextureFormat::R16_UNorm:
        case TextureFormat::R16_SNorm:
        case TextureFormat::R16_UInt:
        case TextureFormat::R16_SInt:
        case TextureFormat::R16_Float:
        case TextureFormat::RG8_UNorm:
        case TextureFormat::RG8_SNorm:
        case TextureFormat::RG8_UInt:
        case TextureFormat::RG8_SInt:
        case TextureFormat::Depth16_UNorm:
            return 2;
        
        // 32位格式 (4字节)
        case TextureFormat::R32_UInt:
        case TextureFormat::R32_SInt:
        case TextureFormat::R32_Float:
        case TextureFormat::RG16_UNorm:
        case TextureFormat::RG16_SNorm:
        case TextureFormat::RG16_UInt:
        case TextureFormat::RG16_SInt:
        case TextureFormat::RG16_Float:
        case TextureFormat::RGBA8_UNorm:
        case TextureFormat::RGBA8_UNorm_sRGB:
        case TextureFormat::RGBA8_SNorm:
        case TextureFormat::RGBA8_UInt:
        case TextureFormat::RGBA8_SInt:
        case TextureFormat::BGRA8_UNorm:
        case TextureFormat::BGRA8_UNorm_sRGB:
        case TextureFormat::Depth24_UNorm_Stencil8_UInt:
        case TextureFormat::Depth32_Float:
            return 4;
        
        // 64位格式 (8字节)
        case TextureFormat::RG32_UInt:
        case TextureFormat::RG32_SInt:
        case TextureFormat::RG32_Float:
        case TextureFormat::RGBA16_UNorm:
        case TextureFormat::RGBA16_SNorm:
        case TextureFormat::RGBA16_UInt:
        case TextureFormat::RGBA16_SInt:
        case TextureFormat::RGBA16_Float:
        case TextureFormat::Depth32_Float_Stencil8_UInt:
            return 8;
        
        // 128位格式 (16字节)
        case TextureFormat::RGBA32_UInt:
        case TextureFormat::RGBA32_SInt:
        case TextureFormat::RGBA32_Float:
            return 16;
        
        // 压缩格式 (变长，返回块大小)
        case TextureFormat::BC1_RGBA_UNorm:
        case TextureFormat::BC1_RGBA_UNorm_sRGB:
        case TextureFormat::BC4_R_UNorm:
        case TextureFormat::BC4_R_SNorm:
            return 8; // 4x4块，8字节
        
        case TextureFormat::BC2_RGBA_UNorm:
        case TextureFormat::BC2_RGBA_UNorm_sRGB:
        case TextureFormat::BC3_RGBA_UNorm:
        case TextureFormat::BC3_RGBA_UNorm_sRGB:
        case TextureFormat::BC5_RG_UNorm:
        case TextureFormat::BC5_RG_SNorm:
        case TextureFormat::BC6H_RGB_UFloat:
        case TextureFormat::BC6H_RGB_SFloat:
        case TextureFormat::BC7_RGBA_UNorm:
        case TextureFormat::BC7_RGBA_UNorm_sRGB:
            return 16; // 4x4块，16字节
        
        default:
            return 4; // 默认4字节
    }
}

bool isDepthFormat(TextureFormat format) {
    switch (format) {
        case TextureFormat::Depth16_UNorm:
        case TextureFormat::Depth24_UNorm_Stencil8_UInt:
        case TextureFormat::Depth32_Float:
        case TextureFormat::Depth32_Float_Stencil8_UInt:
            return true;
        default:
            return false;
    }
}

bool isStencilFormat(TextureFormat format) {
    switch (format) {
        case TextureFormat::Depth24_UNorm_Stencil8_UInt:
        case TextureFormat::Depth32_Float_Stencil8_UInt:
            return true;
        default:
            return false;
    }
}

bool isCompressedFormat(TextureFormat format) {
    switch (format) {
        case TextureFormat::BC1_RGBA_UNorm:
        case TextureFormat::BC1_RGBA_UNorm_sRGB:
        case TextureFormat::BC2_RGBA_UNorm:
        case TextureFormat::BC2_RGBA_UNorm_sRGB:
        case TextureFormat::BC3_RGBA_UNorm:
        case TextureFormat::BC3_RGBA_UNorm_sRGB:
        case TextureFormat::BC4_R_UNorm:
        case TextureFormat::BC4_R_SNorm:
        case TextureFormat::BC5_RG_UNorm:
        case TextureFormat::BC5_RG_SNorm:
        case TextureFormat::BC6H_RGB_UFloat:
        case TextureFormat::BC6H_RGB_SFloat:
        case TextureFormat::BC7_RGBA_UNorm:
        case TextureFormat::BC7_RGBA_UNorm_sRGB:
            return true;
        default:
            return false;
    }
}

} // namespace USG
