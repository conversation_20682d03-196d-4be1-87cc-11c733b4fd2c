#include "OpenGLBackend.h"
#include <iostream>
#include <cassert>
#include <cmath>

// 简化的OpenGL后端 - 主要用于演示后端切换功能
// 在实际项目中应该使用GLAD或类似的OpenGL加载器

namespace USG
{

    OpenGLBackend::OpenGLBackend()
    {
    }

    OpenGLBackend::~OpenGLBackend()
    {
        cleanup();
    }

    bool OpenGLBackend::initialize(const BackendConfig &config)
    {
        if (_initialized)
        {
            return true;
        }

        _config = config;

        std::cout << "[OpenGLBackend] Initializing OpenGL backend..." << std::endl;

        // 获取OpenGL版本信息
        const char *version = reinterpret_cast<const char *>(glGetString(GL_VERSION));
        const char *vendor = reinterpret_cast<const char *>(glGetString(GL_VENDOR));
        const char *renderer = reinterpret_cast<const char *>(glGetString(GL_RENDERER));

        if (version)
        {
            std::cout << "[OpenGLBackend] OpenGL Version: " << version << std::endl;
        }
        if (vendor)
        {
            std::cout << "[OpenGLBackend] Vendor: " << vendor << std::endl;
        }
        if (renderer)
        {
            std::cout << "[OpenGLBackend] Renderer: " << renderer << std::endl;
        }

        // 设置基本的OpenGL状态
        glEnable(GL_DEPTH_TEST);
        glDepthFunc(GL_LESS);

        glEnable(GL_CULL_FACE);
        glCullFace(GL_BACK);
        glFrontFace(GL_CCW);

        // 设置清除颜色
        glClearColor(0.2f, 0.3f, 0.4f, 1.0f);

        // 设置视口
        glViewport(0, 0, config.swapchainWidth, config.swapchainHeight);

        _initialized = true;

        std::cout << "[OpenGLBackend] OpenGL backend initialized successfully" << std::endl;
        return true;
    }

    void OpenGLBackend::cleanup()
    {
        if (!_initialized)
        {
            return;
        }

        std::cout << "[OpenGLBackend] Cleaning up OpenGL backend..." << std::endl;

        _currentProgram = 0;
        _currentVAO = 0;
        _currentVBO = 0;
        _currentEBO = 0;
        _resourceCounter = 0;

        _initialized = false;

        std::cout << "[OpenGLBackend] OpenGL backend cleaned up" << std::endl;
    }

    void OpenGLBackend::beginFrame()
    {
        // 设置清除颜色为深蓝色
        glClearColor(0.2f, 0.3f, 0.4f, 1.0f);

        // 清除颜色和深度缓冲区
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        // 启用深度测试
        glEnable(GL_DEPTH_TEST);

        // 设置投影矩阵
        glMatrixMode(GL_PROJECTION);
        glLoadIdentity();

        // 设置透视投影 (FOV=45度, 宽高比=1.5, 近平面=0.1, 远平面=100)
        float fov = 45.0f;
        float aspect = 1.5f; // 1200/800
        float near_plane = 0.1f;
        float far_plane = 100.0f;

        // 手动计算透视投影矩阵
        float f = 1.0f / tan(fov * 3.14159f / 360.0f); // fov/2 转弧度
        glFrustum(-near_plane * aspect / f, near_plane * aspect / f,
                  -near_plane / f, near_plane / f, near_plane, far_plane);

        // 设置模型视图矩阵
        glMatrixMode(GL_MODELVIEW);
        glLoadIdentity();

        // 设置相机位置 (眼睛位置, 目标位置, 上方向)
        // 相机向后移动一些距离以便看到物体
        glTranslatef(0.0f, 0.0f, -5.0f);

        std::cout << "[OpenGLBackend] Begin frame with real OpenGL rendering" << std::endl;
    }

    void OpenGLBackend::endFrame()
    {
        // OpenGL中帧结束通常由外部调用glfwSwapBuffers
    }

    // 资源创建方法 - 简化实现
    BackendBuffer *OpenGLBackend::createBuffer(const BufferDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created buffer " << id
                  << " (size: " << desc.size << " bytes)" << std::endl;

        return new OpenGLBuffer(id, desc);
    }

    BackendTexture *OpenGLBackend::createTexture(const TextureDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created texture " << id
                  << " (" << desc.width << "x" << desc.height << ")" << std::endl;

        return new OpenGLTexture(id, desc);
    }

    BackendShader *OpenGLBackend::createShader(const ShaderDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created shader " << id
                  << " (stage: " << static_cast<int>(desc.stage) << ")" << std::endl;

        return new OpenGLShader(id, desc);
    }

    BackendPipeline *OpenGLBackend::createPipeline(const PipelineDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created pipeline " << id << std::endl;

        return new OpenGLPipeline(id, desc);
    }

    BackendDescriptorSet *OpenGLBackend::createDescriptorSet()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created descriptor set " << id << std::endl;
        return new OpenGLDescriptorSet();
    }

    BackendCommandList *OpenGLBackend::createCommandList()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created command list " << id << std::endl;
        return new OpenGLCommandList();
    }

    BackendFence *OpenGLBackend::createFence()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created fence " << id << std::endl;
        return new OpenGLFence();
    }

    BackendSemaphore *OpenGLBackend::createSemaphore()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created semaphore " << id << std::endl;
        return new OpenGLSemaphore();
    }

    void OpenGLBackend::executeCommandList(BackendCommandList *cmdList,
                                           const std::vector<BackendSemaphore *> &waitSemaphores,
                                           const std::vector<BackendSemaphore *> &signalSemaphores,
                                           BackendFence *fence)
    {
        std::cout << "[OpenGLBackend] Executing command list" << std::endl;
        // OpenGL是立即模式，命令直接执行
    }

    // 资源销毁方法
    void OpenGLBackend::destroyBuffer(BackendBuffer *buffer)
    {
        if (auto glBuffer = dynamic_cast<OpenGLBuffer *>(buffer))
        {
            uint32_t id = glBuffer->getId();
            std::cout << "[OpenGLBackend] Destroyed buffer (ID: " << id << ")" << std::endl;
        }
        delete buffer;
    }

    void OpenGLBackend::destroyTexture(BackendTexture *texture)
    {
        if (auto glTexture = dynamic_cast<OpenGLTexture *>(texture))
        {
            uint32_t id = glTexture->getId();
            std::cout << "[OpenGLBackend] Destroyed texture (ID: " << id << ")" << std::endl;
        }
        delete texture;
    }

    void OpenGLBackend::destroyShader(BackendShader *shader)
    {
        if (auto glShader = dynamic_cast<OpenGLShader *>(shader))
        {
            uint32_t id = glShader->getId();
            std::cout << "[OpenGLBackend] Destroyed shader (ID: " << id << ")" << std::endl;
        }
        delete shader;
    }

    void OpenGLBackend::destroyPipeline(BackendPipeline *pipeline)
    {
        if (auto glPipeline = dynamic_cast<OpenGLPipeline *>(pipeline))
        {
            uint32_t id = glPipeline->getId();
            std::cout << "[OpenGLBackend] Destroyed pipeline (ID: " << id << ")" << std::endl;
        }
        delete pipeline;
    }

    void OpenGLBackend::destroyDescriptorSet(BackendDescriptorSet *descriptorSet)
    {
        std::cout << "[OpenGLBackend] Destroyed descriptor set" << std::endl;
        delete descriptorSet;
    }

    void OpenGLBackend::destroyCommandList(BackendCommandList *cmdList)
    {
        std::cout << "[OpenGLBackend] Destroyed command list" << std::endl;
        delete cmdList;
    }

    void OpenGLBackend::destroyFence(BackendFence *fence)
    {
        std::cout << "[OpenGLBackend] Destroyed fence" << std::endl;
        delete fence;
    }

    void OpenGLBackend::destroySemaphore(BackendSemaphore *semaphore)
    {
        std::cout << "[OpenGLBackend] Destroyed semaphore" << std::endl;
        delete semaphore;
    }

    // OpenGL资源类实现
    OpenGLBuffer::OpenGLBuffer(uint32_t id, const BufferDesc &desc)
        : _id(id), _desc(desc)
    {
    }

    OpenGLBuffer::~OpenGLBuffer()
    {
        // 资源由后端管理，这里不删除
    }

    OpenGLTexture::OpenGLTexture(uint32_t id, const TextureDesc &desc)
        : _id(id), _desc(desc)
    {
    }

    OpenGLTexture::~OpenGLTexture()
    {
        // 资源由后端管理，这里不删除
    }

    OpenGLShader::OpenGLShader(uint32_t id, const ShaderDesc &desc)
        : _id(id), _desc(desc)
    {
    }

    OpenGLShader::~OpenGLShader()
    {
        // 资源由后端管理，这里不删除
    }

    OpenGLPipeline::OpenGLPipeline(uint32_t id, const PipelineDesc &desc)
        : _id(id), _desc(desc)
    {
    }

    OpenGLPipeline::~OpenGLPipeline()
    {
        // 资源由后端管理，这里不删除
    }

    OpenGLDescriptorSet::OpenGLDescriptorSet()
    {
    }

    OpenGLDescriptorSet::~OpenGLDescriptorSet()
    {
    }

    OpenGLCommandList::OpenGLCommandList()
    {
    }

    OpenGLCommandList::~OpenGLCommandList()
    {
    }

    void OpenGLCommandList::beginRenderPass(const RenderPassDesc &desc)
    {
        _inRenderPass = true;

        // 不在这里清除缓冲区，避免重复清除导致闪烁
        // 缓冲区清除已经在beginFrame()中完成

        std::cout << "[OpenGLCommandList] Begin render pass" << std::endl;
    }

    void OpenGLCommandList::setPipeline(BackendPipeline *pipeline)
    {
        std::cout << "[OpenGLCommandList] Set pipeline" << std::endl;
    }

    void OpenGLCommandList::setVertexBuffer(BackendBuffer *buffer, uint32_t binding, size_t offset)
    {
        std::cout << "[OpenGLCommandList] Set vertex buffer" << std::endl;
    }

    void OpenGLCommandList::setIndexBuffer(BackendBuffer *buffer, IndexFormat format, uint64_t offset)
    {
        std::cout << "[OpenGLCommandList] Set index buffer" << std::endl;
    }

    void OpenGLCommandList::draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance)
    {
        // 绘制一个简单的三角形作为测试
        if (vertexCount == 3)
        {
            // 添加旋转动画
            static float triangleRotation = 0.0f;
            triangleRotation += 2.0f; // 每帧增加2度（比立方体快一些）

            glPushMatrix();
            glTranslatef(-1.0f, 0.0f, 0.0f);               // 向左移动
            glRotatef(triangleRotation, 0.0f, 0.0f, 1.0f); // 绕Z轴旋转

            // 使用立即模式渲染（简化版本）
            glBegin(GL_TRIANGLES);
            glColor3f(1.0f, 0.0f, 0.0f);
            glVertex3f(0.0f, 0.5f, 0.0f);
            glColor3f(0.0f, 1.0f, 0.0f);
            glVertex3f(-0.5f, -0.5f, 0.0f);
            glColor3f(0.0f, 0.0f, 1.0f);
            glVertex3f(0.5f, -0.5f, 0.0f);
            glEnd();

            glPopMatrix(); // 恢复矩阵
        }

        std::cout << "[OpenGLCommandList] Draw " << vertexCount << " vertices (real OpenGL)" << std::endl;
    }

    void OpenGLCommandList::drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t vertexOffset, uint32_t firstInstance)
    {
        // 绘制一个简单的立方体作为测试
        if (indexCount == 36)
        {
            // 添加旋转动画
            static float rotation = 0.0f;
            rotation += 1.0f; // 每帧增加1度

            glPushMatrix();
            glTranslatef(1.0f, 0.0f, 0.0f);               // 向右移动
            glRotatef(rotation, 0.0f, 1.0f, 0.0f);        // 绕Y轴旋转
            glRotatef(rotation * 0.5f, 1.0f, 0.0f, 0.0f); // 绕X轴旋转（慢一些）

            // 使用立即模式渲染一个简单的立方体
            glBegin(GL_TRIANGLES);

            // 前面 (红色)
            glColor3f(1.0f, 0.0f, 0.0f);
            glVertex3f(-0.3f, -0.3f, 0.3f);
            glVertex3f(0.3f, -0.3f, 0.3f);
            glVertex3f(0.3f, 0.3f, 0.3f);
            glVertex3f(0.3f, 0.3f, 0.3f);
            glVertex3f(-0.3f, 0.3f, 0.3f);
            glVertex3f(-0.3f, -0.3f, 0.3f);

            // 后面 (绿色)
            glColor3f(0.0f, 1.0f, 0.0f);
            glVertex3f(-0.3f, -0.3f, -0.3f);
            glVertex3f(-0.3f, 0.3f, -0.3f);
            glVertex3f(0.3f, 0.3f, -0.3f);
            glVertex3f(0.3f, 0.3f, -0.3f);
            glVertex3f(0.3f, -0.3f, -0.3f);
            glVertex3f(-0.3f, -0.3f, -0.3f);

            // 左面 (蓝色)
            glColor3f(0.0f, 0.0f, 1.0f);
            glVertex3f(-0.3f, 0.3f, 0.3f);
            glVertex3f(-0.3f, 0.3f, -0.3f);
            glVertex3f(-0.3f, -0.3f, -0.3f);
            glVertex3f(-0.3f, -0.3f, -0.3f);
            glVertex3f(-0.3f, -0.3f, 0.3f);
            glVertex3f(-0.3f, 0.3f, 0.3f);

            // 右面 (黄色)
            glColor3f(1.0f, 1.0f, 0.0f);
            glVertex3f(0.3f, 0.3f, 0.3f);
            glVertex3f(0.3f, -0.3f, -0.3f);
            glVertex3f(0.3f, 0.3f, -0.3f);
            glVertex3f(0.3f, -0.3f, -0.3f);
            glVertex3f(0.3f, 0.3f, 0.3f);
            glVertex3f(0.3f, -0.3f, 0.3f);

            // 底面 (紫色)
            glColor3f(1.0f, 0.0f, 1.0f);
            glVertex3f(-0.3f, -0.3f, -0.3f);
            glVertex3f(0.3f, -0.3f, -0.3f);
            glVertex3f(0.3f, -0.3f, 0.3f);
            glVertex3f(0.3f, -0.3f, 0.3f);
            glVertex3f(-0.3f, -0.3f, 0.3f);
            glVertex3f(-0.3f, -0.3f, -0.3f);

            // 顶面 (青色)
            glColor3f(0.0f, 1.0f, 1.0f);
            glVertex3f(-0.3f, 0.3f, -0.3f);
            glVertex3f(-0.3f, 0.3f, 0.3f);
            glVertex3f(0.3f, 0.3f, 0.3f);
            glVertex3f(0.3f, 0.3f, 0.3f);
            glVertex3f(0.3f, 0.3f, -0.3f);
            glVertex3f(-0.3f, 0.3f, -0.3f);

            glEnd();

            glPopMatrix(); // 恢复矩阵
        }

        std::cout << "[OpenGLCommandList] Draw indexed " << indexCount << " indices (real OpenGL)" << std::endl;
    }

} // namespace USG
