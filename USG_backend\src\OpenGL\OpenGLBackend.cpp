#include "OpenGLBackend.h"
#include <iostream>
#include <cassert>
#include <cmath>

// 简化的OpenGL后端 - 主要用于演示后端切换功能
// 在实际项目中应该使用GLAD或类似的OpenGL加载器

namespace USG
{

    OpenGLBackend::OpenGLBackend()
    {
    }

    OpenGLBackend::~OpenGLBackend()
    {
        cleanup();
    }

    bool OpenGLBackend::initialize(const BackendConfig &config)
    {
        if (_initialized)
        {
            return true;
        }

        _config = config;

        std::cout << "[OpenGLBackend] Initializing OpenGL backend..." << std::endl;

        // 获取OpenGL版本信息
        const char *version = reinterpret_cast<const char *>(glGetString(GL_VERSION));
        const char *vendor = reinterpret_cast<const char *>(glGetString(GL_VENDOR));
        const char *renderer = reinterpret_cast<const char *>(glGetString(GL_RENDERER));

        if (version)
        {
            std::cout << "[OpenGLBackend] OpenGL Version: " << version << std::endl;
        }
        if (vendor)
        {
            std::cout << "[OpenGLBackend] Vendor: " << vendor << std::endl;
        }
        if (renderer)
        {
            std::cout << "[OpenGLBackend] Renderer: " << renderer << std::endl;
        }

        // 设置基本的OpenGL状态
        glEnable(GL_DEPTH_TEST);
        glDepthFunc(GL_LESS);

        glEnable(GL_CULL_FACE);
        glCullFace(GL_BACK);
        glFrontFace(GL_CCW);

        // 设置清除颜色
        glClearColor(0.2f, 0.3f, 0.4f, 1.0f);

        // 设置视口
        glViewport(0, 0, config.swapchainWidth, config.swapchainHeight);

        _initialized = true;

        std::cout << "[OpenGLBackend] OpenGL backend initialized successfully" << std::endl;
        return true;
    }

    void OpenGLBackend::cleanup()
    {
        if (!_initialized)
        {
            return;
        }

        std::cout << "[OpenGLBackend] Cleaning up OpenGL backend..." << std::endl;

        _currentProgram = 0;
        _currentVAO = 0;
        _currentVBO = 0;
        _currentEBO = 0;
        _resourceCounter = 0;

        _initialized = false;

        std::cout << "[OpenGLBackend] OpenGL backend cleaned up" << std::endl;
    }

    void OpenGLBackend::beginFrame()
    {
        // 设置清除颜色为深蓝色
        glClearColor(0.2f, 0.3f, 0.4f, 1.0f);

        // 清除颜色和深度缓冲区
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        // 启用深度测试
        glEnable(GL_DEPTH_TEST);

        // 设置视口
        glViewport(0, 0, 1200, 800);

        std::cout << "[OpenGLBackend] Begin frame with real OpenGL rendering" << std::endl;
    }

    void OpenGLBackend::endFrame()
    {
        // OpenGL中帧结束通常由外部调用glfwSwapBuffers
    }

    // 资源创建方法 - 简化实现
    BackendBuffer *OpenGLBackend::createBuffer(const BufferDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created buffer " << id
                  << " (size: " << desc.size << " bytes)" << std::endl;

        return new OpenGLBuffer(id, desc);
    }

    // OpenGLBuffer implementation
    OpenGLBuffer::OpenGLBuffer(uint32_t id, const BufferDesc &desc)
        : _id(id), _desc(desc), _glBuffer(0)
    {
        createBuffer();
    }

    OpenGLBuffer::~OpenGLBuffer()
    {
        if (_glBuffer != 0)
        {
            glDeleteBuffers(1, &_glBuffer);
        }
    }

    bool OpenGLBuffer::createBuffer()
    {
        // 生成缓冲区对象
        glGenBuffers(1, &_glBuffer);
        if (_glBuffer == 0)
        {
            std::cerr << "[OpenGLBuffer] Failed to create buffer" << std::endl;
            return false;
        }

        // 确定缓冲区类型
        GLenum target = GL_ARRAY_BUFFER;
        if (_desc.usage == BufferUsage::Index)
        {
            target = GL_ELEMENT_ARRAY_BUFFER;
        }

        // 绑定并分配内存
        glBindBuffer(target, _glBuffer);

        // 创建测试数据
        if (_desc.usage == BufferUsage::Vertex)
        {
            // 创建三角形顶点数据 (位置 + 颜色)
            float triangleVertices[] = {
                // 位置          // 颜色
                0.0f, 0.5f, 0.0f, 1.0f, 0.0f, 0.0f,   // 顶部 - 红色
                -0.5f, -0.5f, 0.0f, 0.0f, 1.0f, 0.0f, // 左下 - 绿色
                0.5f, -0.5f, 0.0f, 0.0f, 0.0f, 1.0f   // 右下 - 蓝色
            };
            glBufferData(target, sizeof(triangleVertices), triangleVertices, GL_STATIC_DRAW);
            std::cout << "[OpenGLBuffer] Uploaded triangle vertex data" << std::endl;
        }
        else if (_desc.usage == BufferUsage::Index)
        {
            // 创建三角形索引数据
            uint32_t triangleIndices[] = {0, 1, 2};
            glBufferData(target, sizeof(triangleIndices), triangleIndices, GL_STATIC_DRAW);
            std::cout << "[OpenGLBuffer] Uploaded triangle index data" << std::endl;
        }
        else
        {
            glBufferData(target, _desc.size, nullptr, GL_STATIC_DRAW);
        }

        std::cout << "[OpenGLBuffer] Buffer created successfully (GL ID: " << _glBuffer << ")" << std::endl;
        return true;
    }

    BackendTexture *OpenGLBackend::createTexture(const TextureDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created texture " << id
                  << " (" << desc.width << "x" << desc.height << ")" << std::endl;

        return new OpenGLTexture(id, desc);
    }

    BackendShader *OpenGLBackend::createShader(const ShaderDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created shader " << id
                  << " (stage: " << static_cast<int>(desc.stage) << ")" << std::endl;

        return new OpenGLShader(id, desc);
    }

    // OpenGLShader implementation
    OpenGLShader::OpenGLShader(uint32_t id, const ShaderDesc &desc)
        : _id(id), _desc(desc), _glShader(0)
    {
        compileShader();
    }

    OpenGLShader::~OpenGLShader()
    {
        if (_glShader != 0)
        {
            glDeleteShader(_glShader);
        }
    }

    bool OpenGLShader::compileShader()
    {
        // 确定着色器类型
        GLenum shaderType;
        switch (_desc.stage)
        {
        case ShaderStage::Vertex:
            shaderType = GL_VERTEX_SHADER;
            break;
        case ShaderStage::Fragment:
            shaderType = GL_FRAGMENT_SHADER;
            break;
        default:
            std::cerr << "[OpenGLShader] Unsupported shader stage" << std::endl;
            return false;
        }

        // 创建着色器
        _glShader = glCreateShader(shaderType);
        if (_glShader == 0)
        {
            std::cerr << "[OpenGLShader] Failed to create shader" << std::endl;
            return false;
        }

        // 使用简单的内置着色器代码
        const char *vertexShaderSource = R"(
            #version 330 core
            layout (location = 0) in vec3 aPos;
            layout (location = 1) in vec3 aColor;

            uniform mat4 uMVP;

            out vec3 vertexColor;

            void main()
            {
                gl_Position = uMVP * vec4(aPos, 1.0);
                vertexColor = aColor;
            }
        )";

        const char *fragmentShaderSource = R"(
            #version 330 core
            in vec3 vertexColor;
            out vec4 FragColor;

            void main()
            {
                FragColor = vec4(vertexColor, 1.0);
            }
        )";

        const char *source = (shaderType == GL_VERTEX_SHADER) ? vertexShaderSource : fragmentShaderSource;

        // 编译着色器
        glShaderSource(_glShader, 1, &source, NULL);
        glCompileShader(_glShader);

        // 检查编译错误
        GLint success;
        glGetShaderiv(_glShader, GL_COMPILE_STATUS, &success);
        if (!success)
        {
            GLchar infoLog[512];
            glGetShaderInfoLog(_glShader, 512, NULL, infoLog);
            std::cerr << "[OpenGLShader] Shader compilation failed: " << infoLog << std::endl;
            glDeleteShader(_glShader);
            _glShader = 0;
            return false;
        }

        std::cout << "[OpenGLShader] Shader compiled successfully (GL ID: " << _glShader << ")" << std::endl;
        return true;
    }

    BackendPipeline *OpenGLBackend::createPipeline(const PipelineDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created pipeline " << id << std::endl;

        return new OpenGLPipeline(id, desc);
    }

    // OpenGLPipeline implementation
    OpenGLPipeline::OpenGLPipeline(uint32_t id, const PipelineDesc &desc)
        : _id(id), _desc(desc), _glProgram(0)
    {
        linkProgram();
    }

    OpenGLPipeline::~OpenGLPipeline()
    {
        if (_glProgram != 0)
        {
            glDeleteProgram(_glProgram);
        }
    }

    bool OpenGLPipeline::linkProgram()
    {
        // 创建程序对象
        _glProgram = glCreateProgram();
        if (_glProgram == 0)
        {
            std::cerr << "[OpenGLPipeline] Failed to create program" << std::endl;
            return false;
        }

        // 附加着色器
        if (_desc.vertexShader)
        {
            OpenGLShader *glVertexShader = static_cast<OpenGLShader *>(_desc.vertexShader);
            glAttachShader(_glProgram, glVertexShader->getGLShader());
        }

        if (_desc.fragmentShader)
        {
            OpenGLShader *glFragmentShader = static_cast<OpenGLShader *>(_desc.fragmentShader);
            glAttachShader(_glProgram, glFragmentShader->getGLShader());
        }

        // 链接程序
        glLinkProgram(_glProgram);

        // 检查链接错误
        GLint success;
        glGetProgramiv(_glProgram, GL_LINK_STATUS, &success);
        if (!success)
        {
            GLchar infoLog[512];
            glGetProgramInfoLog(_glProgram, 512, NULL, infoLog);
            std::cerr << "[OpenGLPipeline] Program linking failed: " << infoLog << std::endl;
            glDeleteProgram(_glProgram);
            _glProgram = 0;
            return false;
        }

        std::cout << "[OpenGLPipeline] Program linked successfully (GL ID: " << _glProgram << ")" << std::endl;
        return true;
    }

    BackendDescriptorSet *OpenGLBackend::createDescriptorSet()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created descriptor set " << id << std::endl;
        return new OpenGLDescriptorSet();
    }

    BackendCommandList *OpenGLBackend::createCommandList()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created command list " << id << std::endl;
        return new OpenGLCommandList();
    }

    BackendFence *OpenGLBackend::createFence()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created fence " << id << std::endl;
        return new OpenGLFence();
    }

    BackendSemaphore *OpenGLBackend::createSemaphore()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created semaphore " << id << std::endl;
        return new OpenGLSemaphore();
    }

    void OpenGLBackend::executeCommandList(BackendCommandList *cmdList,
                                           const std::vector<BackendSemaphore *> &waitSemaphores,
                                           const std::vector<BackendSemaphore *> &signalSemaphores,
                                           BackendFence *fence)
    {
        std::cout << "[OpenGLBackend] Executing command list" << std::endl;
        // OpenGL是立即模式，命令直接执行
    }

    // 资源销毁方法
    void OpenGLBackend::destroyBuffer(BackendBuffer *buffer)
    {
        if (auto glBuffer = dynamic_cast<OpenGLBuffer *>(buffer))
        {
            uint32_t id = glBuffer->getId();
            std::cout << "[OpenGLBackend] Destroyed buffer (ID: " << id << ")" << std::endl;
        }
        delete buffer;
    }

    void OpenGLBackend::destroyTexture(BackendTexture *texture)
    {
        if (auto glTexture = dynamic_cast<OpenGLTexture *>(texture))
        {
            uint32_t id = glTexture->getId();
            std::cout << "[OpenGLBackend] Destroyed texture (ID: " << id << ")" << std::endl;
        }
        delete texture;
    }

    void OpenGLBackend::destroyShader(BackendShader *shader)
    {
        if (auto glShader = dynamic_cast<OpenGLShader *>(shader))
        {
            uint32_t id = glShader->getId();
            std::cout << "[OpenGLBackend] Destroyed shader (ID: " << id << ")" << std::endl;
        }
        delete shader;
    }

    void OpenGLBackend::destroyPipeline(BackendPipeline *pipeline)
    {
        if (auto glPipeline = dynamic_cast<OpenGLPipeline *>(pipeline))
        {
            uint32_t id = glPipeline->getId();
            std::cout << "[OpenGLBackend] Destroyed pipeline (ID: " << id << ")" << std::endl;
        }
        delete pipeline;
    }

    void OpenGLBackend::destroyDescriptorSet(BackendDescriptorSet *descriptorSet)
    {
        std::cout << "[OpenGLBackend] Destroyed descriptor set" << std::endl;
        delete descriptorSet;
    }

    void OpenGLBackend::destroyCommandList(BackendCommandList *cmdList)
    {
        std::cout << "[OpenGLBackend] Destroyed command list" << std::endl;
        delete cmdList;
    }

    void OpenGLBackend::destroyFence(BackendFence *fence)
    {
        std::cout << "[OpenGLBackend] Destroyed fence" << std::endl;
        delete fence;
    }

    void OpenGLBackend::destroySemaphore(BackendSemaphore *semaphore)
    {
        std::cout << "[OpenGLBackend] Destroyed semaphore" << std::endl;
        delete semaphore;
    }

    OpenGLTexture::OpenGLTexture(uint32_t id, const TextureDesc &desc)
        : _id(id), _desc(desc)
    {
    }

    OpenGLTexture::~OpenGLTexture()
    {
        // 资源由后端管理，这里不删除
    }

    OpenGLShader::OpenGLShader(uint32_t id, const ShaderDesc &desc)
        : _id(id), _desc(desc)
    {
    }

    OpenGLShader::~OpenGLShader()
    {
        // 资源由后端管理，这里不删除
    }

    OpenGLDescriptorSet::OpenGLDescriptorSet()
    {
    }

    OpenGLDescriptorSet::~OpenGLDescriptorSet()
    {
    }

    OpenGLCommandList::OpenGLCommandList()
    {
    }

    OpenGLCommandList::~OpenGLCommandList()
    {
    }

    void OpenGLCommandList::beginRenderPass(const RenderPassDesc &desc)
    {
        _inRenderPass = true;

        // 不在这里清除缓冲区，避免重复清除导致闪烁
        // 缓冲区清除已经在beginFrame()中完成

        std::cout << "[OpenGLCommandList] Begin render pass" << std::endl;
    }

    void OpenGLCommandList::setPipeline(BackendPipeline *pipeline)
    {
        if (pipeline)
        {
            OpenGLPipeline *glPipeline = static_cast<OpenGLPipeline *>(pipeline);
            GLuint program = glPipeline->getGLProgram();
            if (program != 0)
            {
                glUseProgram(program);
                _currentProgram = program;
                std::cout << "[OpenGLCommandList] Set pipeline (GL Program: " << program << ")" << std::endl;
            }
            else
            {
                std::cerr << "[OpenGLCommandList] Invalid pipeline program" << std::endl;
            }
        }
        else
        {
            std::cout << "[OpenGLCommandList] Set pipeline (null)" << std::endl;
        }
    }

    void OpenGLCommandList::setVertexBuffer(BackendBuffer *buffer, uint32_t binding, size_t offset)
    {
        if (buffer)
        {
            OpenGLBuffer *glBuffer = static_cast<OpenGLBuffer *>(buffer);

            // 创建VAO如果还没有
            if (_currentVAO == 0)
            {
                glGenVertexArrays(1, &_currentVAO);
                glBindVertexArray(_currentVAO);

                // 设置顶点属性
                // 位置属性 (location = 0)
                glEnableVertexAttribArray(0);
                glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 6 * sizeof(float), (void *)0);

                // 颜色属性 (location = 1)
                glEnableVertexAttribArray(1);
                glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, 6 * sizeof(float), (void *)(3 * sizeof(float)));
            }
            else
            {
                glBindVertexArray(_currentVAO);
            }

            // 绑定顶点缓冲区
            glBindBuffer(GL_ARRAY_BUFFER, glBuffer->getGLBuffer());

            std::cout << "[OpenGLCommandList] Set vertex buffer (VAO: " << _currentVAO
                      << ", VBO: " << glBuffer->getGLBuffer() << ")" << std::endl;
        }
    }

    void OpenGLCommandList::setIndexBuffer(BackendBuffer *buffer, IndexFormat format, uint64_t offset)
    {
        if (buffer)
        {
            OpenGLBuffer *glBuffer = static_cast<OpenGLBuffer *>(buffer);
            glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, glBuffer->getGLBuffer());

            std::cout << "[OpenGLCommandList] Set index buffer (EBO: " << glBuffer->getGLBuffer() << ")" << std::endl;
        }
    }

    void OpenGLCommandList::draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance)
    {
        if (_currentProgram != 0 && _currentVAO != 0)
        {
            // 设置MVP矩阵
            GLint mvpLocation = glGetUniformLocation(_currentProgram, "uMVP");
            if (mvpLocation != -1)
            {
                // 创建简单的MVP矩阵
                float mvp[16] = {
                    1.0f, 0.0f, 0.0f, 0.0f,
                    0.0f, 1.0f, 0.0f, 0.0f,
                    0.0f, 0.0f, 1.0f, 0.0f,
                    0.0f, 0.0f, 0.0f, 1.0f};

                // 添加简单的变换（向左移动）
                mvp[12] = -1.0f; // X偏移

                glUniformMatrix4fv(mvpLocation, 1, GL_FALSE, mvp);
            }

            // 绑定VAO并绘制
            glBindVertexArray(_currentVAO);
            glDrawArrays(GL_TRIANGLES, firstVertex, vertexCount);

            std::cout << "[OpenGLCommandList] Draw " << vertexCount << " vertices (modern OpenGL)" << std::endl;
        }
        else
        {
            std::cerr << "[OpenGLCommandList] Cannot draw: missing program or VAO" << std::endl;
        }
    }

    void OpenGLCommandList::drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t vertexOffset, uint32_t firstInstance)
    {
        if (_currentProgram != 0 && _currentVAO != 0)
        {
            // 设置MVP矩阵
            GLint mvpLocation = glGetUniformLocation(_currentProgram, "uMVP");
            if (mvpLocation != -1)
            {
                // 创建简单的MVP矩阵（向右移动）
                float mvp[16] = {
                    1.0f, 0.0f, 0.0f, 0.0f,
                    0.0f, 1.0f, 0.0f, 0.0f,
                    0.0f, 0.0f, 1.0f, 0.0f,
                    1.0f, 0.0f, 0.0f, 1.0f // X偏移为1.0
                };

                glUniformMatrix4fv(mvpLocation, 1, GL_FALSE, mvp);
            }

            // 绑定VAO并绘制索引
            glBindVertexArray(_currentVAO);
            glDrawElements(GL_TRIANGLES, indexCount, GL_UNSIGNED_INT, (void *)(firstIndex * sizeof(uint32_t)));

            std::cout << "[OpenGLCommandList] Draw indexed " << indexCount << " indices (modern OpenGL)" << std::endl;
        }
        else
        {
            std::cerr << "[OpenGLCommandList] Cannot draw indexed: missing program or VAO" << std::endl;
        }
    }

} // namespace USG
