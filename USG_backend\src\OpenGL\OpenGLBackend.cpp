#include "OpenGLBackend.h"
#include <iostream>
#include <cassert>

// 简化的OpenGL后端 - 主要用于演示后端切换功能
// 在实际项目中应该使用GLAD或类似的OpenGL加载器

namespace USG
{

    OpenGLBackend::OpenGLBackend()
    {
    }

    OpenGLBackend::~OpenGLBackend()
    {
        cleanup();
    }

    bool OpenGLBackend::initialize(const BackendConfig &config)
    {
        if (_initialized)
        {
            return true;
        }

        _config = config;

        std::cout << "[OpenGLBackend] Initializing OpenGL backend..." << std::endl;

        // 获取OpenGL版本信息
        const char *version = reinterpret_cast<const char *>(glGetString(GL_VERSION));
        const char *vendor = reinterpret_cast<const char *>(glGetString(GL_VENDOR));
        const char *renderer = reinterpret_cast<const char *>(glGetString(GL_RENDERER));

        if (version)
        {
            std::cout << "[OpenGLBackend] OpenGL Version: " << version << std::endl;
        }
        if (vendor)
        {
            std::cout << "[OpenGLBackend] Vendor: " << vendor << std::endl;
        }
        if (renderer)
        {
            std::cout << "[OpenGLBackend] Renderer: " << renderer << std::endl;
        }

        // 设置基本的OpenGL状态
        glEnable(GL_DEPTH_TEST);
        glDepthFunc(GL_LESS);

        glEnable(GL_CULL_FACE);
        glCullFace(GL_BACK);
        glFrontFace(GL_CCW);

        // 设置清除颜色
        glClearColor(0.2f, 0.3f, 0.4f, 1.0f);

        // 设置视口
        glViewport(0, 0, config.swapchainWidth, config.swapchainHeight);

        _initialized = true;

        std::cout << "[OpenGLBackend] OpenGL backend initialized successfully" << std::endl;
        return true;
    }

    void OpenGLBackend::cleanup()
    {
        if (!_initialized)
        {
            return;
        }

        std::cout << "[OpenGLBackend] Cleaning up OpenGL backend..." << std::endl;

        _currentProgram = 0;
        _currentVAO = 0;
        _currentVBO = 0;
        _currentEBO = 0;
        _resourceCounter = 0;

        _initialized = false;

        std::cout << "[OpenGLBackend] OpenGL backend cleaned up" << std::endl;
    }

    void OpenGLBackend::beginFrame()
    {
        // 简化实现 - 清除缓冲区
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
        std::cout << "[OpenGLBackend] Begin frame" << std::endl;
    }

    void OpenGLBackend::endFrame()
    {
        // OpenGL中帧结束通常由外部调用glfwSwapBuffers
    }

    // 资源创建方法 - 简化实现
    BackendBuffer *OpenGLBackend::createBuffer(const BufferDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created buffer " << id
                  << " (size: " << desc.size << " bytes)" << std::endl;

        return new OpenGLBuffer(id, desc);
    }

    BackendTexture *OpenGLBackend::createTexture(const TextureDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created texture " << id
                  << " (" << desc.width << "x" << desc.height << ")" << std::endl;

        return new OpenGLTexture(id, desc);
    }

    BackendShader *OpenGLBackend::createShader(const ShaderDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created shader " << id
                  << " (stage: " << static_cast<int>(desc.stage) << ")" << std::endl;

        return new OpenGLShader(id, desc);
    }

    BackendPipeline *OpenGLBackend::createPipeline(const PipelineDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created pipeline " << id << std::endl;

        return new OpenGLPipeline(id, desc);
    }

    BackendDescriptorSet *OpenGLBackend::createDescriptorSet()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created descriptor set " << id << std::endl;
        return new OpenGLDescriptorSet();
    }

    BackendCommandList *OpenGLBackend::createCommandList()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created command list " << id << std::endl;
        return new OpenGLCommandList();
    }

    BackendFence *OpenGLBackend::createFence()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created fence " << id << std::endl;
        return new OpenGLFence();
    }

    BackendSemaphore *OpenGLBackend::createSemaphore()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created semaphore " << id << std::endl;
        return new OpenGLSemaphore();
    }

    void OpenGLBackend::executeCommandList(BackendCommandList *cmdList,
                                           const std::vector<BackendSemaphore *> &waitSemaphores,
                                           const std::vector<BackendSemaphore *> &signalSemaphores,
                                           BackendFence *fence)
    {
        std::cout << "[OpenGLBackend] Executing command list" << std::endl;
        // OpenGL是立即模式，命令直接执行
    }

    // 资源销毁方法
    void OpenGLBackend::destroyBuffer(BackendBuffer *buffer)
    {
        if (auto glBuffer = dynamic_cast<OpenGLBuffer *>(buffer))
        {
            GLuint id = glBuffer->getBuffer();
            glDeleteBuffers(1, &id);
            std::cout << "[OpenGLBackend] Destroyed buffer (OpenGL ID: " << id << ")" << std::endl;
        }
        delete buffer;
    }

    void OpenGLBackend::destroyTexture(BackendTexture *texture)
    {
        if (auto glTexture = dynamic_cast<OpenGLTexture *>(texture))
        {
            GLuint id = glTexture->getTexture();
            glDeleteTextures(1, &id);
            std::cout << "[OpenGLBackend] Destroyed texture (OpenGL ID: " << id << ")" << std::endl;
        }
        delete texture;
    }

    void OpenGLBackend::destroyShader(BackendShader *shader)
    {
        if (auto glShader = dynamic_cast<OpenGLShader *>(shader))
        {
            GLuint id = glShader->getShader();
            glDeleteShader(id);
            std::cout << "[OpenGLBackend] Destroyed shader (OpenGL ID: " << id << ")" << std::endl;
        }
        delete shader;
    }

    void OpenGLBackend::destroyPipeline(BackendPipeline *pipeline)
    {
        if (auto glPipeline = dynamic_cast<OpenGLPipeline *>(pipeline))
        {
            GLuint id = glPipeline->getProgram();
            glDeleteProgram(id);
            std::cout << "[OpenGLBackend] Destroyed pipeline (OpenGL Program ID: " << id << ")" << std::endl;
        }
        delete pipeline;
    }

    void OpenGLBackend::destroyDescriptorSet(BackendDescriptorSet *descriptorSet)
    {
        std::cout << "[OpenGLBackend] Destroyed descriptor set" << std::endl;
        delete descriptorSet;
    }

    void OpenGLBackend::destroyCommandList(BackendCommandList *cmdList)
    {
        std::cout << "[OpenGLBackend] Destroyed command list" << std::endl;
        delete cmdList;
    }

    void OpenGLBackend::destroyFence(BackendFence *fence)
    {
        std::cout << "[OpenGLBackend] Destroyed fence" << std::endl;
        delete fence;
    }

    void OpenGLBackend::destroySemaphore(BackendSemaphore *semaphore)
    {
        std::cout << "[OpenGLBackend] Destroyed semaphore" << std::endl;
        delete semaphore;
    }

    // OpenGL资源类实现
    OpenGLBuffer::OpenGLBuffer(GLuint buffer, const BufferDesc &desc)
        : _buffer(buffer), _desc(desc)
    {
    }

    OpenGLBuffer::~OpenGLBuffer()
    {
        // 资源由后端管理，这里不删除
    }

    OpenGLTexture::OpenGLTexture(GLuint texture, const TextureDesc &desc)
        : _texture(texture), _desc(desc)
    {
    }

    OpenGLTexture::~OpenGLTexture()
    {
        // 资源由后端管理，这里不删除
    }

    OpenGLShader::OpenGLShader(GLuint shader, const ShaderDesc &desc)
        : _shader(shader), _desc(desc)
    {
    }

    OpenGLShader::~OpenGLShader()
    {
        // 资源由后端管理，这里不删除
    }

    OpenGLPipeline::OpenGLPipeline(GLuint program, const PipelineDesc &desc)
        : _program(program), _desc(desc)
    {
    }

    OpenGLPipeline::~OpenGLPipeline()
    {
        // 资源由后端管理，这里不删除
    }

    OpenGLDescriptorSet::OpenGLDescriptorSet()
    {
    }

    OpenGLDescriptorSet::~OpenGLDescriptorSet()
    {
    }

    OpenGLCommandList::OpenGLCommandList()
    {
    }

    OpenGLCommandList::~OpenGLCommandList()
    {
    }

    void OpenGLCommandList::beginRenderPass(const RenderPassDesc &desc)
    {
        _inRenderPass = true;

        // 设置清除颜色
        glClearColor(desc.clearColor[0], desc.clearColor[1], desc.clearColor[2], desc.clearColor[3]);

        // 清除缓冲区
        GLbitfield clearMask = GL_COLOR_BUFFER_BIT;
        if (desc.hasDepthStencil)
        {
            clearMask |= GL_DEPTH_BUFFER_BIT;
            glClearDepth(desc.clearDepth);
        }
        glClear(clearMask);
    }

    void OpenGLCommandList::setPipeline(BackendPipeline *pipeline)
    {
        if (auto glPipeline = dynamic_cast<OpenGLPipeline *>(pipeline))
        {
            _currentProgram = glPipeline->getProgram();
            glUseProgram(_currentProgram);
        }
    }

    void OpenGLCommandList::setVertexBuffer(BackendBuffer *buffer, uint32_t binding, size_t offset)
    {
        if (auto glBuffer = dynamic_cast<OpenGLBuffer *>(buffer))
        {
            glBindBuffer(GL_ARRAY_BUFFER, glBuffer->getBuffer());

            // 简化的顶点属性设置
            glEnableVertexAttribArray(0); // position
            glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 6 * sizeof(float), (void *)0);

            glEnableVertexAttribArray(1); // color
            glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, 6 * sizeof(float), (void *)(3 * sizeof(float)));
        }
    }

    void OpenGLCommandList::setIndexBuffer(BackendBuffer *buffer, IndexFormat format, uint64_t offset)
    {
        if (auto glBuffer = dynamic_cast<OpenGLBuffer *>(buffer))
        {
            glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, glBuffer->getBuffer());
        }
    }

    void OpenGLCommandList::draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance)
    {
        glDrawArrays(GL_TRIANGLES, firstVertex, vertexCount);
    }

    void OpenGLCommandList::drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t vertexOffset, uint32_t firstInstance)
    {
        glDrawElements(GL_TRIANGLES, indexCount, GL_UNSIGNED_INT, (void *)(firstIndex * sizeof(uint32_t)));
    }

} // namespace USG
