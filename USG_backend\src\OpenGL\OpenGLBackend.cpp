#include "OpenGLBackend.h"
#include <iostream>
#include <cassert>
#include <cmath>

// 简化的OpenGL后端 - 主要用于演示后端切换功能
// 在实际项目中应该使用GLAD或类似的OpenGL加载器

namespace USG
{

    OpenGLBackend::OpenGLBackend()
    {
    }

    OpenGLBackend::~OpenGLBackend()
    {
        cleanup();
    }

    bool OpenGLBackend::initialize(const BackendConfig &config)
    {
        if (_initialized)
        {
            return true;
        }

        _config = config;

        std::cout << "[OpenGLBackend] Initializing OpenGL backend..." << std::endl;

        // 获取OpenGL版本信息
        const char *version = reinterpret_cast<const char *>(glGetString(GL_VERSION));
        const char *vendor = reinterpret_cast<const char *>(glGetString(GL_VENDOR));
        const char *renderer = reinterpret_cast<const char *>(glGetString(GL_RENDERER));

        if (version)
        {
            std::cout << "[OpenGLBackend] OpenGL Version: " << version << std::endl;
        }
        if (vendor)
        {
            std::cout << "[OpenGLBackend] Vendor: " << vendor << std::endl;
        }
        if (renderer)
        {
            std::cout << "[OpenGLBackend] Renderer: " << renderer << std::endl;
        }

        // 设置基本的OpenGL状态
        glEnable(GL_DEPTH_TEST);
        glDepthFunc(GL_LESS);

        glEnable(GL_CULL_FACE);
        glCullFace(GL_BACK);
        glFrontFace(GL_CCW);

        // 设置清除颜色
        glClearColor(0.2f, 0.3f, 0.4f, 1.0f);

        // 设置视口
        glViewport(0, 0, config.swapchainWidth, config.swapchainHeight);

        _initialized = true;

        std::cout << "[OpenGLBackend] OpenGL backend initialized successfully" << std::endl;
        return true;
    }

    void OpenGLBackend::cleanup()
    {
        if (!_initialized)
        {
            return;
        }

        std::cout << "[OpenGLBackend] Cleaning up OpenGL backend..." << std::endl;

        _currentProgram = 0;
        _currentVAO = 0;
        _currentVBO = 0;
        _currentEBO = 0;
        _resourceCounter = 0;

        _initialized = false;

        std::cout << "[OpenGLBackend] OpenGL backend cleaned up" << std::endl;
    }

    void OpenGLBackend::beginFrame()
    {
        // 设置视口
        glViewport(0, 0, 1200, 800);

        // 设置清除颜色为深蓝色
        glClearColor(0.2f, 0.3f, 0.4f, 1.0f);

        // 清除颜色和深度缓冲区
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        // 禁用深度测试，简化渲染
        glDisable(GL_DEPTH_TEST);

        // 禁用面剔除，确保所有面都可见
        glDisable(GL_CULL_FACE);

        // 设置投影矩阵
        glMatrixMode(GL_PROJECTION);
        glLoadIdentity();

        // 使用简单的正交投影，确保物体可见
        glOrtho(-2.0, 2.0, -1.5, 1.5, -1.0, 1.0);

        // 设置模型视图矩阵
        glMatrixMode(GL_MODELVIEW);
        glLoadIdentity();

        // 直接在这里绘制一个测试三角形，验证OpenGL是否工作
        glBegin(GL_TRIANGLES);
        glColor3f(1.0f, 0.0f, 0.0f); // 红色
        glVertex2f(0.0f, 0.5f);
        glColor3f(0.0f, 1.0f, 0.0f); // 绿色
        glVertex2f(-0.5f, -0.5f);
        glColor3f(0.0f, 0.0f, 1.0f); // 蓝色
        glVertex2f(0.5f, -0.5f);
        glEnd();

        // 确保OpenGL错误状态清除
        GLenum error = glGetError();
        if (error != GL_NO_ERROR)
        {
            std::cout << "[OpenGLBackend] OpenGL error: " << error << std::endl;
        }

        std::cout << "[OpenGLBackend] Begin frame with immediate mode OpenGL rendering" << std::endl;
    }

    void OpenGLBackend::endFrame()
    {
        // OpenGL中帧结束通常由外部调用glfwSwapBuffers
    }

    // 资源创建方法 - 简化实现
    BackendBuffer *OpenGLBackend::createBuffer(const BufferDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created buffer " << id
                  << " (size: " << desc.size << " bytes)" << std::endl;

        return new OpenGLBuffer(id, desc);
    }

    // OpenGLBuffer implementation
    OpenGLBuffer::OpenGLBuffer(uint32_t id, const BufferDesc &desc)
        : _id(id), _desc(desc), _glBuffer(0)
    {
        createBuffer();
    }

    OpenGLBuffer::~OpenGLBuffer()
    {
        // 简化实现：不调用OpenGL函数
    }

    bool OpenGLBuffer::createBuffer()
    {
        // 简化实现：使用立即模式OpenGL，不需要真正的缓冲区对象
        _glBuffer = 1; // 假的缓冲区ID
        std::cout << "[OpenGLBuffer] Buffer created successfully (immediate mode)" << std::endl;
        return true;
    }

    BackendTexture *OpenGLBackend::createTexture(const TextureDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created texture " << id
                  << " (" << desc.width << "x" << desc.height << ")" << std::endl;

        return new OpenGLTexture(id, desc);
    }

    BackendShader *OpenGLBackend::createShader(const ShaderDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created shader " << id
                  << " (stage: " << static_cast<int>(desc.stage) << ")" << std::endl;

        return new OpenGLShader(id, desc);
    }

    // OpenGLShader implementation
    OpenGLShader::OpenGLShader(uint32_t id, const ShaderDesc &desc)
        : _id(id), _desc(desc), _glShader(0)
    {
        compileShader();
    }

    OpenGLShader::~OpenGLShader()
    {
        // 简化实现：不调用OpenGL函数
    }

    bool OpenGLShader::compileShader()
    {
        // 简化实现：使用立即模式OpenGL，不需要真正的着色器
        _glShader = 1; // 假的着色器ID
        std::cout << "[OpenGLShader] Shader compiled successfully (immediate mode)" << std::endl;
        return true;
    }

    BackendPipeline *OpenGLBackend::createPipeline(const PipelineDesc &desc)
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created pipeline " << id << std::endl;

        return new OpenGLPipeline(id, desc);
    }

    // OpenGLPipeline implementation
    OpenGLPipeline::OpenGLPipeline(uint32_t id, const PipelineDesc &desc)
        : _id(id), _desc(desc), _glProgram(0)
    {
        linkProgram();
    }

    OpenGLPipeline::~OpenGLPipeline()
    {
        // 简化实现：不调用OpenGL函数
    }

    bool OpenGLPipeline::linkProgram()
    {
        // 简化实现：使用立即模式OpenGL，不需要真正的程序对象
        _glProgram = 1; // 假的程序ID
        std::cout << "[OpenGLPipeline] Program linked successfully (immediate mode)" << std::endl;
        return true;
    }

    BackendDescriptorSet *OpenGLBackend::createDescriptorSet()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created descriptor set " << id << std::endl;
        return new OpenGLDescriptorSet();
    }

    BackendCommandList *OpenGLBackend::createCommandList()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created command list " << id << std::endl;
        return new OpenGLCommandList();
    }

    BackendFence *OpenGLBackend::createFence()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created fence " << id << std::endl;
        return new OpenGLFence();
    }

    BackendSemaphore *OpenGLBackend::createSemaphore()
    {
        uint32_t id = ++_resourceCounter;
        std::cout << "[OpenGLBackend] Created semaphore " << id << std::endl;
        return new OpenGLSemaphore();
    }

    void OpenGLBackend::executeCommandList(BackendCommandList *cmdList,
                                           const std::vector<BackendSemaphore *> &waitSemaphores,
                                           const std::vector<BackendSemaphore *> &signalSemaphores,
                                           BackendFence *fence)
    {
        std::cout << "[OpenGLBackend] Executing command list" << std::endl;
        // OpenGL是立即模式，命令直接执行
    }

    // 资源销毁方法
    void OpenGLBackend::destroyBuffer(BackendBuffer *buffer)
    {
        if (auto glBuffer = dynamic_cast<OpenGLBuffer *>(buffer))
        {
            uint32_t id = glBuffer->getId();
            std::cout << "[OpenGLBackend] Destroyed buffer (ID: " << id << ")" << std::endl;
        }
        delete buffer;
    }

    void OpenGLBackend::destroyTexture(BackendTexture *texture)
    {
        if (auto glTexture = dynamic_cast<OpenGLTexture *>(texture))
        {
            uint32_t id = glTexture->getId();
            std::cout << "[OpenGLBackend] Destroyed texture (ID: " << id << ")" << std::endl;
        }
        delete texture;
    }

    void OpenGLBackend::destroyShader(BackendShader *shader)
    {
        if (auto glShader = dynamic_cast<OpenGLShader *>(shader))
        {
            uint32_t id = glShader->getId();
            std::cout << "[OpenGLBackend] Destroyed shader (ID: " << id << ")" << std::endl;
        }
        delete shader;
    }

    void OpenGLBackend::destroyPipeline(BackendPipeline *pipeline)
    {
        if (auto glPipeline = dynamic_cast<OpenGLPipeline *>(pipeline))
        {
            uint32_t id = glPipeline->getId();
            std::cout << "[OpenGLBackend] Destroyed pipeline (ID: " << id << ")" << std::endl;
        }
        delete pipeline;
    }

    void OpenGLBackend::destroyDescriptorSet(BackendDescriptorSet *descriptorSet)
    {
        std::cout << "[OpenGLBackend] Destroyed descriptor set" << std::endl;
        delete descriptorSet;
    }

    void OpenGLBackend::destroyCommandList(BackendCommandList *cmdList)
    {
        std::cout << "[OpenGLBackend] Destroyed command list" << std::endl;
        delete cmdList;
    }

    void OpenGLBackend::destroyFence(BackendFence *fence)
    {
        std::cout << "[OpenGLBackend] Destroyed fence" << std::endl;
        delete fence;
    }

    void OpenGLBackend::destroySemaphore(BackendSemaphore *semaphore)
    {
        std::cout << "[OpenGLBackend] Destroyed semaphore" << std::endl;
        delete semaphore;
    }

    OpenGLTexture::OpenGLTexture(uint32_t id, const TextureDesc &desc)
        : _id(id), _desc(desc)
    {
    }

    OpenGLTexture::~OpenGLTexture()
    {
        // 资源由后端管理，这里不删除
    }

    OpenGLDescriptorSet::OpenGLDescriptorSet()
    {
    }

    OpenGLDescriptorSet::~OpenGLDescriptorSet()
    {
    }

    OpenGLCommandList::OpenGLCommandList()
    {
    }

    OpenGLCommandList::~OpenGLCommandList()
    {
    }

    void OpenGLCommandList::beginRenderPass(const RenderPassDesc &desc)
    {
        _inRenderPass = true;

        // 不在这里清除缓冲区，避免重复清除导致闪烁
        // 缓冲区清除已经在beginFrame()中完成

        std::cout << "[OpenGLCommandList] Begin render pass" << std::endl;
    }

    void OpenGLCommandList::setPipeline(BackendPipeline *pipeline)
    {
        if (pipeline)
        {
            OpenGLPipeline *glPipeline = static_cast<OpenGLPipeline *>(pipeline);
            _currentProgram = glPipeline->getGLProgram();
            std::cout << "[OpenGLCommandList] Set pipeline (immediate mode)" << std::endl;
        }
        else
        {
            std::cout << "[OpenGLCommandList] Set pipeline (null)" << std::endl;
        }
    }

    void OpenGLCommandList::setVertexBuffer(BackendBuffer *buffer, uint32_t binding, size_t offset)
    {
        std::cout << "[OpenGLCommandList] Set vertex buffer (immediate mode)" << std::endl;
    }

    void OpenGLCommandList::setIndexBuffer(BackendBuffer *buffer, IndexFormat format, uint64_t offset)
    {
        std::cout << "[OpenGLCommandList] Set index buffer (immediate mode)" << std::endl;
    }

    void OpenGLCommandList::draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance)
    {
        // 使用立即模式OpenGL绘制三角形
        if (vertexCount == 3)
        {
            // 添加旋转动画
            static float triangleRotation = 0.0f;
            triangleRotation += 2.0f; // 每帧增加2度

            glPushMatrix();
            glTranslatef(-1.0f, 0.0f, 0.0f);               // 向左移动
            glRotatef(triangleRotation, 0.0f, 0.0f, 1.0f); // 绕Z轴旋转

            // 使用立即模式渲染
            glBegin(GL_TRIANGLES);
            glColor3f(1.0f, 0.0f, 0.0f);
            glVertex3f(0.0f, 0.5f, 0.0f);
            glColor3f(0.0f, 1.0f, 0.0f);
            glVertex3f(-0.5f, -0.5f, 0.0f);
            glColor3f(0.0f, 0.0f, 1.0f);
            glVertex3f(0.5f, -0.5f, 0.0f);
            glEnd();

            glPopMatrix();
        }

        std::cout << "[OpenGLCommandList] Draw " << vertexCount << " vertices (immediate mode)" << std::endl;
    }

    void OpenGLCommandList::drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t vertexOffset, uint32_t firstInstance)
    {
        // 使用立即模式OpenGL绘制三角形
        if (indexCount == 3)
        {
            // 添加旋转动画
            static float triangleRotation = 0.0f;
            triangleRotation += 2.0f; // 每帧增加2度

            glPushMatrix();
            glTranslatef(-1.0f, 0.0f, 0.0f);               // 向左移动
            glRotatef(triangleRotation, 0.0f, 0.0f, 1.0f); // 绕Z轴旋转

            // 使用立即模式渲染彩色三角形
            glBegin(GL_TRIANGLES);
            glColor3f(1.0f, 0.0f, 0.0f); // 红色
            glVertex3f(0.0f, 0.5f, 0.0f);
            glColor3f(0.0f, 1.0f, 0.0f); // 绿色
            glVertex3f(-0.5f, -0.5f, 0.0f);
            glColor3f(0.0f, 0.0f, 1.0f); // 蓝色
            glVertex3f(0.5f, -0.5f, 0.0f);
            glEnd();

            glPopMatrix();
        }
        // 使用立即模式OpenGL绘制立方体
        else if (indexCount == 36)
        {
            // 添加旋转动画
            static float rotation = 0.0f;
            rotation += 1.0f; // 每帧增加1度

            glPushMatrix();
            glTranslatef(1.0f, 0.0f, 0.0f);               // 向右移动
            glRotatef(rotation, 0.0f, 1.0f, 0.0f);        // 绕Y轴旋转
            glRotatef(rotation * 0.5f, 1.0f, 0.0f, 0.0f); // 绕X轴旋转（慢一些）

            // 使用立即模式渲染一个简单的立方体
            glBegin(GL_TRIANGLES);

            // 前面 (红色)
            glColor3f(1.0f, 0.0f, 0.0f);
            glVertex3f(-0.3f, -0.3f, 0.3f);
            glVertex3f(0.3f, -0.3f, 0.3f);
            glVertex3f(0.3f, 0.3f, 0.3f);
            glVertex3f(0.3f, 0.3f, 0.3f);
            glVertex3f(-0.3f, 0.3f, 0.3f);
            glVertex3f(-0.3f, -0.3f, 0.3f);

            // 后面 (绿色)
            glColor3f(0.0f, 1.0f, 0.0f);
            glVertex3f(-0.3f, -0.3f, -0.3f);
            glVertex3f(-0.3f, 0.3f, -0.3f);
            glVertex3f(0.3f, 0.3f, -0.3f);
            glVertex3f(0.3f, 0.3f, -0.3f);
            glVertex3f(0.3f, -0.3f, -0.3f);
            glVertex3f(-0.3f, -0.3f, -0.3f);

            // 左面 (蓝色)
            glColor3f(0.0f, 0.0f, 1.0f);
            glVertex3f(-0.3f, 0.3f, 0.3f);
            glVertex3f(-0.3f, 0.3f, -0.3f);
            glVertex3f(-0.3f, -0.3f, -0.3f);
            glVertex3f(-0.3f, -0.3f, -0.3f);
            glVertex3f(-0.3f, -0.3f, 0.3f);
            glVertex3f(-0.3f, 0.3f, 0.3f);

            // 右面 (黄色)
            glColor3f(1.0f, 1.0f, 0.0f);
            glVertex3f(0.3f, 0.3f, 0.3f);
            glVertex3f(0.3f, -0.3f, -0.3f);
            glVertex3f(0.3f, 0.3f, -0.3f);
            glVertex3f(0.3f, -0.3f, -0.3f);
            glVertex3f(0.3f, 0.3f, 0.3f);
            glVertex3f(0.3f, -0.3f, 0.3f);

            // 底面 (紫色)
            glColor3f(1.0f, 0.0f, 1.0f);
            glVertex3f(-0.3f, -0.3f, -0.3f);
            glVertex3f(0.3f, -0.3f, -0.3f);
            glVertex3f(0.3f, -0.3f, 0.3f);
            glVertex3f(0.3f, -0.3f, 0.3f);
            glVertex3f(-0.3f, -0.3f, 0.3f);
            glVertex3f(-0.3f, -0.3f, -0.3f);

            // 顶面 (青色)
            glColor3f(0.0f, 1.0f, 1.0f);
            glVertex3f(-0.3f, 0.3f, -0.3f);
            glVertex3f(-0.3f, 0.3f, 0.3f);
            glVertex3f(0.3f, 0.3f, 0.3f);
            glVertex3f(0.3f, 0.3f, 0.3f);
            glVertex3f(0.3f, 0.3f, -0.3f);
            glVertex3f(-0.3f, 0.3f, -0.3f);

            glEnd();
            glPopMatrix();
        }

        std::cout << "[OpenGLCommandList] Draw indexed " << indexCount << " indices (immediate mode)" << std::endl;
    }

} // namespace USG
