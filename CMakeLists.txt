cmake_minimum_required(VERSION 3.1...3.25)
project(
	LearnWebGPU
	VERSION 0.1.0
	LANGUAGES CXX C
)

include(utils.cmake)

# We add an option to enable different settings when developping the app than
# when distributing it.
option(DEV_MODE "Set up development helper settings" ON)

# WebAssembly specific settings
if (CMAKE_SYSTEM_NAME STREQUAL "Emscripten")
    message(STATUS "Building for WebAssembly")
    
    # Set C++20 standard for WebAssembly
    set(CMAKE_CXX_STANDARD 20)
    set(CMAKE_CXX_STANDARD_REQUIRED ON)
    set(CMAKE_CXX_EXTENSIONS OFF)
    
    # Set UTF-8 encoding for MSVC
    if (MSVC)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /utf-8")
    endif()
    
    # Set output name for WebAssembly
    set(CMAKE_EXECUTABLE_SUFFIX ".html")
    
else()
    message(WARNING "Not building for WebAssembly")
    # 添加非 WebAssembly 环境下的编译选项
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DNOT_WEBASSEMBLY")
    add_subdirectory(glfw)
endif()

add_subdirectory(webgpu)
add_subdirectory(glfw3webgpu)
add_subdirectory(imgui)

add_executable(App
	main.cpp
	Application.h
	Application.cpp
	ResourceManager.h
	ResourceManager.cpp
	implementations.cpp
)

if(DEV_MODE)
	# In dev mode, we load resources from the source tree, so that when we
	# dynamically edit resources (like shaders), these are correctly
	# versionned.
	target_compile_definitions(App PRIVATE
		RESOURCE_DIR="${CMAKE_CURRENT_SOURCE_DIR}/resources"
	)
else()
	# In release mode, we just load resources relatively to wherever the
	# executable is launched from, so that the binary is portable
	target_compile_definitions(App PRIVATE
		RESOURCE_DIR="./resources"
	)
endif()

target_include_directories(App PRIVATE .)

target_link_libraries(App PRIVATE glfw webgpu glfw3webgpu imgui)

# If building with Emscripten, add specific link options
if (CMAKE_SYSTEM_NAME STREQUAL "Emscripten")
    target_link_options(App PRIVATE
        -sUSE_GLFW=3
        -sALLOW_MEMORY_GROWTH=1
        -sMAXIMUM_MEMORY=4GB
        "-sEXPORTED_RUNTIME_METHODS=[\"ccall\",\"cwrap\"]"
        "-sEXPORTED_FUNCTIONS=[\"_main\"]"
        -sASYNCIFY=1
        -sASYNCIFY_STACK_SIZE=65536
        --bind
    )
endif()

# Set C++20 standard for all targets
set_target_properties(App PROPERTIES
	CXX_STANDARD 20
	CXX_STANDARD_REQUIRED ON
	CXX_EXTENSIONS OFF
)

target_treat_all_warnings_as_errors(App)
target_copy_webgpu_binaries(App)

if (MSVC)
	# Ignore a warning that GLM requires to bypass
	# Disable warning C4201: nonstandard extension used: nameless struct/union
	target_compile_options(App PUBLIC /wd4201)
	# Disable warning C4305: truncation from 'int' to 'bool' in 'if' condition
	target_compile_options(App PUBLIC /wd4305)

	# Ignore a warning that stb_image requires to bypass
	# Disable warning C4244: conversion from 'int' to 'short', possible loss of data
	target_compile_options(App PUBLIC /wd4244)
endif (MSVC)

# WebAssembly specific post-build commands
if (CMAKE_SYSTEM_NAME STREQUAL "Emscripten")
    # Copy output files to redist_wasm directory
    add_custom_command(TARGET App POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_CURRENT_SOURCE_DIR}/redist_wasm"
        COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:App> "${CMAKE_CURRENT_SOURCE_DIR}/redist_wasm/"
        COMMAND ${CMAKE_COMMAND} -E copy_directory "${CMAKE_CURRENT_SOURCE_DIR}/resources" "${CMAKE_CURRENT_SOURCE_DIR}/redist_wasm/resources"
        COMMENT "Copying WebAssembly files to redist_wasm directory"
    )
endif()
