#include "MockBackend.h"
#include <iostream>
#include <thread>
#include <chrono>

namespace USG {

MockBackend::MockBackend(BackendType type, const std::string& name) 
    : _backendType(type), _backendName(name) {
}

MockBackend::~MockBackend() {
    cleanup();
}

bool MockBackend::initialize(const BackendConfig& config) {
    if (_initialized) {
        return true;
    }
    
    std::cout << "[" << _backendName << "] Initializing mock backend..." << std::endl;
    
    // 模拟初始化延迟
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    std::cout << "[" << _backendName << "] Application: " << config.applicationName << std::endl;
    std::cout << "[" << _backendName << "] Swapchain: " << config.swapchainWidth 
              << "x" << config.swapchainHeight << std::endl;
    std::cout << "[" << _backendName << "] VSync: " << (config.vsyncEnabled ? "Enabled" : "Disabled") << std::endl;
    
    _initialized = true;
    
    std::cout << "[" << _backendName << "] Mock backend initialized successfully" << std::endl;
    return true;
}

void MockBackend::cleanup() {
    if (!_initialized) {
        return;
    }
    
    std::cout << "[" << _backendName << "] Cleaning up mock backend..." << std::endl;
    
    // 模拟清理延迟
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    
    _initialized = false;
    _resourceCounter = 0;
    
    std::cout << "[" << _backendName << "] Mock backend cleaned up" << std::endl;
}

// 资源创建方法
BackendBuffer* MockBackend::createBuffer(const BufferDesc& desc) {
    uint32_t id = ++_resourceCounter;
    std::cout << "[" << _backendName << "] Created buffer " << id 
              << " (size: " << desc.size << " bytes)" << std::endl;
    return new MockBuffer(id, desc);
}

BackendTexture* MockBackend::createTexture(const TextureDesc& desc) {
    uint32_t id = ++_resourceCounter;
    std::cout << "[" << _backendName << "] Created texture " << id 
              << " (" << desc.width << "x" << desc.height << ")" << std::endl;
    return new MockTexture(id, desc);
}

BackendShader* MockBackend::createShader(const ShaderDesc& desc) {
    uint32_t id = ++_resourceCounter;
    std::cout << "[" << _backendName << "] Created shader " << id 
              << " (stage: " << static_cast<int>(desc.stage) << ")" << std::endl;
    return new MockShader(id, desc);
}

BackendPipeline* MockBackend::createPipeline(const PipelineDesc& desc) {
    uint32_t id = ++_resourceCounter;
    std::cout << "[" << _backendName << "] Created pipeline " << id << std::endl;
    return new MockPipeline(id, desc);
}

BackendDescriptorSet* MockBackend::createDescriptorSet() {
    uint32_t id = ++_resourceCounter;
    std::cout << "[" << _backendName << "] Created descriptor set " << id << std::endl;
    return new MockDescriptorSet(id);
}

BackendCommandList* MockBackend::createCommandList() {
    uint32_t id = ++_resourceCounter;
    std::cout << "[" << _backendName << "] Created command list " << id << std::endl;
    return new MockCommandList(id);
}

BackendFence* MockBackend::createFence() {
    uint32_t id = ++_resourceCounter;
    std::cout << "[" << _backendName << "] Created fence " << id << std::endl;
    return new MockFence(id);
}

BackendSemaphore* MockBackend::createSemaphore() {
    uint32_t id = ++_resourceCounter;
    std::cout << "[" << _backendName << "] Created semaphore " << id << std::endl;
    return new MockSemaphore(id);
}

void MockBackend::executeCommandList(BackendCommandList* cmdList,
                                    const std::vector<BackendSemaphore*>& waitSemaphores,
                                    const std::vector<BackendSemaphore*>& signalSemaphores,
                                    BackendFence* fence) {
    std::cout << "[" << _backendName << "] Executing command list";
    if (fence) {
        std::cout << " with fence";
    }
    std::cout << std::endl;
    
    // 模拟执行延迟
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
}

// 资源销毁方法
void MockBackend::destroyBuffer(BackendBuffer* buffer) {
    if (auto mockBuffer = dynamic_cast<MockBuffer*>(buffer)) {
        std::cout << "[" << _backendName << "] Destroyed buffer " << mockBuffer->getId() << std::endl;
    }
    delete buffer;
}

void MockBackend::destroyTexture(BackendTexture* texture) {
    if (auto mockTexture = dynamic_cast<MockTexture*>(texture)) {
        std::cout << "[" << _backendName << "] Destroyed texture " << mockTexture->getId() << std::endl;
    }
    delete texture;
}

void MockBackend::destroyShader(BackendShader* shader) {
    if (auto mockShader = dynamic_cast<MockShader*>(shader)) {
        std::cout << "[" << _backendName << "] Destroyed shader " << mockShader->getId() << std::endl;
    }
    delete shader;
}

void MockBackend::destroyPipeline(BackendPipeline* pipeline) {
    if (auto mockPipeline = dynamic_cast<MockPipeline*>(pipeline)) {
        std::cout << "[" << _backendName << "] Destroyed pipeline " << mockPipeline->getId() << std::endl;
    }
    delete pipeline;
}

void MockBackend::destroyDescriptorSet(BackendDescriptorSet* descriptorSet) {
    if (auto mockDescriptorSet = dynamic_cast<MockDescriptorSet*>(descriptorSet)) {
        std::cout << "[" << _backendName << "] Destroyed descriptor set " << mockDescriptorSet->getId() << std::endl;
    }
    delete descriptorSet;
}

void MockBackend::destroyCommandList(BackendCommandList* cmdList) {
    if (auto mockCommandList = dynamic_cast<MockCommandList*>(cmdList)) {
        std::cout << "[" << _backendName << "] Destroyed command list " << mockCommandList->getId() << std::endl;
    }
    delete cmdList;
}

void MockBackend::destroyFence(BackendFence* fence) {
    if (auto mockFence = dynamic_cast<MockFence*>(fence)) {
        std::cout << "[" << _backendName << "] Destroyed fence " << mockFence->getId() << std::endl;
    }
    delete fence;
}

void MockBackend::destroySemaphore(BackendSemaphore* semaphore) {
    if (auto mockSemaphore = dynamic_cast<MockSemaphore*>(semaphore)) {
        std::cout << "[" << _backendName << "] Destroyed semaphore " << mockSemaphore->getId() << std::endl;
    }
    delete semaphore;
}

} // namespace USG
