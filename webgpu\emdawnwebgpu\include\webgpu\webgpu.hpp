/**
 * This file is part of the "Learn WebGPU for C++" book.
 *   https://github.com/eliemichel/LearnWebGPU
 *
 * MIT License
 * Copyright (c) 2022-2025 <PERSON><PERSON>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

/**
 * Exactly one of your source files must #define WEBGPU_CPP_IMPLEMENTATION
 * before including this header.
 * 
 * NB: This file has been generated by the webgpu-cpp generator
 *   (see https://github.com/eliemichel/webgpu-cpp )
 */

#pragma once

#include <webgpu/webgpu.h>

#include <iostream>
#include <vector>
#include <functional>
#include <cassert>
#include <cmath>
#include <memory>
#include <string_view>

#if __EMSCRIPTEN__
#include <emscripten.h>
#endif

#ifdef _MSVC_LANG
#  if _MSVC_LANG >= 202002L
#   define NO_DISCARD [[nodiscard("You should keep this handle alive for as long as the callback may get invoked.")]]
#  elif _MSVC_LANG >= 201703L
#   define NO_DISCARD [[nodiscard]]
#  else
#   define NO_DISCARD
#  endif
#else
#  if __cplusplus >= 202002L
#    define NO_DISCARD [[nodiscard("You should keep this handle alive for as long as the callback may get invoked.")]]
#  elif __cplusplus >= 201703L
#    define NO_DISCARD [[nodiscard]]
#  else
#    define NO_DISCARD
#  endif
#endif

/**
 * A namespace providing a more C++ idiomatic API to WebGPU.
 */
namespace wgpu {

struct DefaultFlag {};
constexpr DefaultFlag Default;

#define HANDLE(Type) \
class Type { \
public: \
	typedef Type S; /* S == Self */ \
	typedef WGPU ## Type W; /* W == WGPU Type */ \
	Type() : m_raw(nullptr) {} \
	Type(const W& w) : m_raw(w) {} \
	operator W&() { return m_raw; } \
	operator const W&() const { return m_raw; } \
	operator bool() const { return m_raw != nullptr; } \
	bool operator==(const Type& other) const { return m_raw == other.m_raw; } \
	bool operator!=(const Type& other) const { return m_raw != other.m_raw; } \
	bool operator==(const W& other) const { return m_raw == other; } \
	bool operator!=(const W& other) const { return m_raw != other; } \
	friend auto operator<<(std::ostream &stream, const S& self) -> std::ostream & { \
		return stream << "<wgpu::" << #Type << " " << self.m_raw << ">"; \
	} \
private: \
	W m_raw; \
public:

#define DESCRIPTOR(Type) \
struct Type : public WGPU ## Type { \
public: \
	typedef Type S; /* S == Self */ \
	typedef WGPU ## Type W; /* W == WGPU Type */ \
	Type() : W() { nextInChain = nullptr; } \
	Type(const W &other) : W(other) { nextInChain = nullptr; } \
	Type(const DefaultFlag &) : W() { setDefault(); } \
	Type& operator=(const DefaultFlag &) { setDefault(); return *this; } \
	friend auto operator<<(std::ostream &stream, const S&) -> std::ostream & { \
		return stream << "<wgpu::" << #Type << ">"; \
	} \
public:

#define STRUCT_NO_OSTREAM(Type) \
struct Type : public WGPU ## Type { \
public: \
	typedef Type S; /* S == Self */ \
	typedef WGPU ## Type W; /* W == WGPU Type */ \
	Type() : W() {} \
	Type(const W &other) : W(other) {} \
	Type(const DefaultFlag &) : W() { setDefault(); } \
	Type& operator=(const DefaultFlag &) { setDefault(); return *this; } \
public:

#define STRUCT(Type) \
STRUCT_NO_OSTREAM(Type) \
	friend auto operator<<(std::ostream &stream, const S&) -> std::ostream & { \
		return stream << "<wgpu::" << #Type << ">"; \
	} \
public:

#define ENUM(Type) \
class Type { \
public: \
	typedef Type S; /* S == Self */ \
	typedef WGPU ## Type W; /* W == WGPU Type */ \
	constexpr Type() : m_raw(W{}) {} /* Using default value-initialization */ \
	constexpr Type(const W& w) : m_raw(w) {} \
	constexpr operator W() const { return m_raw; } \
	W m_raw; /* Ideally, this would be private, but then types generated with this macro would not be structural. */

#define ENUM_ENTRY(Name, Value) \
	static constexpr W Name = (W)(Value);

#define END };



// Other type aliases
using Flags = uint64_t;
using Bool = uint32_t;
using RenderPassDescriptorMaxDrawCount = WGPURenderPassMaxDrawCount;
using ShaderModuleSPIRVDescriptor = WGPUShaderSourceSPIRV;
using ShaderModuleWGSLDescriptor = WGPUShaderSourceWGSL;

// Enumerations
ENUM(AdapterType)
	ENUM_ENTRY(DiscreteGPU, WGPUAdapterType_DiscreteGPU)
	ENUM_ENTRY(IntegratedGPU, WGPUAdapterType_IntegratedGPU)
	ENUM_ENTRY(CPU, WGPUAdapterType_CPU)
	ENUM_ENTRY(Unknown, WGPUAdapterType_Unknown)
	ENUM_ENTRY(Force32, WGPUAdapterType_Force32)
END
ENUM(AddressMode)
	ENUM_ENTRY(Undefined, WGPUAddressMode_Undefined)
	ENUM_ENTRY(ClampToEdge, WGPUAddressMode_ClampToEdge)
	ENUM_ENTRY(Repeat, WGPUAddressMode_Repeat)
	ENUM_ENTRY(MirrorRepeat, WGPUAddressMode_MirrorRepeat)
	ENUM_ENTRY(Force32, WGPUAddressMode_Force32)
END
ENUM(BackendType)
	ENUM_ENTRY(Undefined, WGPUBackendType_Undefined)
	ENUM_ENTRY(Null, WGPUBackendType_Null)
	ENUM_ENTRY(WebGPU, WGPUBackendType_WebGPU)
	ENUM_ENTRY(D3D11, WGPUBackendType_D3D11)
	ENUM_ENTRY(D3D12, WGPUBackendType_D3D12)
	ENUM_ENTRY(Metal, WGPUBackendType_Metal)
	ENUM_ENTRY(Vulkan, WGPUBackendType_Vulkan)
	ENUM_ENTRY(OpenGL, WGPUBackendType_OpenGL)
	ENUM_ENTRY(OpenGLES, WGPUBackendType_OpenGLES)
	ENUM_ENTRY(Force32, WGPUBackendType_Force32)
END
ENUM(BlendFactor)
	ENUM_ENTRY(Undefined, WGPUBlendFactor_Undefined)
	ENUM_ENTRY(Zero, WGPUBlendFactor_Zero)
	ENUM_ENTRY(One, WGPUBlendFactor_One)
	ENUM_ENTRY(Src, WGPUBlendFactor_Src)
	ENUM_ENTRY(OneMinusSrc, WGPUBlendFactor_OneMinusSrc)
	ENUM_ENTRY(SrcAlpha, WGPUBlendFactor_SrcAlpha)
	ENUM_ENTRY(OneMinusSrcAlpha, WGPUBlendFactor_OneMinusSrcAlpha)
	ENUM_ENTRY(Dst, WGPUBlendFactor_Dst)
	ENUM_ENTRY(OneMinusDst, WGPUBlendFactor_OneMinusDst)
	ENUM_ENTRY(DstAlpha, WGPUBlendFactor_DstAlpha)
	ENUM_ENTRY(OneMinusDstAlpha, WGPUBlendFactor_OneMinusDstAlpha)
	ENUM_ENTRY(SrcAlphaSaturated, WGPUBlendFactor_SrcAlphaSaturated)
	ENUM_ENTRY(Constant, WGPUBlendFactor_Constant)
	ENUM_ENTRY(OneMinusConstant, WGPUBlendFactor_OneMinusConstant)
	ENUM_ENTRY(Src1, WGPUBlendFactor_Src1)
	ENUM_ENTRY(OneMinusSrc1, WGPUBlendFactor_OneMinusSrc1)
	ENUM_ENTRY(Src1Alpha, WGPUBlendFactor_Src1Alpha)
	ENUM_ENTRY(OneMinusSrc1Alpha, WGPUBlendFactor_OneMinusSrc1Alpha)
	ENUM_ENTRY(Force32, WGPUBlendFactor_Force32)
END
ENUM(BlendOperation)
	ENUM_ENTRY(Undefined, WGPUBlendOperation_Undefined)
	ENUM_ENTRY(Add, WGPUBlendOperation_Add)
	ENUM_ENTRY(Subtract, WGPUBlendOperation_Subtract)
	ENUM_ENTRY(ReverseSubtract, WGPUBlendOperation_ReverseSubtract)
	ENUM_ENTRY(Min, WGPUBlendOperation_Min)
	ENUM_ENTRY(Max, WGPUBlendOperation_Max)
	ENUM_ENTRY(Force32, WGPUBlendOperation_Force32)
END
ENUM(BufferBindingType)
	ENUM_ENTRY(BindingNotUsed, WGPUBufferBindingType_BindingNotUsed)
	ENUM_ENTRY(Undefined, WGPUBufferBindingType_Undefined)
	ENUM_ENTRY(Uniform, WGPUBufferBindingType_Uniform)
	ENUM_ENTRY(Storage, WGPUBufferBindingType_Storage)
	ENUM_ENTRY(ReadOnlyStorage, WGPUBufferBindingType_ReadOnlyStorage)
	ENUM_ENTRY(Force32, WGPUBufferBindingType_Force32)
END
ENUM(BufferMapState)
	ENUM_ENTRY(Unmapped, WGPUBufferMapState_Unmapped)
	ENUM_ENTRY(Pending, WGPUBufferMapState_Pending)
	ENUM_ENTRY(Mapped, WGPUBufferMapState_Mapped)
	ENUM_ENTRY(Force32, WGPUBufferMapState_Force32)
END
ENUM(CallbackMode)
	ENUM_ENTRY(WaitAnyOnly, WGPUCallbackMode_WaitAnyOnly)
	ENUM_ENTRY(AllowProcessEvents, WGPUCallbackMode_AllowProcessEvents)
	ENUM_ENTRY(AllowSpontaneous, WGPUCallbackMode_AllowSpontaneous)
	ENUM_ENTRY(Force32, WGPUCallbackMode_Force32)
END
ENUM(CompareFunction)
	ENUM_ENTRY(Undefined, WGPUCompareFunction_Undefined)
	ENUM_ENTRY(Never, WGPUCompareFunction_Never)
	ENUM_ENTRY(Less, WGPUCompareFunction_Less)
	ENUM_ENTRY(Equal, WGPUCompareFunction_Equal)
	ENUM_ENTRY(LessEqual, WGPUCompareFunction_LessEqual)
	ENUM_ENTRY(Greater, WGPUCompareFunction_Greater)
	ENUM_ENTRY(NotEqual, WGPUCompareFunction_NotEqual)
	ENUM_ENTRY(GreaterEqual, WGPUCompareFunction_GreaterEqual)
	ENUM_ENTRY(Always, WGPUCompareFunction_Always)
	ENUM_ENTRY(Force32, WGPUCompareFunction_Force32)
END
ENUM(CompilationInfoRequestStatus)
	ENUM_ENTRY(Success, WGPUCompilationInfoRequestStatus_Success)
	ENUM_ENTRY(CallbackCancelled, WGPUCompilationInfoRequestStatus_CallbackCancelled)
	ENUM_ENTRY(Force32, WGPUCompilationInfoRequestStatus_Force32)
END
ENUM(CompilationMessageType)
	ENUM_ENTRY(Error, WGPUCompilationMessageType_Error)
	ENUM_ENTRY(Warning, WGPUCompilationMessageType_Warning)
	ENUM_ENTRY(Info, WGPUCompilationMessageType_Info)
	ENUM_ENTRY(Force32, WGPUCompilationMessageType_Force32)
END
ENUM(CompositeAlphaMode)
	ENUM_ENTRY(Auto, WGPUCompositeAlphaMode_Auto)
	ENUM_ENTRY(Opaque, WGPUCompositeAlphaMode_Opaque)
	ENUM_ENTRY(Premultiplied, WGPUCompositeAlphaMode_Premultiplied)
	ENUM_ENTRY(Unpremultiplied, WGPUCompositeAlphaMode_Unpremultiplied)
	ENUM_ENTRY(Inherit, WGPUCompositeAlphaMode_Inherit)
	ENUM_ENTRY(Force32, WGPUCompositeAlphaMode_Force32)
END
ENUM(CreatePipelineAsyncStatus)
	ENUM_ENTRY(Success, WGPUCreatePipelineAsyncStatus_Success)
	ENUM_ENTRY(CallbackCancelled, WGPUCreatePipelineAsyncStatus_CallbackCancelled)
	ENUM_ENTRY(ValidationError, WGPUCreatePipelineAsyncStatus_ValidationError)
	ENUM_ENTRY(InternalError, WGPUCreatePipelineAsyncStatus_InternalError)
	ENUM_ENTRY(Force32, WGPUCreatePipelineAsyncStatus_Force32)
END
ENUM(CullMode)
	ENUM_ENTRY(Undefined, WGPUCullMode_Undefined)
	ENUM_ENTRY(None, WGPUCullMode_None)
	ENUM_ENTRY(Front, WGPUCullMode_Front)
	ENUM_ENTRY(Back, WGPUCullMode_Back)
	ENUM_ENTRY(Force32, WGPUCullMode_Force32)
END
ENUM(DeviceLostReason)
	ENUM_ENTRY(Unknown, WGPUDeviceLostReason_Unknown)
	ENUM_ENTRY(Destroyed, WGPUDeviceLostReason_Destroyed)
	ENUM_ENTRY(CallbackCancelled, WGPUDeviceLostReason_CallbackCancelled)
	ENUM_ENTRY(FailedCreation, WGPUDeviceLostReason_FailedCreation)
	ENUM_ENTRY(Force32, WGPUDeviceLostReason_Force32)
END
ENUM(ErrorFilter)
	ENUM_ENTRY(Validation, WGPUErrorFilter_Validation)
	ENUM_ENTRY(OutOfMemory, WGPUErrorFilter_OutOfMemory)
	ENUM_ENTRY(Internal, WGPUErrorFilter_Internal)
	ENUM_ENTRY(Force32, WGPUErrorFilter_Force32)
END
ENUM(ErrorType)
	ENUM_ENTRY(NoError, WGPUErrorType_NoError)
	ENUM_ENTRY(Validation, WGPUErrorType_Validation)
	ENUM_ENTRY(OutOfMemory, WGPUErrorType_OutOfMemory)
	ENUM_ENTRY(Internal, WGPUErrorType_Internal)
	ENUM_ENTRY(Unknown, WGPUErrorType_Unknown)
	ENUM_ENTRY(Force32, WGPUErrorType_Force32)
END
ENUM(FeatureLevel)
	ENUM_ENTRY(Undefined, WGPUFeatureLevel_Undefined)
	ENUM_ENTRY(Compatibility, WGPUFeatureLevel_Compatibility)
	ENUM_ENTRY(Core, WGPUFeatureLevel_Core)
	ENUM_ENTRY(Force32, WGPUFeatureLevel_Force32)
END
ENUM(FeatureName)
	ENUM_ENTRY(DepthClipControl, WGPUFeatureName_DepthClipControl)
	ENUM_ENTRY(Depth32FloatStencil8, WGPUFeatureName_Depth32FloatStencil8)
	ENUM_ENTRY(TimestampQuery, WGPUFeatureName_TimestampQuery)
	ENUM_ENTRY(TextureCompressionBC, WGPUFeatureName_TextureCompressionBC)
	ENUM_ENTRY(TextureCompressionBCSliced3D, WGPUFeatureName_TextureCompressionBCSliced3D)
	ENUM_ENTRY(TextureCompressionETC2, WGPUFeatureName_TextureCompressionETC2)
	ENUM_ENTRY(TextureCompressionASTC, WGPUFeatureName_TextureCompressionASTC)
	ENUM_ENTRY(TextureCompressionASTCSliced3D, WGPUFeatureName_TextureCompressionASTCSliced3D)
	ENUM_ENTRY(IndirectFirstInstance, WGPUFeatureName_IndirectFirstInstance)
	ENUM_ENTRY(ShaderF16, WGPUFeatureName_ShaderF16)
	ENUM_ENTRY(RG11B10UfloatRenderable, WGPUFeatureName_RG11B10UfloatRenderable)
	ENUM_ENTRY(BGRA8UnormStorage, WGPUFeatureName_BGRA8UnormStorage)
	ENUM_ENTRY(Float32Filterable, WGPUFeatureName_Float32Filterable)
	ENUM_ENTRY(Float32Blendable, WGPUFeatureName_Float32Blendable)
	ENUM_ENTRY(ClipDistances, WGPUFeatureName_ClipDistances)
	ENUM_ENTRY(DualSourceBlending, WGPUFeatureName_DualSourceBlending)
	ENUM_ENTRY(Subgroups, WGPUFeatureName_Subgroups)
	ENUM_ENTRY(CoreFeaturesAndLimits, WGPUFeatureName_CoreFeaturesAndLimits)
	ENUM_ENTRY(Unorm16TextureFormats, WGPUFeatureName_Unorm16TextureFormats)
	ENUM_ENTRY(Snorm16TextureFormats, WGPUFeatureName_Snorm16TextureFormats)
	ENUM_ENTRY(MultiDrawIndirect, WGPUFeatureName_MultiDrawIndirect)
	ENUM_ENTRY(Force32, WGPUFeatureName_Force32)
END
ENUM(FilterMode)
	ENUM_ENTRY(Undefined, WGPUFilterMode_Undefined)
	ENUM_ENTRY(Nearest, WGPUFilterMode_Nearest)
	ENUM_ENTRY(Linear, WGPUFilterMode_Linear)
	ENUM_ENTRY(Force32, WGPUFilterMode_Force32)
END
ENUM(FrontFace)
	ENUM_ENTRY(Undefined, WGPUFrontFace_Undefined)
	ENUM_ENTRY(CCW, WGPUFrontFace_CCW)
	ENUM_ENTRY(CW, WGPUFrontFace_CW)
	ENUM_ENTRY(Force32, WGPUFrontFace_Force32)
END
ENUM(IndexFormat)
	ENUM_ENTRY(Undefined, WGPUIndexFormat_Undefined)
	ENUM_ENTRY(Uint16, WGPUIndexFormat_Uint16)
	ENUM_ENTRY(Uint32, WGPUIndexFormat_Uint32)
	ENUM_ENTRY(Force32, WGPUIndexFormat_Force32)
END
ENUM(LoadOp)
	ENUM_ENTRY(Undefined, WGPULoadOp_Undefined)
	ENUM_ENTRY(Load, WGPULoadOp_Load)
	ENUM_ENTRY(Clear, WGPULoadOp_Clear)
	ENUM_ENTRY(Force32, WGPULoadOp_Force32)
END
ENUM(MapAsyncStatus)
	ENUM_ENTRY(Success, WGPUMapAsyncStatus_Success)
	ENUM_ENTRY(CallbackCancelled, WGPUMapAsyncStatus_CallbackCancelled)
	ENUM_ENTRY(Error, WGPUMapAsyncStatus_Error)
	ENUM_ENTRY(Aborted, WGPUMapAsyncStatus_Aborted)
	ENUM_ENTRY(Force32, WGPUMapAsyncStatus_Force32)
END
ENUM(MipmapFilterMode)
	ENUM_ENTRY(Undefined, WGPUMipmapFilterMode_Undefined)
	ENUM_ENTRY(Nearest, WGPUMipmapFilterMode_Nearest)
	ENUM_ENTRY(Linear, WGPUMipmapFilterMode_Linear)
	ENUM_ENTRY(Force32, WGPUMipmapFilterMode_Force32)
END
ENUM(OptionalBool)
	ENUM_ENTRY(False, WGPUOptionalBool_False)
	ENUM_ENTRY(True, WGPUOptionalBool_True)
	ENUM_ENTRY(Undefined, WGPUOptionalBool_Undefined)
	ENUM_ENTRY(Force32, WGPUOptionalBool_Force32)
END
ENUM(PopErrorScopeStatus)
	ENUM_ENTRY(Success, WGPUPopErrorScopeStatus_Success)
	ENUM_ENTRY(CallbackCancelled, WGPUPopErrorScopeStatus_CallbackCancelled)
	ENUM_ENTRY(Error, WGPUPopErrorScopeStatus_Error)
	ENUM_ENTRY(Force32, WGPUPopErrorScopeStatus_Force32)
END
ENUM(PowerPreference)
	ENUM_ENTRY(Undefined, WGPUPowerPreference_Undefined)
	ENUM_ENTRY(LowPower, WGPUPowerPreference_LowPower)
	ENUM_ENTRY(HighPerformance, WGPUPowerPreference_HighPerformance)
	ENUM_ENTRY(Force32, WGPUPowerPreference_Force32)
END
ENUM(PredefinedColorSpace)
	ENUM_ENTRY(SRGB, WGPUPredefinedColorSpace_SRGB)
	ENUM_ENTRY(DisplayP3, WGPUPredefinedColorSpace_DisplayP3)
	ENUM_ENTRY(Force32, WGPUPredefinedColorSpace_Force32)
END
ENUM(PresentMode)
	ENUM_ENTRY(Undefined, WGPUPresentMode_Undefined)
	ENUM_ENTRY(Fifo, WGPUPresentMode_Fifo)
	ENUM_ENTRY(FifoRelaxed, WGPUPresentMode_FifoRelaxed)
	ENUM_ENTRY(Immediate, WGPUPresentMode_Immediate)
	ENUM_ENTRY(Mailbox, WGPUPresentMode_Mailbox)
	ENUM_ENTRY(Force32, WGPUPresentMode_Force32)
END
ENUM(PrimitiveTopology)
	ENUM_ENTRY(Undefined, WGPUPrimitiveTopology_Undefined)
	ENUM_ENTRY(PointList, WGPUPrimitiveTopology_PointList)
	ENUM_ENTRY(LineList, WGPUPrimitiveTopology_LineList)
	ENUM_ENTRY(LineStrip, WGPUPrimitiveTopology_LineStrip)
	ENUM_ENTRY(TriangleList, WGPUPrimitiveTopology_TriangleList)
	ENUM_ENTRY(TriangleStrip, WGPUPrimitiveTopology_TriangleStrip)
	ENUM_ENTRY(Force32, WGPUPrimitiveTopology_Force32)
END
ENUM(QueryType)
	ENUM_ENTRY(Occlusion, WGPUQueryType_Occlusion)
	ENUM_ENTRY(Timestamp, WGPUQueryType_Timestamp)
	ENUM_ENTRY(Force32, WGPUQueryType_Force32)
END
ENUM(QueueWorkDoneStatus)
	ENUM_ENTRY(Success, WGPUQueueWorkDoneStatus_Success)
	ENUM_ENTRY(CallbackCancelled, WGPUQueueWorkDoneStatus_CallbackCancelled)
	ENUM_ENTRY(Error, WGPUQueueWorkDoneStatus_Error)
	ENUM_ENTRY(Force32, WGPUQueueWorkDoneStatus_Force32)
END
ENUM(RequestAdapterStatus)
	ENUM_ENTRY(Success, WGPURequestAdapterStatus_Success)
	ENUM_ENTRY(CallbackCancelled, WGPURequestAdapterStatus_CallbackCancelled)
	ENUM_ENTRY(Unavailable, WGPURequestAdapterStatus_Unavailable)
	ENUM_ENTRY(Error, WGPURequestAdapterStatus_Error)
	ENUM_ENTRY(Force32, WGPURequestAdapterStatus_Force32)
END
ENUM(RequestDeviceStatus)
	ENUM_ENTRY(Success, WGPURequestDeviceStatus_Success)
	ENUM_ENTRY(CallbackCancelled, WGPURequestDeviceStatus_CallbackCancelled)
	ENUM_ENTRY(Error, WGPURequestDeviceStatus_Error)
	ENUM_ENTRY(Force32, WGPURequestDeviceStatus_Force32)
END
ENUM(SamplerBindingType)
	ENUM_ENTRY(BindingNotUsed, WGPUSamplerBindingType_BindingNotUsed)
	ENUM_ENTRY(Undefined, WGPUSamplerBindingType_Undefined)
	ENUM_ENTRY(Filtering, WGPUSamplerBindingType_Filtering)
	ENUM_ENTRY(NonFiltering, WGPUSamplerBindingType_NonFiltering)
	ENUM_ENTRY(Comparison, WGPUSamplerBindingType_Comparison)
	ENUM_ENTRY(Force32, WGPUSamplerBindingType_Force32)
END
ENUM(Status)
	ENUM_ENTRY(Success, WGPUStatus_Success)
	ENUM_ENTRY(Error, WGPUStatus_Error)
	ENUM_ENTRY(Force32, WGPUStatus_Force32)
END
ENUM(StencilOperation)
	ENUM_ENTRY(Undefined, WGPUStencilOperation_Undefined)
	ENUM_ENTRY(Keep, WGPUStencilOperation_Keep)
	ENUM_ENTRY(Zero, WGPUStencilOperation_Zero)
	ENUM_ENTRY(Replace, WGPUStencilOperation_Replace)
	ENUM_ENTRY(Invert, WGPUStencilOperation_Invert)
	ENUM_ENTRY(IncrementClamp, WGPUStencilOperation_IncrementClamp)
	ENUM_ENTRY(DecrementClamp, WGPUStencilOperation_DecrementClamp)
	ENUM_ENTRY(IncrementWrap, WGPUStencilOperation_IncrementWrap)
	ENUM_ENTRY(DecrementWrap, WGPUStencilOperation_DecrementWrap)
	ENUM_ENTRY(Force32, WGPUStencilOperation_Force32)
END
ENUM(StorageTextureAccess)
	ENUM_ENTRY(BindingNotUsed, WGPUStorageTextureAccess_BindingNotUsed)
	ENUM_ENTRY(Undefined, WGPUStorageTextureAccess_Undefined)
	ENUM_ENTRY(WriteOnly, WGPUStorageTextureAccess_WriteOnly)
	ENUM_ENTRY(ReadOnly, WGPUStorageTextureAccess_ReadOnly)
	ENUM_ENTRY(ReadWrite, WGPUStorageTextureAccess_ReadWrite)
	ENUM_ENTRY(Force32, WGPUStorageTextureAccess_Force32)
END
ENUM(StoreOp)
	ENUM_ENTRY(Undefined, WGPUStoreOp_Undefined)
	ENUM_ENTRY(Store, WGPUStoreOp_Store)
	ENUM_ENTRY(Discard, WGPUStoreOp_Discard)
	ENUM_ENTRY(Force32, WGPUStoreOp_Force32)
END
ENUM(SType)
	ENUM_ENTRY(ShaderSourceSPIRV, WGPUSType_ShaderSourceSPIRV)
	ENUM_ENTRY(ShaderSourceWGSL, WGPUSType_ShaderSourceWGSL)
	ENUM_ENTRY(RenderPassMaxDrawCount, WGPUSType_RenderPassMaxDrawCount)
	ENUM_ENTRY(SurfaceSourceMetalLayer, WGPUSType_SurfaceSourceMetalLayer)
	ENUM_ENTRY(SurfaceSourceWindowsHWND, WGPUSType_SurfaceSourceWindowsHWND)
	ENUM_ENTRY(SurfaceSourceXlibWindow, WGPUSType_SurfaceSourceXlibWindow)
	ENUM_ENTRY(SurfaceSourceWaylandSurface, WGPUSType_SurfaceSourceWaylandSurface)
	ENUM_ENTRY(SurfaceSourceAndroidNativeWindow, WGPUSType_SurfaceSourceAndroidNativeWindow)
	ENUM_ENTRY(SurfaceSourceXCBWindow, WGPUSType_SurfaceSourceXCBWindow)
	ENUM_ENTRY(SurfaceColorManagement, WGPUSType_SurfaceColorManagement)
	ENUM_ENTRY(RequestAdapterWebXROptions, WGPUSType_RequestAdapterWebXROptions)
	ENUM_ENTRY(AdapterPropertiesSubgroups, WGPUSType_AdapterPropertiesSubgroups)
	ENUM_ENTRY(BindGroupLayoutEntryArraySize, WGPUSType_BindGroupLayoutEntryArraySize)
	ENUM_ENTRY(TextureBindingViewDimensionDescriptor, WGPUSType_TextureBindingViewDimensionDescriptor)
	ENUM_ENTRY(EmscriptenSurfaceSourceCanvasHTMLSelector, WGPUSType_EmscriptenSurfaceSourceCanvasHTMLSelector)
	ENUM_ENTRY(DawnCompilationMessageUtf16, WGPUSType_DawnCompilationMessageUtf16)
	ENUM_ENTRY(Force32, WGPUSType_Force32)
END
ENUM(SubgroupMatrixComponentType)
	ENUM_ENTRY(F32, WGPUSubgroupMatrixComponentType_F32)
	ENUM_ENTRY(F16, WGPUSubgroupMatrixComponentType_F16)
	ENUM_ENTRY(U32, WGPUSubgroupMatrixComponentType_U32)
	ENUM_ENTRY(I32, WGPUSubgroupMatrixComponentType_I32)
	ENUM_ENTRY(Force32, WGPUSubgroupMatrixComponentType_Force32)
END
ENUM(SurfaceGetCurrentTextureStatus)
	ENUM_ENTRY(SuccessOptimal, WGPUSurfaceGetCurrentTextureStatus_SuccessOptimal)
	ENUM_ENTRY(SuccessSuboptimal, WGPUSurfaceGetCurrentTextureStatus_SuccessSuboptimal)
	ENUM_ENTRY(Timeout, WGPUSurfaceGetCurrentTextureStatus_Timeout)
	ENUM_ENTRY(Outdated, WGPUSurfaceGetCurrentTextureStatus_Outdated)
	ENUM_ENTRY(Lost, WGPUSurfaceGetCurrentTextureStatus_Lost)
	ENUM_ENTRY(Error, WGPUSurfaceGetCurrentTextureStatus_Error)
	ENUM_ENTRY(Force32, WGPUSurfaceGetCurrentTextureStatus_Force32)
END
ENUM(TextureAspect)
	ENUM_ENTRY(Undefined, WGPUTextureAspect_Undefined)
	ENUM_ENTRY(All, WGPUTextureAspect_All)
	ENUM_ENTRY(StencilOnly, WGPUTextureAspect_StencilOnly)
	ENUM_ENTRY(DepthOnly, WGPUTextureAspect_DepthOnly)
	ENUM_ENTRY(Force32, WGPUTextureAspect_Force32)
END
ENUM(TextureDimension)
	ENUM_ENTRY(Undefined, WGPUTextureDimension_Undefined)
	ENUM_ENTRY(_1D, WGPUTextureDimension_1D)
	ENUM_ENTRY(_2D, WGPUTextureDimension_2D)
	ENUM_ENTRY(_3D, WGPUTextureDimension_3D)
	ENUM_ENTRY(Force32, WGPUTextureDimension_Force32)
END
ENUM(TextureFormat)
	ENUM_ENTRY(Undefined, WGPUTextureFormat_Undefined)
	ENUM_ENTRY(R8Unorm, WGPUTextureFormat_R8Unorm)
	ENUM_ENTRY(R8Snorm, WGPUTextureFormat_R8Snorm)
	ENUM_ENTRY(R8Uint, WGPUTextureFormat_R8Uint)
	ENUM_ENTRY(R8Sint, WGPUTextureFormat_R8Sint)
	ENUM_ENTRY(R16Uint, WGPUTextureFormat_R16Uint)
	ENUM_ENTRY(R16Sint, WGPUTextureFormat_R16Sint)
	ENUM_ENTRY(R16Float, WGPUTextureFormat_R16Float)
	ENUM_ENTRY(RG8Unorm, WGPUTextureFormat_RG8Unorm)
	ENUM_ENTRY(RG8Snorm, WGPUTextureFormat_RG8Snorm)
	ENUM_ENTRY(RG8Uint, WGPUTextureFormat_RG8Uint)
	ENUM_ENTRY(RG8Sint, WGPUTextureFormat_RG8Sint)
	ENUM_ENTRY(R32Float, WGPUTextureFormat_R32Float)
	ENUM_ENTRY(R32Uint, WGPUTextureFormat_R32Uint)
	ENUM_ENTRY(R32Sint, WGPUTextureFormat_R32Sint)
	ENUM_ENTRY(RG16Uint, WGPUTextureFormat_RG16Uint)
	ENUM_ENTRY(RG16Sint, WGPUTextureFormat_RG16Sint)
	ENUM_ENTRY(RG16Float, WGPUTextureFormat_RG16Float)
	ENUM_ENTRY(RGBA8Unorm, WGPUTextureFormat_RGBA8Unorm)
	ENUM_ENTRY(RGBA8UnormSrgb, WGPUTextureFormat_RGBA8UnormSrgb)
	ENUM_ENTRY(RGBA8Snorm, WGPUTextureFormat_RGBA8Snorm)
	ENUM_ENTRY(RGBA8Uint, WGPUTextureFormat_RGBA8Uint)
	ENUM_ENTRY(RGBA8Sint, WGPUTextureFormat_RGBA8Sint)
	ENUM_ENTRY(BGRA8Unorm, WGPUTextureFormat_BGRA8Unorm)
	ENUM_ENTRY(BGRA8UnormSrgb, WGPUTextureFormat_BGRA8UnormSrgb)
	ENUM_ENTRY(RGB10A2Uint, WGPUTextureFormat_RGB10A2Uint)
	ENUM_ENTRY(RGB10A2Unorm, WGPUTextureFormat_RGB10A2Unorm)
	ENUM_ENTRY(RG11B10Ufloat, WGPUTextureFormat_RG11B10Ufloat)
	ENUM_ENTRY(RGB9E5Ufloat, WGPUTextureFormat_RGB9E5Ufloat)
	ENUM_ENTRY(RG32Float, WGPUTextureFormat_RG32Float)
	ENUM_ENTRY(RG32Uint, WGPUTextureFormat_RG32Uint)
	ENUM_ENTRY(RG32Sint, WGPUTextureFormat_RG32Sint)
	ENUM_ENTRY(RGBA16Uint, WGPUTextureFormat_RGBA16Uint)
	ENUM_ENTRY(RGBA16Sint, WGPUTextureFormat_RGBA16Sint)
	ENUM_ENTRY(RGBA16Float, WGPUTextureFormat_RGBA16Float)
	ENUM_ENTRY(RGBA32Float, WGPUTextureFormat_RGBA32Float)
	ENUM_ENTRY(RGBA32Uint, WGPUTextureFormat_RGBA32Uint)
	ENUM_ENTRY(RGBA32Sint, WGPUTextureFormat_RGBA32Sint)
	ENUM_ENTRY(Stencil8, WGPUTextureFormat_Stencil8)
	ENUM_ENTRY(Depth16Unorm, WGPUTextureFormat_Depth16Unorm)
	ENUM_ENTRY(Depth24Plus, WGPUTextureFormat_Depth24Plus)
	ENUM_ENTRY(Depth24PlusStencil8, WGPUTextureFormat_Depth24PlusStencil8)
	ENUM_ENTRY(Depth32Float, WGPUTextureFormat_Depth32Float)
	ENUM_ENTRY(Depth32FloatStencil8, WGPUTextureFormat_Depth32FloatStencil8)
	ENUM_ENTRY(BC1RGBAUnorm, WGPUTextureFormat_BC1RGBAUnorm)
	ENUM_ENTRY(BC1RGBAUnormSrgb, WGPUTextureFormat_BC1RGBAUnormSrgb)
	ENUM_ENTRY(BC2RGBAUnorm, WGPUTextureFormat_BC2RGBAUnorm)
	ENUM_ENTRY(BC2RGBAUnormSrgb, WGPUTextureFormat_BC2RGBAUnormSrgb)
	ENUM_ENTRY(BC3RGBAUnorm, WGPUTextureFormat_BC3RGBAUnorm)
	ENUM_ENTRY(BC3RGBAUnormSrgb, WGPUTextureFormat_BC3RGBAUnormSrgb)
	ENUM_ENTRY(BC4RUnorm, WGPUTextureFormat_BC4RUnorm)
	ENUM_ENTRY(BC4RSnorm, WGPUTextureFormat_BC4RSnorm)
	ENUM_ENTRY(BC5RGUnorm, WGPUTextureFormat_BC5RGUnorm)
	ENUM_ENTRY(BC5RGSnorm, WGPUTextureFormat_BC5RGSnorm)
	ENUM_ENTRY(BC6HRGBUfloat, WGPUTextureFormat_BC6HRGBUfloat)
	ENUM_ENTRY(BC6HRGBFloat, WGPUTextureFormat_BC6HRGBFloat)
	ENUM_ENTRY(BC7RGBAUnorm, WGPUTextureFormat_BC7RGBAUnorm)
	ENUM_ENTRY(BC7RGBAUnormSrgb, WGPUTextureFormat_BC7RGBAUnormSrgb)
	ENUM_ENTRY(ETC2RGB8Unorm, WGPUTextureFormat_ETC2RGB8Unorm)
	ENUM_ENTRY(ETC2RGB8UnormSrgb, WGPUTextureFormat_ETC2RGB8UnormSrgb)
	ENUM_ENTRY(ETC2RGB8A1Unorm, WGPUTextureFormat_ETC2RGB8A1Unorm)
	ENUM_ENTRY(ETC2RGB8A1UnormSrgb, WGPUTextureFormat_ETC2RGB8A1UnormSrgb)
	ENUM_ENTRY(ETC2RGBA8Unorm, WGPUTextureFormat_ETC2RGBA8Unorm)
	ENUM_ENTRY(ETC2RGBA8UnormSrgb, WGPUTextureFormat_ETC2RGBA8UnormSrgb)
	ENUM_ENTRY(EACR11Unorm, WGPUTextureFormat_EACR11Unorm)
	ENUM_ENTRY(EACR11Snorm, WGPUTextureFormat_EACR11Snorm)
	ENUM_ENTRY(EACRG11Unorm, WGPUTextureFormat_EACRG11Unorm)
	ENUM_ENTRY(EACRG11Snorm, WGPUTextureFormat_EACRG11Snorm)
	ENUM_ENTRY(ASTC4x4Unorm, WGPUTextureFormat_ASTC4x4Unorm)
	ENUM_ENTRY(ASTC4x4UnormSrgb, WGPUTextureFormat_ASTC4x4UnormSrgb)
	ENUM_ENTRY(ASTC5x4Unorm, WGPUTextureFormat_ASTC5x4Unorm)
	ENUM_ENTRY(ASTC5x4UnormSrgb, WGPUTextureFormat_ASTC5x4UnormSrgb)
	ENUM_ENTRY(ASTC5x5Unorm, WGPUTextureFormat_ASTC5x5Unorm)
	ENUM_ENTRY(ASTC5x5UnormSrgb, WGPUTextureFormat_ASTC5x5UnormSrgb)
	ENUM_ENTRY(ASTC6x5Unorm, WGPUTextureFormat_ASTC6x5Unorm)
	ENUM_ENTRY(ASTC6x5UnormSrgb, WGPUTextureFormat_ASTC6x5UnormSrgb)
	ENUM_ENTRY(ASTC6x6Unorm, WGPUTextureFormat_ASTC6x6Unorm)
	ENUM_ENTRY(ASTC6x6UnormSrgb, WGPUTextureFormat_ASTC6x6UnormSrgb)
	ENUM_ENTRY(ASTC8x5Unorm, WGPUTextureFormat_ASTC8x5Unorm)
	ENUM_ENTRY(ASTC8x5UnormSrgb, WGPUTextureFormat_ASTC8x5UnormSrgb)
	ENUM_ENTRY(ASTC8x6Unorm, WGPUTextureFormat_ASTC8x6Unorm)
	ENUM_ENTRY(ASTC8x6UnormSrgb, WGPUTextureFormat_ASTC8x6UnormSrgb)
	ENUM_ENTRY(ASTC8x8Unorm, WGPUTextureFormat_ASTC8x8Unorm)
	ENUM_ENTRY(ASTC8x8UnormSrgb, WGPUTextureFormat_ASTC8x8UnormSrgb)
	ENUM_ENTRY(ASTC10x5Unorm, WGPUTextureFormat_ASTC10x5Unorm)
	ENUM_ENTRY(ASTC10x5UnormSrgb, WGPUTextureFormat_ASTC10x5UnormSrgb)
	ENUM_ENTRY(ASTC10x6Unorm, WGPUTextureFormat_ASTC10x6Unorm)
	ENUM_ENTRY(ASTC10x6UnormSrgb, WGPUTextureFormat_ASTC10x6UnormSrgb)
	ENUM_ENTRY(ASTC10x8Unorm, WGPUTextureFormat_ASTC10x8Unorm)
	ENUM_ENTRY(ASTC10x8UnormSrgb, WGPUTextureFormat_ASTC10x8UnormSrgb)
	ENUM_ENTRY(ASTC10x10Unorm, WGPUTextureFormat_ASTC10x10Unorm)
	ENUM_ENTRY(ASTC10x10UnormSrgb, WGPUTextureFormat_ASTC10x10UnormSrgb)
	ENUM_ENTRY(ASTC12x10Unorm, WGPUTextureFormat_ASTC12x10Unorm)
	ENUM_ENTRY(ASTC12x10UnormSrgb, WGPUTextureFormat_ASTC12x10UnormSrgb)
	ENUM_ENTRY(ASTC12x12Unorm, WGPUTextureFormat_ASTC12x12Unorm)
	ENUM_ENTRY(ASTC12x12UnormSrgb, WGPUTextureFormat_ASTC12x12UnormSrgb)
	ENUM_ENTRY(Force32, WGPUTextureFormat_Force32)
END
ENUM(TextureSampleType)
	ENUM_ENTRY(BindingNotUsed, WGPUTextureSampleType_BindingNotUsed)
	ENUM_ENTRY(Undefined, WGPUTextureSampleType_Undefined)
	ENUM_ENTRY(Float, WGPUTextureSampleType_Float)
	ENUM_ENTRY(UnfilterableFloat, WGPUTextureSampleType_UnfilterableFloat)
	ENUM_ENTRY(Depth, WGPUTextureSampleType_Depth)
	ENUM_ENTRY(Sint, WGPUTextureSampleType_Sint)
	ENUM_ENTRY(Uint, WGPUTextureSampleType_Uint)
	ENUM_ENTRY(Force32, WGPUTextureSampleType_Force32)
END
ENUM(TextureViewDimension)
	ENUM_ENTRY(Undefined, WGPUTextureViewDimension_Undefined)
	ENUM_ENTRY(_1D, WGPUTextureViewDimension_1D)
	ENUM_ENTRY(_2D, WGPUTextureViewDimension_2D)
	ENUM_ENTRY(_2DArray, WGPUTextureViewDimension_2DArray)
	ENUM_ENTRY(Cube, WGPUTextureViewDimension_Cube)
	ENUM_ENTRY(CubeArray, WGPUTextureViewDimension_CubeArray)
	ENUM_ENTRY(_3D, WGPUTextureViewDimension_3D)
	ENUM_ENTRY(Force32, WGPUTextureViewDimension_Force32)
END
ENUM(ToneMappingMode)
	ENUM_ENTRY(Standard, WGPUToneMappingMode_Standard)
	ENUM_ENTRY(Extended, WGPUToneMappingMode_Extended)
	ENUM_ENTRY(Force32, WGPUToneMappingMode_Force32)
END
ENUM(VertexFormat)
	ENUM_ENTRY(Uint8, WGPUVertexFormat_Uint8)
	ENUM_ENTRY(Uint8x2, WGPUVertexFormat_Uint8x2)
	ENUM_ENTRY(Uint8x4, WGPUVertexFormat_Uint8x4)
	ENUM_ENTRY(Sint8, WGPUVertexFormat_Sint8)
	ENUM_ENTRY(Sint8x2, WGPUVertexFormat_Sint8x2)
	ENUM_ENTRY(Sint8x4, WGPUVertexFormat_Sint8x4)
	ENUM_ENTRY(Unorm8, WGPUVertexFormat_Unorm8)
	ENUM_ENTRY(Unorm8x2, WGPUVertexFormat_Unorm8x2)
	ENUM_ENTRY(Unorm8x4, WGPUVertexFormat_Unorm8x4)
	ENUM_ENTRY(Snorm8, WGPUVertexFormat_Snorm8)
	ENUM_ENTRY(Snorm8x2, WGPUVertexFormat_Snorm8x2)
	ENUM_ENTRY(Snorm8x4, WGPUVertexFormat_Snorm8x4)
	ENUM_ENTRY(Uint16, WGPUVertexFormat_Uint16)
	ENUM_ENTRY(Uint16x2, WGPUVertexFormat_Uint16x2)
	ENUM_ENTRY(Uint16x4, WGPUVertexFormat_Uint16x4)
	ENUM_ENTRY(Sint16, WGPUVertexFormat_Sint16)
	ENUM_ENTRY(Sint16x2, WGPUVertexFormat_Sint16x2)
	ENUM_ENTRY(Sint16x4, WGPUVertexFormat_Sint16x4)
	ENUM_ENTRY(Unorm16, WGPUVertexFormat_Unorm16)
	ENUM_ENTRY(Unorm16x2, WGPUVertexFormat_Unorm16x2)
	ENUM_ENTRY(Unorm16x4, WGPUVertexFormat_Unorm16x4)
	ENUM_ENTRY(Snorm16, WGPUVertexFormat_Snorm16)
	ENUM_ENTRY(Snorm16x2, WGPUVertexFormat_Snorm16x2)
	ENUM_ENTRY(Snorm16x4, WGPUVertexFormat_Snorm16x4)
	ENUM_ENTRY(Float16, WGPUVertexFormat_Float16)
	ENUM_ENTRY(Float16x2, WGPUVertexFormat_Float16x2)
	ENUM_ENTRY(Float16x4, WGPUVertexFormat_Float16x4)
	ENUM_ENTRY(Float32, WGPUVertexFormat_Float32)
	ENUM_ENTRY(Float32x2, WGPUVertexFormat_Float32x2)
	ENUM_ENTRY(Float32x3, WGPUVertexFormat_Float32x3)
	ENUM_ENTRY(Float32x4, WGPUVertexFormat_Float32x4)
	ENUM_ENTRY(Uint32, WGPUVertexFormat_Uint32)
	ENUM_ENTRY(Uint32x2, WGPUVertexFormat_Uint32x2)
	ENUM_ENTRY(Uint32x3, WGPUVertexFormat_Uint32x3)
	ENUM_ENTRY(Uint32x4, WGPUVertexFormat_Uint32x4)
	ENUM_ENTRY(Sint32, WGPUVertexFormat_Sint32)
	ENUM_ENTRY(Sint32x2, WGPUVertexFormat_Sint32x2)
	ENUM_ENTRY(Sint32x3, WGPUVertexFormat_Sint32x3)
	ENUM_ENTRY(Sint32x4, WGPUVertexFormat_Sint32x4)
	ENUM_ENTRY(Unorm10_10_10_2, WGPUVertexFormat_Unorm10_10_10_2)
	ENUM_ENTRY(Unorm8x4BGRA, WGPUVertexFormat_Unorm8x4BGRA)
	ENUM_ENTRY(Force32, WGPUVertexFormat_Force32)
END
ENUM(VertexStepMode)
	ENUM_ENTRY(Undefined, WGPUVertexStepMode_Undefined)
	ENUM_ENTRY(Vertex, WGPUVertexStepMode_Vertex)
	ENUM_ENTRY(Instance, WGPUVertexStepMode_Instance)
	ENUM_ENTRY(Force32, WGPUVertexStepMode_Force32)
END
ENUM(WaitStatus)
	ENUM_ENTRY(Success, WGPUWaitStatus_Success)
	ENUM_ENTRY(TimedOut, WGPUWaitStatus_TimedOut)
	ENUM_ENTRY(Error, WGPUWaitStatus_Error)
	ENUM_ENTRY(Force32, WGPUWaitStatus_Force32)
END
ENUM(WGSLLanguageFeatureName)
	ENUM_ENTRY(ReadonlyAndReadwriteStorageTextures, WGPUWGSLLanguageFeatureName_ReadonlyAndReadwriteStorageTextures)
	ENUM_ENTRY(Packed4x8IntegerDotProduct, WGPUWGSLLanguageFeatureName_Packed4x8IntegerDotProduct)
	ENUM_ENTRY(UnrestrictedPointerParameters, WGPUWGSLLanguageFeatureName_UnrestrictedPointerParameters)
	ENUM_ENTRY(PointerCompositeAccess, WGPUWGSLLanguageFeatureName_PointerCompositeAccess)
	ENUM_ENTRY(SizedBindingArray, WGPUWGSLLanguageFeatureName_SizedBindingArray)
	ENUM_ENTRY(Force32, WGPUWGSLLanguageFeatureName_Force32)
END
ENUM(BufferUsage)
	ENUM_ENTRY(None, 0x0000000000000000)
	ENUM_ENTRY(MapRead, 0x0000000000000001)
	ENUM_ENTRY(MapWrite, 0x0000000000000002)
	ENUM_ENTRY(CopySrc, 0x0000000000000004)
	ENUM_ENTRY(CopyDst, 0x0000000000000008)
	ENUM_ENTRY(Index, 0x0000000000000010)
	ENUM_ENTRY(Vertex, 0x0000000000000020)
	ENUM_ENTRY(Uniform, 0x0000000000000040)
	ENUM_ENTRY(Storage, 0x0000000000000080)
	ENUM_ENTRY(Indirect, 0x0000000000000100)
	ENUM_ENTRY(QueryResolve, 0x0000000000000200)
END
ENUM(ColorWriteMask)
	ENUM_ENTRY(None, 0x0000000000000000)
	ENUM_ENTRY(Red, 0x0000000000000001)
	ENUM_ENTRY(Green, 0x0000000000000002)
	ENUM_ENTRY(Blue, 0x0000000000000004)
	ENUM_ENTRY(Alpha, 0x0000000000000008)
	ENUM_ENTRY(All, 0x000000000000000F)
END
ENUM(MapMode)
	ENUM_ENTRY(None, 0x0000000000000000)
	ENUM_ENTRY(Read, 0x0000000000000001)
	ENUM_ENTRY(Write, 0x0000000000000002)
END
ENUM(ShaderStage)
	ENUM_ENTRY(None, 0x0000000000000000)
	ENUM_ENTRY(Vertex, 0x0000000000000001)
	ENUM_ENTRY(Fragment, 0x0000000000000002)
	ENUM_ENTRY(Compute, 0x0000000000000004)
END
ENUM(TextureUsage)
	ENUM_ENTRY(None, 0x0000000000000000)
	ENUM_ENTRY(CopySrc, 0x0000000000000001)
	ENUM_ENTRY(CopyDst, 0x0000000000000002)
	ENUM_ENTRY(TextureBinding, 0x0000000000000004)
	ENUM_ENTRY(StorageBinding, 0x0000000000000008)
	ENUM_ENTRY(RenderAttachment, 0x0000000000000010)
END

// Structs
STRUCT_NO_OSTREAM(StringView)
	void setDefault();
	StringView(const std::string_view& cpp) : WGPUStringView{ cpp.data(), cpp.length() } {}
	operator std::string_view() const;
	friend auto operator<<(std::ostream& stream, const S& self) -> std::ostream& {
		return stream << std::string_view(self);
	}
END

STRUCT(ChainedStruct)
	void setDefault();
END

STRUCT(AdapterPropertiesSubgroups)
	void setDefault();
END

STRUCT(BindGroupLayoutEntryArraySize)
	void setDefault();
END

STRUCT(BlendComponent)
	void setDefault();
END

STRUCT(Color)
	void setDefault();
	Color(double r, double g, double b, double a) : WGPUColor{ r, g, b, a } {}
END

STRUCT(DawnCompilationMessageUtf16)
	void setDefault();
END

STRUCT(EmscriptenSurfaceSourceCanvasHTMLSelector)
	void setDefault();
END

STRUCT(Extent3D)
	void setDefault();
	Extent3D(uint32_t width, uint32_t height, uint32_t depthOrArrayLayers) : WGPUExtent3D{ width, height, depthOrArrayLayers } {}
END

STRUCT(Future)
	void setDefault();
END

STRUCT(INTERNAL_HAVE_EMDAWNWEBGPU_HEADER)
	void setDefault();
END

STRUCT(Origin3D)
	void setDefault();
	Origin3D(uint32_t x, uint32_t y, uint32_t z) : WGPUOrigin3D{ x, y, z } {}
END

STRUCT(RenderPassMaxDrawCount)
	void setDefault();
END

STRUCT(RequestAdapterWebXROptions)
	void setDefault();
END

STRUCT(ShaderSourceSPIRV)
	void setDefault();
END

STRUCT(ShaderSourceWGSL)
	void setDefault();
END

STRUCT(StencilFaceState)
	void setDefault();
END

STRUCT(SupportedFeatures)
	void setDefault();
	void freeMembers();
END

STRUCT(SupportedWGSLLanguageFeatures)
	void setDefault();
	void freeMembers();
END

STRUCT(SurfaceColorManagement)
	void setDefault();
END

STRUCT(TexelCopyBufferLayout)
	void setDefault();
END

STRUCT(TextureBindingViewDimensionDescriptor)
	void setDefault();
END

STRUCT(BlendState)
	void setDefault();
END

STRUCT(FutureWaitInfo)
	void setDefault();
END

STRUCT(TexelCopyBufferInfo)
	void setDefault();
END

STRUCT(TexelCopyTextureInfo)
	void setDefault();
END


// Descriptors
DESCRIPTOR(BufferMapCallbackInfo)
	void setDefault();
END

DESCRIPTOR(CompilationInfoCallbackInfo)
	void setDefault();
END

DESCRIPTOR(CreateComputePipelineAsyncCallbackInfo)
	void setDefault();
END

DESCRIPTOR(CreateRenderPipelineAsyncCallbackInfo)
	void setDefault();
END

DESCRIPTOR(DeviceLostCallbackInfo)
	void setDefault();
END

DESCRIPTOR(PopErrorScopeCallbackInfo)
	void setDefault();
END

DESCRIPTOR(QueueWorkDoneCallbackInfo)
	void setDefault();
END

DESCRIPTOR(RequestAdapterCallbackInfo)
	void setDefault();
END

DESCRIPTOR(RequestDeviceCallbackInfo)
	void setDefault();
END

DESCRIPTOR(UncapturedErrorCallbackInfo)
	void setDefault();
END

DESCRIPTOR(BindGroupEntry)
	void setDefault();
END

DESCRIPTOR(BufferBindingLayout)
	void setDefault();
END

DESCRIPTOR(BufferDescriptor)
	void setDefault();
END

DESCRIPTOR(CommandBufferDescriptor)
	void setDefault();
END

DESCRIPTOR(CommandEncoderDescriptor)
	void setDefault();
END

DESCRIPTOR(ConstantEntry)
	void setDefault();
END

DESCRIPTOR(InstanceCapabilities)
	void setDefault();
END

DESCRIPTOR(Limits)
	void setDefault();
END

DESCRIPTOR(MultisampleState)
	void setDefault();
END

DESCRIPTOR(PassTimestampWrites)
	void setDefault();
END

DESCRIPTOR(PipelineLayoutDescriptor)
	void setDefault();
END

DESCRIPTOR(PrimitiveState)
	void setDefault();
END

DESCRIPTOR(QuerySetDescriptor)
	void setDefault();
END

DESCRIPTOR(QueueDescriptor)
	void setDefault();
END

DESCRIPTOR(RenderBundleDescriptor)
	void setDefault();
END

DESCRIPTOR(RenderBundleEncoderDescriptor)
	void setDefault();
END

DESCRIPTOR(RenderPassDepthStencilAttachment)
	void setDefault();
END

DESCRIPTOR(SamplerBindingLayout)
	void setDefault();
END

DESCRIPTOR(SamplerDescriptor)
	void setDefault();
END

DESCRIPTOR(StorageTextureBindingLayout)
	void setDefault();
END

DESCRIPTOR(SurfaceCapabilities)
	void setDefault();
	void freeMembers();
END

DESCRIPTOR(SurfaceConfiguration)
	void setDefault();
END

DESCRIPTOR(SurfaceTexture)
	void setDefault();
END

DESCRIPTOR(TextureBindingLayout)
	void setDefault();
END

DESCRIPTOR(TextureViewDescriptor)
	void setDefault();
END

DESCRIPTOR(VertexAttribute)
	void setDefault();
END

DESCRIPTOR(AdapterInfo)
	void setDefault();
	void freeMembers();
END

DESCRIPTOR(BindGroupDescriptor)
	void setDefault();
END

DESCRIPTOR(BindGroupLayoutEntry)
	void setDefault();
END

DESCRIPTOR(CompilationMessage)
	void setDefault();
END

DESCRIPTOR(ComputePassDescriptor)
	void setDefault();
END

DESCRIPTOR(ComputeState)
	void setDefault();
END

DESCRIPTOR(DepthStencilState)
	void setDefault();
END

DESCRIPTOR(DeviceDescriptor)
	void setDefault();
END

DESCRIPTOR(InstanceDescriptor)
	void setDefault();
END

DESCRIPTOR(RenderPassColorAttachment)
	void setDefault();
END

DESCRIPTOR(RequestAdapterOptions)
	void setDefault();
END

DESCRIPTOR(ShaderModuleDescriptor)
	void setDefault();
END

DESCRIPTOR(SurfaceDescriptor)
	void setDefault();
END

DESCRIPTOR(TextureDescriptor)
	void setDefault();
END

DESCRIPTOR(VertexBufferLayout)
	void setDefault();
END

DESCRIPTOR(BindGroupLayoutDescriptor)
	void setDefault();
END

DESCRIPTOR(ColorTargetState)
	void setDefault();
END

DESCRIPTOR(CompilationInfo)
	void setDefault();
END

DESCRIPTOR(ComputePipelineDescriptor)
	void setDefault();
END

DESCRIPTOR(RenderPassDescriptor)
	void setDefault();
END

DESCRIPTOR(VertexState)
	void setDefault();
END

DESCRIPTOR(FragmentState)
	void setDefault();
END

DESCRIPTOR(RenderPipelineDescriptor)
	void setDefault();
END


// Handles forward declarations
class Adapter;
class BindGroup;
class BindGroupLayout;
class Buffer;
class CommandBuffer;
class CommandEncoder;
class ComputePassEncoder;
class ComputePipeline;
class Device;
class Instance;
class PipelineLayout;
class QuerySet;
class Queue;
class RenderBundle;
class RenderBundleEncoder;
class RenderPassEncoder;
class RenderPipeline;
class Sampler;
class ShaderModule;
class Surface;
class Texture;
class TextureView;

// Callback types
using BufferMapCallback = std::function<void(MapAsyncStatus status, StringView message, void* userdata1)>;
using CompilationInfoCallback = std::function<void(CompilationInfoRequestStatus status, const CompilationInfo& compilationInfo, void* userdata1)>;
using CreateComputePipelineAsyncCallback = std::function<void(CreatePipelineAsyncStatus status, ComputePipeline pipeline, StringView message, void* userdata1)>;
using CreateRenderPipelineAsyncCallback = std::function<void(CreatePipelineAsyncStatus status, RenderPipeline pipeline, StringView message, void* userdata1)>;
using DeviceLostCallback = std::function<void(Device const * device, DeviceLostReason reason, StringView message, void* userdata1)>;
using PopErrorScopeCallback = std::function<void(PopErrorScopeStatus status, ErrorType type, StringView message, void* userdata1)>;
using QueueWorkDoneCallback = std::function<void(QueueWorkDoneStatus status, void* userdata1)>;
using RequestAdapterCallback = std::function<void(RequestAdapterStatus status, Adapter adapter, StringView message, void* userdata1)>;
using RequestDeviceCallback = std::function<void(RequestDeviceStatus status, Device device, StringView message, void* userdata1)>;
using UncapturedErrorCallback = std::function<void(Device const * device, ErrorType type, StringView message, void* userdata1)>;

// Handles detailed declarations
HANDLE(Adapter)
	void getFeatures(SupportedFeatures * features) const;
	Status getInfo(AdapterInfo * info) const;
	Status getLimits(Limits * limits) const;
	Bool hasFeature(FeatureName feature) const;
	template<typename Lambda>
	Future requestDevice(const DeviceDescriptor& options, CallbackMode callbackMode, const Lambda& callback) const {
		auto* lambda = new Lambda(callback);
		auto cCallback = [](WGPURequestDeviceStatus status, WGPUDevice device, WGPUStringView message, void* userdata1, void*) -> void {
			std::unique_ptr<Lambda> lambda(reinterpret_cast<Lambda*>(userdata1));
			(*lambda)(static_cast<RequestDeviceStatus>(status), device, message);
		};
		RequestDeviceCallbackInfo callbackInfo = {
			/* nextInChain = */ nullptr,
			/* mode = */ callbackMode,
			/* callback = */ cCallback,
			/* userdata1 = */ (void*)lambda,
			/* userdata2 = */ nullptr,
		};
		return wgpuAdapterRequestDevice(m_raw, &options, callbackInfo);
	}
	void addRef() const;
	void release() const;
	Device requestDevice(const DeviceDescriptor& descriptor);
END

HANDLE(BindGroup)
	void setLabel(StringView label) const;
	void addRef() const;
	void release() const;
END

HANDLE(BindGroupLayout)
	void setLabel(StringView label) const;
	void addRef() const;
	void release() const;
END

HANDLE(Buffer)
	void destroy() const;
	void const * getConstMappedRange(size_t offset, size_t size) const;
	void * getMappedRange(size_t offset, size_t size) const;
	BufferMapState getMapState() const;
	uint64_t getSize() const;
	BufferUsage getUsage() const;
	template<typename Lambda>
	Future mapAsync(MapMode mode, size_t offset, size_t size, CallbackMode callbackMode, const Lambda& callback) const {
		auto* lambda = new Lambda(callback);
		auto cCallback = [](WGPUMapAsyncStatus status, WGPUStringView message, void* userdata1, void*) -> void {
			std::unique_ptr<Lambda> lambda(reinterpret_cast<Lambda*>(userdata1));
			(*lambda)(static_cast<MapAsyncStatus>(status), message);
		};
		BufferMapCallbackInfo callbackInfo = {
			/* nextInChain = */ nullptr,
			/* mode = */ callbackMode,
			/* callback = */ cCallback,
			/* userdata1 = */ (void*)lambda,
			/* userdata2 = */ nullptr,
		};
		return wgpuBufferMapAsync(m_raw, static_cast<WGPUMapMode>(mode), offset, size, callbackInfo);
	}
	Status readMappedRange(size_t offset, void * data, size_t size) const;
	void setLabel(StringView label) const;
	void unmap() const;
	Status writeMappedRange(size_t offset, void const * data, size_t size) const;
	void addRef() const;
	void release() const;
END

HANDLE(CommandBuffer)
	void setLabel(StringView label) const;
	void addRef() const;
	void release() const;
END

HANDLE(CommandEncoder)
	ComputePassEncoder beginComputePass(const ComputePassDescriptor& descriptor) const;
	ComputePassEncoder beginComputePass() const;
	RenderPassEncoder beginRenderPass(const RenderPassDescriptor& descriptor) const;
	void clearBuffer(Buffer buffer, uint64_t offset, uint64_t size) const;
	void copyBufferToBuffer(Buffer source, uint64_t sourceOffset, Buffer destination, uint64_t destinationOffset, uint64_t size) const;
	void copyBufferToTexture(const TexelCopyBufferInfo& source, const TexelCopyTextureInfo& destination, const Extent3D& copySize) const;
	void copyTextureToBuffer(const TexelCopyTextureInfo& source, const TexelCopyBufferInfo& destination, const Extent3D& copySize) const;
	void copyTextureToTexture(const TexelCopyTextureInfo& source, const TexelCopyTextureInfo& destination, const Extent3D& copySize) const;
	CommandBuffer finish(const CommandBufferDescriptor& descriptor) const;
	CommandBuffer finish() const;
	void insertDebugMarker(StringView markerLabel) const;
	void popDebugGroup() const;
	void pushDebugGroup(StringView groupLabel) const;
	void resolveQuerySet(QuerySet querySet, uint32_t firstQuery, uint32_t queryCount, Buffer destination, uint64_t destinationOffset) const;
	void setLabel(StringView label) const;
	void writeTimestamp(QuerySet querySet, uint32_t queryIndex) const;
	void addRef() const;
	void release() const;
END

HANDLE(ComputePassEncoder)
	void dispatchWorkgroups(uint32_t workgroupCountX, uint32_t workgroupCountY, uint32_t workgroupCountZ) const;
	void dispatchWorkgroupsIndirect(Buffer indirectBuffer, uint64_t indirectOffset) const;
	void end() const;
	void insertDebugMarker(StringView markerLabel) const;
	void popDebugGroup() const;
	void pushDebugGroup(StringView groupLabel) const;
	void setBindGroup(uint32_t groupIndex, BindGroup group, size_t dynamicOffsetCount, uint32_t const * dynamicOffsets) const;
	void setBindGroup(uint32_t groupIndex, BindGroup group, const std::vector<uint32_t>& dynamicOffsets) const;
	void setBindGroup(uint32_t groupIndex, BindGroup group, const uint32_t& dynamicOffsets) const;
	void setLabel(StringView label) const;
	void setPipeline(ComputePipeline pipeline) const;
	void writeTimestamp(QuerySet querySet, uint32_t queryIndex) const;
	void addRef() const;
	void release() const;
END

HANDLE(ComputePipeline)
	BindGroupLayout getBindGroupLayout(uint32_t groupIndex) const;
	void setLabel(StringView label) const;
	void addRef() const;
	void release() const;
END

HANDLE(Device)
	BindGroup createBindGroup(const BindGroupDescriptor& descriptor) const;
	BindGroupLayout createBindGroupLayout(const BindGroupLayoutDescriptor& descriptor) const;
	Buffer createBuffer(const BufferDescriptor& descriptor) const;
	CommandEncoder createCommandEncoder(const CommandEncoderDescriptor& descriptor) const;
	CommandEncoder createCommandEncoder() const;
	ComputePipeline createComputePipeline(const ComputePipelineDescriptor& descriptor) const;
	template<typename Lambda>
	Future createComputePipelineAsync(const ComputePipelineDescriptor& descriptor, CallbackMode callbackMode, const Lambda& callback) const {
		auto* lambda = new Lambda(callback);
		auto cCallback = [](WGPUCreatePipelineAsyncStatus status, WGPUComputePipeline pipeline, WGPUStringView message, void* userdata1, void*) -> void {
			std::unique_ptr<Lambda> lambda(reinterpret_cast<Lambda*>(userdata1));
			(*lambda)(static_cast<CreatePipelineAsyncStatus>(status), pipeline, message);
		};
		CreateComputePipelineAsyncCallbackInfo callbackInfo = {
			/* nextInChain = */ nullptr,
			/* mode = */ callbackMode,
			/* callback = */ cCallback,
			/* userdata1 = */ (void*)lambda,
			/* userdata2 = */ nullptr,
		};
		return wgpuDeviceCreateComputePipelineAsync(m_raw, &descriptor, callbackInfo);
	}
	PipelineLayout createPipelineLayout(const PipelineLayoutDescriptor& descriptor) const;
	QuerySet createQuerySet(const QuerySetDescriptor& descriptor) const;
	RenderBundleEncoder createRenderBundleEncoder(const RenderBundleEncoderDescriptor& descriptor) const;
	RenderPipeline createRenderPipeline(const RenderPipelineDescriptor& descriptor) const;
	template<typename Lambda>
	Future createRenderPipelineAsync(const RenderPipelineDescriptor& descriptor, CallbackMode callbackMode, const Lambda& callback) const {
		auto* lambda = new Lambda(callback);
		auto cCallback = [](WGPUCreatePipelineAsyncStatus status, WGPURenderPipeline pipeline, WGPUStringView message, void* userdata1, void*) -> void {
			std::unique_ptr<Lambda> lambda(reinterpret_cast<Lambda*>(userdata1));
			(*lambda)(static_cast<CreatePipelineAsyncStatus>(status), pipeline, message);
		};
		CreateRenderPipelineAsyncCallbackInfo callbackInfo = {
			/* nextInChain = */ nullptr,
			/* mode = */ callbackMode,
			/* callback = */ cCallback,
			/* userdata1 = */ (void*)lambda,
			/* userdata2 = */ nullptr,
		};
		return wgpuDeviceCreateRenderPipelineAsync(m_raw, &descriptor, callbackInfo);
	}
	Sampler createSampler(const SamplerDescriptor& descriptor) const;
	Sampler createSampler() const;
	ShaderModule createShaderModule(const ShaderModuleDescriptor& descriptor) const;
	Texture createTexture(const TextureDescriptor& descriptor) const;
	void destroy() const;
	Status getAdapterInfo(AdapterInfo * adapterInfo) const;
	void getFeatures(SupportedFeatures * features) const;
	Status getLimits(Limits * limits) const;
	Queue getQueue() const;
	Bool hasFeature(FeatureName feature) const;
	template<typename Lambda>
	Future popErrorScope(CallbackMode callbackMode, const Lambda& callback) const {
		auto* lambda = new Lambda(callback);
		auto cCallback = [](WGPUPopErrorScopeStatus status, WGPUErrorType type, WGPUStringView message, void* userdata1, void*) -> void {
			std::unique_ptr<Lambda> lambda(reinterpret_cast<Lambda*>(userdata1));
			(*lambda)(static_cast<PopErrorScopeStatus>(status), static_cast<ErrorType>(type), message);
		};
		PopErrorScopeCallbackInfo callbackInfo = {
			/* nextInChain = */ nullptr,
			/* mode = */ callbackMode,
			/* callback = */ cCallback,
			/* userdata1 = */ (void*)lambda,
			/* userdata2 = */ nullptr,
		};
		return wgpuDevicePopErrorScope(m_raw, callbackInfo);
	}
	void pushErrorScope(ErrorFilter filter) const;
	void setLabel(StringView label) const;
	void addRef() const;
	void release() const;
END

HANDLE(Instance)
	Surface createSurface(const SurfaceDescriptor& descriptor) const;
	Status getWGSLLanguageFeatures(SupportedWGSLLanguageFeatures * features) const;
	Bool hasWGSLLanguageFeature(WGSLLanguageFeatureName feature) const;
	void processEvents() const;
	template<typename Lambda>
	Future requestAdapter(const RequestAdapterOptions& options, CallbackMode callbackMode, const Lambda& callback) const {
		auto* lambda = new Lambda(callback);
		auto cCallback = [](WGPURequestAdapterStatus status, WGPUAdapter adapter, WGPUStringView message, void* userdata1, void*) -> void {
			std::unique_ptr<Lambda> lambda(reinterpret_cast<Lambda*>(userdata1));
			(*lambda)(static_cast<RequestAdapterStatus>(status), adapter, message);
		};
		RequestAdapterCallbackInfo callbackInfo = {
			/* nextInChain = */ nullptr,
			/* mode = */ callbackMode,
			/* callback = */ cCallback,
			/* userdata1 = */ (void*)lambda,
			/* userdata2 = */ nullptr,
		};
		return wgpuInstanceRequestAdapter(m_raw, &options, callbackInfo);
	}
	WaitStatus waitAny(size_t futureCount, FutureWaitInfo * futures, uint64_t timeoutNS) const;
	void addRef() const;
	void release() const;
	Adapter requestAdapter(const RequestAdapterOptions& options);
END

HANDLE(PipelineLayout)
	void setLabel(StringView label) const;
	void addRef() const;
	void release() const;
END

HANDLE(QuerySet)
	void destroy() const;
	uint32_t getCount() const;
	QueryType getType() const;
	void setLabel(StringView label) const;
	void addRef() const;
	void release() const;
END

HANDLE(Queue)
	template<typename Lambda>
	Future onSubmittedWorkDone(CallbackMode callbackMode, const Lambda& callback) const {
		auto* lambda = new Lambda(callback);
		auto cCallback = [](WGPUQueueWorkDoneStatus status, void* userdata1, void*) -> void {
			std::unique_ptr<Lambda> lambda(reinterpret_cast<Lambda*>(userdata1));
			(*lambda)(static_cast<QueueWorkDoneStatus>(status));
		};
		QueueWorkDoneCallbackInfo callbackInfo = {
			/* nextInChain = */ nullptr,
			/* mode = */ callbackMode,
			/* callback = */ cCallback,
			/* userdata1 = */ (void*)lambda,
			/* userdata2 = */ nullptr,
		};
		return wgpuQueueOnSubmittedWorkDone(m_raw, callbackInfo);
	}
	void setLabel(StringView label) const;
	void submit(size_t commandCount, CommandBuffer const * commands) const;
	void submit(const std::vector<WGPUCommandBuffer>& commands) const;
	void submit(const WGPUCommandBuffer& commands) const;
	void writeBuffer(Buffer buffer, uint64_t bufferOffset, void const * data, size_t size) const;
	void writeTexture(const TexelCopyTextureInfo& destination, void const * data, size_t dataSize, const TexelCopyBufferLayout& dataLayout, const Extent3D& writeSize) const;
	void addRef() const;
	void release() const;
END

HANDLE(RenderBundle)
	void setLabel(StringView label) const;
	void addRef() const;
	void release() const;
END

HANDLE(RenderBundleEncoder)
	void draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance) const;
	void drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t baseVertex, uint32_t firstInstance) const;
	void drawIndexedIndirect(Buffer indirectBuffer, uint64_t indirectOffset) const;
	void drawIndirect(Buffer indirectBuffer, uint64_t indirectOffset) const;
	RenderBundle finish(const RenderBundleDescriptor& descriptor) const;
	RenderBundle finish() const;
	void insertDebugMarker(StringView markerLabel) const;
	void popDebugGroup() const;
	void pushDebugGroup(StringView groupLabel) const;
	void setBindGroup(uint32_t groupIndex, BindGroup group, size_t dynamicOffsetCount, uint32_t const * dynamicOffsets) const;
	void setBindGroup(uint32_t groupIndex, BindGroup group, const std::vector<uint32_t>& dynamicOffsets) const;
	void setBindGroup(uint32_t groupIndex, BindGroup group, const uint32_t& dynamicOffsets) const;
	void setIndexBuffer(Buffer buffer, IndexFormat format, uint64_t offset, uint64_t size) const;
	void setLabel(StringView label) const;
	void setPipeline(RenderPipeline pipeline) const;
	void setVertexBuffer(uint32_t slot, Buffer buffer, uint64_t offset, uint64_t size) const;
	void addRef() const;
	void release() const;
END

HANDLE(RenderPassEncoder)
	void beginOcclusionQuery(uint32_t queryIndex) const;
	void draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance) const;
	void drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t baseVertex, uint32_t firstInstance) const;
	void drawIndexedIndirect(Buffer indirectBuffer, uint64_t indirectOffset) const;
	void drawIndirect(Buffer indirectBuffer, uint64_t indirectOffset) const;
	void end() const;
	void endOcclusionQuery() const;
	void executeBundles(size_t bundleCount, RenderBundle const * bundles) const;
	void executeBundles(const std::vector<WGPURenderBundle>& bundles) const;
	void executeBundles(const WGPURenderBundle& bundles) const;
	void insertDebugMarker(StringView markerLabel) const;
	void multiDrawIndexedIndirect(Buffer indirectBuffer, uint64_t indirectOffset, uint32_t maxDrawCount, Buffer drawCountBuffer, uint64_t drawCountBufferOffset) const;
	void multiDrawIndirect(Buffer indirectBuffer, uint64_t indirectOffset, uint32_t maxDrawCount, Buffer drawCountBuffer, uint64_t drawCountBufferOffset) const;
	void popDebugGroup() const;
	void pushDebugGroup(StringView groupLabel) const;
	void setBindGroup(uint32_t groupIndex, BindGroup group, size_t dynamicOffsetCount, uint32_t const * dynamicOffsets) const;
	void setBindGroup(uint32_t groupIndex, BindGroup group, const std::vector<uint32_t>& dynamicOffsets) const;
	void setBindGroup(uint32_t groupIndex, BindGroup group, const uint32_t& dynamicOffsets) const;
	void setBlendConstant(const Color& color) const;
	void setIndexBuffer(Buffer buffer, IndexFormat format, uint64_t offset, uint64_t size) const;
	void setLabel(StringView label) const;
	void setPipeline(RenderPipeline pipeline) const;
	void setScissorRect(uint32_t x, uint32_t y, uint32_t width, uint32_t height) const;
	void setStencilReference(uint32_t reference) const;
	void setVertexBuffer(uint32_t slot, Buffer buffer, uint64_t offset, uint64_t size) const;
	void setViewport(float x, float y, float width, float height, float minDepth, float maxDepth) const;
	void writeTimestamp(QuerySet querySet, uint32_t queryIndex) const;
	void addRef() const;
	void release() const;
END

HANDLE(RenderPipeline)
	BindGroupLayout getBindGroupLayout(uint32_t groupIndex) const;
	void setLabel(StringView label) const;
	void addRef() const;
	void release() const;
END

HANDLE(Sampler)
	void setLabel(StringView label) const;
	void addRef() const;
	void release() const;
END

HANDLE(ShaderModule)
	template<typename Lambda>
	Future getCompilationInfo(CallbackMode callbackMode, const Lambda& callback) const {
		auto* lambda = new Lambda(callback);
		auto cCallback = [](WGPUCompilationInfoRequestStatus status, struct WGPUCompilationInfo const * compilationInfo, void* userdata1, void*) -> void {
			std::unique_ptr<Lambda> lambda(reinterpret_cast<Lambda*>(userdata1));
			(*lambda)(static_cast<CompilationInfoRequestStatus>(status), *reinterpret_cast<CompilationInfo const *>(compilationInfo));
		};
		CompilationInfoCallbackInfo callbackInfo = {
			/* nextInChain = */ nullptr,
			/* mode = */ callbackMode,
			/* callback = */ cCallback,
			/* userdata1 = */ (void*)lambda,
			/* userdata2 = */ nullptr,
		};
		return wgpuShaderModuleGetCompilationInfo(m_raw, callbackInfo);
	}
	void setLabel(StringView label) const;
	void addRef() const;
	void release() const;
END

HANDLE(Surface)
	void configure(const SurfaceConfiguration& config) const;
	Status getCapabilities(Adapter adapter, SurfaceCapabilities * capabilities) const;
	void getCurrentTexture(SurfaceTexture * surfaceTexture) const;
	void present() const;
	void setLabel(StringView label) const;
	void unconfigure() const;
	void addRef() const;
	void release() const;
END

HANDLE(Texture)
	TextureView createView(const TextureViewDescriptor& descriptor) const;
	TextureView createView() const;
	void destroy() const;
	uint32_t getDepthOrArrayLayers() const;
	TextureDimension getDimension() const;
	TextureFormat getFormat() const;
	uint32_t getHeight() const;
	uint32_t getMipLevelCount() const;
	uint32_t getSampleCount() const;
	TextureUsage getUsage() const;
	uint32_t getWidth() const;
	void setLabel(StringView label) const;
	void addRef() const;
	void release() const;
END

HANDLE(TextureView)
	void setLabel(StringView label) const;
	void addRef() const;
	void release() const;
END


// Non-member procedures


Instance createInstance();
Instance createInstance(const InstanceDescriptor& descriptor);

#ifdef WEBGPU_CPP_IMPLEMENTATION

Instance createInstance() {
	return wgpuCreateInstance(nullptr);
}

Instance createInstance(const InstanceDescriptor& descriptor) {
	return wgpuCreateInstance(&descriptor);
}

// Handles members implementation
// Methods of StringView
void StringView::setDefault() {
	*this = WGPUStringView WGPU_STRING_VIEW_INIT;
}


// Methods of ChainedStruct
void ChainedStruct::setDefault() {
	*this = WGPUChainedStruct {};
}


// Methods of BufferMapCallbackInfo
void BufferMapCallbackInfo::setDefault() {
	*this = WGPUBufferMapCallbackInfo WGPU_BUFFER_MAP_CALLBACK_INFO_INIT;
}


// Methods of CompilationInfoCallbackInfo
void CompilationInfoCallbackInfo::setDefault() {
	*this = WGPUCompilationInfoCallbackInfo WGPU_COMPILATION_INFO_CALLBACK_INFO_INIT;
}


// Methods of CreateComputePipelineAsyncCallbackInfo
void CreateComputePipelineAsyncCallbackInfo::setDefault() {
	*this = WGPUCreateComputePipelineAsyncCallbackInfo WGPU_CREATE_COMPUTE_PIPELINE_ASYNC_CALLBACK_INFO_INIT;
}


// Methods of CreateRenderPipelineAsyncCallbackInfo
void CreateRenderPipelineAsyncCallbackInfo::setDefault() {
	*this = WGPUCreateRenderPipelineAsyncCallbackInfo WGPU_CREATE_RENDER_PIPELINE_ASYNC_CALLBACK_INFO_INIT;
}


// Methods of DeviceLostCallbackInfo
void DeviceLostCallbackInfo::setDefault() {
	*this = WGPUDeviceLostCallbackInfo WGPU_DEVICE_LOST_CALLBACK_INFO_INIT;
}


// Methods of PopErrorScopeCallbackInfo
void PopErrorScopeCallbackInfo::setDefault() {
	*this = WGPUPopErrorScopeCallbackInfo WGPU_POP_ERROR_SCOPE_CALLBACK_INFO_INIT;
}


// Methods of QueueWorkDoneCallbackInfo
void QueueWorkDoneCallbackInfo::setDefault() {
	*this = WGPUQueueWorkDoneCallbackInfo WGPU_QUEUE_WORK_DONE_CALLBACK_INFO_INIT;
}


// Methods of RequestAdapterCallbackInfo
void RequestAdapterCallbackInfo::setDefault() {
	*this = WGPURequestAdapterCallbackInfo WGPU_REQUEST_ADAPTER_CALLBACK_INFO_INIT;
}


// Methods of RequestDeviceCallbackInfo
void RequestDeviceCallbackInfo::setDefault() {
	*this = WGPURequestDeviceCallbackInfo WGPU_REQUEST_DEVICE_CALLBACK_INFO_INIT;
}


// Methods of UncapturedErrorCallbackInfo
void UncapturedErrorCallbackInfo::setDefault() {
	*this = WGPUUncapturedErrorCallbackInfo WGPU_UNCAPTURED_ERROR_CALLBACK_INFO_INIT;
}


// Methods of AdapterPropertiesSubgroups
void AdapterPropertiesSubgroups::setDefault() {
	*this = WGPUAdapterPropertiesSubgroups WGPU_ADAPTER_PROPERTIES_SUBGROUPS_INIT;
	chain.sType = SType::AdapterPropertiesSubgroups;
	chain.next = nullptr;
}


// Methods of BindGroupEntry
void BindGroupEntry::setDefault() {
	*this = WGPUBindGroupEntry WGPU_BIND_GROUP_ENTRY_INIT;
}


// Methods of BindGroupLayoutEntryArraySize
void BindGroupLayoutEntryArraySize::setDefault() {
	*this = WGPUBindGroupLayoutEntryArraySize WGPU_BIND_GROUP_LAYOUT_ENTRY_ARRAY_SIZE_INIT;
	chain.sType = SType::BindGroupLayoutEntryArraySize;
	chain.next = nullptr;
}


// Methods of BlendComponent
void BlendComponent::setDefault() {
	*this = WGPUBlendComponent WGPU_BLEND_COMPONENT_INIT;
}


// Methods of BufferBindingLayout
void BufferBindingLayout::setDefault() {
	*this = WGPUBufferBindingLayout WGPU_BUFFER_BINDING_LAYOUT_INIT;
}


// Methods of BufferDescriptor
void BufferDescriptor::setDefault() {
	*this = WGPUBufferDescriptor WGPU_BUFFER_DESCRIPTOR_INIT;
}


// Methods of Color
void Color::setDefault() {
	*this = WGPUColor WGPU_COLOR_INIT;
}


// Methods of CommandBufferDescriptor
void CommandBufferDescriptor::setDefault() {
	*this = WGPUCommandBufferDescriptor WGPU_COMMAND_BUFFER_DESCRIPTOR_INIT;
}


// Methods of CommandEncoderDescriptor
void CommandEncoderDescriptor::setDefault() {
	*this = WGPUCommandEncoderDescriptor WGPU_COMMAND_ENCODER_DESCRIPTOR_INIT;
}


// Methods of ConstantEntry
void ConstantEntry::setDefault() {
	*this = WGPUConstantEntry WGPU_CONSTANT_ENTRY_INIT;
}


// Methods of DawnCompilationMessageUtf16
void DawnCompilationMessageUtf16::setDefault() {
	*this = WGPUDawnCompilationMessageUtf16 {};
	chain.sType = SType::DawnCompilationMessageUtf16;
	chain.next = nullptr;
}


// Methods of EmscriptenSurfaceSourceCanvasHTMLSelector
void EmscriptenSurfaceSourceCanvasHTMLSelector::setDefault() {
	*this = WGPUEmscriptenSurfaceSourceCanvasHTMLSelector WGPU_EMSCRIPTEN_SURFACE_SOURCE_CANVAS_HTML_SELECTOR_INIT;
	chain.sType = SType::EmscriptenSurfaceSourceCanvasHTMLSelector;
	chain.next = nullptr;
}


// Methods of Extent3D
void Extent3D::setDefault() {
	*this = WGPUExtent3D WGPU_EXTENT_3D_INIT;
}


// Methods of Future
void Future::setDefault() {
	*this = WGPUFuture WGPU_FUTURE_INIT;
}


// Methods of InstanceCapabilities
void InstanceCapabilities::setDefault() {
	*this = WGPUInstanceCapabilities WGPU_INSTANCE_CAPABILITIES_INIT;
}


// Methods of INTERNAL_HAVE_EMDAWNWEBGPU_HEADER
void INTERNAL_HAVE_EMDAWNWEBGPU_HEADER::setDefault() {
	*this = WGPUINTERNAL_HAVE_EMDAWNWEBGPU_HEADER {};
}


// Methods of Limits
void Limits::setDefault() {
	*this = WGPULimits WGPU_LIMITS_INIT;
}


// Methods of MultisampleState
void MultisampleState::setDefault() {
	*this = WGPUMultisampleState WGPU_MULTISAMPLE_STATE_INIT;
}


// Methods of Origin3D
void Origin3D::setDefault() {
	*this = WGPUOrigin3D WGPU_ORIGIN_3D_INIT;
}


// Methods of PassTimestampWrites
void PassTimestampWrites::setDefault() {
	*this = WGPUPassTimestampWrites WGPU_PASS_TIMESTAMP_WRITES_INIT;
}


// Methods of PipelineLayoutDescriptor
void PipelineLayoutDescriptor::setDefault() {
	*this = WGPUPipelineLayoutDescriptor WGPU_PIPELINE_LAYOUT_DESCRIPTOR_INIT;
}


// Methods of PrimitiveState
void PrimitiveState::setDefault() {
	*this = WGPUPrimitiveState WGPU_PRIMITIVE_STATE_INIT;
}


// Methods of QuerySetDescriptor
void QuerySetDescriptor::setDefault() {
	*this = WGPUQuerySetDescriptor WGPU_QUERY_SET_DESCRIPTOR_INIT;
}


// Methods of QueueDescriptor
void QueueDescriptor::setDefault() {
	*this = WGPUQueueDescriptor WGPU_QUEUE_DESCRIPTOR_INIT;
}


// Methods of RenderBundleDescriptor
void RenderBundleDescriptor::setDefault() {
	*this = WGPURenderBundleDescriptor WGPU_RENDER_BUNDLE_DESCRIPTOR_INIT;
}


// Methods of RenderBundleEncoderDescriptor
void RenderBundleEncoderDescriptor::setDefault() {
	*this = WGPURenderBundleEncoderDescriptor WGPU_RENDER_BUNDLE_ENCODER_DESCRIPTOR_INIT;
}


// Methods of RenderPassDepthStencilAttachment
void RenderPassDepthStencilAttachment::setDefault() {
	*this = WGPURenderPassDepthStencilAttachment WGPU_RENDER_PASS_DEPTH_STENCIL_ATTACHMENT_INIT;
}


// Methods of RenderPassMaxDrawCount
void RenderPassMaxDrawCount::setDefault() {
	*this = WGPURenderPassMaxDrawCount WGPU_RENDER_PASS_MAX_DRAW_COUNT_INIT;
	chain.sType = SType::RenderPassMaxDrawCount;
	chain.next = nullptr;
}


// Methods of RequestAdapterWebXROptions
void RequestAdapterWebXROptions::setDefault() {
	*this = WGPURequestAdapterWebXROptions {};
	chain.sType = SType::RequestAdapterWebXROptions;
	chain.next = nullptr;
}


// Methods of SamplerBindingLayout
void SamplerBindingLayout::setDefault() {
	*this = WGPUSamplerBindingLayout WGPU_SAMPLER_BINDING_LAYOUT_INIT;
}


// Methods of SamplerDescriptor
void SamplerDescriptor::setDefault() {
	*this = WGPUSamplerDescriptor WGPU_SAMPLER_DESCRIPTOR_INIT;
}


// Methods of ShaderSourceSPIRV
void ShaderSourceSPIRV::setDefault() {
	*this = WGPUShaderSourceSPIRV WGPU_SHADER_SOURCE_SPIRV_INIT;
	chain.sType = SType::ShaderSourceSPIRV;
	chain.next = nullptr;
}


// Methods of ShaderSourceWGSL
void ShaderSourceWGSL::setDefault() {
	*this = WGPUShaderSourceWGSL WGPU_SHADER_SOURCE_WGSL_INIT;
	chain.sType = SType::ShaderSourceWGSL;
	chain.next = nullptr;
}


// Methods of StencilFaceState
void StencilFaceState::setDefault() {
	*this = WGPUStencilFaceState WGPU_STENCIL_FACE_STATE_INIT;
}


// Methods of StorageTextureBindingLayout
void StorageTextureBindingLayout::setDefault() {
	*this = WGPUStorageTextureBindingLayout WGPU_STORAGE_TEXTURE_BINDING_LAYOUT_INIT;
}


// Methods of SupportedFeatures
void SupportedFeatures::setDefault() {
	*this = WGPUSupportedFeatures WGPU_SUPPORTED_FEATURES_INIT;
}
void SupportedFeatures::freeMembers() {
	return wgpuSupportedFeaturesFreeMembers(*this);
}


// Methods of SupportedWGSLLanguageFeatures
void SupportedWGSLLanguageFeatures::setDefault() {
	*this = WGPUSupportedWGSLLanguageFeatures WGPU_SUPPORTED_WGSL_LANGUAGE_FEATURES_INIT;
}
void SupportedWGSLLanguageFeatures::freeMembers() {
	return wgpuSupportedWGSLLanguageFeaturesFreeMembers(*this);
}


// Methods of SurfaceCapabilities
void SurfaceCapabilities::setDefault() {
	*this = WGPUSurfaceCapabilities WGPU_SURFACE_CAPABILITIES_INIT;
}
void SurfaceCapabilities::freeMembers() {
	return wgpuSurfaceCapabilitiesFreeMembers(*this);
}


// Methods of SurfaceColorManagement
void SurfaceColorManagement::setDefault() {
	*this = WGPUSurfaceColorManagement WGPU_SURFACE_COLOR_MANAGEMENT_INIT;
	chain.sType = SType::SurfaceColorManagement;
	chain.next = nullptr;
}


// Methods of SurfaceConfiguration
void SurfaceConfiguration::setDefault() {
	*this = WGPUSurfaceConfiguration WGPU_SURFACE_CONFIGURATION_INIT;
}


// Methods of SurfaceTexture
void SurfaceTexture::setDefault() {
	*this = WGPUSurfaceTexture WGPU_SURFACE_TEXTURE_INIT;
}


// Methods of TexelCopyBufferLayout
void TexelCopyBufferLayout::setDefault() {
	*this = WGPUTexelCopyBufferLayout WGPU_TEXEL_COPY_BUFFER_LAYOUT_INIT;
}


// Methods of TextureBindingLayout
void TextureBindingLayout::setDefault() {
	*this = WGPUTextureBindingLayout WGPU_TEXTURE_BINDING_LAYOUT_INIT;
}


// Methods of TextureBindingViewDimensionDescriptor
void TextureBindingViewDimensionDescriptor::setDefault() {
	*this = WGPUTextureBindingViewDimensionDescriptor WGPU_TEXTURE_BINDING_VIEW_DIMENSION_DESCRIPTOR_INIT;
	chain.sType = SType::TextureBindingViewDimensionDescriptor;
	chain.next = nullptr;
}


// Methods of TextureViewDescriptor
void TextureViewDescriptor::setDefault() {
	*this = WGPUTextureViewDescriptor WGPU_TEXTURE_VIEW_DESCRIPTOR_INIT;
}


// Methods of VertexAttribute
void VertexAttribute::setDefault() {
	*this = WGPUVertexAttribute WGPU_VERTEX_ATTRIBUTE_INIT;
}


// Methods of AdapterInfo
void AdapterInfo::setDefault() {
	*this = WGPUAdapterInfo WGPU_ADAPTER_INFO_INIT;
}
void AdapterInfo::freeMembers() {
	return wgpuAdapterInfoFreeMembers(*this);
}


// Methods of BindGroupDescriptor
void BindGroupDescriptor::setDefault() {
	*this = WGPUBindGroupDescriptor WGPU_BIND_GROUP_DESCRIPTOR_INIT;
}


// Methods of BindGroupLayoutEntry
void BindGroupLayoutEntry::setDefault() {
	*this = WGPUBindGroupLayoutEntry WGPU_BIND_GROUP_LAYOUT_ENTRY_INIT;
}


// Methods of BlendState
void BlendState::setDefault() {
	*this = WGPUBlendState WGPU_BLEND_STATE_INIT;
}


// Methods of CompilationMessage
void CompilationMessage::setDefault() {
	*this = WGPUCompilationMessage WGPU_COMPILATION_MESSAGE_INIT;
}


// Methods of ComputePassDescriptor
void ComputePassDescriptor::setDefault() {
	*this = WGPUComputePassDescriptor WGPU_COMPUTE_PASS_DESCRIPTOR_INIT;
}


// Methods of ComputeState
void ComputeState::setDefault() {
	*this = WGPUComputeState WGPU_COMPUTE_STATE_INIT;
}


// Methods of DepthStencilState
void DepthStencilState::setDefault() {
	*this = WGPUDepthStencilState WGPU_DEPTH_STENCIL_STATE_INIT;
}


// Methods of DeviceDescriptor
void DeviceDescriptor::setDefault() {
	*this = WGPUDeviceDescriptor WGPU_DEVICE_DESCRIPTOR_INIT;
}


// Methods of FutureWaitInfo
void FutureWaitInfo::setDefault() {
	*this = WGPUFutureWaitInfo WGPU_FUTURE_WAIT_INFO_INIT;
}


// Methods of InstanceDescriptor
void InstanceDescriptor::setDefault() {
	*this = WGPUInstanceDescriptor WGPU_INSTANCE_DESCRIPTOR_INIT;
}


// Methods of RenderPassColorAttachment
void RenderPassColorAttachment::setDefault() {
	*this = WGPURenderPassColorAttachment WGPU_RENDER_PASS_COLOR_ATTACHMENT_INIT;
}


// Methods of RequestAdapterOptions
void RequestAdapterOptions::setDefault() {
	*this = WGPURequestAdapterOptions WGPU_REQUEST_ADAPTER_OPTIONS_INIT;
}


// Methods of ShaderModuleDescriptor
void ShaderModuleDescriptor::setDefault() {
	*this = WGPUShaderModuleDescriptor WGPU_SHADER_MODULE_DESCRIPTOR_INIT;
}


// Methods of SurfaceDescriptor
void SurfaceDescriptor::setDefault() {
	*this = WGPUSurfaceDescriptor WGPU_SURFACE_DESCRIPTOR_INIT;
}


// Methods of TexelCopyBufferInfo
void TexelCopyBufferInfo::setDefault() {
	*this = WGPUTexelCopyBufferInfo WGPU_TEXEL_COPY_BUFFER_INFO_INIT;
}


// Methods of TexelCopyTextureInfo
void TexelCopyTextureInfo::setDefault() {
	*this = WGPUTexelCopyTextureInfo WGPU_TEXEL_COPY_TEXTURE_INFO_INIT;
}


// Methods of TextureDescriptor
void TextureDescriptor::setDefault() {
	*this = WGPUTextureDescriptor WGPU_TEXTURE_DESCRIPTOR_INIT;
}


// Methods of VertexBufferLayout
void VertexBufferLayout::setDefault() {
	*this = WGPUVertexBufferLayout WGPU_VERTEX_BUFFER_LAYOUT_INIT;
}


// Methods of BindGroupLayoutDescriptor
void BindGroupLayoutDescriptor::setDefault() {
	*this = WGPUBindGroupLayoutDescriptor WGPU_BIND_GROUP_LAYOUT_DESCRIPTOR_INIT;
}


// Methods of ColorTargetState
void ColorTargetState::setDefault() {
	*this = WGPUColorTargetState WGPU_COLOR_TARGET_STATE_INIT;
}


// Methods of CompilationInfo
void CompilationInfo::setDefault() {
	*this = WGPUCompilationInfo WGPU_COMPILATION_INFO_INIT;
}


// Methods of ComputePipelineDescriptor
void ComputePipelineDescriptor::setDefault() {
	*this = WGPUComputePipelineDescriptor WGPU_COMPUTE_PIPELINE_DESCRIPTOR_INIT;
}


// Methods of RenderPassDescriptor
void RenderPassDescriptor::setDefault() {
	*this = WGPURenderPassDescriptor WGPU_RENDER_PASS_DESCRIPTOR_INIT;
}


// Methods of VertexState
void VertexState::setDefault() {
	*this = WGPUVertexState WGPU_VERTEX_STATE_INIT;
}


// Methods of FragmentState
void FragmentState::setDefault() {
	*this = WGPUFragmentState WGPU_FRAGMENT_STATE_INIT;
}


// Methods of RenderPipelineDescriptor
void RenderPipelineDescriptor::setDefault() {
	*this = WGPURenderPipelineDescriptor WGPU_RENDER_PIPELINE_DESCRIPTOR_INIT;
}


// Methods of Adapter
void Adapter::getFeatures(SupportedFeatures * features) const {
	return wgpuAdapterGetFeatures(m_raw, features);
}
Status Adapter::getInfo(AdapterInfo * info) const {
	return static_cast<Status>(wgpuAdapterGetInfo(m_raw, info));
}
Status Adapter::getLimits(Limits * limits) const {
	return static_cast<Status>(wgpuAdapterGetLimits(m_raw, limits));
}
Bool Adapter::hasFeature(FeatureName feature) const {
	return wgpuAdapterHasFeature(m_raw, static_cast<WGPUFeatureName>(feature));
}
void Adapter::addRef() const {
	return wgpuAdapterAddRef(m_raw);
}
void Adapter::release() const {
	return wgpuAdapterRelease(m_raw);
}


// Methods of BindGroup
void BindGroup::setLabel(StringView label) const {
	return wgpuBindGroupSetLabel(m_raw, label);
}
void BindGroup::addRef() const {
	return wgpuBindGroupAddRef(m_raw);
}
void BindGroup::release() const {
	return wgpuBindGroupRelease(m_raw);
}


// Methods of BindGroupLayout
void BindGroupLayout::setLabel(StringView label) const {
	return wgpuBindGroupLayoutSetLabel(m_raw, label);
}
void BindGroupLayout::addRef() const {
	return wgpuBindGroupLayoutAddRef(m_raw);
}
void BindGroupLayout::release() const {
	return wgpuBindGroupLayoutRelease(m_raw);
}


// Methods of Buffer
void Buffer::destroy() const {
	return wgpuBufferDestroy(m_raw);
}
void const * Buffer::getConstMappedRange(size_t offset, size_t size) const {
	return wgpuBufferGetConstMappedRange(m_raw, offset, size);
}
void * Buffer::getMappedRange(size_t offset, size_t size) const {
	return wgpuBufferGetMappedRange(m_raw, offset, size);
}
BufferMapState Buffer::getMapState() const {
	return static_cast<BufferMapState>(wgpuBufferGetMapState(m_raw));
}
uint64_t Buffer::getSize() const {
	return wgpuBufferGetSize(m_raw);
}
BufferUsage Buffer::getUsage() const {
	return static_cast<BufferUsage>(wgpuBufferGetUsage(m_raw));
}
Status Buffer::readMappedRange(size_t offset, void * data, size_t size) const {
	return static_cast<Status>(wgpuBufferReadMappedRange(m_raw, offset, data, size));
}
void Buffer::setLabel(StringView label) const {
	return wgpuBufferSetLabel(m_raw, label);
}
void Buffer::unmap() const {
	return wgpuBufferUnmap(m_raw);
}
Status Buffer::writeMappedRange(size_t offset, void const * data, size_t size) const {
	return static_cast<Status>(wgpuBufferWriteMappedRange(m_raw, offset, data, size));
}
void Buffer::addRef() const {
	return wgpuBufferAddRef(m_raw);
}
void Buffer::release() const {
	return wgpuBufferRelease(m_raw);
}


// Methods of CommandBuffer
void CommandBuffer::setLabel(StringView label) const {
	return wgpuCommandBufferSetLabel(m_raw, label);
}
void CommandBuffer::addRef() const {
	return wgpuCommandBufferAddRef(m_raw);
}
void CommandBuffer::release() const {
	return wgpuCommandBufferRelease(m_raw);
}


// Methods of CommandEncoder
ComputePassEncoder CommandEncoder::beginComputePass(const ComputePassDescriptor& descriptor) const {
	return wgpuCommandEncoderBeginComputePass(m_raw, &descriptor);
}
ComputePassEncoder CommandEncoder::beginComputePass() const {
	return wgpuCommandEncoderBeginComputePass(m_raw, nullptr);
}
RenderPassEncoder CommandEncoder::beginRenderPass(const RenderPassDescriptor& descriptor) const {
	return wgpuCommandEncoderBeginRenderPass(m_raw, &descriptor);
}
void CommandEncoder::clearBuffer(Buffer buffer, uint64_t offset, uint64_t size) const {
	return wgpuCommandEncoderClearBuffer(m_raw, buffer, offset, size);
}
void CommandEncoder::copyBufferToBuffer(Buffer source, uint64_t sourceOffset, Buffer destination, uint64_t destinationOffset, uint64_t size) const {
	return wgpuCommandEncoderCopyBufferToBuffer(m_raw, source, sourceOffset, destination, destinationOffset, size);
}
void CommandEncoder::copyBufferToTexture(const TexelCopyBufferInfo& source, const TexelCopyTextureInfo& destination, const Extent3D& copySize) const {
	return wgpuCommandEncoderCopyBufferToTexture(m_raw, &source, &destination, &copySize);
}
void CommandEncoder::copyTextureToBuffer(const TexelCopyTextureInfo& source, const TexelCopyBufferInfo& destination, const Extent3D& copySize) const {
	return wgpuCommandEncoderCopyTextureToBuffer(m_raw, &source, &destination, &copySize);
}
void CommandEncoder::copyTextureToTexture(const TexelCopyTextureInfo& source, const TexelCopyTextureInfo& destination, const Extent3D& copySize) const {
	return wgpuCommandEncoderCopyTextureToTexture(m_raw, &source, &destination, &copySize);
}
CommandBuffer CommandEncoder::finish(const CommandBufferDescriptor& descriptor) const {
	return wgpuCommandEncoderFinish(m_raw, &descriptor);
}
CommandBuffer CommandEncoder::finish() const {
	return wgpuCommandEncoderFinish(m_raw, nullptr);
}
void CommandEncoder::insertDebugMarker(StringView markerLabel) const {
	return wgpuCommandEncoderInsertDebugMarker(m_raw, markerLabel);
}
void CommandEncoder::popDebugGroup() const {
	return wgpuCommandEncoderPopDebugGroup(m_raw);
}
void CommandEncoder::pushDebugGroup(StringView groupLabel) const {
	return wgpuCommandEncoderPushDebugGroup(m_raw, groupLabel);
}
void CommandEncoder::resolveQuerySet(QuerySet querySet, uint32_t firstQuery, uint32_t queryCount, Buffer destination, uint64_t destinationOffset) const {
	return wgpuCommandEncoderResolveQuerySet(m_raw, querySet, firstQuery, queryCount, destination, destinationOffset);
}
void CommandEncoder::setLabel(StringView label) const {
	return wgpuCommandEncoderSetLabel(m_raw, label);
}
void CommandEncoder::writeTimestamp(QuerySet querySet, uint32_t queryIndex) const {
	return wgpuCommandEncoderWriteTimestamp(m_raw, querySet, queryIndex);
}
void CommandEncoder::addRef() const {
	return wgpuCommandEncoderAddRef(m_raw);
}
void CommandEncoder::release() const {
	return wgpuCommandEncoderRelease(m_raw);
}


// Methods of ComputePassEncoder
void ComputePassEncoder::dispatchWorkgroups(uint32_t workgroupCountX, uint32_t workgroupCountY, uint32_t workgroupCountZ) const {
	return wgpuComputePassEncoderDispatchWorkgroups(m_raw, workgroupCountX, workgroupCountY, workgroupCountZ);
}
void ComputePassEncoder::dispatchWorkgroupsIndirect(Buffer indirectBuffer, uint64_t indirectOffset) const {
	return wgpuComputePassEncoderDispatchWorkgroupsIndirect(m_raw, indirectBuffer, indirectOffset);
}
void ComputePassEncoder::end() const {
	return wgpuComputePassEncoderEnd(m_raw);
}
void ComputePassEncoder::insertDebugMarker(StringView markerLabel) const {
	return wgpuComputePassEncoderInsertDebugMarker(m_raw, markerLabel);
}
void ComputePassEncoder::popDebugGroup() const {
	return wgpuComputePassEncoderPopDebugGroup(m_raw);
}
void ComputePassEncoder::pushDebugGroup(StringView groupLabel) const {
	return wgpuComputePassEncoderPushDebugGroup(m_raw, groupLabel);
}
void ComputePassEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, size_t dynamicOffsetCount, uint32_t const * dynamicOffsets) const {
	return wgpuComputePassEncoderSetBindGroup(m_raw, groupIndex, group, dynamicOffsetCount, dynamicOffsets);
}
void ComputePassEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, const std::vector<uint32_t>& dynamicOffsets) const {
	return wgpuComputePassEncoderSetBindGroup(m_raw, groupIndex, group, static_cast<size_t>(dynamicOffsets.size()), dynamicOffsets.data());
}
void ComputePassEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, const uint32_t& dynamicOffsets) const {
	return wgpuComputePassEncoderSetBindGroup(m_raw, groupIndex, group, 1, &dynamicOffsets);
}
void ComputePassEncoder::setLabel(StringView label) const {
	return wgpuComputePassEncoderSetLabel(m_raw, label);
}
void ComputePassEncoder::setPipeline(ComputePipeline pipeline) const {
	return wgpuComputePassEncoderSetPipeline(m_raw, pipeline);
}
void ComputePassEncoder::writeTimestamp(QuerySet querySet, uint32_t queryIndex) const {
	return wgpuComputePassEncoderWriteTimestamp(m_raw, querySet, queryIndex);
}
void ComputePassEncoder::addRef() const {
	return wgpuComputePassEncoderAddRef(m_raw);
}
void ComputePassEncoder::release() const {
	return wgpuComputePassEncoderRelease(m_raw);
}


// Methods of ComputePipeline
BindGroupLayout ComputePipeline::getBindGroupLayout(uint32_t groupIndex) const {
	return wgpuComputePipelineGetBindGroupLayout(m_raw, groupIndex);
}
void ComputePipeline::setLabel(StringView label) const {
	return wgpuComputePipelineSetLabel(m_raw, label);
}
void ComputePipeline::addRef() const {
	return wgpuComputePipelineAddRef(m_raw);
}
void ComputePipeline::release() const {
	return wgpuComputePipelineRelease(m_raw);
}


// Methods of Device
BindGroup Device::createBindGroup(const BindGroupDescriptor& descriptor) const {
	return wgpuDeviceCreateBindGroup(m_raw, &descriptor);
}
BindGroupLayout Device::createBindGroupLayout(const BindGroupLayoutDescriptor& descriptor) const {
	return wgpuDeviceCreateBindGroupLayout(m_raw, &descriptor);
}
Buffer Device::createBuffer(const BufferDescriptor& descriptor) const {
	return wgpuDeviceCreateBuffer(m_raw, &descriptor);
}
CommandEncoder Device::createCommandEncoder(const CommandEncoderDescriptor& descriptor) const {
	return wgpuDeviceCreateCommandEncoder(m_raw, &descriptor);
}
CommandEncoder Device::createCommandEncoder() const {
	return wgpuDeviceCreateCommandEncoder(m_raw, nullptr);
}
ComputePipeline Device::createComputePipeline(const ComputePipelineDescriptor& descriptor) const {
	return wgpuDeviceCreateComputePipeline(m_raw, &descriptor);
}
PipelineLayout Device::createPipelineLayout(const PipelineLayoutDescriptor& descriptor) const {
	return wgpuDeviceCreatePipelineLayout(m_raw, &descriptor);
}
QuerySet Device::createQuerySet(const QuerySetDescriptor& descriptor) const {
	return wgpuDeviceCreateQuerySet(m_raw, &descriptor);
}
RenderBundleEncoder Device::createRenderBundleEncoder(const RenderBundleEncoderDescriptor& descriptor) const {
	return wgpuDeviceCreateRenderBundleEncoder(m_raw, &descriptor);
}
RenderPipeline Device::createRenderPipeline(const RenderPipelineDescriptor& descriptor) const {
	return wgpuDeviceCreateRenderPipeline(m_raw, &descriptor);
}
Sampler Device::createSampler(const SamplerDescriptor& descriptor) const {
	return wgpuDeviceCreateSampler(m_raw, &descriptor);
}
Sampler Device::createSampler() const {
	return wgpuDeviceCreateSampler(m_raw, nullptr);
}
ShaderModule Device::createShaderModule(const ShaderModuleDescriptor& descriptor) const {
	return wgpuDeviceCreateShaderModule(m_raw, &descriptor);
}
Texture Device::createTexture(const TextureDescriptor& descriptor) const {
	return wgpuDeviceCreateTexture(m_raw, &descriptor);
}
void Device::destroy() const {
	return wgpuDeviceDestroy(m_raw);
}
Status Device::getAdapterInfo(AdapterInfo * adapterInfo) const {
	return static_cast<Status>(wgpuDeviceGetAdapterInfo(m_raw, adapterInfo));
}
void Device::getFeatures(SupportedFeatures * features) const {
	return wgpuDeviceGetFeatures(m_raw, features);
}
Status Device::getLimits(Limits * limits) const {
	return static_cast<Status>(wgpuDeviceGetLimits(m_raw, limits));
}
Queue Device::getQueue() const {
	return wgpuDeviceGetQueue(m_raw);
}
Bool Device::hasFeature(FeatureName feature) const {
	return wgpuDeviceHasFeature(m_raw, static_cast<WGPUFeatureName>(feature));
}
void Device::pushErrorScope(ErrorFilter filter) const {
	return wgpuDevicePushErrorScope(m_raw, static_cast<WGPUErrorFilter>(filter));
}
void Device::setLabel(StringView label) const {
	return wgpuDeviceSetLabel(m_raw, label);
}
void Device::addRef() const {
	return wgpuDeviceAddRef(m_raw);
}
void Device::release() const {
	return wgpuDeviceRelease(m_raw);
}


// Methods of Instance
Surface Instance::createSurface(const SurfaceDescriptor& descriptor) const {
	return wgpuInstanceCreateSurface(m_raw, &descriptor);
}
Status Instance::getWGSLLanguageFeatures(SupportedWGSLLanguageFeatures * features) const {
	return static_cast<Status>(wgpuInstanceGetWGSLLanguageFeatures(m_raw, features));
}
Bool Instance::hasWGSLLanguageFeature(WGSLLanguageFeatureName feature) const {
	return wgpuInstanceHasWGSLLanguageFeature(m_raw, static_cast<WGPUWGSLLanguageFeatureName>(feature));
}
void Instance::processEvents() const {
	return wgpuInstanceProcessEvents(m_raw);
}
WaitStatus Instance::waitAny(size_t futureCount, FutureWaitInfo * futures, uint64_t timeoutNS) const {
	return static_cast<WaitStatus>(wgpuInstanceWaitAny(m_raw, futureCount, futures, timeoutNS));
}
void Instance::addRef() const {
	return wgpuInstanceAddRef(m_raw);
}
void Instance::release() const {
	return wgpuInstanceRelease(m_raw);
}


// Methods of PipelineLayout
void PipelineLayout::setLabel(StringView label) const {
	return wgpuPipelineLayoutSetLabel(m_raw, label);
}
void PipelineLayout::addRef() const {
	return wgpuPipelineLayoutAddRef(m_raw);
}
void PipelineLayout::release() const {
	return wgpuPipelineLayoutRelease(m_raw);
}


// Methods of QuerySet
void QuerySet::destroy() const {
	return wgpuQuerySetDestroy(m_raw);
}
uint32_t QuerySet::getCount() const {
	return wgpuQuerySetGetCount(m_raw);
}
QueryType QuerySet::getType() const {
	return static_cast<QueryType>(wgpuQuerySetGetType(m_raw));
}
void QuerySet::setLabel(StringView label) const {
	return wgpuQuerySetSetLabel(m_raw, label);
}
void QuerySet::addRef() const {
	return wgpuQuerySetAddRef(m_raw);
}
void QuerySet::release() const {
	return wgpuQuerySetRelease(m_raw);
}


// Methods of Queue
void Queue::setLabel(StringView label) const {
	return wgpuQueueSetLabel(m_raw, label);
}
void Queue::submit(size_t commandCount, CommandBuffer const * commands) const {
	return wgpuQueueSubmit(m_raw, commandCount, reinterpret_cast<WGPUCommandBuffer const *>(commands));
}
void Queue::submit(const std::vector<WGPUCommandBuffer>& commands) const {
	return wgpuQueueSubmit(m_raw, static_cast<size_t>(commands.size()), commands.data());
}
void Queue::submit(const WGPUCommandBuffer& commands) const {
	return wgpuQueueSubmit(m_raw, 1, &commands);
}
void Queue::writeBuffer(Buffer buffer, uint64_t bufferOffset, void const * data, size_t size) const {
	return wgpuQueueWriteBuffer(m_raw, buffer, bufferOffset, data, size);
}
void Queue::writeTexture(const TexelCopyTextureInfo& destination, void const * data, size_t dataSize, const TexelCopyBufferLayout& dataLayout, const Extent3D& writeSize) const {
	return wgpuQueueWriteTexture(m_raw, &destination, data, dataSize, &dataLayout, &writeSize);
}
void Queue::addRef() const {
	return wgpuQueueAddRef(m_raw);
}
void Queue::release() const {
	return wgpuQueueRelease(m_raw);
}


// Methods of RenderBundle
void RenderBundle::setLabel(StringView label) const {
	return wgpuRenderBundleSetLabel(m_raw, label);
}
void RenderBundle::addRef() const {
	return wgpuRenderBundleAddRef(m_raw);
}
void RenderBundle::release() const {
	return wgpuRenderBundleRelease(m_raw);
}


// Methods of RenderBundleEncoder
void RenderBundleEncoder::draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance) const {
	return wgpuRenderBundleEncoderDraw(m_raw, vertexCount, instanceCount, firstVertex, firstInstance);
}
void RenderBundleEncoder::drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t baseVertex, uint32_t firstInstance) const {
	return wgpuRenderBundleEncoderDrawIndexed(m_raw, indexCount, instanceCount, firstIndex, baseVertex, firstInstance);
}
void RenderBundleEncoder::drawIndexedIndirect(Buffer indirectBuffer, uint64_t indirectOffset) const {
	return wgpuRenderBundleEncoderDrawIndexedIndirect(m_raw, indirectBuffer, indirectOffset);
}
void RenderBundleEncoder::drawIndirect(Buffer indirectBuffer, uint64_t indirectOffset) const {
	return wgpuRenderBundleEncoderDrawIndirect(m_raw, indirectBuffer, indirectOffset);
}
RenderBundle RenderBundleEncoder::finish(const RenderBundleDescriptor& descriptor) const {
	return wgpuRenderBundleEncoderFinish(m_raw, &descriptor);
}
RenderBundle RenderBundleEncoder::finish() const {
	return wgpuRenderBundleEncoderFinish(m_raw, nullptr);
}
void RenderBundleEncoder::insertDebugMarker(StringView markerLabel) const {
	return wgpuRenderBundleEncoderInsertDebugMarker(m_raw, markerLabel);
}
void RenderBundleEncoder::popDebugGroup() const {
	return wgpuRenderBundleEncoderPopDebugGroup(m_raw);
}
void RenderBundleEncoder::pushDebugGroup(StringView groupLabel) const {
	return wgpuRenderBundleEncoderPushDebugGroup(m_raw, groupLabel);
}
void RenderBundleEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, size_t dynamicOffsetCount, uint32_t const * dynamicOffsets) const {
	return wgpuRenderBundleEncoderSetBindGroup(m_raw, groupIndex, group, dynamicOffsetCount, dynamicOffsets);
}
void RenderBundleEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, const std::vector<uint32_t>& dynamicOffsets) const {
	return wgpuRenderBundleEncoderSetBindGroup(m_raw, groupIndex, group, static_cast<size_t>(dynamicOffsets.size()), dynamicOffsets.data());
}
void RenderBundleEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, const uint32_t& dynamicOffsets) const {
	return wgpuRenderBundleEncoderSetBindGroup(m_raw, groupIndex, group, 1, &dynamicOffsets);
}
void RenderBundleEncoder::setIndexBuffer(Buffer buffer, IndexFormat format, uint64_t offset, uint64_t size) const {
	return wgpuRenderBundleEncoderSetIndexBuffer(m_raw, buffer, static_cast<WGPUIndexFormat>(format), offset, size);
}
void RenderBundleEncoder::setLabel(StringView label) const {
	return wgpuRenderBundleEncoderSetLabel(m_raw, label);
}
void RenderBundleEncoder::setPipeline(RenderPipeline pipeline) const {
	return wgpuRenderBundleEncoderSetPipeline(m_raw, pipeline);
}
void RenderBundleEncoder::setVertexBuffer(uint32_t slot, Buffer buffer, uint64_t offset, uint64_t size) const {
	return wgpuRenderBundleEncoderSetVertexBuffer(m_raw, slot, buffer, offset, size);
}
void RenderBundleEncoder::addRef() const {
	return wgpuRenderBundleEncoderAddRef(m_raw);
}
void RenderBundleEncoder::release() const {
	return wgpuRenderBundleEncoderRelease(m_raw);
}


// Methods of RenderPassEncoder
void RenderPassEncoder::beginOcclusionQuery(uint32_t queryIndex) const {
	return wgpuRenderPassEncoderBeginOcclusionQuery(m_raw, queryIndex);
}
void RenderPassEncoder::draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance) const {
	return wgpuRenderPassEncoderDraw(m_raw, vertexCount, instanceCount, firstVertex, firstInstance);
}
void RenderPassEncoder::drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t baseVertex, uint32_t firstInstance) const {
	return wgpuRenderPassEncoderDrawIndexed(m_raw, indexCount, instanceCount, firstIndex, baseVertex, firstInstance);
}
void RenderPassEncoder::drawIndexedIndirect(Buffer indirectBuffer, uint64_t indirectOffset) const {
	return wgpuRenderPassEncoderDrawIndexedIndirect(m_raw, indirectBuffer, indirectOffset);
}
void RenderPassEncoder::drawIndirect(Buffer indirectBuffer, uint64_t indirectOffset) const {
	return wgpuRenderPassEncoderDrawIndirect(m_raw, indirectBuffer, indirectOffset);
}
void RenderPassEncoder::end() const {
	return wgpuRenderPassEncoderEnd(m_raw);
}
void RenderPassEncoder::endOcclusionQuery() const {
	return wgpuRenderPassEncoderEndOcclusionQuery(m_raw);
}
void RenderPassEncoder::executeBundles(size_t bundleCount, RenderBundle const * bundles) const {
	return wgpuRenderPassEncoderExecuteBundles(m_raw, bundleCount, reinterpret_cast<WGPURenderBundle const *>(bundles));
}
void RenderPassEncoder::executeBundles(const std::vector<WGPURenderBundle>& bundles) const {
	return wgpuRenderPassEncoderExecuteBundles(m_raw, static_cast<size_t>(bundles.size()), bundles.data());
}
void RenderPassEncoder::executeBundles(const WGPURenderBundle& bundles) const {
	return wgpuRenderPassEncoderExecuteBundles(m_raw, 1, &bundles);
}
void RenderPassEncoder::insertDebugMarker(StringView markerLabel) const {
	return wgpuRenderPassEncoderInsertDebugMarker(m_raw, markerLabel);
}
void RenderPassEncoder::multiDrawIndexedIndirect(Buffer indirectBuffer, uint64_t indirectOffset, uint32_t maxDrawCount, Buffer drawCountBuffer, uint64_t drawCountBufferOffset) const {
	return wgpuRenderPassEncoderMultiDrawIndexedIndirect(m_raw, indirectBuffer, indirectOffset, maxDrawCount, drawCountBuffer, drawCountBufferOffset);
}
void RenderPassEncoder::multiDrawIndirect(Buffer indirectBuffer, uint64_t indirectOffset, uint32_t maxDrawCount, Buffer drawCountBuffer, uint64_t drawCountBufferOffset) const {
	return wgpuRenderPassEncoderMultiDrawIndirect(m_raw, indirectBuffer, indirectOffset, maxDrawCount, drawCountBuffer, drawCountBufferOffset);
}
void RenderPassEncoder::popDebugGroup() const {
	return wgpuRenderPassEncoderPopDebugGroup(m_raw);
}
void RenderPassEncoder::pushDebugGroup(StringView groupLabel) const {
	return wgpuRenderPassEncoderPushDebugGroup(m_raw, groupLabel);
}
void RenderPassEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, size_t dynamicOffsetCount, uint32_t const * dynamicOffsets) const {
	return wgpuRenderPassEncoderSetBindGroup(m_raw, groupIndex, group, dynamicOffsetCount, dynamicOffsets);
}
void RenderPassEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, const std::vector<uint32_t>& dynamicOffsets) const {
	return wgpuRenderPassEncoderSetBindGroup(m_raw, groupIndex, group, static_cast<size_t>(dynamicOffsets.size()), dynamicOffsets.data());
}
void RenderPassEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, const uint32_t& dynamicOffsets) const {
	return wgpuRenderPassEncoderSetBindGroup(m_raw, groupIndex, group, 1, &dynamicOffsets);
}
void RenderPassEncoder::setBlendConstant(const Color& color) const {
	return wgpuRenderPassEncoderSetBlendConstant(m_raw, &color);
}
void RenderPassEncoder::setIndexBuffer(Buffer buffer, IndexFormat format, uint64_t offset, uint64_t size) const {
	return wgpuRenderPassEncoderSetIndexBuffer(m_raw, buffer, static_cast<WGPUIndexFormat>(format), offset, size);
}
void RenderPassEncoder::setLabel(StringView label) const {
	return wgpuRenderPassEncoderSetLabel(m_raw, label);
}
void RenderPassEncoder::setPipeline(RenderPipeline pipeline) const {
	return wgpuRenderPassEncoderSetPipeline(m_raw, pipeline);
}
void RenderPassEncoder::setScissorRect(uint32_t x, uint32_t y, uint32_t width, uint32_t height) const {
	return wgpuRenderPassEncoderSetScissorRect(m_raw, x, y, width, height);
}
void RenderPassEncoder::setStencilReference(uint32_t reference) const {
	return wgpuRenderPassEncoderSetStencilReference(m_raw, reference);
}
void RenderPassEncoder::setVertexBuffer(uint32_t slot, Buffer buffer, uint64_t offset, uint64_t size) const {
	return wgpuRenderPassEncoderSetVertexBuffer(m_raw, slot, buffer, offset, size);
}
void RenderPassEncoder::setViewport(float x, float y, float width, float height, float minDepth, float maxDepth) const {
	return wgpuRenderPassEncoderSetViewport(m_raw, x, y, width, height, minDepth, maxDepth);
}
void RenderPassEncoder::writeTimestamp(QuerySet querySet, uint32_t queryIndex) const {
	return wgpuRenderPassEncoderWriteTimestamp(m_raw, querySet, queryIndex);
}
void RenderPassEncoder::addRef() const {
	return wgpuRenderPassEncoderAddRef(m_raw);
}
void RenderPassEncoder::release() const {
	return wgpuRenderPassEncoderRelease(m_raw);
}


// Methods of RenderPipeline
BindGroupLayout RenderPipeline::getBindGroupLayout(uint32_t groupIndex) const {
	return wgpuRenderPipelineGetBindGroupLayout(m_raw, groupIndex);
}
void RenderPipeline::setLabel(StringView label) const {
	return wgpuRenderPipelineSetLabel(m_raw, label);
}
void RenderPipeline::addRef() const {
	return wgpuRenderPipelineAddRef(m_raw);
}
void RenderPipeline::release() const {
	return wgpuRenderPipelineRelease(m_raw);
}


// Methods of Sampler
void Sampler::setLabel(StringView label) const {
	return wgpuSamplerSetLabel(m_raw, label);
}
void Sampler::addRef() const {
	return wgpuSamplerAddRef(m_raw);
}
void Sampler::release() const {
	return wgpuSamplerRelease(m_raw);
}


// Methods of ShaderModule
void ShaderModule::setLabel(StringView label) const {
	return wgpuShaderModuleSetLabel(m_raw, label);
}
void ShaderModule::addRef() const {
	return wgpuShaderModuleAddRef(m_raw);
}
void ShaderModule::release() const {
	return wgpuShaderModuleRelease(m_raw);
}


// Methods of Surface
void Surface::configure(const SurfaceConfiguration& config) const {
	return wgpuSurfaceConfigure(m_raw, &config);
}
Status Surface::getCapabilities(Adapter adapter, SurfaceCapabilities * capabilities) const {
	return static_cast<Status>(wgpuSurfaceGetCapabilities(m_raw, adapter, capabilities));
}
void Surface::getCurrentTexture(SurfaceTexture * surfaceTexture) const {
	return wgpuSurfaceGetCurrentTexture(m_raw, surfaceTexture);
}
void Surface::present() const {
	return wgpuSurfacePresent(m_raw);
}
void Surface::setLabel(StringView label) const {
	return wgpuSurfaceSetLabel(m_raw, label);
}
void Surface::unconfigure() const {
	return wgpuSurfaceUnconfigure(m_raw);
}
void Surface::addRef() const {
	return wgpuSurfaceAddRef(m_raw);
}
void Surface::release() const {
	return wgpuSurfaceRelease(m_raw);
}


// Methods of Texture
TextureView Texture::createView(const TextureViewDescriptor& descriptor) const {
	return wgpuTextureCreateView(m_raw, &descriptor);
}
TextureView Texture::createView() const {
	return wgpuTextureCreateView(m_raw, nullptr);
}
void Texture::destroy() const {
	return wgpuTextureDestroy(m_raw);
}
uint32_t Texture::getDepthOrArrayLayers() const {
	return wgpuTextureGetDepthOrArrayLayers(m_raw);
}
TextureDimension Texture::getDimension() const {
	return static_cast<TextureDimension>(wgpuTextureGetDimension(m_raw));
}
TextureFormat Texture::getFormat() const {
	return static_cast<TextureFormat>(wgpuTextureGetFormat(m_raw));
}
uint32_t Texture::getHeight() const {
	return wgpuTextureGetHeight(m_raw);
}
uint32_t Texture::getMipLevelCount() const {
	return wgpuTextureGetMipLevelCount(m_raw);
}
uint32_t Texture::getSampleCount() const {
	return wgpuTextureGetSampleCount(m_raw);
}
TextureUsage Texture::getUsage() const {
	return static_cast<TextureUsage>(wgpuTextureGetUsage(m_raw));
}
uint32_t Texture::getWidth() const {
	return wgpuTextureGetWidth(m_raw);
}
void Texture::setLabel(StringView label) const {
	return wgpuTextureSetLabel(m_raw, label);
}
void Texture::addRef() const {
	return wgpuTextureAddRef(m_raw);
}
void Texture::release() const {
	return wgpuTextureRelease(m_raw);
}


// Methods of TextureView
void TextureView::setLabel(StringView label) const {
	return wgpuTextureViewSetLabel(m_raw, label);
}
void TextureView::addRef() const {
	return wgpuTextureViewAddRef(m_raw);
}
void TextureView::release() const {
	return wgpuTextureViewRelease(m_raw);
}



// Extra implementations
Adapter Instance::requestAdapter(const RequestAdapterOptions& options) {
	struct Context {
		Adapter adapter = nullptr;
		bool requestEnded = false;
	};
	Context context;

	RequestAdapterCallbackInfo callbackInfo;
	callbackInfo.nextInChain = nullptr;
	callbackInfo.userdata1 = &context;
	callbackInfo.callback = [](
		WGPURequestAdapterStatus status,
		WGPUAdapter adapter,
		WGPUStringView message,
		void* userdata1,
		[[maybe_unused]] void* userdata2
	) {
		Context& context = *reinterpret_cast<Context*>(userdata1);
		if (status == RequestAdapterStatus::Success) {
			context.adapter = adapter;
		}
		else {
			std::cout << "Could not get WebGPU adapter: " << StringView(message) << std::endl;
		}
		context.requestEnded = true;
	};
	callbackInfo.mode = CallbackMode::AllowSpontaneous;
	wgpuInstanceRequestAdapter(*this, &options, callbackInfo);

#if __EMSCRIPTEN__
	while (!context.requestEnded) {
		emscripten_sleep(50);
	}
#endif

	assert(context.requestEnded);
	return context.adapter;
}

Device Adapter::requestDevice(const DeviceDescriptor& descriptor) {
	struct Context {
		Device device = nullptr;
		bool requestEnded = false;
	};
	Context context;

	RequestDeviceCallbackInfo callbackInfo;
	callbackInfo.nextInChain = nullptr;
	callbackInfo.userdata1 = &context;
	callbackInfo.callback = [](
		WGPURequestDeviceStatus status,
		WGPUDevice device,
		WGPUStringView message,
		void* userdata1,
		[[maybe_unused]] void* userdata2
	) {
		Context& context = *reinterpret_cast<Context*>(userdata1);
		if (status == RequestDeviceStatus::Success) {
			context.device = device;
		}
		else {
			std::cout << "Could not get WebGPU device: " << StringView(message) << std::endl;
		}
		context.requestEnded = true;
	};
	callbackInfo.mode = CallbackMode::AllowSpontaneous;
	wgpuAdapterRequestDevice(*this, &descriptor, callbackInfo);

#if __EMSCRIPTEN__
	while (!context.requestEnded) {
		emscripten_sleep(50);
	}
#endif

	assert(context.requestEnded);
	return context.device;
}

StringView::operator std::string_view() const {
	return
		length == WGPU_STRLEN
		? std::string_view(data)
		: std::string_view(data, length);
}

#endif // WEBGPU_CPP_IMPLEMENTATION

#undef HANDLE
#undef DESCRIPTOR
#undef ENUM
#undef ENUM_ENTRY
#undef END

} // namespace wgpu
