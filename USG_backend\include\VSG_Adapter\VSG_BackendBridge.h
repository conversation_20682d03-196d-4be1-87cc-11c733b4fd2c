#pragma once

#include <USG_Backend/BackendTypes.h>
#include <USG_Backend/RenderBackend.h>
#include <memory>
#include <vector>
#include <unordered_map>

// 前向声明VSG类型
namespace vsg {
    class Node;
    class Geometry;
    class StateGroup;
    class DescriptorSet;
    class GraphicsPipeline;
    class Buffer;
    class Image;
    class Sampler;
    class CommandBuffer;
    class RenderPass;
    class Framebuffer;
}

namespace USG {

    // 前向声明
    class BackendBuffer;
    class BackendTexture;
    class BackendShader;
    class BackendPipeline;
    class BackendDescriptorSet;
    class BackendCommandList;

    /**
     * @brief VSG到USG Backend的桥接器
     * 
     * 负责将VSG的渲染命令转换为USG Backend的调用
     */
    class VSG_BackendBridge {
    public:
        /**
         * @brief 资源映射信息
         */
        struct ResourceMapping {
            // VSG资源到USG Backend资源的映射
            std::unordered_map<void*, std::shared_ptr<BackendBuffer>> bufferMap;
            std::unordered_map<void*, std::shared_ptr<BackendTexture>> textureMap;
            std::unordered_map<void*, std::shared_ptr<BackendShader>> shaderMap;
            std::unordered_map<void*, std::shared_ptr<BackendPipeline>> pipelineMap;
            std::unordered_map<void*, std::shared_ptr<BackendDescriptorSet>> descriptorSetMap;
        };

        /**
         * @brief 渲染状态
         */
        struct RenderState {
            BackendPipeline* currentPipeline = nullptr;
            BackendDescriptorSet* currentDescriptorSet = nullptr;
            std::vector<BackendBuffer*> vertexBuffers;
            BackendBuffer* indexBuffer = nullptr;
            uint32_t indexCount = 0;
            uint32_t vertexCount = 0;
        };

    public:
        VSG_BackendBridge();
        ~VSG_BackendBridge();

        /**
         * @brief 初始化桥接器
         * @param backend 渲染后端
         * @return 是否成功
         */
        bool initialize(RenderBackend* backend);

        /**
         * @brief 清理桥接器
         */
        void cleanup();

        /**
         * @brief 开始渲染通道
         * @param renderPassDesc 渲染通道描述
         * @return 是否成功
         */
        bool beginRenderPass(const RenderPassDesc& renderPassDesc);

        /**
         * @brief 结束渲染通道
         */
        void endRenderPass();

        /**
         * @brief 转换VSG几何体到USG Backend资源
         * @param geometry VSG几何体
         * @return 是否成功
         */
        bool convertGeometry(std::shared_ptr<vsg::Geometry> geometry);

        /**
         * @brief 转换VSG状态组到USG Backend管线
         * @param stateGroup VSG状态组
         * @return 是否成功
         */
        bool convertStateGroup(std::shared_ptr<vsg::StateGroup> stateGroup);

        /**
         * @brief 转换VSG描述符集
         * @param descriptorSet VSG描述符集
         * @return 是否成功
         */
        bool convertDescriptorSet(std::shared_ptr<vsg::DescriptorSet> descriptorSet);

        /**
         * @brief 绘制几何体
         * @param geometry VSG几何体
         * @return 是否成功
         */
        bool drawGeometry(std::shared_ptr<vsg::Geometry> geometry);

        /**
         * @brief 设置变换矩阵
         * @param matrix 4x4变换矩阵
         */
        void setTransformMatrix(const float matrix[16]);

        /**
         * @brief 设置视图矩阵
         * @param matrix 4x4视图矩阵
         */
        void setViewMatrix(const float matrix[16]);

        /**
         * @brief 设置投影矩阵
         * @param matrix 4x4投影矩阵
         */
        void setProjectionMatrix(const float matrix[16]);

        /**
         * @brief 获取当前后端
         * @return 渲染后端指针
         */
        RenderBackend* getBackend() const { return _backend; }

        /**
         * @brief 获取资源映射
         * @return 资源映射引用
         */
        const ResourceMapping& getResourceMapping() const { return _resourceMapping; }

        /**
         * @brief 获取当前渲染状态
         * @return 渲染状态引用
         */
        const RenderState& getRenderState() const { return _renderState; }

        /**
         * @brief 刷新命令缓冲区
         */
        void flush();

        /**
         * @brief 等待渲染完成
         */
        void waitIdle();

        /**
         * @brief 创建默认着色器
         * @return 是否成功
         */
        bool createDefaultShaders();

        /**
         * @brief 创建默认管线
         * @return 是否成功
         */
        bool createDefaultPipeline();

    private:
        // 内部转换方法
        std::shared_ptr<BackendBuffer> convertVSGBuffer(std::shared_ptr<vsg::Buffer> vsgBuffer);
        std::shared_ptr<BackendTexture> convertVSGImage(std::shared_ptr<vsg::Image> vsgImage);
        std::shared_ptr<BackendShader> convertVSGShader(const std::string& shaderCode, ShaderStage stage);
        std::shared_ptr<BackendPipeline> convertVSGPipeline(std::shared_ptr<vsg::GraphicsPipeline> vsgPipeline);

        // 辅助方法
        BufferUsage convertVSGBufferUsage(uint32_t vsgUsage);
        TextureFormat convertVSGImageFormat(uint32_t vsgFormat);
        PrimitiveTopology convertVSGPrimitiveTopology(uint32_t vsgTopology);

        // 矩阵操作
        void updateUniformBuffer();

    private:
        // 渲染后端
        RenderBackend* _backend = nullptr;

        // 资源映射
        ResourceMapping _resourceMapping;

        // 渲染状态
        RenderState _renderState;

        // 命令列表
        std::shared_ptr<BackendCommandList> _commandList;

        // 统一缓冲区（用于矩阵等）
        std::shared_ptr<BackendBuffer> _uniformBuffer;
        struct UniformData {
            float modelMatrix[16];
            float viewMatrix[16];
            float projectionMatrix[16];
            float mvpMatrix[16];
        } _uniformData;

        // 默认资源
        std::shared_ptr<BackendShader> _defaultVertexShader;
        std::shared_ptr<BackendShader> _defaultFragmentShader;
        std::shared_ptr<BackendPipeline> _defaultPipeline;
        std::shared_ptr<BackendDescriptorSet> _defaultDescriptorSet;

        // 状态
        bool _initialized = false;
        bool _inRenderPass = false;
    };

} // namespace USG
