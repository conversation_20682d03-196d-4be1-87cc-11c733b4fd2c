#include "WebGPUBackend.h"
#include <cstring>

namespace USG {

// WebGPUDevice实现
WebGPUDevice::WebGPUDevice(WGPUDevice device) : _device(device) {
    queryCapabilities();
}

std::string WebGPUDevice::getDeviceName() const {
    return "WebGPU Device (" + WebGPUAdapter::getImplementationName() + ")";
}

DeviceCapabilities WebGPUDevice::getCapabilities() const {
    return _capabilities;
}

void* WebGPUDevice::mapBuffer(BackendBuffer* buffer, size_t offset, size_t size) {
    if (!buffer) {
        return nullptr;
    }
    
    auto* webgpuBuffer = static_cast<WebGPUBuffer*>(buffer);
    
    // 检查缓冲区是否已经映射
    if (webgpuBuffer->isMapped()) {
        return webgpuBuffer->getMappedData();
    }
    
    // WebGPU的映射是异步的，这里简化为同步实现
    // 实际实现应该使用回调或Promise
    
    // 获取映射的数据指针
    void* mappedData = const_cast<void*>(wgpuBufferGetConstMappedRange(webgpuBuffer->getWGPUBuffer(), offset, size));
    
    if (mappedData) {
        webgpuBuffer->setMappedData(mappedData);
    }
    
    return mappedData;
}

void WebGPUDevice::unmapBuffer(BackendBuffer* buffer) {
    if (!buffer) {
        return;
    }
    
    auto* webgpuBuffer = static_cast<WebGPUBuffer*>(buffer);
    
    if (webgpuBuffer->isMapped()) {
        wgpuBufferUnmap(webgpuBuffer->getWGPUBuffer());
        webgpuBuffer->setMappedData(nullptr);
    }
}

void WebGPUDevice::updateBuffer(BackendBuffer* buffer, const void* data, size_t size, size_t offset) {
    if (!buffer || !data || size == 0) {
        return;
    }
    
    auto* webgpuBuffer = static_cast<WebGPUBuffer*>(buffer);
    
    // 使用队列写入数据
    WGPUQueue queue = wgpuDeviceGetQueue(_device);
    wgpuQueueWriteBuffer(queue, webgpuBuffer->getWGPUBuffer(), offset, data, size);
}

void WebGPUDevice::updateTexture(BackendTexture* texture, const void* data, const TextureRegion& region) {
    if (!texture || !data) {
        return;
    }
    
    auto* webgpuTexture = static_cast<WebGPUTexture*>(texture);
    
    // 设置纹理数据源
    WGPUImageCopyTexture destination = {};
    destination.texture = webgpuTexture->getWGPUTexture();
    destination.mipLevel = region.mipLevel;
    destination.origin.x = region.x;
    destination.origin.y = region.y;
    destination.origin.z = region.z;
    destination.aspect = WGPUTextureAspect_All;
    
    // 设置纹理数据布局
    WGPUTextureDataLayout dataLayout = {};
    dataLayout.offset = 0;
    dataLayout.bytesPerRow = region.width * 4; // 假设RGBA8格式
    dataLayout.rowsPerImage = region.height;
    
    // 设置纹理尺寸
    WGPUExtent3D writeSize = {};
    writeSize.width = region.width;
    writeSize.height = region.height;
    writeSize.depthOrArrayLayers = region.depth;
    
    // 写入纹理数据
    WGPUQueue queue = wgpuDeviceGetQueue(_device);
    wgpuQueueWriteTexture(queue, &destination, data, region.width * region.height * 4, &dataLayout, &writeSize);
}

void WebGPUDevice::generateMipmaps(BackendTexture* texture) {
    if (!texture) {
        return;
    }
    
    // WebGPU没有内置的mipmap生成，需要使用计算着色器或渲染通道实现
    // 这里提供一个简化的实现框架
    
    auto* webgpuTexture = static_cast<WebGPUTexture*>(texture);
    uint32_t mipLevels = webgpuTexture->getMipLevels();
    
    if (mipLevels <= 1) {
        return; // 不需要生成mipmap
    }
    
    // 创建命令编码器
    WGPUCommandEncoderDescriptor encoderDesc = {};
    encoderDesc.label = "Mipmap Generation";
    WGPUCommandEncoder encoder = wgpuDeviceCreateCommandEncoder(_device, &encoderDesc);
    
    // 为每个mip级别创建渲染通道
    for (uint32_t mipLevel = 1; mipLevel < mipLevels; ++mipLevel) {
        // 这里应该实现mipmap生成的具体逻辑
        // 通常使用一个简单的下采样着色器
    }
    
    // 提交命令
    WGPUCommandBufferDescriptor cmdBufferDesc = {};
    cmdBufferDesc.label = "Mipmap Generation Commands";
    WGPUCommandBuffer commandBuffer = wgpuCommandEncoderFinish(encoder, &cmdBufferDesc);
    
    WGPUQueue queue = wgpuDeviceGetQueue(_device);
    wgpuQueueSubmit(queue, 1, &commandBuffer);
    
    // 清理
    wgpuCommandBufferRelease(commandBuffer);
    wgpuCommandEncoderRelease(encoder);
}

void WebGPUDevice::queryCapabilities() {
    // 查询WebGPU设备能力
    // 注意：WebGPU的能力查询API可能因实现而异
    
    // 设置默认能力
    _capabilities.maxTextureSize2D = 8192;
    _capabilities.maxTextureSize3D = 2048;
    _capabilities.maxTextureSizeCube = 8192;
    _capabilities.maxTextureArrayLayers = 256;
    _capabilities.maxColorAttachments = 8;
    _capabilities.maxVertexAttributes = 16;
    _capabilities.maxVertexBuffers = 8;
    _capabilities.maxUniformBufferBindings = 12;
    _capabilities.maxStorageBufferBindings = 8;
    _capabilities.maxSampledTextureBindings = 16;
    _capabilities.maxStorageTextureBindings = 8;
    _capabilities.maxSamplerBindings = 16;
    
    // 功能支持
    _capabilities.supportsComputeShaders = true;
    _capabilities.supportsGeometryShaders = false; // WebGPU不支持几何着色器
    _capabilities.supportsTessellationShaders = false; // WebGPU不支持细分着色器
    _capabilities.supportsMultiDrawIndirect = false; // 取决于具体实现
    _capabilities.supportsTimestampQueries = false; // 取决于具体实现
    _capabilities.supportsPipelineStatistics = false; // 取决于具体实现
    
    // 纹理格式支持
    _capabilities.supportsBC1 = false; // 取决于平台
    _capabilities.supportsBC2 = false;
    _capabilities.supportsBC3 = false;
    _capabilities.supportsBC4 = false;
    _capabilities.supportsBC5 = false;
    _capabilities.supportsBC6H = false;
    _capabilities.supportsBC7 = false;
    _capabilities.supportsETC2 = false;
    _capabilities.supportsASTC = false;
    
    // 其他限制
    _capabilities.maxUniformBufferSize = 65536; // 64KB
    _capabilities.maxStorageBufferSize = 134217728; // 128MB
    _capabilities.maxPushConstantSize = 0; // WebGPU没有push constants
    _capabilities.minUniformBufferAlignment = 256;
    _capabilities.minStorageBufferAlignment = 256;
    
    // 可以根据具体的WebGPU实现查询更精确的能力
    // 例如通过wgpuAdapterGetLimits()等API
}

} // namespace USG
