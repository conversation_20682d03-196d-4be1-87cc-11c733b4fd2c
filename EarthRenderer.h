#pragma once

#include <webgpu/webgpu.hpp>
#include <glm/glm.hpp>
#include <vector>
#include <memory>
#include <string>
#include <unordered_map>

using namespace wgpu;

struct TileInfo
{
    int x, y, z; // 瓦片坐标和缩放级别
    std::string url;
    Texture texture;
    bool loaded = false;
};

struct EarthVertex
{
    glm::vec3 position;
    glm::vec2 texCoord;
    glm::vec3 normal;
};

class EarthRenderer
{
public:
    EarthRenderer();
    ~EarthRenderer();

    bool initialize(Device device, TextureFormat swapChainFormat);
    void render(RenderPassEncoder renderPass, const glm::mat4 &viewMatrix, const glm::mat4 &projMatrix);
    void render(RenderPassEncoder renderPass, const glm::mat4 &viewMatrix, const glm::mat4 &projMatrix, bool wireframeMode);
    void update(float deltaTime);
    void cleanup();

    // 瓦片纹理管理
    void createTileTexture(int x, int y, int z);
    void updateBindGroupWithTileTexture(const Texture &tileTexture);

    // 地球控制
    void setRadius(float radius) { m_earthRadius = radius; }
    void setPosition(const glm::vec3 &position) { m_position = position; }
    void setRotation(const glm::vec3 &rotation) { m_rotation = rotation; }

    // 瓦片管理
    void loadTile(int x, int y, int z);
    void updateTiles(const glm::vec3 &cameraPos);

private:
    // 几何体生成
    void generateSphere(int segments = 64);
    void generateTileGeometry(int x, int y, int z, std::vector<EarthVertex> &vertices, std::vector<uint32_t> &indices);

    // 瓦片系统
    std::string getTileUrl(int x, int y, int z);
    void loadTileTexture(TileInfo &tile);

    // WebGPU资源
    Device m_device;
    RenderPipeline m_pipeline;
    Buffer m_vertexBuffer;
    Buffer m_indexBuffer;
    Buffer m_uniformBuffer;
    BindGroup m_bindGroup;
    BindGroupLayout m_bindGroupLayout;
    Sampler m_sampler;

    // 地球参数
    float m_earthRadius = 6371000.0f; // 地球半径（米）
    glm::vec3 m_position = glm::vec3(0.0f);
    glm::vec3 m_rotation = glm::vec3(0.0f);

    // 几何数据
    std::vector<EarthVertex> m_vertices;
    std::vector<uint32_t> m_indices;
    uint32_t m_indexCount = 0;

    // 瓦片系统
    std::unordered_map<std::string, std::unique_ptr<TileInfo>> m_tiles;
    int m_currentZoomLevel = 2;
    Texture m_defaultTexture;

    // 瓦片纹理管理
    std::vector<Texture> m_tileTextures;
    int m_maxTileTextures = 64; // 最大缓存的瓦片纹理数量

    // 着色器
    ShaderModule m_shaderModule;

    // 统一缓冲区数据
    struct Uniforms
    {
        glm::mat4 modelMatrix;
        glm::mat4 viewMatrix;
        glm::mat4 projMatrix;
        glm::vec3 lightDirection;
        float time;
    };
};

// C接口函数（供JavaScript调用）
extern "C"
{
    void switchToEarthMode();
    void switchToModelMode();
    void resetCamera();
    void toggleWireframe();
}
