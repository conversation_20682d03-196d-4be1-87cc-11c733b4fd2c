#include "SimpleScene.h"
#include <USG_Backend/RenderBackend.h>
#include <iostream>
#include <cmath>

namespace USG
{

    SimpleScene::SimpleScene()
    {
    }

    SimpleScene::~SimpleScene()
    {
        cleanup();
    }

    bool SimpleScene::initialize(RenderBackend *backend, const SceneConfig &config)
    {
        if (!backend)
        {
            std::cerr << "[SimpleScene] Invalid backend" << std::endl;
            return false;
        }

        _backend = backend;
        _config = config;

        std::cout << "[SimpleScene] Initializing scene..." << std::endl;

        // 创建着色器
        if (!createShaders())
        {
            std::cerr << "[SimpleScene] Failed to create shaders" << std::endl;
            return false;
        }

        // 创建管线
        if (!createPipeline())
        {
            std::cerr << "[SimpleScene] Failed to create pipeline" << std::endl;
            return false;
        }

        // 创建一个简单的三角形
        addObject(ObjectType::Triangle, "TestTriangle");

        _initialized = true;

        std::cout << "[SimpleScene] Scene initialized successfully" << std::endl;
        return true;
    }

    void SimpleScene::cleanup()
    {
        if (!_initialized)
        {
            return;
        }

        std::cout << "[SimpleScene] Cleaning up scene..." << std::endl;

        // 清理场景对象
        _objects.clear();

        // 清理渲染资源
        _vertexShader.reset();
        _fragmentShader.reset();
        _pipeline.reset();
        _uniformBuffer.reset();

        _backend = nullptr;
        _initialized = false;

        std::cout << "[SimpleScene] Scene cleaned up" << std::endl;
    }

    int SimpleScene::addObject(ObjectType type, const std::string &name)
    {
        SceneObject object;
        object.type = type;
        object.name = name.empty() ? ("Object_" + std::to_string(_objects.size())) : name;

        // 创建几何体
        if (!createGeometry(type, object))
        {
            std::cerr << "[SimpleScene] Failed to create geometry for " << object.name << std::endl;
            return -1;
        }

        _objects.push_back(std::move(object));

        std::cout << "[SimpleScene] Added object: " << object.name << std::endl;
        return static_cast<int>(_objects.size() - 1);
    }

    void SimpleScene::removeObject(int index)
    {
        if (index >= 0 && index < static_cast<int>(_objects.size()))
        {
            std::cout << "[SimpleScene] Removed object: " << _objects[index].name << std::endl;
            _objects.erase(_objects.begin() + index);
        }
    }

    SimpleScene::SceneObject *SimpleScene::getObject(int index)
    {
        if (index >= 0 && index < static_cast<int>(_objects.size()))
        {
            return &_objects[index];
        }
        return nullptr;
    }

    SimpleScene::SceneObject *SimpleScene::getObject(const std::string &name)
    {
        for (auto &object : _objects)
        {
            if (object.name == name)
            {
                return &object;
            }
        }
        return nullptr;
    }

    void SimpleScene::update(double deltaTime)
    {
        if (!_initialized)
        {
            return;
        }

        _totalTime += deltaTime;

        // 更新场景对象（简单的旋转动画）
        for (auto &object : _objects)
        {
            if (object.visible)
            {
                object.rotation[1] += static_cast<float>(deltaTime * 45.0); // 45度/秒
                if (object.rotation[1] > 360.0f)
                {
                    object.rotation[1] -= 360.0f;
                }
            }
        }

        // 更新统计信息
        _renderStats.objectCount = static_cast<uint32_t>(_objects.size());
        _renderStats.visibleObjectCount = 0;
        _renderStats.triangleCount = 0;

        for (const auto &object : _objects)
        {
            if (object.visible)
            {
                _renderStats.visibleObjectCount++;
                // 简化：假设每个立方体有12个三角形
                if (object.type == ObjectType::Cube)
                {
                    _renderStats.triangleCount += 12;
                }
            }
        }

        _renderStats.drawCalls = _renderStats.visibleObjectCount;
    }

    bool SimpleScene::render(BackendCommandList *commandList, const float viewMatrix[16], const float projMatrix[16])
    {
        if (!_initialized || !commandList)
        {
            return false;
        }

        // 开始命令缓冲区记录
        commandList->begin();

        // 开始渲染通道
        RenderPassDesc renderPassDesc = {};
        renderPassDesc.clearColor[0] = _config.clearColor[0];
        renderPassDesc.clearColor[1] = _config.clearColor[1];
        renderPassDesc.clearColor[2] = _config.clearColor[2];
        renderPassDesc.clearColor[3] = _config.clearColor[3];
        renderPassDesc.hasDepthStencil = true;
        renderPassDesc.clearDepth = 1.0f;

        commandList->beginRenderPass(renderPassDesc);

        // 设置渲染管线
        if (_pipeline)
        {
            commandList->setPipeline(_pipeline.get());
        }

        // 渲染所有可见对象
        for (const auto &object : _objects)
        {
            if (!object.visible)
            {
                continue;
            }

            // 绑定顶点缓冲区
            if (object.vertexBuffer)
            {
                commandList->setVertexBuffer(object.vertexBuffer.get(), 0, 0);
            }

            // 绑定索引缓冲区并绘制
            if (object.indexBuffer && object.indexCount > 0)
            {
                commandList->setIndexBuffer(object.indexBuffer.get(), IndexFormat::UInt32);
                commandList->drawIndexed(object.indexCount, 1, 0, 0, 0);
            }
            else if (object.vertexCount > 0)
            {
                commandList->draw(object.vertexCount, 1, 0, 0);
            }
        }

        commandList->endRenderPass();

        // 结束命令缓冲区记录
        commandList->end();

        std::cout << "[SimpleScene] Rendered " << _renderStats.visibleObjectCount << " objects" << std::endl;

        return true;
    }

    void SimpleScene::setSceneConfig(const SceneConfig &config)
    {
        _config = config;
    }

    bool SimpleScene::switchBackend(RenderBackend *newBackend)
    {
        if (!newBackend)
        {
            return false;
        }

        std::cout << "[SimpleScene] Switching backend..." << std::endl;

        // 保存当前状态
        auto oldObjects = _objects;
        auto oldConfig = _config;

        // 清理当前资源
        cleanup();

        // 使用新后端重新初始化
        if (!initialize(newBackend, oldConfig))
        {
            std::cerr << "[SimpleScene] Failed to reinitialize with new backend" << std::endl;
            return false;
        }

        // 重新创建场景对象
        for (const auto &oldObject : oldObjects)
        {
            int index = addObject(oldObject.type, oldObject.name);
            if (index >= 0)
            {
                auto *newObject = getObject(index);
                if (newObject)
                {
                    // 复制变换信息
                    for (int i = 0; i < 3; ++i)
                    {
                        newObject->position[i] = oldObject.position[i];
                        newObject->rotation[i] = oldObject.rotation[i];
                        newObject->scale[i] = oldObject.scale[i];
                    }
                    for (int i = 0; i < 4; ++i)
                    {
                        newObject->color[i] = oldObject.color[i];
                    }
                    newObject->visible = oldObject.visible;
                }
            }
        }

        std::cout << "[SimpleScene] Backend switch completed" << std::endl;
        return true;
    }

    // 私有方法实现
    bool SimpleScene::createShaders()
    {
        if (!_backend)
        {
            return false;
        }

        // 简单的顶点着色器代码（WGSL格式）
        std::string vertexShaderCode = R"(
        struct VertexInput {
            @location(0) position: vec3<f32>,
            @location(1) color: vec3<f32>,
        }
        
        struct VertexOutput {
            @builtin(position) position: vec4<f32>,
            @location(0) color: vec3<f32>,
        }
        
        struct Uniforms {
            mvpMatrix: mat4x4<f32>,
        }
        
        @group(0) @binding(0) var<uniform> uniforms: Uniforms;
        
        @vertex
        fn vs_main(input: VertexInput) -> VertexOutput {
            var output: VertexOutput;
            output.position = uniforms.mvpMatrix * vec4<f32>(input.position, 1.0);
            output.color = input.color;
            return output;
        }
    )";

        // 简单的片段着色器代码（WGSL格式）
        std::string fragmentShaderCode = R"(
        struct FragmentInput {
            @location(0) color: vec3<f32>,
        }
        
        @fragment
        fn fs_main(input: FragmentInput) -> @location(0) vec4<f32> {
            return vec4<f32>(input.color, 1.0);
        }
    )";

        // 创建着色器描述符
        ShaderDesc vertexDesc;
        vertexDesc.stage = ShaderStage::Vertex;
        vertexDesc.entryPoint = "vs_main";
        vertexDesc.label = "SimpleScene Vertex Shader";
        vertexDesc.bytecode = std::vector<uint8_t>(vertexShaderCode.begin(), vertexShaderCode.end());

        ShaderDesc fragmentDesc;
        fragmentDesc.stage = ShaderStage::Fragment;
        fragmentDesc.entryPoint = "fs_main";
        fragmentDesc.label = "SimpleScene Fragment Shader";
        fragmentDesc.bytecode = std::vector<uint8_t>(fragmentShaderCode.begin(), fragmentShaderCode.end());

        // 创建着色器
        _vertexShader.reset(_backend->createShader(vertexDesc));
        _fragmentShader.reset(_backend->createShader(fragmentDesc));

        if (!_vertexShader || !_fragmentShader)
        {
            std::cerr << "[SimpleScene] Failed to create shaders" << std::endl;
            return false;
        }

        std::cout << "[SimpleScene] Shaders created successfully" << std::endl;
        return true;
    }

    bool SimpleScene::createPipeline()
    {
        if (!_backend || !_vertexShader || !_fragmentShader)
        {
            return false;
        }

        // 创建管线描述符
        PipelineDesc pipelineDesc;
        pipelineDesc.label = "SimpleScene Pipeline";
        pipelineDesc.vertexShader = _vertexShader.get();
        pipelineDesc.fragmentShader = _fragmentShader.get();
        pipelineDesc.primitiveTopology = PrimitiveTopology::TriangleList;
        pipelineDesc.isComputePipeline = false;

        // 创建管线
        _pipeline.reset(_backend->createPipeline(pipelineDesc));

        if (!_pipeline)
        {
            std::cerr << "[SimpleScene] Failed to create pipeline" << std::endl;
            return false;
        }

        std::cout << "[SimpleScene] Pipeline created successfully" << std::endl;
        return true;
    }

    bool SimpleScene::createGeometry(ObjectType type, SceneObject &object)
    {
        switch (type)
        {
        case ObjectType::Cube:
            return createCubeGeometry(object);
        case ObjectType::Triangle:
            return createTriangleGeometry(object);
        default:
            std::cerr << "[SimpleScene] Unsupported object type" << std::endl;
            return false;
        }
    }

    bool SimpleScene::createCubeGeometry(SceneObject &object)
    {
        // 简化的立方体顶点数据
        struct Vertex
        {
            float position[3];
            float color[3];
        };

        // 立方体顶点（8个顶点）
        std::vector<Vertex> vertices = {
            // 前面
            {{-0.5f, -0.5f, 0.5f}, {1.0f, 0.0f, 0.0f}},
            {{0.5f, -0.5f, 0.5f}, {0.0f, 1.0f, 0.0f}},
            {{0.5f, 0.5f, 0.5f}, {0.0f, 0.0f, 1.0f}},
            {{-0.5f, 0.5f, 0.5f}, {1.0f, 1.0f, 0.0f}},
            // 后面
            {{-0.5f, -0.5f, -0.5f}, {1.0f, 0.0f, 1.0f}},
            {{0.5f, -0.5f, -0.5f}, {0.0f, 1.0f, 1.0f}},
            {{0.5f, 0.5f, -0.5f}, {1.0f, 1.0f, 1.0f}},
            {{-0.5f, 0.5f, -0.5f}, {0.5f, 0.5f, 0.5f}},
        };

        // 立方体索引（12个三角形）
        std::vector<uint16_t> indices = {
            // 前面
            0, 1, 2, 2, 3, 0,
            // 后面
            4, 6, 5, 6, 4, 7,
            // 左面
            4, 0, 3, 3, 7, 4,
            // 右面
            1, 5, 6, 6, 2, 1,
            // 上面
            3, 2, 6, 6, 7, 3,
            // 下面
            4, 5, 1, 1, 0, 4};

        object.vertexCount = static_cast<uint32_t>(vertices.size());
        object.indexCount = static_cast<uint32_t>(indices.size());

        // 在实际实现中，这里会创建顶点缓冲区和索引缓冲区
        // 由于我们还没有完整实现所有后端，这里只是占位符

        std::cout << "[SimpleScene] Created cube geometry: " << object.vertexCount
                  << " vertices, " << object.indexCount << " indices" << std::endl;

        return true;
    }

    bool SimpleScene::createTriangleGeometry(SceneObject &object)
    {
        // 三角形顶点数据 (位置 + 颜色)
        float vertices[] = {
            // 位置          // 颜色
            0.0f, 0.5f, 0.0f, 1.0f, 0.0f, 0.0f,   // 顶部 - 红色
            -0.5f, -0.5f, 0.0f, 0.0f, 1.0f, 0.0f, // 左下 - 绿色
            0.5f, -0.5f, 0.0f, 0.0f, 0.0f, 1.0f   // 右下 - 蓝色
        };

        uint32_t indices[] = {
            0, 1, 2 // 三角形
        };

        object.vertexCount = 3;
        object.indexCount = 3;

        // 创建顶点缓冲区
        BufferDesc vertexBufferDesc = {};
        vertexBufferDesc.size = sizeof(vertices);
        vertexBufferDesc.usage = BufferUsage::Vertex;
        vertexBufferDesc.memoryProperties = MemoryProperty::HostVisible;
        vertexBufferDesc.label = "Triangle Vertex Buffer";

        object.vertexBuffer.reset(_backend->createBuffer(vertexBufferDesc));
        if (!object.vertexBuffer)
        {
            std::cerr << "[SimpleScene] Failed to create vertex buffer" << std::endl;
            return false;
        }

        // 创建索引缓冲区
        BufferDesc indexBufferDesc = {};
        indexBufferDesc.size = sizeof(indices);
        indexBufferDesc.usage = BufferUsage::Index;
        indexBufferDesc.memoryProperties = MemoryProperty::HostVisible;
        indexBufferDesc.label = "Triangle Index Buffer";

        object.indexBuffer.reset(_backend->createBuffer(indexBufferDesc));
        if (!object.indexBuffer)
        {
            std::cerr << "[SimpleScene] Failed to create index buffer" << std::endl;
            return false;
        }

        std::cout << "[SimpleScene] Created triangle geometry: "
                  << object.vertexCount << " vertices, " << object.indexCount << " indices" << std::endl;
        return true;
    }

} // namespace USG
