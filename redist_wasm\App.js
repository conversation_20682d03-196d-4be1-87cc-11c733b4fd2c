var Module=typeof Module!="undefined"?Module:{};var ENVIRONMENT_IS_WEB=typeof window=="object";var ENVIRONMENT_IS_WORKER=typeof WorkerGlobalScope!="undefined";var ENVIRONMENT_IS_NODE=typeof process=="object"&&process.versions?.node&&process.type!="renderer";Module["expectedDataFileDownloads"]??=0;Module["expectedDataFileDownloads"]++;(()=>{var isPthread=typeof ENVIRONMENT_IS_PTHREAD!="undefined"&&ENVIRONMENT_IS_PTHREAD;var isWasmWorker=typeof ENVIRONMENT_IS_WASM_WORKER!="undefined"&&ENVIRONMENT_IS_WASM_WORKER;if(isPthread||isWasmWorker)return;var isNode=typeof process==="object"&&typeof process.versions==="object"&&typeof process.versions.node==="string";function loadPackage(metadata){var PACKAGE_PATH="";if(typeof window==="object"){PACKAGE_PATH=window["encodeURIComponent"](window.location.pathname.substring(0,window.location.pathname.lastIndexOf("/"))+"/")}else if(typeof process==="undefined"&&typeof location!=="undefined"){PACKAGE_PATH=encodeURIComponent(location.pathname.substring(0,location.pathname.lastIndexOf("/"))+"/")}var PACKAGE_NAME="App.data";var REMOTE_PACKAGE_BASE="App.data";var REMOTE_PACKAGE_NAME=Module["locateFile"]?Module["locateFile"](REMOTE_PACKAGE_BASE,""):REMOTE_PACKAGE_BASE;var REMOTE_PACKAGE_SIZE=metadata["remote_package_size"];function fetchRemotePackage(packageName,packageSize,callback,errback){if(isNode){require("fs").readFile(packageName,(err,contents)=>{if(err){errback(err)}else{callback(contents.buffer)}});return}Module["dataFileDownloads"]??={};fetch(packageName).catch(cause=>Promise.reject(new Error(`Network Error: ${packageName}`,{cause}))).then(response=>{if(!response.ok){return Promise.reject(new Error(`${response.status}: ${response.url}`))}if(!response.body&&response.arrayBuffer){return response.arrayBuffer().then(callback)}const reader=response.body.getReader();const iterate=()=>reader.read().then(handleChunk).catch(cause=>Promise.reject(new Error(`Unexpected error while handling : ${response.url} ${cause}`,{cause})));const chunks=[];const headers=response.headers;const total=Number(headers.get("Content-Length")??packageSize);let loaded=0;const handleChunk=({done,value})=>{if(!done){chunks.push(value);loaded+=value.length;Module["dataFileDownloads"][packageName]={loaded,total};let totalLoaded=0;let totalSize=0;for(const download of Object.values(Module["dataFileDownloads"])){totalLoaded+=download.loaded;totalSize+=download.total}Module["setStatus"]?.(`Downloading data... (${totalLoaded}/${totalSize})`);return iterate()}else{const packageData=new Uint8Array(chunks.map(c=>c.length).reduce((a,b)=>a+b,0));let offset=0;for(const chunk of chunks){packageData.set(chunk,offset);offset+=chunk.length}callback(packageData.buffer)}};Module["setStatus"]?.("Downloading data...");return iterate()})}function handleError(error){console.error("package error:",error)}var fetchedCallback=null;var fetched=Module["getPreloadedPackage"]?Module["getPreloadedPackage"](REMOTE_PACKAGE_NAME,REMOTE_PACKAGE_SIZE):null;if(!fetched)fetchRemotePackage(REMOTE_PACKAGE_NAME,REMOTE_PACKAGE_SIZE,data=>{if(fetchedCallback){fetchedCallback(data);fetchedCallback=null}else{fetched=data}},handleError);function runWithFS(Module){function assert(check,msg){if(!check)throw msg+(new Error).stack}Module["FS_createPath"]("/","resources",true,true);function DataRequest(start,end,audio){this.start=start;this.end=end;this.audio=audio}DataRequest.prototype={requests:{},open:function(mode,name){this.name=name;this.requests[name]=this;Module["addRunDependency"](`fp ${this.name}`)},send:function(){},onload:function(){var byteArray=this.byteArray.subarray(this.start,this.end);this.finish(byteArray)},finish:function(byteArray){var that=this;Module["FS_createDataFile"](this.name,null,byteArray,true,true,true);Module["removeRunDependency"](`fp ${that.name}`);this.requests[this.name]=null}};var files=metadata["files"];for(var i=0;i<files.length;++i){new DataRequest(files[i]["start"],files[i]["end"],files[i]["audio"]||0).open("GET",files[i]["filename"])}function processPackageData(arrayBuffer){assert(arrayBuffer,"Loading data file failed.");assert(arrayBuffer.constructor.name===ArrayBuffer.name,"bad input to processPackageData");var byteArray=new Uint8Array(arrayBuffer);DataRequest.prototype.byteArray=byteArray;var files=metadata["files"];for(var i=0;i<files.length;++i){DataRequest.prototype.requests[files[i].filename].onload()}Module["removeRunDependency"]("datafile_App.data")}Module["addRunDependency"]("datafile_App.data");Module["preloadResults"]??={};Module["preloadResults"][PACKAGE_NAME]={fromCache:false};if(fetched){processPackageData(fetched);fetched=null}else{fetchedCallback=processPackageData}}if(Module["calledRun"]){runWithFS(Module)}else{(Module["preRun"]??=[]).push(runWithFS)}}loadPackage({files:[{filename:"/resources/earth_shader.wgsl",start:0,end:2451},{filename:"/resources/fourareen.mtl",start:2451,end:2773},{filename:"/resources/fourareen.obj",start:2773,end:5241319},{filename:"/resources/fourareen2K_albedo.jpg",start:5241319,end:6646001},{filename:"/resources/shader.wgsl",start:6646001,end:6648538},{filename:"/resources/wireframe_shader.wgsl",start:6648538,end:6650228}],remote_package_size:6650228})})();var arguments_=[];var thisProgram="./this.program";var quit_=(status,toThrow)=>{throw toThrow};var _scriptName=typeof document!="undefined"?document.currentScript?.src:undefined;if(typeof __filename!="undefined"){_scriptName=__filename}else if(ENVIRONMENT_IS_WORKER){_scriptName=self.location.href}var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var readAsync,readBinary;if(ENVIRONMENT_IS_NODE){var fs=require("fs");scriptDirectory=__dirname+"/";readBinary=filename=>{filename=isFileURI(filename)?new URL(filename):filename;var ret=fs.readFileSync(filename);return ret};readAsync=async(filename,binary=true)=>{filename=isFileURI(filename)?new URL(filename):filename;var ret=fs.readFileSync(filename,binary?undefined:"utf8");return ret};if(process.argv.length>1){thisProgram=process.argv[1].replace(/\\/g,"/")}arguments_=process.argv.slice(2);if(typeof module!="undefined"){module["exports"]=Module}quit_=(status,toThrow)=>{process.exitCode=status;throw toThrow}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){try{scriptDirectory=new URL(".",_scriptName).href}catch{}{if(ENVIRONMENT_IS_WORKER){readBinary=url=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=async url=>{if(isFileURI(url)){return new Promise((resolve,reject)=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=()=>{if(xhr.status==200||xhr.status==0&&xhr.response){resolve(xhr.response);return}reject(xhr.status)};xhr.onerror=reject;xhr.send(null)})}var response=await fetch(url,{credentials:"same-origin"});if(response.ok){return response.arrayBuffer()}throw new Error(response.status+" : "+response.url)}}}else{}var out=console.log.bind(console);var err=console.error.bind(console);var wasmBinary;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort(text)}}var isFileURI=filename=>filename.startsWith("file://");var wasmMemory;var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;var HEAP64,HEAPU64;var runtimeInitialized=false;function updateMemoryViews(){var b=wasmMemory.buffer;HEAP8=new Int8Array(b);HEAP16=new Int16Array(b);HEAPU8=new Uint8Array(b);HEAPU16=new Uint16Array(b);HEAP32=new Int32Array(b);HEAPU32=new Uint32Array(b);HEAPF32=new Float32Array(b);HEAPF64=new Float64Array(b);HEAP64=new BigInt64Array(b);HEAPU64=new BigUint64Array(b)}function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(onPreRuns)}function initRuntime(){runtimeInitialized=true;if(!Module["noFSInit"]&&!FS.initialized)FS.init();TTY.init();wasmExports["Ta"]();FS.ignorePermissions=false}function preMain(){}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(onPostRuns)}var runDependencies=0;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;Module["monitorRunDependencies"]?.(runDependencies)}function removeRunDependency(id){runDependencies--;Module["monitorRunDependencies"]?.(runDependencies);if(runDependencies==0){if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){Module["onAbort"]?.(what);what="Aborted("+what+")";err(what);ABORT=true;what+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(what);throw e}var wasmBinaryFile;function findWasmBinary(){return locateFile("App.wasm")}function getBinarySync(file){if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}throw"both async and sync fetching of the wasm failed"}async function getWasmBinary(binaryFile){if(!wasmBinary){try{var response=await readAsync(binaryFile);return new Uint8Array(response)}catch{}}return getBinarySync(binaryFile)}async function instantiateArrayBuffer(binaryFile,imports){try{var binary=await getWasmBinary(binaryFile);var instance=await WebAssembly.instantiate(binary,imports);return instance}catch(reason){err(`failed to asynchronously prepare wasm: ${reason}`);abort(reason)}}async function instantiateAsync(binary,binaryFile,imports){if(!binary&&typeof WebAssembly.instantiateStreaming=="function"&&!isFileURI(binaryFile)&&!ENVIRONMENT_IS_NODE){try{var response=fetch(binaryFile,{credentials:"same-origin"});var instantiationResult=await WebAssembly.instantiateStreaming(response,imports);return instantiationResult}catch(reason){err(`wasm streaming compile failed: ${reason}`);err("falling back to ArrayBuffer instantiation")}}return instantiateArrayBuffer(binaryFile,imports)}function getWasmImports(){return{a:wasmImports}}async function createWasm(){function receiveInstance(instance,module){wasmExports=instance.exports;wasmExports=Asyncify.instrumentWasmExports(wasmExports);wasmExports=applySignatureConversions(wasmExports);wasmMemory=wasmExports["Sa"];updateMemoryViews();assignWasmExports(wasmExports);removeRunDependency("wasm-instantiate");return wasmExports}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){return receiveInstance(result["instance"])}var info=getWasmImports();if(Module["instantiateWasm"]){return new Promise((resolve,reject)=>{Module["instantiateWasm"](info,(mod,inst)=>{resolve(receiveInstance(mod,inst))})})}wasmBinaryFile??=findWasmBinary();var result=await instantiateAsync(wasmBinary,wasmBinaryFile,info);var exports=receiveInstantiationResult(result);return exports}class ExitStatus{name="ExitStatus";constructor(status){this.message=`Program terminated with exit(${status})`;this.status=status}}var callRuntimeCallbacks=callbacks=>{while(callbacks.length>0){callbacks.shift()(Module)}};var onPostRuns=[];var addOnPostRun=cb=>onPostRuns.push(cb);var onPreRuns=[];var addOnPreRun=cb=>onPreRuns.push(cb);var dynCalls={};var noExitRuntime=true;var stackRestore=val=>__emscripten_stack_restore(val);var stackSave=()=>_emscripten_stack_get_current();class ExceptionInfo{constructor(excPtr){this.excPtr=excPtr;this.ptr=excPtr-24}set_type(type){HEAPU32[this.ptr+4>>>2>>>0]=type}get_type(){return HEAPU32[this.ptr+4>>>2>>>0]}set_destructor(destructor){HEAPU32[this.ptr+8>>>2>>>0]=destructor}get_destructor(){return HEAPU32[this.ptr+8>>>2>>>0]}set_caught(caught){caught=caught?1:0;HEAP8[this.ptr+12>>>0]=caught}get_caught(){return HEAP8[this.ptr+12>>>0]!=0}set_rethrown(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>>0]=rethrown}get_rethrown(){return HEAP8[this.ptr+13>>>0]!=0}init(type,destructor){this.set_adjusted_ptr(0);this.set_type(type);this.set_destructor(destructor)}set_adjusted_ptr(adjustedPtr){HEAPU32[this.ptr+16>>>2>>>0]=adjustedPtr}get_adjusted_ptr(){return HEAPU32[this.ptr+16>>>2>>>0]}}var exceptionLast=0;var uncaughtExceptionCount=0;var INT53_MAX=9007199254740992;var INT53_MIN=-9007199254740992;var bigintToI53Checked=num=>num<INT53_MIN||num>INT53_MAX?NaN:Number(num);function ___cxa_throw(ptr,type,destructor){ptr>>>=0;type>>>=0;destructor>>>=0;var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw exceptionLast}var syscallGetVarargI=()=>{var ret=HEAP32[+SYSCALLS.varargs>>>2>>>0];SYSCALLS.varargs+=4;return ret};var syscallGetVarargP=syscallGetVarargI;var PATH={isAbs:path=>path.charAt(0)==="/",splitPath:filename=>{var splitPathRe=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;return splitPathRe.exec(filename).slice(1)},normalizeArray:(parts,allowAboveRoot)=>{var up=0;for(var i=parts.length-1;i>=0;i--){var last=parts[i];if(last==="."){parts.splice(i,1)}else if(last===".."){parts.splice(i,1);up++}else if(up){parts.splice(i,1);up--}}if(allowAboveRoot){for(;up;up--){parts.unshift("..")}}return parts},normalize:path=>{var isAbsolute=PATH.isAbs(path),trailingSlash=path.slice(-1)==="/";path=PATH.normalizeArray(path.split("/").filter(p=>!!p),!isAbsolute).join("/");if(!path&&!isAbsolute){path="."}if(path&&trailingSlash){path+="/"}return(isAbsolute?"/":"")+path},dirname:path=>{var result=PATH.splitPath(path),root=result[0],dir=result[1];if(!root&&!dir){return"."}if(dir){dir=dir.slice(0,-1)}return root+dir},basename:path=>path&&path.match(/([^\/]+|\/)\/*$/)[1],join:(...paths)=>PATH.normalize(paths.join("/")),join2:(l,r)=>PATH.normalize(l+"/"+r)};var initRandomFill=()=>{if(ENVIRONMENT_IS_NODE){var nodeCrypto=require("crypto");return view=>nodeCrypto.randomFillSync(view)}return view=>crypto.getRandomValues(view)};var randomFill=view=>{(randomFill=initRandomFill())(view)};var PATH_FS={resolve:(...args)=>{var resolvedPath="",resolvedAbsolute=false;for(var i=args.length-1;i>=-1&&!resolvedAbsolute;i--){var path=i>=0?args[i]:FS.cwd();if(typeof path!="string"){throw new TypeError("Arguments to path.resolve must be strings")}else if(!path){return""}resolvedPath=path+"/"+resolvedPath;resolvedAbsolute=PATH.isAbs(path)}resolvedPath=PATH.normalizeArray(resolvedPath.split("/").filter(p=>!!p),!resolvedAbsolute).join("/");return(resolvedAbsolute?"/":"")+resolvedPath||"."},relative:(from,to)=>{from=PATH_FS.resolve(from).slice(1);to=PATH_FS.resolve(to).slice(1);function trim(arr){var start=0;for(;start<arr.length;start++){if(arr[start]!=="")break}var end=arr.length-1;for(;end>=0;end--){if(arr[end]!=="")break}if(start>end)return[];return arr.slice(start,end-start+1)}var fromParts=trim(from.split("/"));var toParts=trim(to.split("/"));var length=Math.min(fromParts.length,toParts.length);var samePartsLength=length;for(var i=0;i<length;i++){if(fromParts[i]!==toParts[i]){samePartsLength=i;break}}var outputParts=[];for(var i=samePartsLength;i<fromParts.length;i++){outputParts.push("..")}outputParts=outputParts.concat(toParts.slice(samePartsLength));return outputParts.join("/")}};var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder:undefined;var findStringEnd=(heapOrArray,idx,maxBytesToRead,ignoreNul)=>{var maxIdx=idx+maxBytesToRead;if(ignoreNul)return maxIdx;while(heapOrArray[idx]&&!(idx>=maxIdx))++idx;return idx};var UTF8ArrayToString=(heapOrArray,idx=0,maxBytesToRead,ignoreNul)=>{idx>>>=0;var endPtr=findStringEnd(heapOrArray,idx,maxBytesToRead,ignoreNul);if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str="";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var FS_stdin_getChar_buffer=[];var lengthBytesUTF8=str=>{var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++}else if(c<=2047){len+=2}else if(c>=55296&&c<=57343){len+=4;++i}else{len+=3}}return len};var stringToUTF8Array=(str,heap,outIdx,maxBytesToWrite)=>{outIdx>>>=0;if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.codePointAt(i);if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++>>>0]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++>>>0]=192|u>>6;heap[outIdx++>>>0]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++>>>0]=224|u>>12;heap[outIdx++>>>0]=128|u>>6&63;heap[outIdx++>>>0]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++>>>0]=240|u>>18;heap[outIdx++>>>0]=128|u>>12&63;heap[outIdx++>>>0]=128|u>>6&63;heap[outIdx++>>>0]=128|u&63;i++}}heap[outIdx>>>0]=0;return outIdx-startIdx};var intArrayFromString=(stringy,dontAddNull,length)=>{var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array};var FS_stdin_getChar=()=>{if(!FS_stdin_getChar_buffer.length){var result=null;if(ENVIRONMENT_IS_NODE){var BUFSIZE=256;var buf=Buffer.alloc(BUFSIZE);var bytesRead=0;var fd=process.stdin.fd;try{bytesRead=fs.readSync(fd,buf,0,BUFSIZE)}catch(e){if(e.toString().includes("EOF"))bytesRead=0;else throw e}if(bytesRead>0){result=buf.slice(0,bytesRead).toString("utf-8")}}else if(typeof window!="undefined"&&typeof window.prompt=="function"){result=window.prompt("Input: ");if(result!==null){result+="\n"}}else{}if(!result){return null}FS_stdin_getChar_buffer=intArrayFromString(result,true)}return FS_stdin_getChar_buffer.shift()};var TTY={ttys:[],init(){},shutdown(){},register(dev,ops){TTY.ttys[dev]={input:[],output:[],ops};FS.registerDevice(dev,TTY.stream_ops)},stream_ops:{open(stream){var tty=TTY.ttys[stream.node.rdev];if(!tty){throw new FS.ErrnoError(43)}stream.tty=tty;stream.seekable=false},close(stream){stream.tty.ops.fsync(stream.tty)},fsync(stream){stream.tty.ops.fsync(stream.tty)},read(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.get_char){throw new FS.ErrnoError(60)}var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=stream.tty.ops.get_char(stream.tty)}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.atime=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.put_char){throw new FS.ErrnoError(60)}try{for(var i=0;i<length;i++){stream.tty.ops.put_char(stream.tty,buffer[offset+i])}}catch(e){throw new FS.ErrnoError(29)}if(length){stream.node.mtime=stream.node.ctime=Date.now()}return i}},default_tty_ops:{get_char(tty){return FS_stdin_getChar()},put_char(tty,val){if(val===null||val===10){out(UTF8ArrayToString(tty.output));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output?.length>0){out(UTF8ArrayToString(tty.output));tty.output=[]}},ioctl_tcgets(tty){return{c_iflag:25856,c_oflag:5,c_cflag:191,c_lflag:35387,c_cc:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}},ioctl_tcsets(tty,optional_actions,data){return 0},ioctl_tiocgwinsz(tty){return[24,80]}},default_tty1_ops:{put_char(tty,val){if(val===null||val===10){err(UTF8ArrayToString(tty.output));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output?.length>0){err(UTF8ArrayToString(tty.output));tty.output=[]}}}};var mmapAlloc=size=>{abort()};var MEMFS={ops_table:null,mount(mount){return MEMFS.createNode(null,"/",16895,0)},createNode(parent,name,mode,dev){if(FS.isBlkdev(mode)||FS.isFIFO(mode)){throw new FS.ErrnoError(63)}MEMFS.ops_table||={dir:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,lookup:MEMFS.node_ops.lookup,mknod:MEMFS.node_ops.mknod,rename:MEMFS.node_ops.rename,unlink:MEMFS.node_ops.unlink,rmdir:MEMFS.node_ops.rmdir,readdir:MEMFS.node_ops.readdir,symlink:MEMFS.node_ops.symlink},stream:{llseek:MEMFS.stream_ops.llseek}},file:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:{llseek:MEMFS.stream_ops.llseek,read:MEMFS.stream_ops.read,write:MEMFS.stream_ops.write,mmap:MEMFS.stream_ops.mmap,msync:MEMFS.stream_ops.msync}},link:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,readlink:MEMFS.node_ops.readlink},stream:{}},chrdev:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:FS.chrdev_stream_ops}};var node=FS.createNode(parent,name,mode,dev);if(FS.isDir(node.mode)){node.node_ops=MEMFS.ops_table.dir.node;node.stream_ops=MEMFS.ops_table.dir.stream;node.contents={}}else if(FS.isFile(node.mode)){node.node_ops=MEMFS.ops_table.file.node;node.stream_ops=MEMFS.ops_table.file.stream;node.usedBytes=0;node.contents=null}else if(FS.isLink(node.mode)){node.node_ops=MEMFS.ops_table.link.node;node.stream_ops=MEMFS.ops_table.link.stream}else if(FS.isChrdev(node.mode)){node.node_ops=MEMFS.ops_table.chrdev.node;node.stream_ops=MEMFS.ops_table.chrdev.stream}node.atime=node.mtime=node.ctime=Date.now();if(parent){parent.contents[name]=node;parent.atime=parent.mtime=parent.ctime=node.atime}return node},getFileDataAsTypedArray(node){if(!node.contents)return new Uint8Array(0);if(node.contents.subarray)return node.contents.subarray(0,node.usedBytes);return new Uint8Array(node.contents)},expandFileStorage(node,newCapacity){var prevCapacity=node.contents?node.contents.length:0;if(prevCapacity>=newCapacity)return;var CAPACITY_DOUBLING_MAX=1024*1024;newCapacity=Math.max(newCapacity,prevCapacity*(prevCapacity<CAPACITY_DOUBLING_MAX?2:1.125)>>>0);if(prevCapacity!=0)newCapacity=Math.max(newCapacity,256);var oldContents=node.contents;node.contents=new Uint8Array(newCapacity);if(node.usedBytes>0)node.contents.set(oldContents.subarray(0,node.usedBytes),0)},resizeFileStorage(node,newSize){if(node.usedBytes==newSize)return;if(newSize==0){node.contents=null;node.usedBytes=0}else{var oldContents=node.contents;node.contents=new Uint8Array(newSize);if(oldContents){node.contents.set(oldContents.subarray(0,Math.min(newSize,node.usedBytes)))}node.usedBytes=newSize}},node_ops:{getattr(node){var attr={};attr.dev=FS.isChrdev(node.mode)?node.id:1;attr.ino=node.id;attr.mode=node.mode;attr.nlink=1;attr.uid=0;attr.gid=0;attr.rdev=node.rdev;if(FS.isDir(node.mode)){attr.size=4096}else if(FS.isFile(node.mode)){attr.size=node.usedBytes}else if(FS.isLink(node.mode)){attr.size=node.link.length}else{attr.size=0}attr.atime=new Date(node.atime);attr.mtime=new Date(node.mtime);attr.ctime=new Date(node.ctime);attr.blksize=4096;attr.blocks=Math.ceil(attr.size/attr.blksize);return attr},setattr(node,attr){for(const key of["mode","atime","mtime","ctime"]){if(attr[key]!=null){node[key]=attr[key]}}if(attr.size!==undefined){MEMFS.resizeFileStorage(node,attr.size)}},lookup(parent,name){if(!MEMFS.doesNotExistError){MEMFS.doesNotExistError=new FS.ErrnoError(44);MEMFS.doesNotExistError.stack="<generic error, no stack>"}throw MEMFS.doesNotExistError},mknod(parent,name,mode,dev){return MEMFS.createNode(parent,name,mode,dev)},rename(old_node,new_dir,new_name){var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(new_node){if(FS.isDir(old_node.mode)){for(var i in new_node.contents){throw new FS.ErrnoError(55)}}FS.hashRemoveNode(new_node)}delete old_node.parent.contents[old_node.name];new_dir.contents[new_name]=old_node;old_node.name=new_name;new_dir.ctime=new_dir.mtime=old_node.parent.ctime=old_node.parent.mtime=Date.now()},unlink(parent,name){delete parent.contents[name];parent.ctime=parent.mtime=Date.now()},rmdir(parent,name){var node=FS.lookupNode(parent,name);for(var i in node.contents){throw new FS.ErrnoError(55)}delete parent.contents[name];parent.ctime=parent.mtime=Date.now()},readdir(node){return[".","..",...Object.keys(node.contents)]},symlink(parent,newname,oldpath){var node=MEMFS.createNode(parent,newname,511|40960,0);node.link=oldpath;return node},readlink(node){if(!FS.isLink(node.mode)){throw new FS.ErrnoError(28)}return node.link}},stream_ops:{read(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=stream.node.usedBytes)return 0;var size=Math.min(stream.node.usedBytes-position,length);if(size>8&&contents.subarray){buffer.set(contents.subarray(position,position+size),offset)}else{for(var i=0;i<size;i++)buffer[offset+i]=contents[position+i]}return size},write(stream,buffer,offset,length,position,canOwn){if(buffer.buffer===HEAP8.buffer){canOwn=false}if(!length)return 0;var node=stream.node;node.mtime=node.ctime=Date.now();if(buffer.subarray&&(!node.contents||node.contents.subarray)){if(canOwn){node.contents=buffer.subarray(offset,offset+length);node.usedBytes=length;return length}else if(node.usedBytes===0&&position===0){node.contents=buffer.slice(offset,offset+length);node.usedBytes=length;return length}else if(position+length<=node.usedBytes){node.contents.set(buffer.subarray(offset,offset+length),position);return length}}MEMFS.expandFileStorage(node,position+length);if(node.contents.subarray&&buffer.subarray){node.contents.set(buffer.subarray(offset,offset+length),position)}else{for(var i=0;i<length;i++){node.contents[position+i]=buffer[offset+i]}}node.usedBytes=Math.max(node.usedBytes,position+length);return length},llseek(stream,offset,whence){var position=offset;if(whence===1){position+=stream.position}else if(whence===2){if(FS.isFile(stream.node.mode)){position+=stream.node.usedBytes}}if(position<0){throw new FS.ErrnoError(28)}return position},mmap(stream,length,position,prot,flags){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}var ptr;var allocated;var contents=stream.node.contents;if(!(flags&2)&&contents&&contents.buffer===HEAP8.buffer){allocated=false;ptr=contents.byteOffset}else{allocated=true;ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}if(contents){if(position>0||position+length<contents.length){if(contents.subarray){contents=contents.subarray(position,position+length)}else{contents=Array.prototype.slice.call(contents,position,position+length)}}HEAP8.set(contents,ptr>>>0)}}return{ptr,allocated}},msync(stream,buffer,offset,length,mmapFlags){MEMFS.stream_ops.write(stream,buffer,0,length,offset,false);return 0}}};var asyncLoad=async url=>{var arrayBuffer=await readAsync(url);return new Uint8Array(arrayBuffer)};var FS_createDataFile=(...args)=>FS.createDataFile(...args);var getUniqueRunDependency=id=>id;var preloadPlugins=[];var FS_handledByPreloadPlugin=(byteArray,fullname,finish,onerror)=>{if(typeof Browser!="undefined")Browser.init();var handled=false;preloadPlugins.forEach(plugin=>{if(handled)return;if(plugin["canHandle"](fullname)){plugin["handle"](byteArray,fullname,finish,onerror);handled=true}});return handled};var FS_createPreloadedFile=(parent,name,url,canRead,canWrite,onload,onerror,dontCreateFile,canOwn,preFinish)=>{var fullname=name?PATH_FS.resolve(PATH.join2(parent,name)):parent;var dep=getUniqueRunDependency(`cp ${fullname}`);function processData(byteArray){function finish(byteArray){preFinish?.();if(!dontCreateFile){FS_createDataFile(parent,name,byteArray,canRead,canWrite,canOwn)}onload?.();removeRunDependency(dep)}if(FS_handledByPreloadPlugin(byteArray,fullname,finish,()=>{onerror?.();removeRunDependency(dep)})){return}finish(byteArray)}addRunDependency(dep);if(typeof url=="string"){asyncLoad(url).then(processData,onerror)}else{processData(url)}};var FS_modeStringToFlags=str=>{var flagModes={r:0,"r+":2,w:512|64|1,"w+":512|64|2,a:1024|64|1,"a+":1024|64|2};var flags=flagModes[str];if(typeof flags=="undefined"){throw new Error(`Unknown file open mode: ${str}`)}return flags};var FS_getMode=(canRead,canWrite)=>{var mode=0;if(canRead)mode|=292|73;if(canWrite)mode|=146;return mode};var FS={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:false,ignorePermissions:true,filesystems:null,syncFSRequests:0,readFiles:{},ErrnoError:class{name="ErrnoError";constructor(errno){this.errno=errno}},FSStream:class{shared={};get object(){return this.node}set object(val){this.node=val}get isRead(){return(this.flags&2097155)!==1}get isWrite(){return(this.flags&2097155)!==0}get isAppend(){return this.flags&1024}get flags(){return this.shared.flags}set flags(val){this.shared.flags=val}get position(){return this.shared.position}set position(val){this.shared.position=val}},FSNode:class{node_ops={};stream_ops={};readMode=292|73;writeMode=146;mounted=null;constructor(parent,name,mode,rdev){if(!parent){parent=this}this.parent=parent;this.mount=parent.mount;this.id=FS.nextInode++;this.name=name;this.mode=mode;this.rdev=rdev;this.atime=this.mtime=this.ctime=Date.now()}get read(){return(this.mode&this.readMode)===this.readMode}set read(val){val?this.mode|=this.readMode:this.mode&=~this.readMode}get write(){return(this.mode&this.writeMode)===this.writeMode}set write(val){val?this.mode|=this.writeMode:this.mode&=~this.writeMode}get isFolder(){return FS.isDir(this.mode)}get isDevice(){return FS.isChrdev(this.mode)}},lookupPath(path,opts={}){if(!path){throw new FS.ErrnoError(44)}opts.follow_mount??=true;if(!PATH.isAbs(path)){path=FS.cwd()+"/"+path}linkloop:for(var nlinks=0;nlinks<40;nlinks++){var parts=path.split("/").filter(p=>!!p);var current=FS.root;var current_path="/";for(var i=0;i<parts.length;i++){var islast=i===parts.length-1;if(islast&&opts.parent){break}if(parts[i]==="."){continue}if(parts[i]===".."){current_path=PATH.dirname(current_path);if(FS.isRoot(current)){path=current_path+"/"+parts.slice(i+1).join("/");nlinks--;continue linkloop}else{current=current.parent}continue}current_path=PATH.join2(current_path,parts[i]);try{current=FS.lookupNode(current,parts[i])}catch(e){if(e?.errno===44&&islast&&opts.noent_okay){return{path:current_path}}throw e}if(FS.isMountpoint(current)&&(!islast||opts.follow_mount)){current=current.mounted.root}if(FS.isLink(current.mode)&&(!islast||opts.follow)){if(!current.node_ops.readlink){throw new FS.ErrnoError(52)}var link=current.node_ops.readlink(current);if(!PATH.isAbs(link)){link=PATH.dirname(current_path)+"/"+link}path=link+"/"+parts.slice(i+1).join("/");continue linkloop}}return{path:current_path,node:current}}throw new FS.ErrnoError(32)},getPath(node){var path;while(true){if(FS.isRoot(node)){var mount=node.mount.mountpoint;if(!path)return mount;return mount[mount.length-1]!=="/"?`${mount}/${path}`:mount+path}path=path?`${node.name}/${path}`:node.name;node=node.parent}},hashName(parentid,name){var hash=0;for(var i=0;i<name.length;i++){hash=(hash<<5)-hash+name.charCodeAt(i)|0}return(parentid+hash>>>0)%FS.nameTable.length},hashAddNode(node){var hash=FS.hashName(node.parent.id,node.name);node.name_next=FS.nameTable[hash];FS.nameTable[hash]=node},hashRemoveNode(node){var hash=FS.hashName(node.parent.id,node.name);if(FS.nameTable[hash]===node){FS.nameTable[hash]=node.name_next}else{var current=FS.nameTable[hash];while(current){if(current.name_next===node){current.name_next=node.name_next;break}current=current.name_next}}},lookupNode(parent,name){var errCode=FS.mayLookup(parent);if(errCode){throw new FS.ErrnoError(errCode)}var hash=FS.hashName(parent.id,name);for(var node=FS.nameTable[hash];node;node=node.name_next){var nodeName=node.name;if(node.parent.id===parent.id&&nodeName===name){return node}}return FS.lookup(parent,name)},createNode(parent,name,mode,rdev){var node=new FS.FSNode(parent,name,mode,rdev);FS.hashAddNode(node);return node},destroyNode(node){FS.hashRemoveNode(node)},isRoot(node){return node===node.parent},isMountpoint(node){return!!node.mounted},isFile(mode){return(mode&61440)===32768},isDir(mode){return(mode&61440)===16384},isLink(mode){return(mode&61440)===40960},isChrdev(mode){return(mode&61440)===8192},isBlkdev(mode){return(mode&61440)===24576},isFIFO(mode){return(mode&61440)===4096},isSocket(mode){return(mode&49152)===49152},flagsToPermissionString(flag){var perms=["r","w","rw"][flag&3];if(flag&512){perms+="w"}return perms},nodePermissions(node,perms){if(FS.ignorePermissions){return 0}if(perms.includes("r")&&!(node.mode&292)){return 2}else if(perms.includes("w")&&!(node.mode&146)){return 2}else if(perms.includes("x")&&!(node.mode&73)){return 2}return 0},mayLookup(dir){if(!FS.isDir(dir.mode))return 54;var errCode=FS.nodePermissions(dir,"x");if(errCode)return errCode;if(!dir.node_ops.lookup)return 2;return 0},mayCreate(dir,name){if(!FS.isDir(dir.mode)){return 54}try{var node=FS.lookupNode(dir,name);return 20}catch(e){}return FS.nodePermissions(dir,"wx")},mayDelete(dir,name,isdir){var node;try{node=FS.lookupNode(dir,name)}catch(e){return e.errno}var errCode=FS.nodePermissions(dir,"wx");if(errCode){return errCode}if(isdir){if(!FS.isDir(node.mode)){return 54}if(FS.isRoot(node)||FS.getPath(node)===FS.cwd()){return 10}}else{if(FS.isDir(node.mode)){return 31}}return 0},mayOpen(node,flags){if(!node){return 44}if(FS.isLink(node.mode)){return 32}else if(FS.isDir(node.mode)){if(FS.flagsToPermissionString(flags)!=="r"||flags&(512|64)){return 31}}return FS.nodePermissions(node,FS.flagsToPermissionString(flags))},checkOpExists(op,err){if(!op){throw new FS.ErrnoError(err)}return op},MAX_OPEN_FDS:4096,nextfd(){for(var fd=0;fd<=FS.MAX_OPEN_FDS;fd++){if(!FS.streams[fd]){return fd}}throw new FS.ErrnoError(33)},getStreamChecked(fd){var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(8)}return stream},getStream:fd=>FS.streams[fd],createStream(stream,fd=-1){stream=Object.assign(new FS.FSStream,stream);if(fd==-1){fd=FS.nextfd()}stream.fd=fd;FS.streams[fd]=stream;return stream},closeStream(fd){FS.streams[fd]=null},dupStream(origStream,fd=-1){var stream=FS.createStream(origStream,fd);stream.stream_ops?.dup?.(stream);return stream},doSetAttr(stream,node,attr){var setattr=stream?.stream_ops.setattr;var arg=setattr?stream:node;setattr??=node.node_ops.setattr;FS.checkOpExists(setattr,63);setattr(arg,attr)},chrdev_stream_ops:{open(stream){var device=FS.getDevice(stream.node.rdev);stream.stream_ops=device.stream_ops;stream.stream_ops.open?.(stream)},llseek(){throw new FS.ErrnoError(70)}},major:dev=>dev>>8,minor:dev=>dev&255,makedev:(ma,mi)=>ma<<8|mi,registerDevice(dev,ops){FS.devices[dev]={stream_ops:ops}},getDevice:dev=>FS.devices[dev],getMounts(mount){var mounts=[];var check=[mount];while(check.length){var m=check.pop();mounts.push(m);check.push(...m.mounts)}return mounts},syncfs(populate,callback){if(typeof populate=="function"){callback=populate;populate=false}FS.syncFSRequests++;if(FS.syncFSRequests>1){err(`warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`)}var mounts=FS.getMounts(FS.root.mount);var completed=0;function doCallback(errCode){FS.syncFSRequests--;return callback(errCode)}function done(errCode){if(errCode){if(!done.errored){done.errored=true;return doCallback(errCode)}return}if(++completed>=mounts.length){doCallback(null)}}mounts.forEach(mount=>{if(!mount.type.syncfs){return done(null)}mount.type.syncfs(mount,populate,done)})},mount(type,opts,mountpoint){var root=mountpoint==="/";var pseudo=!mountpoint;var node;if(root&&FS.root){throw new FS.ErrnoError(10)}else if(!root&&!pseudo){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});mountpoint=lookup.path;node=lookup.node;if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}if(!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}}var mount={type,opts,mountpoint,mounts:[]};var mountRoot=type.mount(mount);mountRoot.mount=mount;mount.root=mountRoot;if(root){FS.root=mountRoot}else if(node){node.mounted=mount;if(node.mount){node.mount.mounts.push(mount)}}return mountRoot},unmount(mountpoint){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});if(!FS.isMountpoint(lookup.node)){throw new FS.ErrnoError(28)}var node=lookup.node;var mount=node.mounted;var mounts=FS.getMounts(mount);Object.keys(FS.nameTable).forEach(hash=>{var current=FS.nameTable[hash];while(current){var next=current.name_next;if(mounts.includes(current.mount)){FS.destroyNode(current)}current=next}});node.mounted=null;var idx=node.mount.mounts.indexOf(mount);node.mount.mounts.splice(idx,1)},lookup(parent,name){return parent.node_ops.lookup(parent,name)},mknod(path,mode,dev){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);if(!name){throw new FS.ErrnoError(28)}if(name==="."||name===".."){throw new FS.ErrnoError(20)}var errCode=FS.mayCreate(parent,name);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.mknod){throw new FS.ErrnoError(63)}return parent.node_ops.mknod(parent,name,mode,dev)},statfs(path){return FS.statfsNode(FS.lookupPath(path,{follow:true}).node)},statfsStream(stream){return FS.statfsNode(stream.node)},statfsNode(node){var rtn={bsize:4096,frsize:4096,blocks:1e6,bfree:5e5,bavail:5e5,files:FS.nextInode,ffree:FS.nextInode-1,fsid:42,flags:2,namelen:255};if(node.node_ops.statfs){Object.assign(rtn,node.node_ops.statfs(node.mount.opts.root))}return rtn},create(path,mode=438){mode&=4095;mode|=32768;return FS.mknod(path,mode,0)},mkdir(path,mode=511){mode&=511|512;mode|=16384;return FS.mknod(path,mode,0)},mkdirTree(path,mode){var dirs=path.split("/");var d="";for(var dir of dirs){if(!dir)continue;if(d||PATH.isAbs(path))d+="/";d+=dir;try{FS.mkdir(d,mode)}catch(e){if(e.errno!=20)throw e}}},mkdev(path,mode,dev){if(typeof dev=="undefined"){dev=mode;mode=438}mode|=8192;return FS.mknod(path,mode,dev)},symlink(oldpath,newpath){if(!PATH_FS.resolve(oldpath)){throw new FS.ErrnoError(44)}var lookup=FS.lookupPath(newpath,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var newname=PATH.basename(newpath);var errCode=FS.mayCreate(parent,newname);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.symlink){throw new FS.ErrnoError(63)}return parent.node_ops.symlink(parent,newname,oldpath)},rename(old_path,new_path){var old_dirname=PATH.dirname(old_path);var new_dirname=PATH.dirname(new_path);var old_name=PATH.basename(old_path);var new_name=PATH.basename(new_path);var lookup,old_dir,new_dir;lookup=FS.lookupPath(old_path,{parent:true});old_dir=lookup.node;lookup=FS.lookupPath(new_path,{parent:true});new_dir=lookup.node;if(!old_dir||!new_dir)throw new FS.ErrnoError(44);if(old_dir.mount!==new_dir.mount){throw new FS.ErrnoError(75)}var old_node=FS.lookupNode(old_dir,old_name);var relative=PATH_FS.relative(old_path,new_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(28)}relative=PATH_FS.relative(new_path,old_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(55)}var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(old_node===new_node){return}var isdir=FS.isDir(old_node.mode);var errCode=FS.mayDelete(old_dir,old_name,isdir);if(errCode){throw new FS.ErrnoError(errCode)}errCode=new_node?FS.mayDelete(new_dir,new_name,isdir):FS.mayCreate(new_dir,new_name);if(errCode){throw new FS.ErrnoError(errCode)}if(!old_dir.node_ops.rename){throw new FS.ErrnoError(63)}if(FS.isMountpoint(old_node)||new_node&&FS.isMountpoint(new_node)){throw new FS.ErrnoError(10)}if(new_dir!==old_dir){errCode=FS.nodePermissions(old_dir,"w");if(errCode){throw new FS.ErrnoError(errCode)}}FS.hashRemoveNode(old_node);try{old_dir.node_ops.rename(old_node,new_dir,new_name);old_node.parent=new_dir}catch(e){throw e}finally{FS.hashAddNode(old_node)}},rmdir(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,true);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.rmdir){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.rmdir(parent,name);FS.destroyNode(node)},readdir(path){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;var readdir=FS.checkOpExists(node.node_ops.readdir,54);return readdir(node)},unlink(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,false);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.unlink){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.unlink(parent,name);FS.destroyNode(node)},readlink(path){var lookup=FS.lookupPath(path);var link=lookup.node;if(!link){throw new FS.ErrnoError(44)}if(!link.node_ops.readlink){throw new FS.ErrnoError(28)}return link.node_ops.readlink(link)},stat(path,dontFollow){var lookup=FS.lookupPath(path,{follow:!dontFollow});var node=lookup.node;var getattr=FS.checkOpExists(node.node_ops.getattr,63);return getattr(node)},fstat(fd){var stream=FS.getStreamChecked(fd);var node=stream.node;var getattr=stream.stream_ops.getattr;var arg=getattr?stream:node;getattr??=node.node_ops.getattr;FS.checkOpExists(getattr,63);return getattr(arg)},lstat(path){return FS.stat(path,true)},doChmod(stream,node,mode,dontFollow){FS.doSetAttr(stream,node,{mode:mode&4095|node.mode&~4095,ctime:Date.now(),dontFollow})},chmod(path,mode,dontFollow){var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}FS.doChmod(null,node,mode,dontFollow)},lchmod(path,mode){FS.chmod(path,mode,true)},fchmod(fd,mode){var stream=FS.getStreamChecked(fd);FS.doChmod(stream,stream.node,mode,false)},doChown(stream,node,dontFollow){FS.doSetAttr(stream,node,{timestamp:Date.now(),dontFollow})},chown(path,uid,gid,dontFollow){var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}FS.doChown(null,node,dontFollow)},lchown(path,uid,gid){FS.chown(path,uid,gid,true)},fchown(fd,uid,gid){var stream=FS.getStreamChecked(fd);FS.doChown(stream,stream.node,false)},doTruncate(stream,node,len){if(FS.isDir(node.mode)){throw new FS.ErrnoError(31)}if(!FS.isFile(node.mode)){throw new FS.ErrnoError(28)}var errCode=FS.nodePermissions(node,"w");if(errCode){throw new FS.ErrnoError(errCode)}FS.doSetAttr(stream,node,{size:len,timestamp:Date.now()})},truncate(path,len){if(len<0){throw new FS.ErrnoError(28)}var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:true});node=lookup.node}else{node=path}FS.doTruncate(null,node,len)},ftruncate(fd,len){var stream=FS.getStreamChecked(fd);if(len<0||(stream.flags&2097155)===0){throw new FS.ErrnoError(28)}FS.doTruncate(stream,stream.node,len)},utime(path,atime,mtime){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;var setattr=FS.checkOpExists(node.node_ops.setattr,63);setattr(node,{atime,mtime})},open(path,flags,mode=438){if(path===""){throw new FS.ErrnoError(44)}flags=typeof flags=="string"?FS_modeStringToFlags(flags):flags;if(flags&64){mode=mode&4095|32768}else{mode=0}var node;var isDirPath;if(typeof path=="object"){node=path}else{isDirPath=path.endsWith("/");var lookup=FS.lookupPath(path,{follow:!(flags&131072),noent_okay:true});node=lookup.node;path=lookup.path}var created=false;if(flags&64){if(node){if(flags&128){throw new FS.ErrnoError(20)}}else if(isDirPath){throw new FS.ErrnoError(31)}else{node=FS.mknod(path,mode|511,0);created=true}}if(!node){throw new FS.ErrnoError(44)}if(FS.isChrdev(node.mode)){flags&=~512}if(flags&65536&&!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}if(!created){var errCode=FS.mayOpen(node,flags);if(errCode){throw new FS.ErrnoError(errCode)}}if(flags&512&&!created){FS.truncate(node,0)}flags&=~(128|512|131072);var stream=FS.createStream({node,path:FS.getPath(node),flags,seekable:true,position:0,stream_ops:node.stream_ops,ungotten:[],error:false});if(stream.stream_ops.open){stream.stream_ops.open(stream)}if(created){FS.chmod(node,mode&511)}if(Module["logReadFiles"]&&!(flags&1)){if(!(path in FS.readFiles)){FS.readFiles[path]=1}}return stream},close(stream){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(stream.getdents)stream.getdents=null;try{if(stream.stream_ops.close){stream.stream_ops.close(stream)}}catch(e){throw e}finally{FS.closeStream(stream.fd)}stream.fd=null},isClosed(stream){return stream.fd===null},llseek(stream,offset,whence){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(!stream.seekable||!stream.stream_ops.llseek){throw new FS.ErrnoError(70)}if(whence!=0&&whence!=1&&whence!=2){throw new FS.ErrnoError(28)}stream.position=stream.stream_ops.llseek(stream,offset,whence);stream.ungotten=[];return stream.position},read(stream,buffer,offset,length,position){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.read){throw new FS.ErrnoError(28)}var seeking=typeof position!="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesRead=stream.stream_ops.read(stream,buffer,offset,length,position);if(!seeking)stream.position+=bytesRead;return bytesRead},write(stream,buffer,offset,length,position,canOwn){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.write){throw new FS.ErrnoError(28)}if(stream.seekable&&stream.flags&1024){FS.llseek(stream,0,2)}var seeking=typeof position!="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesWritten=stream.stream_ops.write(stream,buffer,offset,length,position,canOwn);if(!seeking)stream.position+=bytesWritten;return bytesWritten},mmap(stream,length,position,prot,flags){if((prot&2)!==0&&(flags&2)===0&&(stream.flags&2097155)!==2){throw new FS.ErrnoError(2)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(2)}if(!stream.stream_ops.mmap){throw new FS.ErrnoError(43)}if(!length){throw new FS.ErrnoError(28)}return stream.stream_ops.mmap(stream,length,position,prot,flags)},msync(stream,buffer,offset,length,mmapFlags){if(!stream.stream_ops.msync){return 0}return stream.stream_ops.msync(stream,buffer,offset,length,mmapFlags)},ioctl(stream,cmd,arg){if(!stream.stream_ops.ioctl){throw new FS.ErrnoError(59)}return stream.stream_ops.ioctl(stream,cmd,arg)},readFile(path,opts={}){opts.flags=opts.flags||0;opts.encoding=opts.encoding||"binary";if(opts.encoding!=="utf8"&&opts.encoding!=="binary"){throw new Error(`Invalid encoding type "${opts.encoding}"`)}var stream=FS.open(path,opts.flags);var stat=FS.stat(path);var length=stat.size;var buf=new Uint8Array(length);FS.read(stream,buf,0,length,0);if(opts.encoding==="utf8"){buf=UTF8ArrayToString(buf)}FS.close(stream);return buf},writeFile(path,data,opts={}){opts.flags=opts.flags||577;var stream=FS.open(path,opts.flags,opts.mode);if(typeof data=="string"){data=new Uint8Array(intArrayFromString(data,true))}if(ArrayBuffer.isView(data)){FS.write(stream,data,0,data.byteLength,undefined,opts.canOwn)}else{throw new Error("Unsupported data type")}FS.close(stream)},cwd:()=>FS.currentPath,chdir(path){var lookup=FS.lookupPath(path,{follow:true});if(lookup.node===null){throw new FS.ErrnoError(44)}if(!FS.isDir(lookup.node.mode)){throw new FS.ErrnoError(54)}var errCode=FS.nodePermissions(lookup.node,"x");if(errCode){throw new FS.ErrnoError(errCode)}FS.currentPath=lookup.path},createDefaultDirectories(){FS.mkdir("/tmp");FS.mkdir("/home");FS.mkdir("/home/<USER>")},createDefaultDevices(){FS.mkdir("/dev");FS.registerDevice(FS.makedev(1,3),{read:()=>0,write:(stream,buffer,offset,length,pos)=>length,llseek:()=>0});FS.mkdev("/dev/null",FS.makedev(1,3));TTY.register(FS.makedev(5,0),TTY.default_tty_ops);TTY.register(FS.makedev(6,0),TTY.default_tty1_ops);FS.mkdev("/dev/tty",FS.makedev(5,0));FS.mkdev("/dev/tty1",FS.makedev(6,0));var randomBuffer=new Uint8Array(1024),randomLeft=0;var randomByte=()=>{if(randomLeft===0){randomFill(randomBuffer);randomLeft=randomBuffer.byteLength}return randomBuffer[--randomLeft]};FS.createDevice("/dev","random",randomByte);FS.createDevice("/dev","urandom",randomByte);FS.mkdir("/dev/shm");FS.mkdir("/dev/shm/tmp")},createSpecialDirectories(){FS.mkdir("/proc");var proc_self=FS.mkdir("/proc/self");FS.mkdir("/proc/self/fd");FS.mount({mount(){var node=FS.createNode(proc_self,"fd",16895,73);node.stream_ops={llseek:MEMFS.stream_ops.llseek};node.node_ops={lookup(parent,name){var fd=+name;var stream=FS.getStreamChecked(fd);var ret={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>stream.path},id:fd+1};ret.parent=ret;return ret},readdir(){return Array.from(FS.streams.entries()).filter(([k,v])=>v).map(([k,v])=>k.toString())}};return node}},{},"/proc/self/fd")},createStandardStreams(input,output,error){if(input){FS.createDevice("/dev","stdin",input)}else{FS.symlink("/dev/tty","/dev/stdin")}if(output){FS.createDevice("/dev","stdout",null,output)}else{FS.symlink("/dev/tty","/dev/stdout")}if(error){FS.createDevice("/dev","stderr",null,error)}else{FS.symlink("/dev/tty1","/dev/stderr")}var stdin=FS.open("/dev/stdin",0);var stdout=FS.open("/dev/stdout",1);var stderr=FS.open("/dev/stderr",1)},staticInit(){FS.nameTable=new Array(4096);FS.mount(MEMFS,{},"/");FS.createDefaultDirectories();FS.createDefaultDevices();FS.createSpecialDirectories();FS.filesystems={MEMFS}},init(input,output,error){FS.initialized=true;input??=Module["stdin"];output??=Module["stdout"];error??=Module["stderr"];FS.createStandardStreams(input,output,error)},quit(){FS.initialized=false;for(var stream of FS.streams){if(stream){FS.close(stream)}}},findObject(path,dontResolveLastLink){var ret=FS.analyzePath(path,dontResolveLastLink);if(!ret.exists){return null}return ret.object},analyzePath(path,dontResolveLastLink){try{var lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});path=lookup.path}catch(e){}var ret={isRoot:false,exists:false,error:0,name:null,path:null,object:null,parentExists:false,parentPath:null,parentObject:null};try{var lookup=FS.lookupPath(path,{parent:true});ret.parentExists=true;ret.parentPath=lookup.path;ret.parentObject=lookup.node;ret.name=PATH.basename(path);lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});ret.exists=true;ret.path=lookup.path;ret.object=lookup.node;ret.name=lookup.node.name;ret.isRoot=lookup.path==="/"}catch(e){ret.error=e.errno}return ret},createPath(parent,path,canRead,canWrite){parent=typeof parent=="string"?parent:FS.getPath(parent);var parts=path.split("/").reverse();while(parts.length){var part=parts.pop();if(!part)continue;var current=PATH.join2(parent,part);try{FS.mkdir(current)}catch(e){if(e.errno!=20)throw e}parent=current}return current},createFile(parent,name,properties,canRead,canWrite){var path=PATH.join2(typeof parent=="string"?parent:FS.getPath(parent),name);var mode=FS_getMode(canRead,canWrite);return FS.create(path,mode)},createDataFile(parent,name,data,canRead,canWrite,canOwn){var path=name;if(parent){parent=typeof parent=="string"?parent:FS.getPath(parent);path=name?PATH.join2(parent,name):parent}var mode=FS_getMode(canRead,canWrite);var node=FS.create(path,mode);if(data){if(typeof data=="string"){var arr=new Array(data.length);for(var i=0,len=data.length;i<len;++i)arr[i]=data.charCodeAt(i);data=arr}FS.chmod(node,mode|146);var stream=FS.open(node,577);FS.write(stream,data,0,data.length,0,canOwn);FS.close(stream);FS.chmod(node,mode)}},createDevice(parent,name,input,output){var path=PATH.join2(typeof parent=="string"?parent:FS.getPath(parent),name);var mode=FS_getMode(!!input,!!output);FS.createDevice.major??=64;var dev=FS.makedev(FS.createDevice.major++,0);FS.registerDevice(dev,{open(stream){stream.seekable=false},close(stream){if(output?.buffer?.length){output(10)}},read(stream,buffer,offset,length,pos){var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=input()}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.atime=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){for(var i=0;i<length;i++){try{output(buffer[offset+i])}catch(e){throw new FS.ErrnoError(29)}}if(length){stream.node.mtime=stream.node.ctime=Date.now()}return i}});return FS.mkdev(path,mode,dev)},forceLoadFile(obj){if(obj.isDevice||obj.isFolder||obj.link||obj.contents)return true;if(typeof XMLHttpRequest!="undefined"){throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.")}else{try{obj.contents=readBinary(obj.url);obj.usedBytes=obj.contents.length}catch(e){throw new FS.ErrnoError(29)}}},createLazyFile(parent,name,url,canRead,canWrite){class LazyUint8Array{lengthKnown=false;chunks=[];get(idx){if(idx>this.length-1||idx<0){return undefined}var chunkOffset=idx%this.chunkSize;var chunkNum=idx/this.chunkSize|0;return this.getter(chunkNum)[chunkOffset]}setDataGetter(getter){this.getter=getter}cacheLength(){var xhr=new XMLHttpRequest;xhr.open("HEAD",url,false);xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);var datalength=Number(xhr.getResponseHeader("Content-length"));var header;var hasByteServing=(header=xhr.getResponseHeader("Accept-Ranges"))&&header==="bytes";var usesGzip=(header=xhr.getResponseHeader("Content-Encoding"))&&header==="gzip";var chunkSize=1024*1024;if(!hasByteServing)chunkSize=datalength;var doXHR=(from,to)=>{if(from>to)throw new Error("invalid range ("+from+", "+to+") or no bytes requested!");if(to>datalength-1)throw new Error("only "+datalength+" bytes available! programmer error!");var xhr=new XMLHttpRequest;xhr.open("GET",url,false);if(datalength!==chunkSize)xhr.setRequestHeader("Range","bytes="+from+"-"+to);xhr.responseType="arraybuffer";if(xhr.overrideMimeType){xhr.overrideMimeType("text/plain; charset=x-user-defined")}xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);if(xhr.response!==undefined){return new Uint8Array(xhr.response||[])}return intArrayFromString(xhr.responseText||"",true)};var lazyArray=this;lazyArray.setDataGetter(chunkNum=>{var start=chunkNum*chunkSize;var end=(chunkNum+1)*chunkSize-1;end=Math.min(end,datalength-1);if(typeof lazyArray.chunks[chunkNum]=="undefined"){lazyArray.chunks[chunkNum]=doXHR(start,end)}if(typeof lazyArray.chunks[chunkNum]=="undefined")throw new Error("doXHR failed!");return lazyArray.chunks[chunkNum]});if(usesGzip||!datalength){chunkSize=datalength=1;datalength=this.getter(0).length;chunkSize=datalength;out("LazyFiles on gzip forces download of the whole file when length is accessed")}this._length=datalength;this._chunkSize=chunkSize;this.lengthKnown=true}get length(){if(!this.lengthKnown){this.cacheLength()}return this._length}get chunkSize(){if(!this.lengthKnown){this.cacheLength()}return this._chunkSize}}if(typeof XMLHttpRequest!="undefined"){if(!ENVIRONMENT_IS_WORKER)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var lazyArray=new LazyUint8Array;var properties={isDevice:false,contents:lazyArray}}else{var properties={isDevice:false,url}}var node=FS.createFile(parent,name,properties,canRead,canWrite);if(properties.contents){node.contents=properties.contents}else if(properties.url){node.contents=null;node.url=properties.url}Object.defineProperties(node,{usedBytes:{get:function(){return this.contents.length}}});var stream_ops={};var keys=Object.keys(node.stream_ops);keys.forEach(key=>{var fn=node.stream_ops[key];stream_ops[key]=(...args)=>{FS.forceLoadFile(node);return fn(...args)}});function writeChunks(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=contents.length)return 0;var size=Math.min(contents.length-position,length);if(contents.slice){for(var i=0;i<size;i++){buffer[offset+i]=contents[position+i]}}else{for(var i=0;i<size;i++){buffer[offset+i]=contents.get(position+i)}}return size}stream_ops.read=(stream,buffer,offset,length,position)=>{FS.forceLoadFile(node);return writeChunks(stream,buffer,offset,length,position)};stream_ops.mmap=(stream,length,position,prot,flags)=>{FS.forceLoadFile(node);var ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}writeChunks(stream,HEAP8,ptr,length,position);return{ptr,allocated:true}};node.stream_ops=stream_ops;return node}};var UTF8ToString=(ptr,maxBytesToRead,ignoreNul)=>{ptr>>>=0;return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead,ignoreNul):""};var SYSCALLS={DEFAULT_POLLMASK:5,calculateAt(dirfd,path,allowEmpty){if(PATH.isAbs(path)){return path}var dir;if(dirfd===-100){dir=FS.cwd()}else{var dirstream=SYSCALLS.getStreamFromFD(dirfd);dir=dirstream.path}if(path.length==0){if(!allowEmpty){throw new FS.ErrnoError(44)}return dir}return dir+"/"+path},writeStat(buf,stat){HEAP32[buf>>>2>>>0]=stat.dev;HEAP32[buf+4>>>2>>>0]=stat.mode;HEAPU32[buf+8>>>2>>>0]=stat.nlink;HEAP32[buf+12>>>2>>>0]=stat.uid;HEAP32[buf+16>>>2>>>0]=stat.gid;HEAP32[buf+20>>>2>>>0]=stat.rdev;HEAP64[buf+24>>>3>>>0]=BigInt(stat.size);HEAP32[buf+32>>>2>>>0]=4096;HEAP32[buf+36>>>2>>>0]=stat.blocks;var atime=stat.atime.getTime();var mtime=stat.mtime.getTime();var ctime=stat.ctime.getTime();HEAP64[buf+40>>>3>>>0]=BigInt(Math.floor(atime/1e3));HEAPU32[buf+48>>>2>>>0]=atime%1e3*1e3*1e3;HEAP64[buf+56>>>3>>>0]=BigInt(Math.floor(mtime/1e3));HEAPU32[buf+64>>>2>>>0]=mtime%1e3*1e3*1e3;HEAP64[buf+72>>>3>>>0]=BigInt(Math.floor(ctime/1e3));HEAPU32[buf+80>>>2>>>0]=ctime%1e3*1e3*1e3;HEAP64[buf+88>>>3>>>0]=BigInt(stat.ino);return 0},writeStatFs(buf,stats){HEAP32[buf+4>>>2>>>0]=stats.bsize;HEAP32[buf+40>>>2>>>0]=stats.bsize;HEAP32[buf+8>>>2>>>0]=stats.blocks;HEAP32[buf+12>>>2>>>0]=stats.bfree;HEAP32[buf+16>>>2>>>0]=stats.bavail;HEAP32[buf+20>>>2>>>0]=stats.files;HEAP32[buf+24>>>2>>>0]=stats.ffree;HEAP32[buf+28>>>2>>>0]=stats.fsid;HEAP32[buf+44>>>2>>>0]=stats.flags;HEAP32[buf+36>>>2>>>0]=stats.namelen},doMsync(addr,stream,len,flags,offset){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}if(flags&2){return 0}var buffer=HEAPU8.slice(addr,addr+len);FS.msync(stream,buffer,offset,len,flags)},getStreamFromFD(fd){var stream=FS.getStreamChecked(fd);return stream},varargs:undefined,getStr(ptr){var ret=UTF8ToString(ptr);return ret}};function ___syscall_fcntl64(fd,cmd,varargs){varargs>>>=0;SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(fd);switch(cmd){case 0:{var arg=syscallGetVarargI();if(arg<0){return-28}while(FS.streams[arg]){arg++}var newStream;newStream=FS.dupStream(stream,arg);return newStream.fd}case 1:case 2:return 0;case 3:return stream.flags;case 4:{var arg=syscallGetVarargI();stream.flags|=arg;return 0}case 12:{var arg=syscallGetVarargP();var offset=0;HEAP16[arg+offset>>>1>>>0]=2;return 0}case 13:case 14:return 0}return-28}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function ___syscall_ioctl(fd,op,varargs){varargs>>>=0;SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(fd);switch(op){case 21509:{if(!stream.tty)return-59;return 0}case 21505:{if(!stream.tty)return-59;if(stream.tty.ops.ioctl_tcgets){var termios=stream.tty.ops.ioctl_tcgets(stream);var argp=syscallGetVarargP();HEAP32[argp>>>2>>>0]=termios.c_iflag||0;HEAP32[argp+4>>>2>>>0]=termios.c_oflag||0;HEAP32[argp+8>>>2>>>0]=termios.c_cflag||0;HEAP32[argp+12>>>2>>>0]=termios.c_lflag||0;for(var i=0;i<32;i++){HEAP8[argp+i+17>>>0]=termios.c_cc[i]||0}return 0}return 0}case 21510:case 21511:case 21512:{if(!stream.tty)return-59;return 0}case 21506:case 21507:case 21508:{if(!stream.tty)return-59;if(stream.tty.ops.ioctl_tcsets){var argp=syscallGetVarargP();var c_iflag=HEAP32[argp>>>2>>>0];var c_oflag=HEAP32[argp+4>>>2>>>0];var c_cflag=HEAP32[argp+8>>>2>>>0];var c_lflag=HEAP32[argp+12>>>2>>>0];var c_cc=[];for(var i=0;i<32;i++){c_cc.push(HEAP8[argp+i+17>>>0])}return stream.tty.ops.ioctl_tcsets(stream.tty,op,{c_iflag,c_oflag,c_cflag,c_lflag,c_cc})}return 0}case 21519:{if(!stream.tty)return-59;var argp=syscallGetVarargP();HEAP32[argp>>>2>>>0]=0;return 0}case 21520:{if(!stream.tty)return-59;return-28}case 21537:case 21531:{var argp=syscallGetVarargP();return FS.ioctl(stream,op,argp)}case 21523:{if(!stream.tty)return-59;if(stream.tty.ops.ioctl_tiocgwinsz){var winsize=stream.tty.ops.ioctl_tiocgwinsz(stream.tty);var argp=syscallGetVarargP();HEAP16[argp>>>1>>>0]=winsize[0];HEAP16[argp+2>>>1>>>0]=winsize[1]}return 0}case 21524:{if(!stream.tty)return-59;return 0}case 21515:{if(!stream.tty)return-59;return 0}default:return-28}}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function ___syscall_openat(dirfd,path,flags,varargs){path>>>=0;varargs>>>=0;SYSCALLS.varargs=varargs;try{path=SYSCALLS.getStr(path);path=SYSCALLS.calculateAt(dirfd,path);var mode=varargs?syscallGetVarargI():0;return FS.open(path,flags,mode).fd}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}var __abort_js=()=>abort("");var AsciiToString=ptr=>{ptr>>>=0;var str="";while(1){var ch=HEAPU8[ptr++>>>0];if(!ch)return str;str+=String.fromCharCode(ch)}};var awaitingDependencies={};var registeredTypes={};var typeDependencies={};var BindingError=class BindingError extends Error{constructor(message){super(message);this.name="BindingError"}};var throwBindingError=message=>{throw new BindingError(message)};function sharedRegisterType(rawType,registeredInstance,options={}){var name=registeredInstance.name;if(!rawType){throwBindingError(`type "${name}" must have a positive integer typeid pointer`)}if(registeredTypes.hasOwnProperty(rawType)){if(options.ignoreDuplicateRegistrations){return}else{throwBindingError(`Cannot register type '${name}' twice`)}}registeredTypes[rawType]=registeredInstance;delete typeDependencies[rawType];if(awaitingDependencies.hasOwnProperty(rawType)){var callbacks=awaitingDependencies[rawType];delete awaitingDependencies[rawType];callbacks.forEach(cb=>cb())}}function registerType(rawType,registeredInstance,options={}){return sharedRegisterType(rawType,registeredInstance,options)}var integerReadValueFromPointer=(name,width,signed)=>{switch(width){case 1:return signed?pointer=>HEAP8[pointer>>>0]:pointer=>HEAPU8[pointer>>>0];case 2:return signed?pointer=>HEAP16[pointer>>>1>>>0]:pointer=>HEAPU16[pointer>>>1>>>0];case 4:return signed?pointer=>HEAP32[pointer>>>2>>>0]:pointer=>HEAPU32[pointer>>>2>>>0];case 8:return signed?pointer=>HEAP64[pointer>>>3>>>0]:pointer=>HEAPU64[pointer>>>3>>>0];default:throw new TypeError(`invalid integer width (${width}): ${name}`)}};var __embind_register_bigint=function(primitiveType,name,size,minRange,maxRange){primitiveType>>>=0;name>>>=0;size>>>=0;name=AsciiToString(name);const isUnsignedType=minRange===0n;let fromWireType=value=>value;if(isUnsignedType){const bitSize=size*8;fromWireType=value=>BigInt.asUintN(bitSize,value);maxRange=fromWireType(maxRange)}registerType(primitiveType,{name,fromWireType,toWireType:(destructors,value)=>{if(typeof value=="number"){value=BigInt(value)}return value},readValueFromPointer:integerReadValueFromPointer(name,size,!isUnsignedType),destructorFunction:null})};function __embind_register_bool(rawType,name,trueValue,falseValue){rawType>>>=0;name>>>=0;name=AsciiToString(name);registerType(rawType,{name,fromWireType:function(wt){return!!wt},toWireType:function(destructors,o){return o?trueValue:falseValue},readValueFromPointer:function(pointer){return this.fromWireType(HEAPU8[pointer>>>0])},destructorFunction:null})}var emval_freelist=[];var emval_handles=[0,1,,1,null,1,true,1,false,1];function __emval_decref(handle){handle>>>=0;if(handle>9&&0===--emval_handles[handle+1]){emval_handles[handle]=undefined;emval_freelist.push(handle)}}var Emval={toValue:handle=>{if(!handle){throwBindingError(`Cannot use deleted val. handle = ${handle}`)}return emval_handles[handle]},toHandle:value=>{switch(value){case undefined:return 2;case null:return 4;case true:return 6;case false:return 8;default:{const handle=emval_freelist.pop()||emval_handles.length;emval_handles[handle]=value;emval_handles[handle+1]=1;return handle}}}};function readPointer(pointer){return this.fromWireType(HEAPU32[pointer>>>2>>>0])}var EmValType={name:"emscripten::val",fromWireType:handle=>{var rv=Emval.toValue(handle);__emval_decref(handle);return rv},toWireType:(destructors,value)=>Emval.toHandle(value),readValueFromPointer:readPointer,destructorFunction:null};function __embind_register_emval(rawType){rawType>>>=0;return registerType(rawType,EmValType)}var floatReadValueFromPointer=(name,width)=>{switch(width){case 4:return function(pointer){return this.fromWireType(HEAPF32[pointer>>>2>>>0])};case 8:return function(pointer){return this.fromWireType(HEAPF64[pointer>>>3>>>0])};default:throw new TypeError(`invalid float width (${width}): ${name}`)}};var __embind_register_float=function(rawType,name,size){rawType>>>=0;name>>>=0;size>>>=0;name=AsciiToString(name);registerType(rawType,{name,fromWireType:value=>value,toWireType:(destructors,value)=>value,readValueFromPointer:floatReadValueFromPointer(name,size),destructorFunction:null})};var __embind_register_integer=function(primitiveType,name,size,minRange,maxRange){primitiveType>>>=0;name>>>=0;size>>>=0;name=AsciiToString(name);const isUnsignedType=minRange===0;let fromWireType=value=>value;if(isUnsignedType){var bitshift=32-8*size;fromWireType=value=>value<<bitshift>>>bitshift;maxRange=fromWireType(maxRange)}registerType(primitiveType,{name,fromWireType,toWireType:(destructors,value)=>value,readValueFromPointer:integerReadValueFromPointer(name,size,minRange!==0),destructorFunction:null})};function __embind_register_memory_view(rawType,dataTypeIndex,name){rawType>>>=0;name>>>=0;var typeMapping=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,BigInt64Array,BigUint64Array];var TA=typeMapping[dataTypeIndex];function decodeMemoryView(handle){var size=HEAPU32[handle>>>2>>>0];var data=HEAPU32[handle+4>>>2>>>0];return new TA(HEAP8.buffer,data,size)}name=AsciiToString(name);registerType(rawType,{name,fromWireType:decodeMemoryView,readValueFromPointer:decodeMemoryView},{ignoreDuplicateRegistrations:true})}var stringToUTF8=(str,outPtr,maxBytesToWrite)=>stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite);function __embind_register_std_string(rawType,name){rawType>>>=0;name>>>=0;name=AsciiToString(name);var stdStringIsUTF8=true;registerType(rawType,{name,fromWireType(value){var length=HEAPU32[value>>>2>>>0];var payload=value+4;var str;if(stdStringIsUTF8){str=UTF8ToString(payload,length,true)}else{str="";for(var i=0;i<length;++i){str+=String.fromCharCode(HEAPU8[payload+i>>>0])}}_free(value);return str},toWireType(destructors,value){if(value instanceof ArrayBuffer){value=new Uint8Array(value)}var length;var valueIsOfTypeString=typeof value=="string";if(!(valueIsOfTypeString||ArrayBuffer.isView(value)&&value.BYTES_PER_ELEMENT==1)){throwBindingError("Cannot pass non-string to std::string")}if(stdStringIsUTF8&&valueIsOfTypeString){length=lengthBytesUTF8(value)}else{length=value.length}var base=_malloc(4+length+1);var ptr=base+4;HEAPU32[base>>>2>>>0]=length;if(valueIsOfTypeString){if(stdStringIsUTF8){stringToUTF8(value,ptr,length+1)}else{for(var i=0;i<length;++i){var charCode=value.charCodeAt(i);if(charCode>255){_free(base);throwBindingError("String has UTF-16 code units that do not fit in 8 bits")}HEAPU8[ptr+i>>>0]=charCode}}}else{HEAPU8.set(value,ptr>>>0)}if(destructors!==null){destructors.push(_free,base)}return base},readValueFromPointer:readPointer,destructorFunction(ptr){_free(ptr)}})}var UTF16Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf-16le"):undefined;var UTF16ToString=(ptr,maxBytesToRead,ignoreNul)=>{var idx=ptr>>>1;var endIdx=findStringEnd(HEAPU16,idx,maxBytesToRead/2,ignoreNul);if(endIdx-idx>16&&UTF16Decoder)return UTF16Decoder.decode(HEAPU16.subarray(idx>>>0,endIdx>>>0));var str="";for(var i=idx;i<endIdx;++i){var codeUnit=HEAPU16[i>>>0];str+=String.fromCharCode(codeUnit)}return str};var stringToUTF16=(str,outPtr,maxBytesToWrite)=>{maxBytesToWrite??=2147483647;if(maxBytesToWrite<2)return 0;maxBytesToWrite-=2;var startPtr=outPtr;var numCharsToWrite=maxBytesToWrite<str.length*2?maxBytesToWrite/2:str.length;for(var i=0;i<numCharsToWrite;++i){var codeUnit=str.charCodeAt(i);HEAP16[outPtr>>>1>>>0]=codeUnit;outPtr+=2}HEAP16[outPtr>>>1>>>0]=0;return outPtr-startPtr};var lengthBytesUTF16=str=>str.length*2;var UTF32ToString=(ptr,maxBytesToRead,ignoreNul)=>{var str="";var startIdx=ptr>>>2;for(var i=0;!(i>=maxBytesToRead/4);i++){var utf32=HEAPU32[startIdx+i>>>0];if(!utf32&&!ignoreNul)break;str+=String.fromCodePoint(utf32)}return str};var stringToUTF32=(str,outPtr,maxBytesToWrite)=>{outPtr>>>=0;maxBytesToWrite??=2147483647;if(maxBytesToWrite<4)return 0;var startPtr=outPtr;var endPtr=startPtr+maxBytesToWrite-4;for(var i=0;i<str.length;++i){var codePoint=str.codePointAt(i);if(codePoint>65535){i++}HEAP32[outPtr>>>2>>>0]=codePoint;outPtr+=4;if(outPtr+4>endPtr)break}HEAP32[outPtr>>>2>>>0]=0;return outPtr-startPtr};var lengthBytesUTF32=str=>{var len=0;for(var i=0;i<str.length;++i){var codePoint=str.codePointAt(i);if(codePoint>65535){i++}len+=4}return len};function __embind_register_std_wstring(rawType,charSize,name){rawType>>>=0;charSize>>>=0;name>>>=0;name=AsciiToString(name);var decodeString,encodeString,lengthBytesUTF;if(charSize===2){decodeString=UTF16ToString;encodeString=stringToUTF16;lengthBytesUTF=lengthBytesUTF16}else{decodeString=UTF32ToString;encodeString=stringToUTF32;lengthBytesUTF=lengthBytesUTF32}registerType(rawType,{name,fromWireType:value=>{var length=HEAPU32[value>>>2>>>0];var str=decodeString(value+4,length*charSize,true);_free(value);return str},toWireType:(destructors,value)=>{if(!(typeof value=="string")){throwBindingError(`Cannot pass non-string to C++ string type ${name}`)}var length=lengthBytesUTF(value);var ptr=_malloc(4+length+charSize);HEAPU32[ptr>>>2>>>0]=length/charSize;encodeString(value,ptr+4,length+charSize);if(destructors!==null){destructors.push(_free,ptr)}return ptr},readValueFromPointer:readPointer,destructorFunction(ptr){_free(ptr)}})}var __embind_register_void=function(rawType,name){rawType>>>=0;name>>>=0;name=AsciiToString(name);registerType(rawType,{isVoid:true,name,fromWireType:()=>undefined,toWireType:(destructors,o)=>undefined})};var __tzset_js=function(timezone,daylight,std_name,dst_name){timezone>>>=0;daylight>>>=0;std_name>>>=0;dst_name>>>=0;var currentYear=(new Date).getFullYear();var winter=new Date(currentYear,0,1);var summer=new Date(currentYear,6,1);var winterOffset=winter.getTimezoneOffset();var summerOffset=summer.getTimezoneOffset();var stdTimezoneOffset=Math.max(winterOffset,summerOffset);HEAPU32[timezone>>>2>>>0]=stdTimezoneOffset*60;HEAP32[daylight>>>2>>>0]=Number(winterOffset!=summerOffset);var extractZone=timezoneOffset=>{var sign=timezoneOffset>=0?"-":"+";var absOffset=Math.abs(timezoneOffset);var hours=String(Math.floor(absOffset/60)).padStart(2,"0");var minutes=String(absOffset%60).padStart(2,"0");return`UTC${sign}${hours}${minutes}`};var winterName=extractZone(winterOffset);var summerName=extractZone(summerOffset);if(summerOffset<winterOffset){stringToUTF8(winterName,std_name,17);stringToUTF8(summerName,dst_name,17)}else{stringToUTF8(winterName,dst_name,17);stringToUTF8(summerName,std_name,17)}};var _emscripten_get_now=()=>performance.now();var _emscripten_date_now=()=>Date.now();var nowIsMonotonic=1;var checkWasiClock=clock_id=>clock_id>=0&&clock_id<=3;function _clock_time_get(clk_id,ignored_precision,ptime){ignored_precision=bigintToI53Checked(ignored_precision);ptime>>>=0;if(!checkWasiClock(clk_id)){return 28}var now;if(clk_id===0){now=_emscripten_date_now()}else if(nowIsMonotonic){now=_emscripten_get_now()}else{return 52}var nsec=Math.round(now*1e3*1e3);HEAP64[ptime>>>3>>>0]=BigInt(nsec);return 0}function _emscripten_fetch_free(id){if(Fetch.xhrs.has(id)){var xhr=Fetch.xhrs.get(id);Fetch.xhrs.free(id);if(xhr.readyState>0&&xhr.readyState<4){xhr.abort()}}}var _emscripten_has_asyncify=()=>1;var _emscripten_is_main_browser_thread=()=>!ENVIRONMENT_IS_WORKER;var getHeapMax=()=>4294901760;var alignMemory=(size,alignment)=>Math.ceil(size/alignment)*alignment;var growMemory=size=>{var oldHeapSize=wasmMemory.buffer.byteLength;var pages=(size-oldHeapSize+65535)/65536|0;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){}};function _emscripten_resize_heap(requestedSize){requestedSize>>>=0;var oldSize=HEAPU8.length;var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){return false}for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignMemory(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=growMemory(newSize);if(replacement){return true}}return false}var handleException=e=>{if(e instanceof ExitStatus||e=="unwind"){return EXITSTATUS}quit_(1,e)};var runtimeKeepaliveCounter=0;var keepRuntimeAlive=()=>noExitRuntime||runtimeKeepaliveCounter>0;var _proc_exit=code=>{EXITSTATUS=code;if(!keepRuntimeAlive()){Module["onExit"]?.(code);ABORT=true}quit_(code,new ExitStatus(code))};var exitJS=(status,implicit)=>{EXITSTATUS=status;_proc_exit(status)};var _exit=exitJS;var maybeExit=()=>{if(!keepRuntimeAlive()){try{_exit(EXITSTATUS)}catch(e){handleException(e)}}};var callUserCallback=func=>{if(ABORT){return}try{func();maybeExit()}catch(e){handleException(e)}};var _emscripten_set_main_loop_timing=(mode,value)=>{MainLoop.timingMode=mode;MainLoop.timingValue=value;if(!MainLoop.func){return 1}if(!MainLoop.running){MainLoop.running=true}if(mode==0){MainLoop.scheduler=function MainLoop_scheduler_setTimeout(){var timeUntilNextTick=Math.max(0,MainLoop.tickStartTime+value-_emscripten_get_now())|0;setTimeout(MainLoop.runner,timeUntilNextTick)};MainLoop.method="timeout"}else if(mode==1){MainLoop.scheduler=function MainLoop_scheduler_rAF(){MainLoop.requestAnimationFrame(MainLoop.runner)};MainLoop.method="rAF"}else if(mode==2){if(typeof MainLoop.setImmediate=="undefined"){if(typeof setImmediate=="undefined"){var setImmediates=[];var emscriptenMainLoopMessageId="setimmediate";var MainLoop_setImmediate_messageHandler=event=>{if(event.data===emscriptenMainLoopMessageId||event.data.target===emscriptenMainLoopMessageId){event.stopPropagation();setImmediates.shift()()}};addEventListener("message",MainLoop_setImmediate_messageHandler,true);MainLoop.setImmediate=func=>{setImmediates.push(func);if(ENVIRONMENT_IS_WORKER){Module["setImmediates"]??=[];Module["setImmediates"].push(func);postMessage({target:emscriptenMainLoopMessageId})}else postMessage(emscriptenMainLoopMessageId,"*")}}else{MainLoop.setImmediate=setImmediate}}MainLoop.scheduler=function MainLoop_scheduler_setImmediate(){MainLoop.setImmediate(MainLoop.runner)};MainLoop.method="immediate"}return 0};var MainLoop={running:false,scheduler:null,method:"",currentlyRunningMainloop:0,func:null,arg:0,timingMode:0,timingValue:0,currentFrameNumber:0,queue:[],preMainLoop:[],postMainLoop:[],pause(){MainLoop.scheduler=null;MainLoop.currentlyRunningMainloop++},resume(){MainLoop.currentlyRunningMainloop++;var timingMode=MainLoop.timingMode;var timingValue=MainLoop.timingValue;var func=MainLoop.func;MainLoop.func=null;setMainLoop(func,0,false,MainLoop.arg,true);_emscripten_set_main_loop_timing(timingMode,timingValue);MainLoop.scheduler()},updateStatus(){if(Module["setStatus"]){var message=Module["statusMessage"]||"Please wait...";var remaining=MainLoop.remainingBlockers??0;var expected=MainLoop.expectedBlockers??0;if(remaining){if(remaining<expected){Module["setStatus"](`{message} ({expected - remaining}/{expected})`)}else{Module["setStatus"](message)}}else{Module["setStatus"]("")}}},init(){Module["preMainLoop"]&&MainLoop.preMainLoop.push(Module["preMainLoop"]);Module["postMainLoop"]&&MainLoop.postMainLoop.push(Module["postMainLoop"])},runIter(func){if(ABORT)return;for(var pre of MainLoop.preMainLoop){if(pre()===false){return}}callUserCallback(func);for(var post of MainLoop.postMainLoop){post()}},nextRAF:0,fakeRequestAnimationFrame(func){var now=Date.now();if(MainLoop.nextRAF===0){MainLoop.nextRAF=now+1e3/60}else{while(now+2>=MainLoop.nextRAF){MainLoop.nextRAF+=1e3/60}}var delay=Math.max(MainLoop.nextRAF-now,0);setTimeout(func,delay)},requestAnimationFrame(func){if(typeof requestAnimationFrame=="function"){requestAnimationFrame(func);return}var RAF=MainLoop.fakeRequestAnimationFrame;RAF(func)}};var setMainLoop=(iterFunc,fps,simulateInfiniteLoop,arg,noSetTiming)=>{MainLoop.func=iterFunc;MainLoop.arg=arg;var thisMainLoopId=MainLoop.currentlyRunningMainloop;function checkIsRunning(){if(thisMainLoopId<MainLoop.currentlyRunningMainloop){maybeExit();return false}return true}MainLoop.running=false;MainLoop.runner=function MainLoop_runner(){if(ABORT)return;if(MainLoop.queue.length>0){var start=Date.now();var blocker=MainLoop.queue.shift();blocker.func(blocker.arg);if(MainLoop.remainingBlockers){var remaining=MainLoop.remainingBlockers;var next=remaining%1==0?remaining-1:Math.floor(remaining);if(blocker.counted){MainLoop.remainingBlockers=next}else{next=next+.5;MainLoop.remainingBlockers=(8*remaining+next)/9}}MainLoop.updateStatus();if(!checkIsRunning())return;setTimeout(MainLoop.runner,0);return}if(!checkIsRunning())return;MainLoop.currentFrameNumber=MainLoop.currentFrameNumber+1|0;if(MainLoop.timingMode==1&&MainLoop.timingValue>1&&MainLoop.currentFrameNumber%MainLoop.timingValue!=0){MainLoop.scheduler();return}else if(MainLoop.timingMode==0){MainLoop.tickStartTime=_emscripten_get_now()}MainLoop.runIter(iterFunc);if(!checkIsRunning())return;MainLoop.scheduler()};if(!noSetTiming){if(fps>0){_emscripten_set_main_loop_timing(0,1e3/fps)}else{_emscripten_set_main_loop_timing(1,1)}MainLoop.scheduler()}if(simulateInfiniteLoop){throw"unwind"}};var _emscripten_set_main_loop=function(func,fps,simulateInfiniteLoop){func>>>=0;var iterFunc=()=>dynCall_v(func);setMainLoop(iterFunc,fps,simulateInfiniteLoop)};var safeSetTimeout=(func,timeout)=>setTimeout(()=>{callUserCallback(func)},timeout);var _emscripten_sleep=ms=>Asyncify.handleSleep(wakeUp=>safeSetTimeout(wakeUp,ms));_emscripten_sleep.isAsync=true;class HandleAllocator{allocated=[undefined];freelist=[];get(id){return this.allocated[id]}has(id){return this.allocated[id]!==undefined}allocate(handle){var id=this.freelist.pop()||this.allocated.length;this.allocated[id]=handle;return id}free(id){this.allocated[id]=undefined;this.freelist.push(id)}}var Fetch={openDatabase(dbname,dbversion,onsuccess,onerror){try{var openRequest=indexedDB.open(dbname,dbversion)}catch(e){return onerror(e)}openRequest.onupgradeneeded=event=>{var db=event.target.result;if(db.objectStoreNames.contains("FILES")){db.deleteObjectStore("FILES")}db.createObjectStore("FILES")};openRequest.onsuccess=event=>onsuccess(event.target.result);openRequest.onerror=onerror},init(){Fetch.xhrs=new HandleAllocator;var onsuccess=db=>{Fetch.dbInstance=db;removeRunDependency("library_fetch_init")};var onerror=()=>{Fetch.dbInstance=false;removeRunDependency("library_fetch_init")};addRunDependency("library_fetch_init");Fetch.openDatabase("emscripten_filesystem",1,onsuccess,onerror)}};function fetchXHR(fetch,onsuccess,onerror,onprogress,onreadystatechange){var url=HEAPU32[fetch+8>>>2>>>0];if(!url){onerror(fetch,0,"no url specified!");return}var url_=UTF8ToString(url);var fetch_attr=fetch+108;var requestMethod=UTF8ToString(fetch_attr+0);requestMethod||="GET";var timeoutMsecs=HEAPU32[fetch_attr+56>>>2>>>0];var userName=HEAPU32[fetch_attr+68>>>2>>>0];var password=HEAPU32[fetch_attr+72>>>2>>>0];var requestHeaders=HEAPU32[fetch_attr+76>>>2>>>0];var overriddenMimeType=HEAPU32[fetch_attr+80>>>2>>>0];var dataPtr=HEAPU32[fetch_attr+84>>>2>>>0];var dataLength=HEAPU32[fetch_attr+88>>>2>>>0];var fetchAttributes=HEAPU32[fetch_attr+52>>>2>>>0];var fetchAttrLoadToMemory=!!(fetchAttributes&1);var fetchAttrStreamData=!!(fetchAttributes&2);var fetchAttrSynchronous=!!(fetchAttributes&64);var userNameStr=userName?UTF8ToString(userName):undefined;var passwordStr=password?UTF8ToString(password):undefined;var xhr=new XMLHttpRequest;xhr.withCredentials=!!HEAPU8[fetch_attr+60>>>0];xhr.open(requestMethod,url_,!fetchAttrSynchronous,userNameStr,passwordStr);if(!fetchAttrSynchronous)xhr.timeout=timeoutMsecs;xhr.url_=url_;xhr.responseType="arraybuffer";if(overriddenMimeType){var overriddenMimeTypeStr=UTF8ToString(overriddenMimeType);xhr.overrideMimeType(overriddenMimeTypeStr)}if(requestHeaders){for(;;){var key=HEAPU32[requestHeaders>>>2>>>0];if(!key)break;var value=HEAPU32[requestHeaders+4>>>2>>>0];if(!value)break;requestHeaders+=8;var keyStr=UTF8ToString(key);var valueStr=UTF8ToString(value);xhr.setRequestHeader(keyStr,valueStr)}}var id=Fetch.xhrs.allocate(xhr);HEAPU32[fetch>>>2>>>0]=id;var data=dataPtr&&dataLength?HEAPU8.slice(dataPtr,dataPtr+dataLength):null;function saveResponseAndStatus(){var ptr=0;var ptrLen=0;if(xhr.response&&fetchAttrLoadToMemory&&HEAPU32[fetch+12>>>2>>>0]===0){ptrLen=xhr.response.byteLength}if(ptrLen>0){ptr=_malloc(ptrLen);HEAPU8.set(new Uint8Array(xhr.response),ptr>>>0)}HEAPU32[fetch+12>>>2>>>0]=ptr;writeI53ToI64(fetch+16,ptrLen);writeI53ToI64(fetch+24,0);var len=xhr.response?xhr.response.byteLength:0;if(len){writeI53ToI64(fetch+32,len)}HEAP16[fetch+40>>>1>>>0]=xhr.readyState;HEAP16[fetch+42>>>1>>>0]=xhr.status;if(xhr.statusText)stringToUTF8(xhr.statusText,fetch+44,64);if(fetchAttrSynchronous){var ruPtr=stringToNewUTF8(xhr.responseURL);HEAPU32[fetch+200>>>2>>>0]=ruPtr}}xhr.onload=e=>{if(!Fetch.xhrs.has(id)){return}saveResponseAndStatus();if(xhr.status>=200&&xhr.status<300){onsuccess?.(fetch,xhr,e)}else{onerror?.(fetch,xhr,e)}};xhr.onerror=e=>{if(!Fetch.xhrs.has(id)){return}saveResponseAndStatus();onerror?.(fetch,xhr,e)};xhr.ontimeout=e=>{if(!Fetch.xhrs.has(id)){return}onerror?.(fetch,xhr,e)};xhr.onprogress=e=>{if(!Fetch.xhrs.has(id)){return}var ptrLen=fetchAttrLoadToMemory&&fetchAttrStreamData&&xhr.response?xhr.response.byteLength:0;var ptr=0;if(ptrLen>0&&fetchAttrLoadToMemory&&fetchAttrStreamData){ptr=_malloc(ptrLen);HEAPU8.set(new Uint8Array(xhr.response),ptr>>>0)}HEAPU32[fetch+12>>>2>>>0]=ptr;writeI53ToI64(fetch+16,ptrLen);writeI53ToI64(fetch+24,e.loaded-ptrLen);writeI53ToI64(fetch+32,e.total);HEAP16[fetch+40>>>1>>>0]=xhr.readyState;if(xhr.readyState>=3&&xhr.status===0&&e.loaded>0)xhr.status=200;HEAP16[fetch+42>>>1>>>0]=xhr.status;if(xhr.statusText)stringToUTF8(xhr.statusText,fetch+44,64);onprogress?.(fetch,xhr,e);_free(ptr)};xhr.onreadystatechange=e=>{if(!Fetch.xhrs.has(id)){return}HEAP16[fetch+40>>>1>>>0]=xhr.readyState;if(xhr.readyState>=2){HEAP16[fetch+42>>>1>>>0]=xhr.status}if(!fetchAttrSynchronous&&(xhr.readyState===2&&xhr.responseURL.length>0)){var ruPtr=stringToNewUTF8(xhr.responseURL);HEAPU32[fetch+200>>>2>>>0]=ruPtr}onreadystatechange?.(fetch,xhr,e)};try{xhr.send(data)}catch(e){onerror?.(fetch,xhr,e)}}var writeI53ToI64=(ptr,num)=>{HEAPU32[ptr>>>2>>>0]=num;var lower=HEAPU32[ptr>>>2>>>0];HEAPU32[ptr+4>>>2>>>0]=(num-lower)/4294967296};var stringToNewUTF8=str=>{var size=lengthBytesUTF8(str)+1;var ret=_malloc(size);if(ret)stringToUTF8(str,ret,size);return ret};function fetchCacheData(db,fetch,data,onsuccess,onerror){if(!db){onerror(fetch,0,"IndexedDB not available!");return}var fetch_attr=fetch+108;var destinationPath=HEAPU32[fetch_attr+64>>>2>>>0];destinationPath||=HEAPU32[fetch+8>>>2>>>0];var destinationPathStr=UTF8ToString(destinationPath);try{var transaction=db.transaction(["FILES"],"readwrite");var packages=transaction.objectStore("FILES");var putRequest=packages.put(data,destinationPathStr);putRequest.onsuccess=event=>{HEAP16[fetch+40>>>1>>>0]=4;HEAP16[fetch+42>>>1>>>0]=200;stringToUTF8("OK",fetch+44,64);onsuccess(fetch,0,destinationPathStr)};putRequest.onerror=error=>{HEAP16[fetch+40>>>1>>>0]=4;HEAP16[fetch+42>>>1>>>0]=413;stringToUTF8("Payload Too Large",fetch+44,64);onerror(fetch,0,error)}}catch(e){onerror(fetch,0,e)}}function fetchLoadCachedData(db,fetch,onsuccess,onerror){if(!db){onerror(fetch,0,"IndexedDB not available!");return}var fetch_attr=fetch+108;var path=HEAPU32[fetch_attr+64>>>2>>>0];path||=HEAPU32[fetch+8>>>2>>>0];var pathStr=UTF8ToString(path);try{var transaction=db.transaction(["FILES"],"readonly");var packages=transaction.objectStore("FILES");var getRequest=packages.get(pathStr);getRequest.onsuccess=event=>{if(event.target.result){var value=event.target.result;var len=value.byteLength||value.length;var ptr=_malloc(len);HEAPU8.set(new Uint8Array(value),ptr>>>0);HEAPU32[fetch+12>>>2>>>0]=ptr;writeI53ToI64(fetch+16,len);writeI53ToI64(fetch+24,0);writeI53ToI64(fetch+32,len);HEAP16[fetch+40>>>1>>>0]=4;HEAP16[fetch+42>>>1>>>0]=200;stringToUTF8("OK",fetch+44,64);onsuccess(fetch,0,value)}else{HEAP16[fetch+40>>>1>>>0]=4;HEAP16[fetch+42>>>1>>>0]=404;stringToUTF8("Not Found",fetch+44,64);onerror(fetch,0,"no data")}};getRequest.onerror=error=>{HEAP16[fetch+40>>>1>>>0]=4;HEAP16[fetch+42>>>1>>>0]=404;stringToUTF8("Not Found",fetch+44,64);onerror(fetch,0,error)}}catch(e){onerror(fetch,0,e)}}function fetchDeleteCachedData(db,fetch,onsuccess,onerror){if(!db){onerror(fetch,0,"IndexedDB not available!");return}var fetch_attr=fetch+108;var path=HEAPU32[fetch_attr+64>>>2>>>0];path||=HEAPU32[fetch+8>>>2>>>0];var pathStr=UTF8ToString(path);try{var transaction=db.transaction(["FILES"],"readwrite");var packages=transaction.objectStore("FILES");var request=packages.delete(pathStr);request.onsuccess=event=>{var value=event.target.result;HEAPU32[fetch+12>>>2>>>0]=0;writeI53ToI64(fetch+16,0);writeI53ToI64(fetch+24,0);writeI53ToI64(fetch+32,0);HEAP16[fetch+40>>>1>>>0]=4;HEAP16[fetch+42>>>1>>>0]=200;stringToUTF8("OK",fetch+44,64);onsuccess(fetch,0,value)};request.onerror=error=>{HEAP16[fetch+40>>>1>>>0]=4;HEAP16[fetch+42>>>1>>>0]=404;stringToUTF8("Not Found",fetch+44,64);onerror(fetch,0,error)}}catch(e){onerror(fetch,0,e)}}function _emscripten_start_fetch(fetch,successcb,errorcb,progresscb,readystatechangecb){fetch>>>=0;var fetch_attr=fetch+108;var onsuccess=HEAPU32[fetch_attr+36>>>2>>>0];var onerror=HEAPU32[fetch_attr+40>>>2>>>0];var onprogress=HEAPU32[fetch_attr+44>>>2>>>0];var onreadystatechange=HEAPU32[fetch_attr+48>>>2>>>0];var fetchAttributes=HEAPU32[fetch_attr+52>>>2>>>0];var fetchAttrSynchronous=!!(fetchAttributes&64);function doCallback(f){if(fetchAttrSynchronous){f()}else{callUserCallback(f)}}var reportSuccess=(fetch,xhr,e)=>{doCallback(()=>{if(onsuccess)(a1=>dynCall_vi(onsuccess,a1))(fetch);else successcb?.(fetch)})};var reportProgress=(fetch,xhr,e)=>{doCallback(()=>{if(onprogress)(a1=>dynCall_vi(onprogress,a1))(fetch);else progresscb?.(fetch)})};var reportError=(fetch,xhr,e)=>{doCallback(()=>{if(onerror)(a1=>dynCall_vi(onerror,a1))(fetch);else errorcb?.(fetch)})};var reportReadyStateChange=(fetch,xhr,e)=>{doCallback(()=>{if(onreadystatechange)(a1=>dynCall_vi(onreadystatechange,a1))(fetch);else readystatechangecb?.(fetch)})};var performUncachedXhr=(fetch,xhr,e)=>{fetchXHR(fetch,reportSuccess,reportError,reportProgress,reportReadyStateChange)};var cacheResultAndReportSuccess=(fetch,xhr,e)=>{var storeSuccess=(fetch,xhr,e)=>{doCallback(()=>{if(onsuccess)(a1=>dynCall_vi(onsuccess,a1))(fetch);else successcb?.(fetch)})};var storeError=(fetch,xhr,e)=>{doCallback(()=>{if(onsuccess)(a1=>dynCall_vi(onsuccess,a1))(fetch);else successcb?.(fetch)})};fetchCacheData(Fetch.dbInstance,fetch,xhr.response,storeSuccess,storeError)};var performCachedXhr=(fetch,xhr,e)=>{fetchXHR(fetch,cacheResultAndReportSuccess,reportError,reportProgress,reportReadyStateChange)};var requestMethod=UTF8ToString(fetch_attr+0);var fetchAttrReplace=!!(fetchAttributes&16);var fetchAttrPersistFile=!!(fetchAttributes&4);var fetchAttrNoDownload=!!(fetchAttributes&32);if(requestMethod==="EM_IDB_STORE"){var ptr=HEAPU32[fetch_attr+84>>>2>>>0];var size=HEAPU32[fetch_attr+88>>>2>>>0];fetchCacheData(Fetch.dbInstance,fetch,HEAPU8.slice(ptr,ptr+size),reportSuccess,reportError)}else if(requestMethod==="EM_IDB_DELETE"){fetchDeleteCachedData(Fetch.dbInstance,fetch,reportSuccess,reportError)}else if(!fetchAttrReplace){fetchLoadCachedData(Fetch.dbInstance,fetch,reportSuccess,fetchAttrNoDownload?reportError:fetchAttrPersistFile?performCachedXhr:performUncachedXhr)}else if(!fetchAttrNoDownload){fetchXHR(fetch,fetchAttrPersistFile?cacheResultAndReportSuccess:reportSuccess,reportError,reportProgress,reportReadyStateChange)}else{return 0}return fetch}var stackAlloc=sz=>__emscripten_stack_alloc(sz);var stringToUTF8OnStack=str=>{var size=lengthBytesUTF8(str)+1;var ret=stackAlloc(size);stringToUTF8(str,ret,size);return ret};var WebGPU={Internals:{jsObjects:[],jsObjectInsert:(ptr,jsObject)=>{WebGPU.Internals.jsObjects[ptr]=jsObject},bufferOnUnmaps:[],futures:[],futureInsert:(futureId,promise)=>{WebGPU.Internals.futures[futureId]=new Promise(resolve=>promise.finally(()=>resolve(futureId)))}},getJsObject:ptr=>{if(!ptr)return undefined;return WebGPU.Internals.jsObjects[ptr]},importJsAdapter:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateAdapter(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsBindGroup:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateBindGroup(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsBindGroupLayout:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateBindGroupLayout(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsBuffer:(buffer,parentPtr=0)=>{assert(buffer.mapState!="pending");var mapState=buffer.mapState=="mapped"?3:1;var bufferPtr=_emwgpuCreateBuffer(parentPtr,mapState);WebGPU.Internals.jsObjectInsert(bufferPtr,buffer);if(buffer.mapState=="mapped"){WebGPU.Internals.bufferOnUnmaps[bufferPtr]=[]}return bufferPtr},importJsCommandBuffer:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateCommandBuffer(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsCommandEncoder:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateCommandEncoder(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsComputePassEncoder:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateComputePassEncoder(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsComputePipeline:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateComputePipeline(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsDevice:(device,parentPtr=0)=>{var queuePtr=_emwgpuCreateQueue(parentPtr);var devicePtr=_emwgpuCreateDevice(parentPtr,queuePtr);WebGPU.Internals.jsObjectInsert(queuePtr,device.queue);WebGPU.Internals.jsObjectInsert(devicePtr,device);return devicePtr},importJsPipelineLayout:(obj,parentPtr=0)=>{var ptr=_emwgpuCreatePipelineLayout(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsQuerySet:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateQuerySet(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsQueue:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateQueue(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsRenderBundle:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateRenderBundle(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsRenderBundleEncoder:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateRenderBundleEncoder(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsRenderPassEncoder:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateRenderPassEncoder(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsRenderPipeline:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateRenderPipeline(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsSampler:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateSampler(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsShaderModule:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateShaderModule(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsSurface:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateSurface(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsTexture:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateTexture(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},importJsTextureView:(obj,parentPtr=0)=>{var ptr=_emwgpuCreateTextureView(parentPtr);WebGPU.Internals.jsObjects[ptr]=obj;return ptr},errorCallback:(callback,type,message,userdata)=>{var sp=stackSave();var messagePtr=stringToUTF8OnStack(message);((a1,a2,a3)=>dynCall_viii(callback,a1,a2,a3))(type,messagePtr,userdata);stackRestore(sp)},setStringView:(ptr,data,length)=>{HEAPU32[ptr>>>2>>>0]=data;HEAPU32[ptr+4>>>2>>>0]=length},makeStringFromStringView:stringViewPtr=>{var ptr=HEAPU32[stringViewPtr>>>2>>>0];var length=HEAPU32[stringViewPtr+4>>>2>>>0];return UTF8ToString(ptr,length)},makeStringFromOptionalStringView:stringViewPtr=>{var ptr=HEAPU32[stringViewPtr>>>2>>>0];var length=HEAPU32[stringViewPtr+4>>>2>>>0];if(!ptr){if(length===0){return""}return undefined}return UTF8ToString(ptr,length)},makeColor:ptr=>({r:HEAPF64[ptr>>>3>>>0],g:HEAPF64[ptr+8>>>3>>>0],b:HEAPF64[ptr+16>>>3>>>0],a:HEAPF64[ptr+24>>>3>>>0]}),makeExtent3D:ptr=>({width:HEAPU32[ptr>>>2>>>0],height:HEAPU32[ptr+4>>>2>>>0],depthOrArrayLayers:HEAPU32[ptr+8>>>2>>>0]}),makeOrigin3D:ptr=>({x:HEAPU32[ptr>>>2>>>0],y:HEAPU32[ptr+4>>>2>>>0],z:HEAPU32[ptr+8>>>2>>>0]}),makeTexelCopyTextureInfo:ptr=>({texture:WebGPU.getJsObject(HEAPU32[ptr>>>2>>>0]),mipLevel:HEAPU32[ptr+4>>>2>>>0],origin:WebGPU.makeOrigin3D(ptr+8),aspect:WebGPU.TextureAspect[HEAPU32[ptr+20>>>2>>>0]]}),makeTexelCopyBufferLayout:ptr=>{var bytesPerRow=HEAPU32[ptr+8>>>2>>>0];var rowsPerImage=HEAPU32[ptr+12>>>2>>>0];return{offset:HEAPU32[ptr+4>>>2>>>0]*4294967296+HEAPU32[ptr>>>2>>>0],bytesPerRow:bytesPerRow===4294967295?undefined:bytesPerRow,rowsPerImage:rowsPerImage===4294967295?undefined:rowsPerImage}},makeTexelCopyBufferInfo:ptr=>{var layoutPtr=ptr+0;var bufferCopyView=WebGPU.makeTexelCopyBufferLayout(layoutPtr);bufferCopyView["buffer"]=WebGPU.getJsObject(HEAPU32[ptr+16>>>2>>>0]);return bufferCopyView},makePassTimestampWrites:ptr=>{if(ptr===0)return undefined;return{querySet:WebGPU.getJsObject(HEAPU32[ptr+4>>>2>>>0]),beginningOfPassWriteIndex:HEAPU32[ptr+8>>>2>>>0],endOfPassWriteIndex:HEAPU32[ptr+12>>>2>>>0]}},makePipelineConstants:(constantCount,constantsPtr)=>{if(!constantCount)return;var constants={};for(var i=0;i<constantCount;++i){var entryPtr=constantsPtr+24*i;var key=WebGPU.makeStringFromStringView(entryPtr+4);constants[key]=HEAPF64[entryPtr+16>>>3>>>0]}return constants},makePipelineLayout:layoutPtr=>{if(!layoutPtr)return"auto";return WebGPU.getJsObject(layoutPtr)},makeComputeState:ptr=>{if(!ptr)return undefined;var desc={module:WebGPU.getJsObject(HEAPU32[ptr+4>>>2>>>0]),constants:WebGPU.makePipelineConstants(HEAPU32[ptr+16>>>2>>>0],HEAPU32[ptr+20>>>2>>>0]),entryPoint:WebGPU.makeStringFromOptionalStringView(ptr+8)};return desc},makeComputePipelineDesc:descriptor=>{var desc={label:WebGPU.makeStringFromOptionalStringView(descriptor+4),layout:WebGPU.makePipelineLayout(HEAPU32[descriptor+12>>>2>>>0]),compute:WebGPU.makeComputeState(descriptor+16)};return desc},makeRenderPipelineDesc:descriptor=>{function makePrimitiveState(psPtr){if(!psPtr)return undefined;return{topology:WebGPU.PrimitiveTopology[HEAPU32[psPtr+4>>>2>>>0]],stripIndexFormat:WebGPU.IndexFormat[HEAPU32[psPtr+8>>>2>>>0]],frontFace:WebGPU.FrontFace[HEAPU32[psPtr+12>>>2>>>0]],cullMode:WebGPU.CullMode[HEAPU32[psPtr+16>>>2>>>0]],unclippedDepth:!!HEAPU32[psPtr+20>>>2>>>0]}}function makeBlendComponent(bdPtr){if(!bdPtr)return undefined;return{operation:WebGPU.BlendOperation[HEAPU32[bdPtr>>>2>>>0]],srcFactor:WebGPU.BlendFactor[HEAPU32[bdPtr+4>>>2>>>0]],dstFactor:WebGPU.BlendFactor[HEAPU32[bdPtr+8>>>2>>>0]]}}function makeBlendState(bsPtr){if(!bsPtr)return undefined;return{alpha:makeBlendComponent(bsPtr+12),color:makeBlendComponent(bsPtr+0)}}function makeColorState(csPtr){var formatInt=HEAPU32[csPtr+4>>>2>>>0];return formatInt===0?undefined:{format:WebGPU.TextureFormat[formatInt],blend:makeBlendState(HEAPU32[csPtr+8>>>2>>>0]),writeMask:HEAPU32[csPtr+16>>>2>>>0]}}function makeColorStates(count,csArrayPtr){var states=[];for(var i=0;i<count;++i){states.push(makeColorState(csArrayPtr+24*i))}return states}function makeStencilStateFace(ssfPtr){return{compare:WebGPU.CompareFunction[HEAPU32[ssfPtr>>>2>>>0]],failOp:WebGPU.StencilOperation[HEAPU32[ssfPtr+4>>>2>>>0]],depthFailOp:WebGPU.StencilOperation[HEAPU32[ssfPtr+8>>>2>>>0]],passOp:WebGPU.StencilOperation[HEAPU32[ssfPtr+12>>>2>>>0]]}}function makeDepthStencilState(dssPtr){if(!dssPtr)return undefined;return{format:WebGPU.TextureFormat[HEAPU32[dssPtr+4>>>2>>>0]],depthWriteEnabled:!!HEAPU32[dssPtr+8>>>2>>>0],depthCompare:WebGPU.CompareFunction[HEAPU32[dssPtr+12>>>2>>>0]],stencilFront:makeStencilStateFace(dssPtr+16),stencilBack:makeStencilStateFace(dssPtr+32),stencilReadMask:HEAPU32[dssPtr+48>>>2>>>0],stencilWriteMask:HEAPU32[dssPtr+52>>>2>>>0],depthBias:HEAP32[dssPtr+56>>>2>>>0],depthBiasSlopeScale:HEAPF32[dssPtr+60>>>2>>>0],depthBiasClamp:HEAPF32[dssPtr+64>>>2>>>0]}}function makeVertexAttribute(vaPtr){return{format:WebGPU.VertexFormat[HEAPU32[vaPtr+4>>>2>>>0]],offset:HEAPU32[vaPtr+4+8>>>2>>>0]*4294967296+HEAPU32[vaPtr+8>>>2>>>0],shaderLocation:HEAPU32[vaPtr+16>>>2>>>0]}}function makeVertexAttributes(count,vaArrayPtr){var vas=[];for(var i=0;i<count;++i){vas.push(makeVertexAttribute(vaArrayPtr+i*24))}return vas}function makeVertexBuffer(vbPtr){if(!vbPtr)return undefined;var stepModeInt=HEAPU32[vbPtr+4>>>2>>>0];var attributeCountInt=HEAPU32[vbPtr+16>>>2>>>0];if(stepModeInt===0&&attributeCountInt===0){return null}return{arrayStride:HEAPU32[vbPtr+4+8>>>2>>>0]*4294967296+HEAPU32[vbPtr+8>>>2>>>0],stepMode:WebGPU.VertexStepMode[stepModeInt],attributes:makeVertexAttributes(attributeCountInt,HEAPU32[vbPtr+20>>>2>>>0])}}function makeVertexBuffers(count,vbArrayPtr){if(!count)return undefined;var vbs=[];for(var i=0;i<count;++i){vbs.push(makeVertexBuffer(vbArrayPtr+i*24))}return vbs}function makeVertexState(viPtr){if(!viPtr)return undefined;var desc={module:WebGPU.getJsObject(HEAPU32[viPtr+4>>>2>>>0]),constants:WebGPU.makePipelineConstants(HEAPU32[viPtr+16>>>2>>>0],HEAPU32[viPtr+20>>>2>>>0]),buffers:makeVertexBuffers(HEAPU32[viPtr+24>>>2>>>0],HEAPU32[viPtr+28>>>2>>>0]),entryPoint:WebGPU.makeStringFromOptionalStringView(viPtr+8)};return desc}function makeMultisampleState(msPtr){if(!msPtr)return undefined;return{count:HEAPU32[msPtr+4>>>2>>>0],mask:HEAPU32[msPtr+8>>>2>>>0],alphaToCoverageEnabled:!!HEAPU32[msPtr+12>>>2>>>0]}}function makeFragmentState(fsPtr){if(!fsPtr)return undefined;var desc={module:WebGPU.getJsObject(HEAPU32[fsPtr+4>>>2>>>0]),constants:WebGPU.makePipelineConstants(HEAPU32[fsPtr+16>>>2>>>0],HEAPU32[fsPtr+20>>>2>>>0]),targets:makeColorStates(HEAPU32[fsPtr+24>>>2>>>0],HEAPU32[fsPtr+28>>>2>>>0]),entryPoint:WebGPU.makeStringFromOptionalStringView(fsPtr+8)};return desc}var desc={label:WebGPU.makeStringFromOptionalStringView(descriptor+4),layout:WebGPU.makePipelineLayout(HEAPU32[descriptor+12>>>2>>>0]),vertex:makeVertexState(descriptor+16),primitive:makePrimitiveState(descriptor+48),depthStencil:makeDepthStencilState(HEAPU32[descriptor+72>>>2>>>0]),multisample:makeMultisampleState(descriptor+76),fragment:makeFragmentState(HEAPU32[descriptor+92>>>2>>>0])};return desc},fillLimitStruct:(limits,limitsOutPtr)=>{function setLimitValueU32(name,limitOffset){var limitValue=limits[name];HEAP32[limitsOutPtr+limitOffset>>>2>>>0]=limitValue}function setLimitValueU64(name,limitOffset){var limitValue=limits[name];HEAP64[limitsOutPtr+limitOffset>>>3>>>0]=BigInt(limitValue)}setLimitValueU32("maxTextureDimension1D",4);setLimitValueU32("maxTextureDimension2D",8);setLimitValueU32("maxTextureDimension3D",12);setLimitValueU32("maxTextureArrayLayers",16);setLimitValueU32("maxBindGroups",20);setLimitValueU32("maxBindGroupsPlusVertexBuffers",24);setLimitValueU32("maxBindingsPerBindGroup",28);setLimitValueU32("maxDynamicUniformBuffersPerPipelineLayout",32);setLimitValueU32("maxDynamicStorageBuffersPerPipelineLayout",36);setLimitValueU32("maxSampledTexturesPerShaderStage",40);setLimitValueU32("maxSamplersPerShaderStage",44);setLimitValueU32("maxStorageBuffersPerShaderStage",48);setLimitValueU32("maxStorageTexturesPerShaderStage",52);setLimitValueU32("maxUniformBuffersPerShaderStage",56);setLimitValueU32("minUniformBufferOffsetAlignment",80);setLimitValueU32("minStorageBufferOffsetAlignment",84);setLimitValueU64("maxUniformBufferBindingSize",64);setLimitValueU64("maxStorageBufferBindingSize",72);setLimitValueU32("maxVertexBuffers",88);setLimitValueU64("maxBufferSize",96);setLimitValueU32("maxVertexAttributes",104);setLimitValueU32("maxVertexBufferArrayStride",108);setLimitValueU32("maxInterStageShaderVariables",112);setLimitValueU32("maxColorAttachments",116);setLimitValueU32("maxColorAttachmentBytesPerSample",120);setLimitValueU32("maxComputeWorkgroupStorageSize",124);setLimitValueU32("maxComputeInvocationsPerWorkgroup",128);setLimitValueU32("maxComputeWorkgroupSizeX",132);setLimitValueU32("maxComputeWorkgroupSizeY",136);setLimitValueU32("maxComputeWorkgroupSizeZ",140);setLimitValueU32("maxComputeWorkgroupsPerDimension",144);if(limits.maxImmediateSize!==undefined){setLimitValueU32("maxImmediateSize",148)}},fillAdapterInfoStruct:(info,infoStruct)=>{HEAP32[infoStruct+52>>>2>>>0]=info.subgroupMinSize;HEAP32[infoStruct+56>>>2>>>0]=info.subgroupMaxSize;var strs=info.vendor+info.architecture+info.device+info.description;var strPtr=stringToNewUTF8(strs);var vendorLen=lengthBytesUTF8(info.vendor);WebGPU.setStringView(infoStruct+4,strPtr,vendorLen);strPtr+=vendorLen;var architectureLen=lengthBytesUTF8(info.architecture);WebGPU.setStringView(infoStruct+12,strPtr,architectureLen);strPtr+=architectureLen;var deviceLen=lengthBytesUTF8(info.device);WebGPU.setStringView(infoStruct+20,strPtr,deviceLen);strPtr+=deviceLen;var descriptionLen=lengthBytesUTF8(info.description);WebGPU.setStringView(infoStruct+28,strPtr,descriptionLen);strPtr+=descriptionLen;HEAP32[infoStruct+36>>>2>>>0]=2;var adapterType=info.isFallbackAdapter?3:4;HEAP32[infoStruct+40>>>2>>>0]=adapterType;HEAP32[infoStruct+44>>>2>>>0]=0;HEAP32[infoStruct+48>>>2>>>0]=0},Int_BufferMapState:{unmapped:1,pending:2,mapped:3},Int_CompilationMessageType:{error:1,warning:2,info:3},Int_DeviceLostReason:{undefined:1,unknown:1,destroyed:2},Int_PreferredFormat:{rgba8unorm:18,bgra8unorm:23},AddressMode:[,"clamp-to-edge","repeat","mirror-repeat"],BlendFactor:[,"zero","one","src","one-minus-src","src-alpha","one-minus-src-alpha","dst","one-minus-dst","dst-alpha","one-minus-dst-alpha","src-alpha-saturated","constant","one-minus-constant","src1","one-minus-src1","src1alpha","one-minus-src1alpha"],BlendOperation:[,"add","subtract","reverse-subtract","min","max"],BufferBindingType:["binding-not-used",,"uniform","storage","read-only-storage"],BufferMapState:{1:"unmapped",2:"pending",3:"mapped"},CompareFunction:[,"never","less","equal","less-equal","greater","not-equal","greater-equal","always"],CompilationInfoRequestStatus:{1:"success",2:"callback-cancelled"},CompositeAlphaMode:[,"opaque","premultiplied","unpremultiplied","inherit"],CullMode:[,"none","front","back"],ErrorFilter:{1:"validation",2:"out-of-memory",3:"internal"},FeatureLevel:[,"compatibility","core"],FeatureName:{1:"depth-clip-control",2:"depth32float-stencil8",3:"timestamp-query",4:"texture-compression-bc",5:"texture-compression-bc-sliced-3d",6:"texture-compression-etc2",7:"texture-compression-astc",8:"texture-compression-astc-sliced-3d",9:"indirect-first-instance",10:"shader-f16",11:"rg11b10ufloat-renderable",12:"bgra8unorm-storage",13:"float32-filterable",14:"float32-blendable",15:"clip-distances",16:"dual-source-blending",17:"subgroups",18:"core-features-and-limits",327692:"chromium-experimental-unorm16-texture-formats",327693:"chromium-experimental-snorm16-texture-formats",327732:"chromium-experimental-multi-draw-indirect"},FilterMode:[,"nearest","linear"],FrontFace:[,"ccw","cw"],IndexFormat:[,"uint16","uint32"],LoadOp:[,"load","clear"],MipmapFilterMode:[,"nearest","linear"],OptionalBool:["false","true"],PowerPreference:[,"low-power","high-performance"],PredefinedColorSpace:{1:"srgb",2:"display-p3"},PrimitiveTopology:[,"point-list","line-list","line-strip","triangle-list","triangle-strip"],QueryType:{1:"occlusion",2:"timestamp"},SamplerBindingType:["binding-not-used",,"filtering","non-filtering","comparison"],Status:{1:"success",2:"error"},StencilOperation:[,"keep","zero","replace","invert","increment-clamp","decrement-clamp","increment-wrap","decrement-wrap"],StorageTextureAccess:["binding-not-used",,"write-only","read-only","read-write"],StoreOp:[,"store","discard"],SurfaceGetCurrentTextureStatus:{1:"success-optimal",2:"success-suboptimal",3:"timeout",4:"outdated",5:"lost",6:"error"},TextureAspect:[,"all","stencil-only","depth-only"],TextureDimension:[,"1d","2d","3d"],TextureFormat:[,"r8unorm","r8snorm","r8uint","r8sint","r16uint","r16sint","r16float","rg8unorm","rg8snorm","rg8uint","rg8sint","r32float","r32uint","r32sint","rg16uint","rg16sint","rg16float","rgba8unorm","rgba8unorm-srgb","rgba8snorm","rgba8uint","rgba8sint","bgra8unorm","bgra8unorm-srgb","rgb10a2uint","rgb10a2unorm","rg11b10ufloat","rgb9e5ufloat","rg32float","rg32uint","rg32sint","rgba16uint","rgba16sint","rgba16float","rgba32float","rgba32uint","rgba32sint","stencil8","depth16unorm","depth24plus","depth24plus-stencil8","depth32float","depth32float-stencil8","bc1-rgba-unorm","bc1-rgba-unorm-srgb","bc2-rgba-unorm","bc2-rgba-unorm-srgb","bc3-rgba-unorm","bc3-rgba-unorm-srgb","bc4-r-unorm","bc4-r-snorm","bc5-rg-unorm","bc5-rg-snorm","bc6h-rgb-ufloat","bc6h-rgb-float","bc7-rgba-unorm","bc7-rgba-unorm-srgb","etc2-rgb8unorm","etc2-rgb8unorm-srgb","etc2-rgb8a1unorm","etc2-rgb8a1unorm-srgb","etc2-rgba8unorm","etc2-rgba8unorm-srgb","eac-r11unorm","eac-r11snorm","eac-rg11unorm","eac-rg11snorm","astc-4x4-unorm","astc-4x4-unorm-srgb","astc-5x4-unorm","astc-5x4-unorm-srgb","astc-5x5-unorm","astc-5x5-unorm-srgb","astc-6x5-unorm","astc-6x5-unorm-srgb","astc-6x6-unorm","astc-6x6-unorm-srgb","astc-8x5-unorm","astc-8x5-unorm-srgb","astc-8x6-unorm","astc-8x6-unorm-srgb","astc-8x8-unorm","astc-8x8-unorm-srgb","astc-10x5-unorm","astc-10x5-unorm-srgb","astc-10x6-unorm","astc-10x6-unorm-srgb","astc-10x8-unorm","astc-10x8-unorm-srgb","astc-10x10-unorm","astc-10x10-unorm-srgb","astc-12x10-unorm","astc-12x10-unorm-srgb","astc-12x12-unorm","astc-12x12-unorm-srgb"],TextureSampleType:["binding-not-used",,"float","unfilterable-float","depth","sint","uint"],TextureViewDimension:[,"1d","2d","2d-array","cube","cube-array","3d"],ToneMappingMode:{1:"standard",2:"extended"},VertexFormat:{1:"uint8",2:"uint8x2",3:"uint8x4",4:"sint8",5:"sint8x2",6:"sint8x4",7:"unorm8",8:"unorm8x2",9:"unorm8x4",10:"snorm8",11:"snorm8x2",12:"snorm8x4",13:"uint16",14:"uint16x2",15:"uint16x4",16:"sint16",17:"sint16x2",18:"sint16x4",19:"unorm16",20:"unorm16x2",21:"unorm16x4",22:"snorm16",23:"snorm16x2",24:"snorm16x4",25:"float16",26:"float16x2",27:"float16x4",28:"float32",29:"float32x2",30:"float32x3",31:"float32x4",32:"uint32",33:"uint32x2",34:"uint32x3",35:"uint32x4",36:"sint32",37:"sint32x2",38:"sint32x3",39:"sint32x4",40:"unorm10-10-10-2",41:"unorm8x4-bgra"},VertexStepMode:[,"vertex","instance"],WGSLLanguageFeatureName:{1:"readonly_and_readwrite_storage_textures",2:"packed_4x8_integer_dot_product",3:"unrestricted_pointer_parameters",4:"pointer_composite_access",5:"sized_binding_array"},FeatureNameString2Enum:{"depth-clip-control":"1","depth32float-stencil8":"2","timestamp-query":"3","texture-compression-bc":"4","texture-compression-bc-sliced-3d":"5","texture-compression-etc2":"6","texture-compression-astc":"7","texture-compression-astc-sliced-3d":"8","indirect-first-instance":"9","shader-f16":"10","rg11b10ufloat-renderable":"11","bgra8unorm-storage":"12","float32-filterable":"13","float32-blendable":"14","clip-distances":"15","dual-source-blending":"16",subgroups:"17","core-features-and-limits":"18","chromium-experimental-unorm16-texture-formats":"327692","chromium-experimental-snorm16-texture-formats":"327693","chromium-experimental-multi-draw-indirect":"327732"},WGSLLanguageFeatureNameString2Enum:{readonly_and_readwrite_storage_textures:"1",packed_4x8_integer_dot_product:"2",unrestricted_pointer_parameters:"3",pointer_composite_access:"4",sized_binding_array:"5"}};function _emwgpuAdapterRequestDevice(adapterPtr,futureId,deviceLostFutureId,devicePtr,queuePtr,descriptor){adapterPtr>>>=0;futureId=bigintToI53Checked(futureId);deviceLostFutureId=bigintToI53Checked(deviceLostFutureId);devicePtr>>>=0;queuePtr>>>=0;descriptor>>>=0;var adapter=WebGPU.getJsObject(adapterPtr);var desc={};if(descriptor){var requiredFeatureCount=HEAPU32[descriptor+12>>>2>>>0];if(requiredFeatureCount){var requiredFeaturesPtr=HEAPU32[descriptor+16>>>2>>>0];desc["requiredFeatures"]=Array.from(HEAPU32.subarray(requiredFeaturesPtr>>>2>>>0,requiredFeaturesPtr+requiredFeatureCount*4>>>2>>>0),feature=>WebGPU.FeatureName[feature])}var limitsPtr=HEAPU32[descriptor+20>>>2>>>0];if(limitsPtr){var requiredLimits={};function setLimitU32IfDefined(name,limitOffset,ignoreIfZero=false){var ptr=limitsPtr+limitOffset;var value=HEAPU32[ptr>>>2>>>0];if(value!=4294967295&&(!ignoreIfZero||value!=0)){requiredLimits[name]=value}}function setLimitU64IfDefined(name,limitOffset){var ptr=limitsPtr+limitOffset;var limitPart1=HEAPU32[ptr>>>2>>>0];var limitPart2=HEAPU32[ptr+4>>>2>>>0];if(limitPart1!=4294967295||limitPart2!=4294967295){requiredLimits[name]=HEAPU32[ptr+4>>>2>>>0]*4294967296+HEAPU32[ptr>>>2>>>0]}}setLimitU32IfDefined("maxTextureDimension1D",4);setLimitU32IfDefined("maxTextureDimension2D",8);setLimitU32IfDefined("maxTextureDimension3D",12);setLimitU32IfDefined("maxTextureArrayLayers",16);setLimitU32IfDefined("maxBindGroups",20);setLimitU32IfDefined("maxBindGroupsPlusVertexBuffers",24);setLimitU32IfDefined("maxDynamicUniformBuffersPerPipelineLayout",32);setLimitU32IfDefined("maxDynamicStorageBuffersPerPipelineLayout",36);setLimitU32IfDefined("maxSampledTexturesPerShaderStage",40);setLimitU32IfDefined("maxSamplersPerShaderStage",44);setLimitU32IfDefined("maxStorageBuffersPerShaderStage",48);setLimitU32IfDefined("maxStorageTexturesPerShaderStage",52);setLimitU32IfDefined("maxUniformBuffersPerShaderStage",56);setLimitU32IfDefined("minUniformBufferOffsetAlignment",80);setLimitU32IfDefined("minStorageBufferOffsetAlignment",84);setLimitU64IfDefined("maxUniformBufferBindingSize",64);setLimitU64IfDefined("maxStorageBufferBindingSize",72);setLimitU32IfDefined("maxVertexBuffers",88);setLimitU64IfDefined("maxBufferSize",96);setLimitU32IfDefined("maxVertexAttributes",104);setLimitU32IfDefined("maxVertexBufferArrayStride",108);setLimitU32IfDefined("maxInterStageShaderVariables",112);setLimitU32IfDefined("maxColorAttachments",116);setLimitU32IfDefined("maxColorAttachmentBytesPerSample",120);setLimitU32IfDefined("maxComputeWorkgroupStorageSize",124);setLimitU32IfDefined("maxComputeInvocationsPerWorkgroup",128);setLimitU32IfDefined("maxComputeWorkgroupSizeX",132);setLimitU32IfDefined("maxComputeWorkgroupSizeY",136);setLimitU32IfDefined("maxComputeWorkgroupSizeZ",140);setLimitU32IfDefined("maxComputeWorkgroupsPerDimension",144);setLimitU32IfDefined("maxImmediateSize",148,true);desc["requiredLimits"]=requiredLimits}var defaultQueuePtr=HEAPU32[descriptor+24>>>2>>>0];if(defaultQueuePtr){var defaultQueueDesc={label:WebGPU.makeStringFromOptionalStringView(defaultQueuePtr+4)};desc["defaultQueue"]=defaultQueueDesc}desc["label"]=WebGPU.makeStringFromOptionalStringView(descriptor+4)}WebGPU.Internals.futureInsert(futureId,adapter.requestDevice(desc).then(device=>{WebGPU.Internals.jsObjectInsert(queuePtr,device.queue);WebGPU.Internals.jsObjectInsert(devicePtr,device);if(deviceLostFutureId){WebGPU.Internals.futureInsert(deviceLostFutureId,device.lost.then(info=>{device.onuncapturederror=ev=>{};var sp=stackSave();var messagePtr=stringToUTF8OnStack(info.message);_emwgpuOnDeviceLostCompleted(deviceLostFutureId,WebGPU.Int_DeviceLostReason[info.reason],messagePtr);stackRestore(sp)}))}device.onuncapturederror=ev=>{var type=5;if(ev.error instanceof GPUValidationError)type=2;else if(ev.error instanceof GPUOutOfMemoryError)type=3;else if(ev.error instanceof GPUInternalError)type=4;var sp=stackSave();var messagePtr=stringToUTF8OnStack(ev.error.message);_emwgpuOnUncapturedError(devicePtr,type,messagePtr);stackRestore(sp)};_emwgpuOnRequestDeviceCompleted(futureId,1,devicePtr,0)},ex=>{var sp=stackSave();var messagePtr=stringToUTF8OnStack(ex.message);_emwgpuOnRequestDeviceCompleted(futureId,3,devicePtr,messagePtr);if(deviceLostFutureId){_emwgpuOnDeviceLostCompleted(deviceLostFutureId,4,messagePtr)}stackRestore(sp)}))}function _emwgpuBufferDestroy(bufferPtr){bufferPtr>>>=0;var buffer=WebGPU.getJsObject(bufferPtr);var onUnmap=WebGPU.Internals.bufferOnUnmaps[bufferPtr];if(onUnmap){for(var i=0;i<onUnmap.length;++i){onUnmap[i]()}delete WebGPU.Internals.bufferOnUnmaps[bufferPtr]}buffer.destroy()}function _emwgpuDelete(ptr){ptr>>>=0;delete WebGPU.Internals.jsObjects[ptr]}function _emwgpuDeviceCreateBuffer(devicePtr,descriptor,bufferPtr){devicePtr>>>=0;descriptor>>>=0;bufferPtr>>>=0;var mappedAtCreation=!!HEAPU32[descriptor+32>>>2>>>0];var desc={label:WebGPU.makeStringFromOptionalStringView(descriptor+4),usage:HEAPU32[descriptor+16>>>2>>>0],size:HEAPU32[descriptor+4+24>>>2>>>0]*4294967296+HEAPU32[descriptor+24>>>2>>>0],mappedAtCreation};var device=WebGPU.getJsObject(devicePtr);var buffer;try{buffer=device.createBuffer(desc)}catch(ex){return false}WebGPU.Internals.jsObjectInsert(bufferPtr,buffer);if(mappedAtCreation){WebGPU.Internals.bufferOnUnmaps[bufferPtr]=[]}return true}function _emwgpuDeviceCreateShaderModule(devicePtr,descriptor,shaderModulePtr){devicePtr>>>=0;descriptor>>>=0;shaderModulePtr>>>=0;var nextInChainPtr=HEAPU32[descriptor>>>2>>>0];var sType=HEAPU32[nextInChainPtr+4>>>2>>>0];var desc={label:WebGPU.makeStringFromOptionalStringView(descriptor+4),code:""};switch(sType){case 2:{desc["code"]=WebGPU.makeStringFromStringView(nextInChainPtr+8);break}}var device=WebGPU.getJsObject(devicePtr);WebGPU.Internals.jsObjectInsert(shaderModulePtr,device.createShaderModule(desc))}var _emwgpuDeviceDestroy=devicePtr=>{WebGPU.getJsObject(devicePtr).destroy()};var _emwgpuGetPreferredFormat=()=>{var format=navigator["gpu"]["getPreferredCanvasFormat"]();return WebGPU.Int_PreferredFormat[format]};function _emwgpuInstanceRequestAdapter(instancePtr,futureId,options,adapterPtr){instancePtr>>>=0;futureId=bigintToI53Checked(futureId);options>>>=0;adapterPtr>>>=0;var opts;if(options){var featureLevel=HEAPU32[options+4>>>2>>>0];opts={featureLevel:WebGPU.FeatureLevel[featureLevel],powerPreference:WebGPU.PowerPreference[HEAPU32[options+8>>>2>>>0]],forceFallbackAdapter:!!HEAPU32[options+12>>>2>>>0]};var nextInChainPtr=HEAPU32[options>>>2>>>0];if(nextInChainPtr!==0){var sType=HEAPU32[nextInChainPtr+4>>>2>>>0];var webxrOptions=nextInChainPtr;opts.xrCompatible=!!HEAPU32[webxrOptions+8>>>2>>>0]}}if(!("gpu"in navigator)){var sp=stackSave();var messagePtr=stringToUTF8OnStack("WebGPU not available on this browser (navigator.gpu is not available)");_emwgpuOnRequestAdapterCompleted(futureId,3,adapterPtr,messagePtr);stackRestore(sp);return}WebGPU.Internals.futureInsert(futureId,navigator["gpu"]["requestAdapter"](opts).then(adapter=>{if(adapter){WebGPU.Internals.jsObjectInsert(adapterPtr,adapter);_emwgpuOnRequestAdapterCompleted(futureId,1,adapterPtr,0)}else{var sp=stackSave();var messagePtr=stringToUTF8OnStack("WebGPU not available on this browser (requestAdapter returned null)");_emwgpuOnRequestAdapterCompleted(futureId,3,adapterPtr,messagePtr);stackRestore(sp)}},ex=>{var sp=stackSave();var messagePtr=stringToUTF8OnStack(ex.message);_emwgpuOnRequestAdapterCompleted(futureId,4,adapterPtr,messagePtr);stackRestore(sp)}))}var ENV={};var getExecutableName=()=>thisProgram||"./this.program";var getEnvStrings=()=>{if(!getEnvStrings.strings){var lang=(typeof navigator=="object"&&navigator.language||"C").replace("-","_")+".UTF-8";var env={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:lang,_:getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(`${x}=${env[x]}`)}getEnvStrings.strings=strings}return getEnvStrings.strings};function _environ_get(__environ,environ_buf){__environ>>>=0;environ_buf>>>=0;var bufSize=0;var envp=0;for(var string of getEnvStrings()){var ptr=environ_buf+bufSize;HEAPU32[__environ+envp>>>2>>>0]=ptr;bufSize+=stringToUTF8(string,ptr,Infinity)+1;envp+=4}return 0}function _environ_sizes_get(penviron_count,penviron_buf_size){penviron_count>>>=0;penviron_buf_size>>>=0;var strings=getEnvStrings();HEAPU32[penviron_count>>>2>>>0]=strings.length;var bufSize=0;for(var string of strings){bufSize+=lengthBytesUTF8(string)+1}HEAPU32[penviron_buf_size>>>2>>>0]=bufSize;return 0}function _fd_close(fd){try{var stream=SYSCALLS.getStreamFromFD(fd);FS.close(stream);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var doReadv=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>>2>>>0];var len=HEAPU32[iov+4>>>2>>>0];iov+=8;var curr=FS.read(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(curr<len)break;if(typeof offset!="undefined"){offset+=curr}}return ret};function _fd_read(fd,iov,iovcnt,pnum){iov>>>=0;iovcnt>>>=0;pnum>>>=0;try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doReadv(stream,iov,iovcnt);HEAPU32[pnum>>>2>>>0]=num;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}function _fd_seek(fd,offset,whence,newOffset){offset=bigintToI53Checked(offset);newOffset>>>=0;try{if(isNaN(offset))return 61;var stream=SYSCALLS.getStreamFromFD(fd);FS.llseek(stream,offset,whence);HEAP64[newOffset>>>3>>>0]=BigInt(stream.position);if(stream.getdents&&offset===0&&whence===0)stream.getdents=null;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var doWritev=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>>2>>>0];var len=HEAPU32[iov+4>>>2>>>0];iov+=8;var curr=FS.write(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(curr<len){break}if(typeof offset!="undefined"){offset+=curr}}return ret};function _fd_write(fd,iov,iovcnt,pnum){iov>>>=0;iovcnt>>>=0;pnum>>>=0;try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doWritev(stream,iov,iovcnt);HEAPU32[pnum>>>2>>>0]=num;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var GLctx;var webgl_enable_ANGLE_instanced_arrays=ctx=>{var ext=ctx.getExtension("ANGLE_instanced_arrays");if(ext){ctx["vertexAttribDivisor"]=(index,divisor)=>ext["vertexAttribDivisorANGLE"](index,divisor);ctx["drawArraysInstanced"]=(mode,first,count,primcount)=>ext["drawArraysInstancedANGLE"](mode,first,count,primcount);ctx["drawElementsInstanced"]=(mode,count,type,indices,primcount)=>ext["drawElementsInstancedANGLE"](mode,count,type,indices,primcount);return 1}};var webgl_enable_OES_vertex_array_object=ctx=>{var ext=ctx.getExtension("OES_vertex_array_object");if(ext){ctx["createVertexArray"]=()=>ext["createVertexArrayOES"]();ctx["deleteVertexArray"]=vao=>ext["deleteVertexArrayOES"](vao);ctx["bindVertexArray"]=vao=>ext["bindVertexArrayOES"](vao);ctx["isVertexArray"]=vao=>ext["isVertexArrayOES"](vao);return 1}};var webgl_enable_WEBGL_draw_buffers=ctx=>{var ext=ctx.getExtension("WEBGL_draw_buffers");if(ext){ctx["drawBuffers"]=(n,bufs)=>ext["drawBuffersWEBGL"](n,bufs);return 1}};var webgl_enable_EXT_polygon_offset_clamp=ctx=>!!(ctx.extPolygonOffsetClamp=ctx.getExtension("EXT_polygon_offset_clamp"));var webgl_enable_EXT_clip_control=ctx=>!!(ctx.extClipControl=ctx.getExtension("EXT_clip_control"));var webgl_enable_WEBGL_polygon_mode=ctx=>!!(ctx.webglPolygonMode=ctx.getExtension("WEBGL_polygon_mode"));var webgl_enable_WEBGL_multi_draw=ctx=>!!(ctx.multiDrawWebgl=ctx.getExtension("WEBGL_multi_draw"));var getEmscriptenSupportedExtensions=ctx=>{var supportedExtensions=["ANGLE_instanced_arrays","EXT_blend_minmax","EXT_disjoint_timer_query","EXT_frag_depth","EXT_shader_texture_lod","EXT_sRGB","OES_element_index_uint","OES_fbo_render_mipmap","OES_standard_derivatives","OES_texture_float","OES_texture_half_float","OES_texture_half_float_linear","OES_vertex_array_object","WEBGL_color_buffer_float","WEBGL_depth_texture","WEBGL_draw_buffers","EXT_clip_control","EXT_color_buffer_half_float","EXT_depth_clamp","EXT_float_blend","EXT_polygon_offset_clamp","EXT_texture_compression_bptc","EXT_texture_compression_rgtc","EXT_texture_filter_anisotropic","KHR_parallel_shader_compile","OES_texture_float_linear","WEBGL_blend_func_extended","WEBGL_compressed_texture_astc","WEBGL_compressed_texture_etc","WEBGL_compressed_texture_etc1","WEBGL_compressed_texture_s3tc","WEBGL_compressed_texture_s3tc_srgb","WEBGL_debug_renderer_info","WEBGL_debug_shaders","WEBGL_lose_context","WEBGL_multi_draw","WEBGL_polygon_mode"];return(ctx.getSupportedExtensions()||[]).filter(ext=>supportedExtensions.includes(ext))};var GL={counter:1,buffers:[],programs:[],framebuffers:[],renderbuffers:[],textures:[],shaders:[],vaos:[],contexts:[],offscreenCanvases:{},queries:[],stringCache:{},unpackAlignment:4,unpackRowLength:0,recordError:errorCode=>{if(!GL.lastError){GL.lastError=errorCode}},getNewId:table=>{var ret=GL.counter++;for(var i=table.length;i<ret;i++){table[i]=null}return ret},genObject:(n,buffers,createFunction,objectTable)=>{for(var i=0;i<n;i++){var buffer=GLctx[createFunction]();var id=buffer&&GL.getNewId(objectTable);if(buffer){buffer.name=id;objectTable[id]=buffer}else{GL.recordError(1282)}HEAP32[buffers+i*4>>>2>>>0]=id}},getSource:(shader,count,string,length)=>{var source="";for(var i=0;i<count;++i){var len=length?HEAPU32[length+i*4>>>2>>>0]:undefined;source+=UTF8ToString(HEAPU32[string+i*4>>>2>>>0],len)}return source},createContext:(canvas,webGLContextAttributes)=>{if(!canvas.getContextSafariWebGL2Fixed){canvas.getContextSafariWebGL2Fixed=canvas.getContext;function fixedGetContext(ver,attrs){var gl=canvas.getContextSafariWebGL2Fixed(ver,attrs);return ver=="webgl"==gl instanceof WebGLRenderingContext?gl:null}canvas.getContext=fixedGetContext}var ctx=canvas.getContext("webgl",webGLContextAttributes);if(!ctx)return 0;var handle=GL.registerContext(ctx,webGLContextAttributes);return handle},registerContext:(ctx,webGLContextAttributes)=>{var handle=GL.getNewId(GL.contexts);var context={handle,attributes:webGLContextAttributes,version:webGLContextAttributes.majorVersion,GLctx:ctx};if(ctx.canvas)ctx.canvas.GLctxObject=context;GL.contexts[handle]=context;if(typeof webGLContextAttributes.enableExtensionsByDefault=="undefined"||webGLContextAttributes.enableExtensionsByDefault){GL.initExtensions(context)}return handle},makeContextCurrent:contextHandle=>{GL.currentContext=GL.contexts[contextHandle];Module["ctx"]=GLctx=GL.currentContext?.GLctx;return!(contextHandle&&!GLctx)},getContext:contextHandle=>GL.contexts[contextHandle],deleteContext:contextHandle=>{if(GL.currentContext===GL.contexts[contextHandle]){GL.currentContext=null}if(typeof JSEvents=="object"){JSEvents.removeAllHandlersOnTarget(GL.contexts[contextHandle].GLctx.canvas)}if(GL.contexts[contextHandle]?.GLctx.canvas){GL.contexts[contextHandle].GLctx.canvas.GLctxObject=undefined}GL.contexts[contextHandle]=null},initExtensions:context=>{context||=GL.currentContext;if(context.initExtensionsDone)return;context.initExtensionsDone=true;var GLctx=context.GLctx;webgl_enable_WEBGL_multi_draw(GLctx);webgl_enable_EXT_polygon_offset_clamp(GLctx);webgl_enable_EXT_clip_control(GLctx);webgl_enable_WEBGL_polygon_mode(GLctx);webgl_enable_ANGLE_instanced_arrays(GLctx);webgl_enable_OES_vertex_array_object(GLctx);webgl_enable_WEBGL_draw_buffers(GLctx);{GLctx.disjointTimerQueryExt=GLctx.getExtension("EXT_disjoint_timer_query")}getEmscriptenSupportedExtensions(GLctx).forEach(ext=>{if(!ext.includes("lose_context")&&!ext.includes("debug")){GLctx.getExtension(ext)}})}};var warnOnce=text=>{warnOnce.shown||={};if(!warnOnce.shown[text]){warnOnce.shown[text]=1;if(ENVIRONMENT_IS_NODE)text="warning: "+text;err(text)}};var Browser={useWebGL:false,isFullscreen:false,pointerLock:false,moduleContextCreatedCallbacks:[],workers:[],preloadedImages:{},preloadedAudios:{},getCanvas:()=>Module["canvas"],init(){if(Browser.initted)return;Browser.initted=true;var imagePlugin={};imagePlugin["canHandle"]=function imagePlugin_canHandle(name){return!Module["noImageDecoding"]&&/\.(jpg|jpeg|png|bmp|webp)$/i.test(name)};imagePlugin["handle"]=function imagePlugin_handle(byteArray,name,onload,onerror){var b=new Blob([byteArray],{type:Browser.getMimetype(name)});if(b.size!==byteArray.length){b=new Blob([new Uint8Array(byteArray).buffer],{type:Browser.getMimetype(name)})}var url=URL.createObjectURL(b);var img=new Image;img.onload=()=>{var canvas=document.createElement("canvas");canvas.width=img.width;canvas.height=img.height;var ctx=canvas.getContext("2d");ctx.drawImage(img,0,0);Browser.preloadedImages[name]=canvas;URL.revokeObjectURL(url);onload?.(byteArray)};img.onerror=event=>{err(`Image ${url} could not be decoded`);onerror?.()};img.src=url};preloadPlugins.push(imagePlugin);var audioPlugin={};audioPlugin["canHandle"]=function audioPlugin_canHandle(name){return!Module["noAudioDecoding"]&&name.slice(-4)in{".ogg":1,".wav":1,".mp3":1}};audioPlugin["handle"]=function audioPlugin_handle(byteArray,name,onload,onerror){var done=false;function finish(audio){if(done)return;done=true;Browser.preloadedAudios[name]=audio;onload?.(byteArray)}var b=new Blob([byteArray],{type:Browser.getMimetype(name)});var url=URL.createObjectURL(b);var audio=new Audio;audio.addEventListener("canplaythrough",()=>finish(audio),false);audio.onerror=function audio_onerror(event){if(done)return;err(`warning: browser could not fully decode audio ${name}, trying slower base64 approach`);function encode64(data){var BASE="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";var PAD="=";var ret="";var leftchar=0;var leftbits=0;for(var i=0;i<data.length;i++){leftchar=leftchar<<8|data[i];leftbits+=8;while(leftbits>=6){var curr=leftchar>>leftbits-6&63;leftbits-=6;ret+=BASE[curr]}}if(leftbits==2){ret+=BASE[(leftchar&3)<<4];ret+=PAD+PAD}else if(leftbits==4){ret+=BASE[(leftchar&15)<<2];ret+=PAD}return ret}audio.src="data:audio/x-"+name.slice(-3)+";base64,"+encode64(byteArray);finish(audio)};audio.src=url;safeSetTimeout(()=>{finish(audio)},1e4)};preloadPlugins.push(audioPlugin);function pointerLockChange(){var canvas=Browser.getCanvas();Browser.pointerLock=document.pointerLockElement===canvas}var canvas=Browser.getCanvas();if(canvas){document.addEventListener("pointerlockchange",pointerLockChange,false);if(Module["elementPointerLock"]){canvas.addEventListener("click",ev=>{if(!Browser.pointerLock&&Browser.getCanvas().requestPointerLock){Browser.getCanvas().requestPointerLock();ev.preventDefault()}},false)}}},createContext(canvas,useWebGL,setInModule,webGLContextAttributes){if(useWebGL&&Module["ctx"]&&canvas==Browser.getCanvas())return Module["ctx"];var ctx;var contextHandle;if(useWebGL){var contextAttributes={antialias:false,alpha:false,majorVersion:1};if(webGLContextAttributes){for(var attribute in webGLContextAttributes){contextAttributes[attribute]=webGLContextAttributes[attribute]}}if(typeof GL!="undefined"){contextHandle=GL.createContext(canvas,contextAttributes);if(contextHandle){ctx=GL.getContext(contextHandle).GLctx}}}else{ctx=canvas.getContext("2d")}if(!ctx)return null;if(setInModule){Module["ctx"]=ctx;if(useWebGL)GL.makeContextCurrent(contextHandle);Browser.useWebGL=useWebGL;Browser.moduleContextCreatedCallbacks.forEach(callback=>callback());Browser.init()}return ctx},fullscreenHandlersInstalled:false,lockPointer:undefined,resizeCanvas:undefined,requestFullscreen(lockPointer,resizeCanvas){Browser.lockPointer=lockPointer;Browser.resizeCanvas=resizeCanvas;if(typeof Browser.lockPointer=="undefined")Browser.lockPointer=true;if(typeof Browser.resizeCanvas=="undefined")Browser.resizeCanvas=false;var canvas=Browser.getCanvas();function fullscreenChange(){Browser.isFullscreen=false;var canvasContainer=canvas.parentNode;if((document["fullscreenElement"]||document["mozFullScreenElement"]||document["msFullscreenElement"]||document["webkitFullscreenElement"]||document["webkitCurrentFullScreenElement"])===canvasContainer){canvas.exitFullscreen=Browser.exitFullscreen;if(Browser.lockPointer)canvas.requestPointerLock();Browser.isFullscreen=true;if(Browser.resizeCanvas){Browser.setFullscreenCanvasSize()}else{Browser.updateCanvasDimensions(canvas)}}else{canvasContainer.parentNode.insertBefore(canvas,canvasContainer);canvasContainer.parentNode.removeChild(canvasContainer);if(Browser.resizeCanvas){Browser.setWindowedCanvasSize()}else{Browser.updateCanvasDimensions(canvas)}}Module["onFullScreen"]?.(Browser.isFullscreen);Module["onFullscreen"]?.(Browser.isFullscreen)}if(!Browser.fullscreenHandlersInstalled){Browser.fullscreenHandlersInstalled=true;document.addEventListener("fullscreenchange",fullscreenChange,false);document.addEventListener("mozfullscreenchange",fullscreenChange,false);document.addEventListener("webkitfullscreenchange",fullscreenChange,false);document.addEventListener("MSFullscreenChange",fullscreenChange,false)}var canvasContainer=document.createElement("div");canvas.parentNode.insertBefore(canvasContainer,canvas);canvasContainer.appendChild(canvas);canvasContainer.requestFullscreen=canvasContainer["requestFullscreen"]||canvasContainer["mozRequestFullScreen"]||canvasContainer["msRequestFullscreen"]||(canvasContainer["webkitRequestFullscreen"]?()=>canvasContainer["webkitRequestFullscreen"](Element["ALLOW_KEYBOARD_INPUT"]):null)||(canvasContainer["webkitRequestFullScreen"]?()=>canvasContainer["webkitRequestFullScreen"](Element["ALLOW_KEYBOARD_INPUT"]):null);canvasContainer.requestFullscreen()},exitFullscreen(){if(!Browser.isFullscreen){return false}var CFS=document["exitFullscreen"]||document["cancelFullScreen"]||document["mozCancelFullScreen"]||document["msExitFullscreen"]||document["webkitCancelFullScreen"]||(()=>{});CFS.apply(document,[]);return true},safeSetTimeout(func,timeout){return safeSetTimeout(func,timeout)},getMimetype(name){return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",bmp:"image/bmp",ogg:"audio/ogg",wav:"audio/wav",mp3:"audio/mpeg"}[name.slice(name.lastIndexOf(".")+1)]},getUserMedia(func){window.getUserMedia||=navigator["getUserMedia"]||navigator["mozGetUserMedia"];window.getUserMedia(func)},getMovementX(event){return event["movementX"]||event["mozMovementX"]||event["webkitMovementX"]||0},getMovementY(event){return event["movementY"]||event["mozMovementY"]||event["webkitMovementY"]||0},getMouseWheelDelta(event){var delta=0;switch(event.type){case"DOMMouseScroll":delta=event.detail/3;break;case"mousewheel":delta=event.wheelDelta/120;break;case"wheel":delta=event.deltaY;switch(event.deltaMode){case 0:delta/=100;break;case 1:delta/=3;break;case 2:delta*=80;break;default:throw"unrecognized mouse wheel delta mode: "+event.deltaMode}break;default:throw"unrecognized mouse wheel event: "+event.type}return delta},mouseX:0,mouseY:0,mouseMovementX:0,mouseMovementY:0,touches:{},lastTouches:{},calculateMouseCoords(pageX,pageY){var canvas=Browser.getCanvas();var rect=canvas.getBoundingClientRect();var scrollX=typeof window.scrollX!="undefined"?window.scrollX:window.pageXOffset;var scrollY=typeof window.scrollY!="undefined"?window.scrollY:window.pageYOffset;var adjustedX=pageX-(scrollX+rect.left);var adjustedY=pageY-(scrollY+rect.top);adjustedX=adjustedX*(canvas.width/rect.width);adjustedY=adjustedY*(canvas.height/rect.height);return{x:adjustedX,y:adjustedY}},setMouseCoords(pageX,pageY){const{x,y}=Browser.calculateMouseCoords(pageX,pageY);Browser.mouseMovementX=x-Browser.mouseX;Browser.mouseMovementY=y-Browser.mouseY;Browser.mouseX=x;Browser.mouseY=y},calculateMouseEvent(event){if(Browser.pointerLock){if(event.type!="mousemove"&&"mozMovementX"in event){Browser.mouseMovementX=Browser.mouseMovementY=0}else{Browser.mouseMovementX=Browser.getMovementX(event);Browser.mouseMovementY=Browser.getMovementY(event)}Browser.mouseX+=Browser.mouseMovementX;Browser.mouseY+=Browser.mouseMovementY}else{if(event.type==="touchstart"||event.type==="touchend"||event.type==="touchmove"){var touch=event.touch;if(touch===undefined){return}var coords=Browser.calculateMouseCoords(touch.pageX,touch.pageY);if(event.type==="touchstart"){Browser.lastTouches[touch.identifier]=coords;Browser.touches[touch.identifier]=coords}else if(event.type==="touchend"||event.type==="touchmove"){var last=Browser.touches[touch.identifier];last||=coords;Browser.lastTouches[touch.identifier]=last;Browser.touches[touch.identifier]=coords}return}Browser.setMouseCoords(event.pageX,event.pageY)}},resizeListeners:[],updateResizeListeners(){var canvas=Browser.getCanvas();Browser.resizeListeners.forEach(listener=>listener(canvas.width,canvas.height))},setCanvasSize(width,height,noUpdates){var canvas=Browser.getCanvas();Browser.updateCanvasDimensions(canvas,width,height);if(!noUpdates)Browser.updateResizeListeners()},windowedWidth:0,windowedHeight:0,setFullscreenCanvasSize(){if(typeof SDL!="undefined"){var flags=HEAPU32[SDL.screen>>>2>>>0];flags=flags|8388608;HEAP32[SDL.screen>>>2>>>0]=flags}Browser.updateCanvasDimensions(Browser.getCanvas());Browser.updateResizeListeners()},setWindowedCanvasSize(){if(typeof SDL!="undefined"){var flags=HEAPU32[SDL.screen>>>2>>>0];flags=flags&~8388608;HEAP32[SDL.screen>>>2>>>0]=flags}Browser.updateCanvasDimensions(Browser.getCanvas());Browser.updateResizeListeners()},updateCanvasDimensions(canvas,wNative,hNative){if(wNative&&hNative){canvas.widthNative=wNative;canvas.heightNative=hNative}else{wNative=canvas.widthNative;hNative=canvas.heightNative}var w=wNative;var h=hNative;if(Module["forcedAspectRatio"]>0){if(w/h<Module["forcedAspectRatio"]){w=Math.round(h*Module["forcedAspectRatio"])}else{h=Math.round(w/Module["forcedAspectRatio"])}}if((document["fullscreenElement"]||document["mozFullScreenElement"]||document["msFullscreenElement"]||document["webkitFullscreenElement"]||document["webkitCurrentFullScreenElement"])===canvas.parentNode&&typeof screen!="undefined"){var factor=Math.min(screen.width/w,screen.height/h);w=Math.round(w*factor);h=Math.round(h*factor)}if(Browser.resizeCanvas){if(canvas.width!=w)canvas.width=w;if(canvas.height!=h)canvas.height=h;if(typeof canvas.style!="undefined"){canvas.style.removeProperty("width");canvas.style.removeProperty("height")}}else{if(canvas.width!=wNative)canvas.width=wNative;if(canvas.height!=hNative)canvas.height=hNative;if(typeof canvas.style!="undefined"){if(w!=wNative||h!=hNative){canvas.style.setProperty("width",w+"px","important");canvas.style.setProperty("height",h+"px","important")}else{canvas.style.removeProperty("width");canvas.style.removeProperty("height")}}}}};function GLFW_Window(id,width,height,framebufferWidth,framebufferHeight,title,monitor,share){this.id=id;this.x=0;this.y=0;this.fullscreen=false;this.storedX=0;this.storedY=0;this.width=width;this.height=height;this.framebufferWidth=framebufferWidth;this.framebufferHeight=framebufferHeight;this.storedWidth=width;this.storedHeight=height;this.title=title;this.monitor=monitor;this.share=share;this.attributes={...GLFW.hints};this.inputModes={208897:212993,208898:0,208899:0};this.buttons=0;this.keys=new Array;this.domKeys=new Array;this.shouldClose=0;this.title=null;this.windowPosFunc=0;this.windowSizeFunc=0;this.windowCloseFunc=0;this.windowRefreshFunc=0;this.windowFocusFunc=0;this.windowIconifyFunc=0;this.windowMaximizeFunc=0;this.framebufferSizeFunc=0;this.windowContentScaleFunc=0;this.mouseButtonFunc=0;this.cursorPosFunc=0;this.cursorEnterFunc=0;this.scrollFunc=0;this.dropFunc=0;this.keyFunc=0;this.charFunc=0;this.userptr=0}function _emscripten_set_window_title(title){title>>>=0;return document.title=UTF8ToString(title)}var GLFW={WindowFromId:id=>{if(id<=0||!GLFW.windows)return null;return GLFW.windows[id-1]},joystickFunc:0,errorFunc:0,monitorFunc:0,active:null,scale:null,windows:null,monitors:null,monitorString:null,versionString:null,initialTime:null,extensions:null,devicePixelRatioMQL:null,hints:null,primaryTouchId:null,defaultHints:{131073:0,131074:0,131075:1,131076:1,131077:1,131082:0,135169:8,135170:8,135171:8,135172:8,135173:24,135174:8,135175:0,135176:0,135177:0,135178:0,135179:0,135180:0,135181:0,135182:0,135183:0,139265:196609,139266:1,139267:0,139268:0,139269:0,139270:0,139271:0,139272:0,139276:0},DOMToGLFWKeyCode:keycode=>{switch(keycode){case 32:return 32;case 222:return 39;case 188:return 44;case 173:return 45;case 189:return 45;case 190:return 46;case 191:return 47;case 48:return 48;case 49:return 49;case 50:return 50;case 51:return 51;case 52:return 52;case 53:return 53;case 54:return 54;case 55:return 55;case 56:return 56;case 57:return 57;case 59:return 59;case 61:return 61;case 187:return 61;case 65:return 65;case 66:return 66;case 67:return 67;case 68:return 68;case 69:return 69;case 70:return 70;case 71:return 71;case 72:return 72;case 73:return 73;case 74:return 74;case 75:return 75;case 76:return 76;case 77:return 77;case 78:return 78;case 79:return 79;case 80:return 80;case 81:return 81;case 82:return 82;case 83:return 83;case 84:return 84;case 85:return 85;case 86:return 86;case 87:return 87;case 88:return 88;case 89:return 89;case 90:return 90;case 219:return 91;case 220:return 92;case 221:return 93;case 192:return 96;case 27:return 256;case 13:return 257;case 9:return 258;case 8:return 259;case 45:return 260;case 46:return 261;case 39:return 262;case 37:return 263;case 40:return 264;case 38:return 265;case 33:return 266;case 34:return 267;case 36:return 268;case 35:return 269;case 20:return 280;case 145:return 281;case 144:return 282;case 44:return 283;case 19:return 284;case 112:return 290;case 113:return 291;case 114:return 292;case 115:return 293;case 116:return 294;case 117:return 295;case 118:return 296;case 119:return 297;case 120:return 298;case 121:return 299;case 122:return 300;case 123:return 301;case 124:return 302;case 125:return 303;case 126:return 304;case 127:return 305;case 128:return 306;case 129:return 307;case 130:return 308;case 131:return 309;case 132:return 310;case 133:return 311;case 134:return 312;case 135:return 313;case 136:return 314;case 96:return 320;case 97:return 321;case 98:return 322;case 99:return 323;case 100:return 324;case 101:return 325;case 102:return 326;case 103:return 327;case 104:return 328;case 105:return 329;case 110:return 330;case 111:return 331;case 106:return 332;case 109:return 333;case 107:return 334;case 16:return 340;case 17:return 341;case 18:return 342;case 91:return 343;case 224:return 343;case 93:return 348;default:return-1}},getModBits:win=>{var mod=0;if(win.keys[340])mod|=1;if(win.keys[341])mod|=2;if(win.keys[342])mod|=4;if(win.keys[343]||win.keys[348])mod|=8;return mod},onKeyPress:event=>{if(!GLFW.active||!GLFW.active.charFunc)return;if(event.ctrlKey||event.metaKey)return;var charCode=event.charCode;if(charCode==0||charCode>=0&&charCode<=31)return;((a1,a2)=>dynCall_vii(GLFW.active.charFunc,a1,a2))(GLFW.active.id,charCode)},onKeyChanged:(keyCode,status)=>{if(!GLFW.active)return;var key=GLFW.DOMToGLFWKeyCode(keyCode);if(key==-1)return;var repeat=status&&GLFW.active.keys[key];GLFW.active.keys[key]=status;GLFW.active.domKeys[keyCode]=status;if(GLFW.active.keyFunc){if(repeat)status=2;((a1,a2,a3,a4,a5)=>dynCall_viiiii(GLFW.active.keyFunc,a1,a2,a3,a4,a5))(GLFW.active.id,key,keyCode,status,GLFW.getModBits(GLFW.active))}},onGamepadConnected:event=>{GLFW.refreshJoysticks()},onGamepadDisconnected:event=>{GLFW.refreshJoysticks()},onKeydown:event=>{GLFW.onKeyChanged(event.keyCode,1);if(event.key=="Backspace"||event.key=="Tab"){event.preventDefault()}},onKeyup:event=>{GLFW.onKeyChanged(event.keyCode,0)},onBlur:event=>{if(!GLFW.active)return;for(var i=0;i<GLFW.active.domKeys.length;++i){if(GLFW.active.domKeys[i]){GLFW.onKeyChanged(i,0)}}},onMousemove:event=>{if(!GLFW.active)return;if(event.type==="touchmove"){event.preventDefault();let primaryChanged=false;for(let i of event.changedTouches){if(GLFW.primaryTouchId===i.identifier){Browser.setMouseCoords(i.pageX,i.pageY);primaryChanged=true;break}}if(!primaryChanged){return}}else{Browser.calculateMouseEvent(event)}if(event.target!=Browser.getCanvas()||!GLFW.active.cursorPosFunc)return;if(GLFW.active.cursorPosFunc){((a1,a2,a3)=>dynCall_vidd(GLFW.active.cursorPosFunc,a1,a2,a3))(GLFW.active.id,Browser.mouseX,Browser.mouseY)}},DOMToGLFWMouseButton:event=>{var eventButton=event["button"];if(eventButton>0){if(eventButton==1){eventButton=2}else{eventButton=1}}return eventButton},onMouseenter:event=>{if(!GLFW.active)return;if(event.target!=Browser.getCanvas())return;if(GLFW.active.cursorEnterFunc){((a1,a2)=>dynCall_vii(GLFW.active.cursorEnterFunc,a1,a2))(GLFW.active.id,1)}},onMouseleave:event=>{if(!GLFW.active)return;if(event.target!=Browser.getCanvas())return;if(GLFW.active.cursorEnterFunc){((a1,a2)=>dynCall_vii(GLFW.active.cursorEnterFunc,a1,a2))(GLFW.active.id,0)}},onMouseButtonChanged:(event,status)=>{if(!GLFW.active)return;if(event.target!=Browser.getCanvas())return;const isTouchType=event.type==="touchstart"||event.type==="touchend"||event.type==="touchcancel";let eventButton=0;if(isTouchType){event.preventDefault();let primaryChanged=false;if(GLFW.primaryTouchId===null&&event.type==="touchstart"&&event.targetTouches.length>0){const chosenTouch=event.targetTouches[0];GLFW.primaryTouchId=chosenTouch.identifier;Browser.setMouseCoords(chosenTouch.pageX,chosenTouch.pageY);primaryChanged=true}else if(event.type==="touchend"||event.type==="touchcancel"){for(let i of event.changedTouches){if(GLFW.primaryTouchId===i.identifier){GLFW.primaryTouchId=null;primaryChanged=true;break}}}if(!primaryChanged){return}}else{Browser.calculateMouseEvent(event);eventButton=GLFW.DOMToGLFWMouseButton(event)}if(status==1){GLFW.active.buttons|=1<<eventButton;try{event.target.setCapture()}catch(e){}}else{GLFW.active.buttons&=~(1<<eventButton)}if(GLFW.active.mouseButtonFunc){((a1,a2,a3,a4)=>dynCall_viiii(GLFW.active.mouseButtonFunc,a1,a2,a3,a4))(GLFW.active.id,eventButton,status,GLFW.getModBits(GLFW.active))}},onMouseButtonDown:event=>{if(!GLFW.active)return;GLFW.onMouseButtonChanged(event,1)},onMouseButtonUp:event=>{if(!GLFW.active)return;GLFW.onMouseButtonChanged(event,0)},onMouseWheel:event=>{var delta=-Browser.getMouseWheelDelta(event);delta=delta==0?0:delta>0?Math.max(delta,1):Math.min(delta,-1);GLFW.wheelPos+=delta;if(!GLFW.active||!GLFW.active.scrollFunc||event.target!=Browser.getCanvas())return;var sx=0;var sy=delta;if(event.type=="mousewheel"){sx=event.wheelDeltaX}else{sx=event.deltaX}((a1,a2,a3)=>dynCall_vidd(GLFW.active.scrollFunc,a1,a2,a3))(GLFW.active.id,sx,sy);event.preventDefault()},onCanvasResize:(width,height,framebufferWidth,framebufferHeight)=>{if(!GLFW.active)return;var resizeNeeded=false;if(document["fullscreen"]||document["fullScreen"]||document["mozFullScreen"]||document["webkitIsFullScreen"]){if(!GLFW.active.fullscreen){resizeNeeded=width!=screen.width||height!=screen.height;GLFW.active.storedX=GLFW.active.x;GLFW.active.storedY=GLFW.active.y;GLFW.active.storedWidth=GLFW.active.width;GLFW.active.storedHeight=GLFW.active.height;GLFW.active.x=GLFW.active.y=0;GLFW.active.width=screen.width;GLFW.active.height=screen.height;GLFW.active.fullscreen=true}}else if(GLFW.active.fullscreen==true){resizeNeeded=width!=GLFW.active.storedWidth||height!=GLFW.active.storedHeight;GLFW.active.x=GLFW.active.storedX;GLFW.active.y=GLFW.active.storedY;GLFW.active.width=GLFW.active.storedWidth;GLFW.active.height=GLFW.active.storedHeight;GLFW.active.fullscreen=false}if(resizeNeeded){Browser.setCanvasSize(GLFW.active.width,GLFW.active.height)}else if(GLFW.active.width!=width||GLFW.active.height!=height||GLFW.active.framebufferWidth!=framebufferWidth||GLFW.active.framebufferHeight!=framebufferHeight){GLFW.active.width=width;GLFW.active.height=height;GLFW.active.framebufferWidth=framebufferWidth;GLFW.active.framebufferHeight=framebufferHeight;GLFW.onWindowSizeChanged();GLFW.onFramebufferSizeChanged()}},onWindowSizeChanged:()=>{if(!GLFW.active)return;if(GLFW.active.windowSizeFunc){((a1,a2,a3)=>dynCall_viii(GLFW.active.windowSizeFunc,a1,a2,a3))(GLFW.active.id,GLFW.active.width,GLFW.active.height)}},onFramebufferSizeChanged:()=>{if(!GLFW.active)return;if(GLFW.active.framebufferSizeFunc){((a1,a2,a3)=>dynCall_viii(GLFW.active.framebufferSizeFunc,a1,a2,a3))(GLFW.active.id,GLFW.active.framebufferWidth,GLFW.active.framebufferHeight)}},onWindowContentScaleChanged:scale=>{GLFW.scale=scale;if(!GLFW.active)return;if(GLFW.active.windowContentScaleFunc){((a1,a2,a3)=>{})(GLFW.active.id,GLFW.scale,GLFW.scale)}},getTime:()=>_emscripten_get_now()/1e3,setWindowTitle:(winid,title)=>{var win=GLFW.WindowFromId(winid);if(!win)return;win.title=title;if(GLFW.active.id==win.id){_emscripten_set_window_title(title)}},setJoystickCallback:cbfun=>{var prevcbfun=GLFW.joystickFunc;GLFW.joystickFunc=cbfun;GLFW.refreshJoysticks();return prevcbfun},joys:{},lastGamepadState:[],lastGamepadStateFrame:null,refreshJoysticks:()=>{if(MainLoop.currentFrameNumber!==GLFW.lastGamepadStateFrame||!MainLoop.currentFrameNumber){GLFW.lastGamepadState=navigator.getGamepads?navigator.getGamepads():navigator.webkitGetGamepads||[];GLFW.lastGamepadStateFrame=MainLoop.currentFrameNumber;for(var joy=0;joy<GLFW.lastGamepadState.length;++joy){var gamepad=GLFW.lastGamepadState[joy];if(gamepad){if(!GLFW.joys[joy]){out("glfw joystick connected:",joy);GLFW.joys[joy]={id:stringToNewUTF8(gamepad.id),buttonsCount:gamepad.buttons.length,axesCount:gamepad.axes.length,buttons:_malloc(gamepad.buttons.length),axes:_malloc(gamepad.axes.length*4)};if(GLFW.joystickFunc){((a1,a2)=>dynCall_vii(GLFW.joystickFunc,a1,a2))(joy,262145)}}var data=GLFW.joys[joy];for(var i=0;i<gamepad.buttons.length;++i){HEAP8[data.buttons+i>>>0]=gamepad.buttons[i].pressed}for(var i=0;i<gamepad.axes.length;++i){HEAPF32[data.axes+i*4>>>2>>>0]=gamepad.axes[i]}}else{if(GLFW.joys[joy]){out("glfw joystick disconnected",joy);if(GLFW.joystickFunc){((a1,a2)=>dynCall_vii(GLFW.joystickFunc,a1,a2))(joy,262146)}_free(GLFW.joys[joy].id);_free(GLFW.joys[joy].buttons);_free(GLFW.joys[joy].axes);delete GLFW.joys[joy]}}}}},setKeyCallback:(winid,cbfun)=>{var win=GLFW.WindowFromId(winid);if(!win)return null;var prevcbfun=win.keyFunc;win.keyFunc=cbfun;return prevcbfun},setCharCallback:(winid,cbfun)=>{var win=GLFW.WindowFromId(winid);if(!win)return null;var prevcbfun=win.charFunc;win.charFunc=cbfun;return prevcbfun},setMouseButtonCallback:(winid,cbfun)=>{var win=GLFW.WindowFromId(winid);if(!win)return null;var prevcbfun=win.mouseButtonFunc;win.mouseButtonFunc=cbfun;return prevcbfun},setCursorPosCallback:(winid,cbfun)=>{var win=GLFW.WindowFromId(winid);if(!win)return null;var prevcbfun=win.cursorPosFunc;win.cursorPosFunc=cbfun;return prevcbfun},setScrollCallback:(winid,cbfun)=>{var win=GLFW.WindowFromId(winid);if(!win)return null;var prevcbfun=win.scrollFunc;win.scrollFunc=cbfun;return prevcbfun},setDropCallback:(winid,cbfun)=>{var win=GLFW.WindowFromId(winid);if(!win)return null;var prevcbfun=win.dropFunc;win.dropFunc=cbfun;return prevcbfun},onDrop:event=>{if(!GLFW.active||!GLFW.active.dropFunc)return;if(!event.dataTransfer||!event.dataTransfer.files||event.dataTransfer.files.length==0)return;event.preventDefault();var filenames=_malloc(event.dataTransfer.files.length*4);var filenamesArray=[];var count=event.dataTransfer.files.length;var written=0;var drop_dir=".glfw_dropped_files";FS.createPath("/",drop_dir);function save(file){var path="/"+drop_dir+"/"+file.name.replace(/\//g,"_");var reader=new FileReader;reader.onloadend=e=>{if(reader.readyState!=2){++written;out("failed to read dropped file: "+file.name+": "+reader.error);return}var data=e.target.result;FS.writeFile(path,new Uint8Array(data));if(++written===count){((a1,a2,a3)=>dynCall_viii(GLFW.active.dropFunc,a1,a2,a3))(GLFW.active.id,count,filenames);for(var i=0;i<filenamesArray.length;++i){_free(filenamesArray[i])}_free(filenames)}};reader.readAsArrayBuffer(file);var filename=stringToNewUTF8(path);filenamesArray.push(filename);HEAPU32[filenames+i*4>>>2>>>0]=filename}for(var i=0;i<count;++i){save(event.dataTransfer.files[i])}return false},onDragover:event=>{if(!GLFW.active||!GLFW.active.dropFunc)return;event.preventDefault();return false},setWindowSizeCallback:(winid,cbfun)=>{var win=GLFW.WindowFromId(winid);if(!win)return null;var prevcbfun=win.windowSizeFunc;win.windowSizeFunc=cbfun;return prevcbfun},setWindowCloseCallback:(winid,cbfun)=>{var win=GLFW.WindowFromId(winid);if(!win)return null;var prevcbfun=win.windowCloseFunc;win.windowCloseFunc=cbfun;return prevcbfun},setWindowRefreshCallback:(winid,cbfun)=>{var win=GLFW.WindowFromId(winid);if(!win)return null;var prevcbfun=win.windowRefreshFunc;win.windowRefreshFunc=cbfun;return prevcbfun},onClickRequestPointerLock:e=>{var canvas=Browser.getCanvas();if(!Browser.pointerLock&&canvas.requestPointerLock){canvas.requestPointerLock();e.preventDefault()}},setInputMode:(winid,mode,value)=>{var win=GLFW.WindowFromId(winid);if(!win)return;switch(mode){case 208897:{var canvas=Browser.getCanvas();switch(value){case 212993:{win.inputModes[mode]=value;canvas.removeEventListener("click",GLFW.onClickRequestPointerLock,true);document.exitPointerLock();break}case 212994:{err("glfwSetInputMode called with GLFW_CURSOR_HIDDEN value not implemented");break}case 212995:{win.inputModes[mode]=value;canvas.addEventListener("click",GLFW.onClickRequestPointerLock,true);canvas.requestPointerLock();break}default:{err(`glfwSetInputMode called with unknown value parameter value: ${value}`);break}}break}case 208898:{err("glfwSetInputMode called with GLFW_STICKY_KEYS mode not implemented");break}case 208899:{err("glfwSetInputMode called with GLFW_STICKY_MOUSE_BUTTONS mode not implemented");break}case 208900:{err("glfwSetInputMode called with GLFW_LOCK_KEY_MODS mode not implemented");break}case 3342341:{err("glfwSetInputMode called with GLFW_RAW_MOUSE_MOTION mode not implemented");break}default:{err(`glfwSetInputMode called with unknown mode parameter value: ${mode}`);break}}},getKey:(winid,key)=>{var win=GLFW.WindowFromId(winid);if(!win)return 0;return win.keys[key]},getMouseButton:(winid,button)=>{var win=GLFW.WindowFromId(winid);if(!win)return 0;return(win.buttons&1<<button)>0},getCursorPos:(winid,x,y)=>{HEAPF64[x>>>3>>>0]=Browser.mouseX;HEAPF64[y>>>3>>>0]=Browser.mouseY},getMousePos:(winid,x,y)=>{HEAP32[x>>>2>>>0]=Browser.mouseX;HEAP32[y>>>2>>>0]=Browser.mouseY},setCursorPos:(winid,x,y)=>{},getWindowPos:(winid,x,y)=>{var wx=0;var wy=0;var win=GLFW.WindowFromId(winid);if(win){wx=win.x;wy=win.y}if(x){HEAP32[x>>>2>>>0]=wx}if(y){HEAP32[y>>>2>>>0]=wy}},setWindowPos:(winid,x,y)=>{var win=GLFW.WindowFromId(winid);if(!win)return;win.x=x;win.y=y},getWindowSize:(winid,width,height)=>{var ww=0;var wh=0;var win=GLFW.WindowFromId(winid);if(win){ww=win.width;wh=win.height}if(width){HEAP32[width>>>2>>>0]=ww}if(height){HEAP32[height>>>2>>>0]=wh}},setWindowSize:(winid,width,height)=>{var win=GLFW.WindowFromId(winid);if(!win)return;if(GLFW.active.id==win.id){Browser.setCanvasSize(width,height)}},defaultWindowHints:()=>{GLFW.hints={...GLFW.defaultHints}},createWindow:(width,height,title,monitor,share)=>{var i,id;for(i=0;i<GLFW.windows.length&&GLFW.windows[i]!==null;i++){}if(i>0)throw"glfwCreateWindow only supports one window at time currently";id=i+1;if(width<=0||height<=0)return 0;if(monitor){Browser.requestFullscreen()}else{Browser.setCanvasSize(width,height)}for(i=0;i<GLFW.windows.length&&GLFW.windows[i]==null;i++){}const canvas=Browser.getCanvas();var useWebGL=GLFW.hints[139265]>0;if(i==GLFW.windows.length){if(useWebGL){var contextAttributes={antialias:GLFW.hints[135181]>1,depth:GLFW.hints[135173]>0,stencil:GLFW.hints[135174]>0,alpha:GLFW.hints[135172]>0};Browser.createContext(canvas,true,true,contextAttributes)}else{Browser.init()}}if(!Module["ctx"]&&useWebGL)return 0;var win=new GLFW_Window(id,width,height,canvas.width,canvas.height,title,monitor,share);if(id-1==GLFW.windows.length){GLFW.windows.push(win)}else{GLFW.windows[id-1]=win}GLFW.active=win;GLFW.adjustCanvasDimensions();return win.id},destroyWindow:winid=>{var win=GLFW.WindowFromId(winid);if(!win)return;if(win.windowCloseFunc){(a1=>dynCall_vi(win.windowCloseFunc,a1))(win.id)}GLFW.windows[win.id-1]=null;if(GLFW.active.id==win.id){GLFW.active=null}for(win of GLFW.windows){if(win!==null)return}delete Module["ctx"]},swapBuffers:winid=>{},requestFullscreen(lockPointer,resizeCanvas){Browser.lockPointer=lockPointer;Browser.resizeCanvas=resizeCanvas;if(typeof Browser.lockPointer=="undefined")Browser.lockPointer=true;if(typeof Browser.resizeCanvas=="undefined")Browser.resizeCanvas=false;var canvas=Browser.getCanvas();function fullscreenChange(){Browser.isFullscreen=false;var canvasContainer=canvas.parentNode;if((document["fullscreenElement"]||document["mozFullScreenElement"]||document["msFullscreenElement"]||document["webkitFullscreenElement"]||document["webkitCurrentFullScreenElement"])===canvasContainer){canvas.exitFullscreen=Browser.exitFullscreen;if(Browser.lockPointer)canvas.requestPointerLock();Browser.isFullscreen=true;if(Browser.resizeCanvas){Browser.setFullscreenCanvasSize()}else{Browser.updateCanvasDimensions(canvas);Browser.updateResizeListeners()}}else{canvasContainer.parentNode.insertBefore(canvas,canvasContainer);canvasContainer.parentNode.removeChild(canvasContainer);if(Browser.resizeCanvas){Browser.setWindowedCanvasSize()}else{Browser.updateCanvasDimensions(canvas);Browser.updateResizeListeners()}}Module["onFullScreen"]?.(Browser.isFullscreen);Module["onFullscreen"]?.(Browser.isFullscreen)}if(!Browser.fullscreenHandlersInstalled){Browser.fullscreenHandlersInstalled=true;document.addEventListener("fullscreenchange",fullscreenChange,false);document.addEventListener("mozfullscreenchange",fullscreenChange,false);document.addEventListener("webkitfullscreenchange",fullscreenChange,false);document.addEventListener("MSFullscreenChange",fullscreenChange,false)}var canvasContainer=document.createElement("div");canvas.parentNode.insertBefore(canvasContainer,canvas);canvasContainer.appendChild(canvas);canvasContainer.requestFullscreen=canvasContainer["requestFullscreen"]||canvasContainer["mozRequestFullScreen"]||canvasContainer["msRequestFullscreen"]||(canvasContainer["webkitRequestFullscreen"]?()=>canvasContainer["webkitRequestFullscreen"](Element["ALLOW_KEYBOARD_INPUT"]):null)||(canvasContainer["webkitRequestFullScreen"]?()=>canvasContainer["webkitRequestFullScreen"](Element["ALLOW_KEYBOARD_INPUT"]):null);canvasContainer.requestFullscreen()},updateCanvasDimensions(canvas,wNative,hNative){const scale=GLFW.getHiDPIScale();if(wNative&&hNative){canvas.widthNative=wNative;canvas.heightNative=hNative}else{wNative=canvas.widthNative;hNative=canvas.heightNative}var w=wNative;var h=hNative;if(Module["forcedAspectRatio"]&&Module["forcedAspectRatio"]>0){if(w/h<Module["forcedAspectRatio"]){w=Math.round(h*Module["forcedAspectRatio"])}else{h=Math.round(w/Module["forcedAspectRatio"])}}if((document["fullscreenElement"]||document["mozFullScreenElement"]||document["msFullscreenElement"]||document["webkitFullscreenElement"]||document["webkitCurrentFullScreenElement"])===canvas.parentNode&&typeof screen!="undefined"){var factor=Math.min(screen.width/w,screen.height/h);w=Math.round(w*factor);h=Math.round(h*factor)}if(Browser.resizeCanvas){wNative=w;hNative=h}const wNativeScaled=Math.floor(wNative*scale);const hNativeScaled=Math.floor(hNative*scale);if(canvas.width!=wNativeScaled)canvas.width=wNativeScaled;if(canvas.height!=hNativeScaled)canvas.height=hNativeScaled;if(typeof canvas.style!="undefined"){if(!GLFW.isCSSScalingEnabled()){canvas.style.setProperty("width",wNative+"px","important");canvas.style.setProperty("height",hNative+"px","important")}else{canvas.style.removeProperty("width");canvas.style.removeProperty("height")}}},calculateMouseCoords(pageX,pageY){const rect=Browser.getCanvas().getBoundingClientRect();var scrollX=typeof window.scrollX!="undefined"?window.scrollX:window.pageXOffset;var scrollY=typeof window.scrollY!="undefined"?window.scrollY:window.pageYOffset;var adjustedX=pageX-(scrollX+rect.left);var adjustedY=pageY-(scrollY+rect.top);if(GLFW.isCSSScalingEnabled()&&GLFW.active){adjustedX=adjustedX*(GLFW.active.width/rect.width);adjustedY=adjustedY*(GLFW.active.height/rect.height)}return{x:adjustedX,y:adjustedY}},setWindowAttrib:(winid,attrib,value)=>{var win=GLFW.WindowFromId(winid);if(!win)return;const isHiDPIAware=GLFW.isHiDPIAware();win.attributes[attrib]=value;if(isHiDPIAware!==GLFW.isHiDPIAware())GLFW.adjustCanvasDimensions()},getDevicePixelRatio(){return typeof devicePixelRatio=="number"&&devicePixelRatio||1},isHiDPIAware(){if(GLFW.active)return GLFW.active.attributes[139276]>0;else return false},isCSSScalingEnabled(){return!GLFW.isHiDPIAware()},adjustCanvasDimensions(){if(GLFW.active){Browser.updateCanvasDimensions(Browser.getCanvas(),GLFW.active.width,GLFW.active.height);Browser.updateResizeListeners()}},getHiDPIScale(){return GLFW.isHiDPIAware()?GLFW.scale:1},onDevicePixelRatioChange(){GLFW.onWindowContentScaleChanged(GLFW.getDevicePixelRatio());GLFW.adjustCanvasDimensions()},GLFW2ParamToGLFW3Param:param=>{var table={196609:0,196610:0,196611:0,196612:0,196613:0,196614:0,131073:0,131074:0,131075:0,131076:0,131077:135169,131078:135170,131079:135171,131080:135172,131081:135173,131082:135174,131083:135183,131084:135175,131085:135176,131086:135177,131087:135178,131088:135179,131089:135180,131090:0,131091:135181,131092:139266,131093:139267,131094:139270,131095:139271,131096:139272};return table[param]}};function _glfwCreateStandardCursor(shape){return 0}function _glfwCreateWindow(width,height,title,monitor,share){title>>>=0;monitor>>>=0;share>>>=0;return GLFW.createWindow(width,height,title,monitor,share)}function _glfwGetClipboardString(win){win>>>=0;return 0}function _glfwGetCursorPos(winid,x,y){winid>>>=0;x>>>=0;y>>>=0;return GLFW.getCursorPos(winid,x,y)}function _glfwGetFramebufferSize(winid,width,height){winid>>>=0;width>>>=0;height>>>=0;var ww=0;var wh=0;var win=GLFW.WindowFromId(winid);if(win){ww=win.framebufferWidth;wh=win.framebufferHeight}if(width){HEAP32[width>>>2>>>0]=ww}if(height){HEAP32[height>>>2>>>0]=wh}}function _glfwGetInputMode(winid,mode){winid>>>=0;var win=GLFW.WindowFromId(winid);if(!win)return;switch(mode){case 208897:{if(Browser.pointerLock){win.inputModes[mode]=212995}else{win.inputModes[mode]=212993}}}return win.inputModes[mode]}function _glfwGetJoystickAxes(joy,count){count>>>=0;GLFW.refreshJoysticks();var state=GLFW.joys[joy];if(!state||!state.axes){HEAP32[count>>>2>>>0]=0;return}HEAP32[count>>>2>>>0]=state.axesCount;return state.axes}function _glfwGetJoystickButtons(joy,count){count>>>=0;GLFW.refreshJoysticks();var state=GLFW.joys[joy];if(!state||!state.buttons){HEAP32[count>>>2>>>0]=0;return}HEAP32[count>>>2>>>0]=state.buttonsCount;return state.buttons}function _glfwGetKey(winid,key){winid>>>=0;return GLFW.getKey(winid,key)}var _glfwGetTime=()=>GLFW.getTime()-GLFW.initialTime;function _glfwGetWindowSize(winid,width,height){winid>>>=0;width>>>=0;height>>>=0;return GLFW.getWindowSize(winid,width,height)}function _glfwGetWindowUserPointer(winid){winid>>>=0;var win=GLFW.WindowFromId(winid);if(!win)return 0;return win.userptr}var _glfwInit=()=>{if(GLFW.windows)return 1;GLFW.initialTime=GLFW.getTime();GLFW.defaultWindowHints();GLFW.windows=new Array;GLFW.active=null;GLFW.scale=GLFW.getDevicePixelRatio();window.addEventListener("gamepadconnected",GLFW.onGamepadConnected,true);window.addEventListener("gamepaddisconnected",GLFW.onGamepadDisconnected,true);window.addEventListener("keydown",GLFW.onKeydown,true);window.addEventListener("keypress",GLFW.onKeyPress,true);window.addEventListener("keyup",GLFW.onKeyup,true);window.addEventListener("blur",GLFW.onBlur,true);GLFW.devicePixelRatioMQL=window.matchMedia("(resolution: "+GLFW.getDevicePixelRatio()+"dppx)");GLFW.devicePixelRatioMQL.addEventListener("change",GLFW.onDevicePixelRatioChange);var canvas=Browser.getCanvas();canvas.addEventListener("touchmove",GLFW.onMousemove,true);canvas.addEventListener("touchstart",GLFW.onMouseButtonDown,true);canvas.addEventListener("touchcancel",GLFW.onMouseButtonUp,true);canvas.addEventListener("touchend",GLFW.onMouseButtonUp,true);canvas.addEventListener("mousemove",GLFW.onMousemove,true);canvas.addEventListener("mousedown",GLFW.onMouseButtonDown,true);canvas.addEventListener("mouseup",GLFW.onMouseButtonUp,true);canvas.addEventListener("wheel",GLFW.onMouseWheel,true);canvas.addEventListener("mousewheel",GLFW.onMouseWheel,true);canvas.addEventListener("mouseenter",GLFW.onMouseenter,true);canvas.addEventListener("mouseleave",GLFW.onMouseleave,true);canvas.addEventListener("drop",GLFW.onDrop,true);canvas.addEventListener("dragover",GLFW.onDragover,true);Browser.requestFullscreen=GLFW.requestFullscreen;Browser.calculateMouseCoords=GLFW.calculateMouseCoords;Browser.updateCanvasDimensions=GLFW.updateCanvasDimensions;Browser.resizeListeners.push((width,height)=>{if(GLFW.isHiDPIAware()){var canvas=Browser.getCanvas();GLFW.onCanvasResize(canvas.clientWidth,canvas.clientHeight,width,height)}else{GLFW.onCanvasResize(width,height,width,height)}});return 1};var _glfwPollEvents=()=>0;function _glfwSetCharCallback(winid,cbfun){winid>>>=0;cbfun>>>=0;return GLFW.setCharCallback(winid,cbfun)}function _glfwSetClipboardString(win,string){win>>>=0;string>>>=0;return 0}function _glfwSetCursor(winid,cursor){winid>>>=0;cursor>>>=0;return 0}function _glfwSetCursorEnterCallback(winid,cbfun){winid>>>=0;cbfun>>>=0;var win=GLFW.WindowFromId(winid);if(!win)return null;var prevcbfun=win.cursorEnterFunc;win.cursorEnterFunc=cbfun;return prevcbfun}function _glfwSetCursorPos(winid,x,y){winid>>>=0;return GLFW.setCursorPos(winid,x,y)}function _glfwSetCursorPosCallback(winid,cbfun){winid>>>=0;cbfun>>>=0;return GLFW.setCursorPosCallback(winid,cbfun)}function _glfwSetErrorCallback(cbfun){cbfun>>>=0;var prevcbfun=GLFW.errorFunc;GLFW.errorFunc=cbfun;return prevcbfun}function _glfwSetFramebufferSizeCallback(winid,cbfun){winid>>>=0;cbfun>>>=0;var win=GLFW.WindowFromId(winid);if(!win)return null;var prevcbfun=win.framebufferSizeFunc;win.framebufferSizeFunc=cbfun;return prevcbfun}function _glfwSetInputMode(winid,mode,value){winid>>>=0;GLFW.setInputMode(winid,mode,value)}function _glfwSetKeyCallback(winid,cbfun){winid>>>=0;cbfun>>>=0;return GLFW.setKeyCallback(winid,cbfun)}function _glfwSetMonitorCallback(cbfun){cbfun>>>=0;var prevcbfun=GLFW.monitorFunc;GLFW.monitorFunc=cbfun;return prevcbfun}function _glfwSetMouseButtonCallback(winid,cbfun){winid>>>=0;cbfun>>>=0;return GLFW.setMouseButtonCallback(winid,cbfun)}function _glfwSetScrollCallback(winid,cbfun){winid>>>=0;cbfun>>>=0;return GLFW.setScrollCallback(winid,cbfun)}function _glfwSetWindowFocusCallback(winid,cbfun){winid>>>=0;cbfun>>>=0;var win=GLFW.WindowFromId(winid);if(!win)return null;var prevcbfun=win.windowFocusFunc;win.windowFocusFunc=cbfun;return prevcbfun}function _glfwSetWindowUserPointer(winid,ptr){winid>>>=0;ptr>>>=0;var win=GLFW.WindowFromId(winid);if(!win)return;win.userptr=ptr}var _glfwWindowHint=(target,hint)=>{GLFW.hints[target]=hint};function _glfwWindowShouldClose(winid){winid>>>=0;var win=GLFW.WindowFromId(winid);if(!win)return 0;return win.shouldClose}function _wgpuAdapterGetLimits(adapterPtr,limitsOutPtr){adapterPtr>>>=0;limitsOutPtr>>>=0;var adapter=WebGPU.getJsObject(adapterPtr);WebGPU.fillLimitStruct(adapter.limits,limitsOutPtr);return 1}function _wgpuCommandEncoderBeginRenderPass(encoderPtr,descriptor){encoderPtr>>>=0;descriptor>>>=0;function makeColorAttachment(caPtr){var viewPtr=HEAPU32[caPtr+4>>>2>>>0];if(viewPtr===0){return undefined}var depthSlice=HEAP32[caPtr+8>>>2>>>0];if(depthSlice==-1)depthSlice=undefined;var loadOpInt=HEAPU32[caPtr+16>>>2>>>0];var storeOpInt=HEAPU32[caPtr+20>>>2>>>0];var clearValue=WebGPU.makeColor(caPtr+24);return{view:WebGPU.getJsObject(viewPtr),depthSlice,resolveTarget:WebGPU.getJsObject(HEAPU32[caPtr+12>>>2>>>0]),clearValue,loadOp:WebGPU.LoadOp[loadOpInt],storeOp:WebGPU.StoreOp[storeOpInt]}}function makeColorAttachments(count,caPtr){var attachments=[];for(var i=0;i<count;++i){attachments.push(makeColorAttachment(caPtr+56*i))}return attachments}function makeDepthStencilAttachment(dsaPtr){if(dsaPtr===0)return undefined;return{view:WebGPU.getJsObject(HEAPU32[dsaPtr+4>>>2>>>0]),depthClearValue:HEAPF32[dsaPtr+16>>>2>>>0],depthLoadOp:WebGPU.LoadOp[HEAPU32[dsaPtr+8>>>2>>>0]],depthStoreOp:WebGPU.StoreOp[HEAPU32[dsaPtr+12>>>2>>>0]],depthReadOnly:!!HEAPU32[dsaPtr+20>>>2>>>0],stencilClearValue:HEAPU32[dsaPtr+32>>>2>>>0],stencilLoadOp:WebGPU.LoadOp[HEAPU32[dsaPtr+24>>>2>>>0]],stencilStoreOp:WebGPU.StoreOp[HEAPU32[dsaPtr+28>>>2>>>0]],stencilReadOnly:!!HEAPU32[dsaPtr+36>>>2>>>0]}}function makeRenderPassDescriptor(descriptor){var nextInChainPtr=HEAPU32[descriptor>>>2>>>0];var maxDrawCount=undefined;if(nextInChainPtr!==0){var sType=HEAPU32[nextInChainPtr+4>>>2>>>0];var renderPassMaxDrawCount=nextInChainPtr;maxDrawCount=HEAPU32[renderPassMaxDrawCount+4+8>>>2>>>0]*4294967296+HEAPU32[renderPassMaxDrawCount+8>>>2>>>0]}var desc={label:WebGPU.makeStringFromOptionalStringView(descriptor+4),colorAttachments:makeColorAttachments(HEAPU32[descriptor+12>>>2>>>0],HEAPU32[descriptor+16>>>2>>>0]),depthStencilAttachment:makeDepthStencilAttachment(HEAPU32[descriptor+20>>>2>>>0]),occlusionQuerySet:WebGPU.getJsObject(HEAPU32[descriptor+24>>>2>>>0]),timestampWrites:WebGPU.makePassTimestampWrites(HEAPU32[descriptor+28>>>2>>>0]),maxDrawCount};return desc}var desc=makeRenderPassDescriptor(descriptor);var commandEncoder=WebGPU.getJsObject(encoderPtr);var ptr=_emwgpuCreateRenderPassEncoder(0);WebGPU.Internals.jsObjectInsert(ptr,commandEncoder.beginRenderPass(desc));return ptr}function _wgpuCommandEncoderFinish(encoderPtr,descriptor){encoderPtr>>>=0;descriptor>>>=0;var commandEncoder=WebGPU.getJsObject(encoderPtr);var ptr=_emwgpuCreateCommandBuffer(0);WebGPU.Internals.jsObjectInsert(ptr,commandEncoder.finish());return ptr}var readI53FromI64=ptr=>HEAPU32[ptr>>>2>>>0]+HEAP32[ptr+4>>>2>>>0]*4294967296;function _wgpuDeviceCreateBindGroup(devicePtr,descriptor){devicePtr>>>=0;descriptor>>>=0;function makeEntry(entryPtr){var bufferPtr=HEAPU32[entryPtr+8>>>2>>>0];var samplerPtr=HEAPU32[entryPtr+32>>>2>>>0];var textureViewPtr=HEAPU32[entryPtr+36>>>2>>>0];var binding=HEAPU32[entryPtr+4>>>2>>>0];if(bufferPtr){var size=readI53FromI64(entryPtr+24);if(size==-1)size=undefined;return{binding,resource:{buffer:WebGPU.getJsObject(bufferPtr),offset:HEAPU32[entryPtr+4+16>>>2>>>0]*4294967296+HEAPU32[entryPtr+16>>>2>>>0],size}}}else if(samplerPtr){return{binding,resource:WebGPU.getJsObject(samplerPtr)}}else{return{binding,resource:WebGPU.getJsObject(textureViewPtr)}}}function makeEntries(count,entriesPtrs){var entries=[];for(var i=0;i<count;++i){entries.push(makeEntry(entriesPtrs+40*i))}return entries}var desc={label:WebGPU.makeStringFromOptionalStringView(descriptor+4),layout:WebGPU.getJsObject(HEAPU32[descriptor+12>>>2>>>0]),entries:makeEntries(HEAPU32[descriptor+16>>>2>>>0],HEAPU32[descriptor+20>>>2>>>0])};var device=WebGPU.getJsObject(devicePtr);var ptr=_emwgpuCreateBindGroup(0);WebGPU.Internals.jsObjectInsert(ptr,device.createBindGroup(desc));return ptr}function _wgpuDeviceCreateBindGroupLayout(devicePtr,descriptor){devicePtr>>>=0;descriptor>>>=0;function makeBufferEntry(entryPtr){var typeInt=HEAPU32[entryPtr+4>>>2>>>0];if(!typeInt)return undefined;return{type:WebGPU.BufferBindingType[typeInt],hasDynamicOffset:!!HEAPU32[entryPtr+8>>>2>>>0],minBindingSize:HEAPU32[entryPtr+4+16>>>2>>>0]*4294967296+HEAPU32[entryPtr+16>>>2>>>0]}}function makeSamplerEntry(entryPtr){var typeInt=HEAPU32[entryPtr+4>>>2>>>0];if(!typeInt)return undefined;return{type:WebGPU.SamplerBindingType[typeInt]}}function makeTextureEntry(entryPtr){var sampleTypeInt=HEAPU32[entryPtr+4>>>2>>>0];if(!sampleTypeInt)return undefined;return{sampleType:WebGPU.TextureSampleType[sampleTypeInt],viewDimension:WebGPU.TextureViewDimension[HEAPU32[entryPtr+8>>>2>>>0]],multisampled:!!HEAPU32[entryPtr+12>>>2>>>0]}}function makeStorageTextureEntry(entryPtr){var accessInt=HEAPU32[entryPtr+4>>>2>>>0];if(!accessInt)return undefined;return{access:WebGPU.StorageTextureAccess[accessInt],format:WebGPU.TextureFormat[HEAPU32[entryPtr+8>>>2>>>0]],viewDimension:WebGPU.TextureViewDimension[HEAPU32[entryPtr+12>>>2>>>0]]}}function makeEntry(entryPtr){return{binding:HEAPU32[entryPtr+4>>>2>>>0],visibility:HEAPU32[entryPtr+8>>>2>>>0],buffer:makeBufferEntry(entryPtr+16),sampler:makeSamplerEntry(entryPtr+40),texture:makeTextureEntry(entryPtr+48),storageTexture:makeStorageTextureEntry(entryPtr+64)}}function makeEntries(count,entriesPtrs){var entries=[];for(var i=0;i<count;++i){entries.push(makeEntry(entriesPtrs+80*i))}return entries}var desc={label:WebGPU.makeStringFromOptionalStringView(descriptor+4),entries:makeEntries(HEAPU32[descriptor+12>>>2>>>0],HEAPU32[descriptor+16>>>2>>>0])};var device=WebGPU.getJsObject(devicePtr);var ptr=_emwgpuCreateBindGroupLayout(0);WebGPU.Internals.jsObjectInsert(ptr,device.createBindGroupLayout(desc));return ptr}function _wgpuDeviceCreateCommandEncoder(devicePtr,descriptor){devicePtr>>>=0;descriptor>>>=0;var desc;if(descriptor){desc={label:WebGPU.makeStringFromOptionalStringView(descriptor+4)}}var device=WebGPU.getJsObject(devicePtr);var ptr=_emwgpuCreateCommandEncoder(0);WebGPU.Internals.jsObjectInsert(ptr,device.createCommandEncoder(desc));return ptr}function _wgpuDeviceCreatePipelineLayout(devicePtr,descriptor){devicePtr>>>=0;descriptor>>>=0;var bglCount=HEAPU32[descriptor+12>>>2>>>0];var bglPtr=HEAPU32[descriptor+16>>>2>>>0];var bgls=[];for(var i=0;i<bglCount;++i){bgls.push(WebGPU.getJsObject(HEAPU32[bglPtr+4*i>>>2>>>0]))}var desc={label:WebGPU.makeStringFromOptionalStringView(descriptor+4),bindGroupLayouts:bgls};var device=WebGPU.getJsObject(devicePtr);var ptr=_emwgpuCreatePipelineLayout(0);WebGPU.Internals.jsObjectInsert(ptr,device.createPipelineLayout(desc));return ptr}function _wgpuDeviceCreateRenderPipeline(devicePtr,descriptor){devicePtr>>>=0;descriptor>>>=0;var desc=WebGPU.makeRenderPipelineDesc(descriptor);var device=WebGPU.getJsObject(devicePtr);var ptr=_emwgpuCreateRenderPipeline(0);WebGPU.Internals.jsObjectInsert(ptr,device.createRenderPipeline(desc));return ptr}function _wgpuDeviceCreateSampler(devicePtr,descriptor){devicePtr>>>=0;descriptor>>>=0;var desc;if(descriptor){desc={label:WebGPU.makeStringFromOptionalStringView(descriptor+4),addressModeU:WebGPU.AddressMode[HEAPU32[descriptor+12>>>2>>>0]],addressModeV:WebGPU.AddressMode[HEAPU32[descriptor+16>>>2>>>0]],addressModeW:WebGPU.AddressMode[HEAPU32[descriptor+20>>>2>>>0]],magFilter:WebGPU.FilterMode[HEAPU32[descriptor+24>>>2>>>0]],minFilter:WebGPU.FilterMode[HEAPU32[descriptor+28>>>2>>>0]],mipmapFilter:WebGPU.MipmapFilterMode[HEAPU32[descriptor+32>>>2>>>0]],lodMinClamp:HEAPF32[descriptor+36>>>2>>>0],lodMaxClamp:HEAPF32[descriptor+40>>>2>>>0],compare:WebGPU.CompareFunction[HEAPU32[descriptor+44>>>2>>>0]]}}var device=WebGPU.getJsObject(devicePtr);var ptr=_emwgpuCreateSampler(0);WebGPU.Internals.jsObjectInsert(ptr,device.createSampler(desc));return ptr}function _wgpuDeviceCreateTexture(devicePtr,descriptor){devicePtr>>>=0;descriptor>>>=0;var desc={label:WebGPU.makeStringFromOptionalStringView(descriptor+4),size:WebGPU.makeExtent3D(descriptor+28),mipLevelCount:HEAPU32[descriptor+44>>>2>>>0],sampleCount:HEAPU32[descriptor+48>>>2>>>0],dimension:WebGPU.TextureDimension[HEAPU32[descriptor+24>>>2>>>0]],format:WebGPU.TextureFormat[HEAPU32[descriptor+40>>>2>>>0]],usage:HEAPU32[descriptor+16>>>2>>>0]};var viewFormatCount=HEAPU32[descriptor+52>>>2>>>0];if(viewFormatCount){var viewFormatsPtr=HEAPU32[descriptor+56>>>2>>>0];desc["viewFormats"]=Array.from(HEAP32.subarray(viewFormatsPtr>>>2>>>0,viewFormatsPtr+viewFormatCount*4>>>2>>>0),format=>WebGPU.TextureFormat[format])}var device=WebGPU.getJsObject(devicePtr);var ptr=_emwgpuCreateTexture(0);WebGPU.Internals.jsObjectInsert(ptr,device.createTexture(desc));return ptr}var maybeCStringToJsString=cString=>cString>2?UTF8ToString(cString):cString;var specialHTMLTargets=[0,typeof document!="undefined"?document:0,typeof window!="undefined"?window:0];var findEventTarget=target=>{target=maybeCStringToJsString(target);var domElement=specialHTMLTargets[target]||(typeof document!="undefined"?document.querySelector(target):null);return domElement};var findCanvasEventTarget=findEventTarget;function _wgpuInstanceCreateSurface(instancePtr,descriptor){instancePtr>>>=0;descriptor>>>=0;var nextInChainPtr=HEAPU32[descriptor>>>2>>>0];var sourceCanvasHTMLSelector=nextInChainPtr;var selectorPtr=HEAPU32[sourceCanvasHTMLSelector+8>>>2>>>0];var canvas=findCanvasEventTarget(selectorPtr);var context=canvas.getContext("webgpu");if(!context)return 0;context.surfaceLabelWebGPU=WebGPU.makeStringFromOptionalStringView(descriptor+4);var ptr=_emwgpuCreateSurface(0);WebGPU.Internals.jsObjectInsert(ptr,context);return ptr}var _wgpuQueueSubmit=function(queuePtr,commandCount,commands){queuePtr>>>=0;commandCount>>>=0;commands>>>=0;var queue=WebGPU.getJsObject(queuePtr);var cmds=Array.from(HEAP32.subarray(commands>>>2>>>0,commands+commandCount*4>>>2>>>0),id=>WebGPU.getJsObject(id));queue.submit(cmds)};function _wgpuQueueWriteBuffer(queuePtr,bufferPtr,bufferOffset,data,size){queuePtr>>>=0;bufferPtr>>>=0;bufferOffset=bigintToI53Checked(bufferOffset);data>>>=0;size>>>=0;var queue=WebGPU.getJsObject(queuePtr);var buffer=WebGPU.getJsObject(bufferPtr);var subarray=HEAPU8.subarray(data>>>0,data+size>>>0);queue.writeBuffer(buffer,bufferOffset,subarray,0,size)}function _wgpuQueueWriteTexture(queuePtr,destinationPtr,data,dataSize,dataLayoutPtr,writeSizePtr){queuePtr>>>=0;destinationPtr>>>=0;data>>>=0;dataSize>>>=0;dataLayoutPtr>>>=0;writeSizePtr>>>=0;var queue=WebGPU.getJsObject(queuePtr);var destination=WebGPU.makeTexelCopyTextureInfo(destinationPtr);var dataLayout=WebGPU.makeTexelCopyBufferLayout(dataLayoutPtr);var writeSize=WebGPU.makeExtent3D(writeSizePtr);var subarray=HEAPU8.subarray(data>>>0,data+dataSize>>>0);queue.writeTexture(destination,subarray,dataLayout,writeSize)}function _wgpuRenderPassEncoderDraw(passPtr,vertexCount,instanceCount,firstVertex,firstInstance){passPtr>>>=0;var pass=WebGPU.getJsObject(passPtr);pass.draw(vertexCount,instanceCount,firstVertex,firstInstance)}function _wgpuRenderPassEncoderDrawIndexed(passPtr,indexCount,instanceCount,firstIndex,baseVertex,firstInstance){passPtr>>>=0;var pass=WebGPU.getJsObject(passPtr);pass.drawIndexed(indexCount,instanceCount,firstIndex,baseVertex,firstInstance)}function _wgpuRenderPassEncoderEnd(encoderPtr){encoderPtr>>>=0;var encoder=WebGPU.getJsObject(encoderPtr);encoder.end()}function _wgpuRenderPassEncoderSetBindGroup(passPtr,groupIndex,groupPtr,dynamicOffsetCount,dynamicOffsetsPtr){passPtr>>>=0;groupPtr>>>=0;dynamicOffsetCount>>>=0;dynamicOffsetsPtr>>>=0;var pass=WebGPU.getJsObject(passPtr);var group=WebGPU.getJsObject(groupPtr);if(dynamicOffsetCount==0){pass.setBindGroup(groupIndex,group)}else{pass.setBindGroup(groupIndex,group,HEAPU32,dynamicOffsetsPtr>>>2,dynamicOffsetCount)}}function _wgpuRenderPassEncoderSetBlendConstant(passPtr,colorPtr){passPtr>>>=0;colorPtr>>>=0;var pass=WebGPU.getJsObject(passPtr);var color=WebGPU.makeColor(colorPtr);pass.setBlendConstant(color)}function _wgpuRenderPassEncoderSetIndexBuffer(passPtr,bufferPtr,format,offset,size){passPtr>>>=0;bufferPtr>>>=0;offset=bigintToI53Checked(offset);size=bigintToI53Checked(size);var pass=WebGPU.getJsObject(passPtr);var buffer=WebGPU.getJsObject(bufferPtr);if(size==-1)size=undefined;pass.setIndexBuffer(buffer,WebGPU.IndexFormat[format],offset,size)}function _wgpuRenderPassEncoderSetPipeline(passPtr,pipelinePtr){passPtr>>>=0;pipelinePtr>>>=0;var pass=WebGPU.getJsObject(passPtr);var pipeline=WebGPU.getJsObject(pipelinePtr);pass.setPipeline(pipeline)}function _wgpuRenderPassEncoderSetScissorRect(passPtr,x,y,w,h){passPtr>>>=0;var pass=WebGPU.getJsObject(passPtr);pass.setScissorRect(x,y,w,h)}function _wgpuRenderPassEncoderSetVertexBuffer(passPtr,slot,bufferPtr,offset,size){passPtr>>>=0;bufferPtr>>>=0;offset=bigintToI53Checked(offset);size=bigintToI53Checked(size);var pass=WebGPU.getJsObject(passPtr);var buffer=WebGPU.getJsObject(bufferPtr);if(size==-1)size=undefined;pass.setVertexBuffer(slot,buffer,offset,size)}function _wgpuRenderPassEncoderSetViewport(passPtr,x,y,w,h,minDepth,maxDepth){passPtr>>>=0;var pass=WebGPU.getJsObject(passPtr);pass.setViewport(x,y,w,h,minDepth,maxDepth)}function _wgpuSurfaceConfigure(surfacePtr,config){surfacePtr>>>=0;config>>>=0;var devicePtr=HEAPU32[config+4>>>2>>>0];var context=WebGPU.getJsObject(surfacePtr);var canvasSize=[HEAPU32[config+24>>>2>>>0],HEAPU32[config+28>>>2>>>0]];if(canvasSize[0]!==0){context["canvas"]["width"]=canvasSize[0]}if(canvasSize[1]!==0){context["canvas"]["height"]=canvasSize[1]}var configuration={device:WebGPU.getJsObject(devicePtr),format:WebGPU.TextureFormat[HEAPU32[config+8>>>2>>>0]],usage:HEAPU32[config+16>>>2>>>0],alphaMode:WebGPU.CompositeAlphaMode[HEAPU32[config+40>>>2>>>0]]};var viewFormatCount=HEAPU32[config+32>>>2>>>0];if(viewFormatCount){var viewFormatsPtr=HEAPU32[config+36>>>2>>>0];configuration["viewFormats"]=Array.from(HEAP32.subarray(viewFormatsPtr>>>2>>>0,viewFormatsPtr+viewFormatCount*4>>>2>>>0),format=>WebGPU.TextureFormat[format])}{var nextInChainPtr=HEAPU32[config>>>2>>>0];if(nextInChainPtr!==0){var sType=HEAPU32[nextInChainPtr+4>>>2>>>0];var surfaceColorManagement=nextInChainPtr;configuration.colorSpace=WebGPU.PredefinedColorSpace[HEAPU32[surfaceColorManagement+8>>>2>>>0]];configuration.toneMapping={mode:WebGPU.ToneMappingMode[HEAPU32[surfaceColorManagement+12>>>2>>>0]]}}}context.configure(configuration)}function _wgpuSurfaceGetCurrentTexture(surfacePtr,surfaceTexturePtr){surfacePtr>>>=0;surfaceTexturePtr>>>=0;var context=WebGPU.getJsObject(surfacePtr);try{var texturePtr=_emwgpuCreateTexture(0);WebGPU.Internals.jsObjectInsert(texturePtr,context.getCurrentTexture());HEAPU32[surfaceTexturePtr+4>>>2>>>0]=texturePtr;HEAP32[surfaceTexturePtr+8>>>2>>>0]=1}catch(ex){HEAPU32[surfaceTexturePtr+4>>>2>>>0]=0;HEAP32[surfaceTexturePtr+8>>>2>>>0]=6}}function _wgpuSurfaceUnconfigure(surfacePtr){surfacePtr>>>=0;var context=WebGPU.getJsObject(surfacePtr);context.unconfigure()}function _wgpuTextureCreateView(texturePtr,descriptor){texturePtr>>>=0;descriptor>>>=0;var desc;if(descriptor){var mipLevelCount=HEAPU32[descriptor+24>>>2>>>0];var arrayLayerCount=HEAPU32[descriptor+32>>>2>>>0];desc={label:WebGPU.makeStringFromOptionalStringView(descriptor+4),format:WebGPU.TextureFormat[HEAPU32[descriptor+12>>>2>>>0]],dimension:WebGPU.TextureViewDimension[HEAPU32[descriptor+16>>>2>>>0]],baseMipLevel:HEAPU32[descriptor+20>>>2>>>0],mipLevelCount:mipLevelCount===4294967295?undefined:mipLevelCount,baseArrayLayer:HEAPU32[descriptor+28>>>2>>>0],arrayLayerCount:arrayLayerCount===4294967295?undefined:arrayLayerCount,aspect:WebGPU.TextureAspect[HEAPU32[descriptor+36>>>2>>>0]]}}var texture=WebGPU.getJsObject(texturePtr);var ptr=_emwgpuCreateTextureView(0);WebGPU.Internals.jsObjectInsert(ptr,texture.createView(desc));return ptr}function _wgpuTextureDestroy(texturePtr){texturePtr>>>=0;WebGPU.getJsObject(texturePtr).destroy()}var runAndAbortIfError=func=>{try{return func()}catch(e){abort(e)}};var runtimeKeepalivePush=()=>{runtimeKeepaliveCounter+=1};var runtimeKeepalivePop=()=>{runtimeKeepaliveCounter-=1};var Asyncify={instrumentWasmImports(imports){var importPattern=/^(invoke_.*|__asyncjs__.*)$/;for(let[x,original]of Object.entries(imports)){if(typeof original=="function"){let isAsyncifyImport=original.isAsync||importPattern.test(x)}}},instrumentFunction(original){var wrapper=(...args)=>{Asyncify.exportCallStack.push(original);try{return original(...args)}finally{if(!ABORT){var top=Asyncify.exportCallStack.pop();Asyncify.maybeStopUnwind()}}};Asyncify.funcWrappers.set(original,wrapper);return wrapper},instrumentWasmExports(exports){var ret={};for(let[x,original]of Object.entries(exports)){if(typeof original=="function"){var wrapper=Asyncify.instrumentFunction(original);ret[x]=wrapper}else{ret[x]=original}}return ret},State:{Normal:0,Unwinding:1,Rewinding:2,Disabled:3},state:0,StackSize:65536,currData:null,handleSleepReturnValue:0,exportCallStack:[],callstackFuncToId:new Map,callStackIdToFunc:new Map,funcWrappers:new Map,callStackId:0,asyncPromiseHandlers:null,sleepCallbacks:[],getCallStackId(func){if(!Asyncify.callstackFuncToId.has(func)){var id=Asyncify.callStackId++;Asyncify.callstackFuncToId.set(func,id);Asyncify.callStackIdToFunc.set(id,func)}return Asyncify.callstackFuncToId.get(func)},maybeStopUnwind(){if(Asyncify.currData&&Asyncify.state===Asyncify.State.Unwinding&&Asyncify.exportCallStack.length===0){Asyncify.state=Asyncify.State.Normal;runAndAbortIfError(_asyncify_stop_unwind);if(typeof Fibers!="undefined"){Fibers.trampoline()}}},whenDone(){return new Promise((resolve,reject)=>{Asyncify.asyncPromiseHandlers={resolve,reject}})},allocateData(){var ptr=_malloc(12+Asyncify.StackSize);Asyncify.setDataHeader(ptr,ptr+12,Asyncify.StackSize);Asyncify.setDataRewindFunc(ptr);return ptr},setDataHeader(ptr,stack,stackSize){HEAPU32[ptr>>>2>>>0]=stack;HEAPU32[ptr+4>>>2>>>0]=stack+stackSize},setDataRewindFunc(ptr){var bottomOfCallStack=Asyncify.exportCallStack[0];var rewindId=Asyncify.getCallStackId(bottomOfCallStack);HEAP32[ptr+8>>>2>>>0]=rewindId},getDataRewindFunc(ptr){var id=HEAP32[ptr+8>>>2>>>0];var func=Asyncify.callStackIdToFunc.get(id);return func},doRewind(ptr){var original=Asyncify.getDataRewindFunc(ptr);var func=Asyncify.funcWrappers.get(original);return func()},handleSleep(startAsync){if(ABORT)return;if(Asyncify.state===Asyncify.State.Normal){var reachedCallback=false;var reachedAfterCallback=false;startAsync((handleSleepReturnValue=0)=>{if(ABORT)return;Asyncify.handleSleepReturnValue=handleSleepReturnValue;reachedCallback=true;if(!reachedAfterCallback){return}Asyncify.state=Asyncify.State.Rewinding;runAndAbortIfError(()=>_asyncify_start_rewind(Asyncify.currData));if(typeof MainLoop!="undefined"&&MainLoop.func){MainLoop.resume()}var asyncWasmReturnValue,isError=false;try{asyncWasmReturnValue=Asyncify.doRewind(Asyncify.currData)}catch(err){asyncWasmReturnValue=err;isError=true}var handled=false;if(!Asyncify.currData){var asyncPromiseHandlers=Asyncify.asyncPromiseHandlers;if(asyncPromiseHandlers){Asyncify.asyncPromiseHandlers=null;(isError?asyncPromiseHandlers.reject:asyncPromiseHandlers.resolve)(asyncWasmReturnValue);handled=true}}if(isError&&!handled){throw asyncWasmReturnValue}});reachedAfterCallback=true;if(!reachedCallback){Asyncify.state=Asyncify.State.Unwinding;Asyncify.currData=Asyncify.allocateData();if(typeof MainLoop!="undefined"&&MainLoop.func){MainLoop.pause()}runAndAbortIfError(()=>_asyncify_start_unwind(Asyncify.currData))}}else if(Asyncify.state===Asyncify.State.Rewinding){Asyncify.state=Asyncify.State.Normal;runAndAbortIfError(_asyncify_stop_rewind);_free(Asyncify.currData);Asyncify.currData=null;Asyncify.sleepCallbacks.forEach(callUserCallback)}else{abort(`invalid state: ${Asyncify.state}`)}return Asyncify.handleSleepReturnValue},handleAsync:startAsync=>Asyncify.handleSleep(wakeUp=>{startAsync().then(wakeUp)})};var getCFunc=ident=>{var func=Module["_"+ident];return func};var writeArrayToMemory=(array,buffer)=>{HEAP8.set(array,buffer>>>0)};var ccall=(ident,returnType,argTypes,args,opts)=>{var toC={string:str=>{var ret=0;if(str!==null&&str!==undefined&&str!==0){ret=stringToUTF8OnStack(str)}return ret},array:arr=>{var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType==="string"){return UTF8ToString(ret)}if(returnType==="boolean")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var previousAsync=Asyncify.currData;var ret=func(...cArgs);function onDone(ret){runtimeKeepalivePop();if(stack!==0)stackRestore(stack);return convertReturnValue(ret)}var asyncMode=opts?.async;runtimeKeepalivePush();if(Asyncify.currData!=previousAsync){return Asyncify.whenDone().then(onDone)}ret=onDone(ret);if(asyncMode)return Promise.resolve(ret);return ret};var cwrap=(ident,returnType,argTypes,opts)=>{var numericArgs=!argTypes||argTypes.every(type=>type==="number"||type==="boolean");var numericRet=returnType!=="string";if(numericRet&&numericArgs&&!opts){return getCFunc(ident)}return(...args)=>ccall(ident,returnType,argTypes,args,opts)};var FS_createPath=(...args)=>FS.createPath(...args);var FS_unlink=(...args)=>FS.unlink(...args);var FS_createLazyFile=(...args)=>FS.createLazyFile(...args);var FS_createDevice=(...args)=>FS.createDevice(...args);FS.createPreloadedFile=FS_createPreloadedFile;FS.staticInit();Module["requestAnimationFrame"]=MainLoop.requestAnimationFrame;Module["pauseMainLoop"]=MainLoop.pause;Module["resumeMainLoop"]=MainLoop.resume;MainLoop.init();Fetch.init();{if(Module["noExitRuntime"])noExitRuntime=Module["noExitRuntime"];if(Module["preloadPlugins"])preloadPlugins=Module["preloadPlugins"];if(Module["print"])out=Module["print"];if(Module["printErr"])err=Module["printErr"];if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"]}Module["addRunDependency"]=addRunDependency;Module["removeRunDependency"]=removeRunDependency;Module["ccall"]=ccall;Module["cwrap"]=cwrap;Module["FS_createPreloadedFile"]=FS_createPreloadedFile;Module["FS_unlink"]=FS_unlink;Module["FS_createPath"]=FS_createPath;Module["FS_createDevice"]=FS_createDevice;Module["FS_createDataFile"]=FS_createDataFile;Module["FS_createLazyFile"]=FS_createLazyFile;function ImGui_ImplGlfw_EmscriptenOpenURL(url){url=url?UTF8ToString(url):null;if(url)window.open(url,"_blank")}var _main,_switchToEarthMode,_switchToModelMode,_resetCamera,_toggleWireframe,_getFPS,_getTriangleCount,_getVertexCount,_getCameraX,_getCameraY,_getCameraZ,_getFOV,_getWireframeMode,_free,_malloc,_emwgpuCreateBindGroup,_emwgpuCreateBindGroupLayout,_emwgpuCreateCommandBuffer,_emwgpuCreateCommandEncoder,_emwgpuCreateComputePassEncoder,_emwgpuCreateComputePipeline,_emwgpuCreatePipelineLayout,_emwgpuCreateQuerySet,_emwgpuCreateRenderBundle,_emwgpuCreateRenderBundleEncoder,_emwgpuCreateRenderPassEncoder,_emwgpuCreateRenderPipeline,_emwgpuCreateSampler,_emwgpuCreateSurface,_emwgpuCreateTexture,_emwgpuCreateTextureView,_emwgpuCreateAdapter,_emwgpuCreateBuffer,_emwgpuCreateDevice,_emwgpuCreateQueue,_emwgpuCreateShaderModule,_emwgpuOnDeviceLostCompleted,_emwgpuOnRequestAdapterCompleted,_emwgpuOnRequestDeviceCompleted,_emwgpuOnUncapturedError,__emscripten_stack_restore,__emscripten_stack_alloc,_emscripten_stack_get_current,dynCall_v,dynCall_viiiii,dynCall_viii,dynCall_vidd,dynCall_viiii,dynCall_ii,dynCall_vi,dynCall_iiiiii,dynCall_viiiiii,dynCall_iiiiiii,dynCall_iiii,dynCall_vii,dynCall_iii,dynCall_iiiiiiii,dynCall_iiiii,dynCall_viji,dynCall_jiji,dynCall_iidiiii,dynCall_viijii,dynCall_iiiiiiiii,dynCall_iiiiij,dynCall_iiiiid,dynCall_iiiiijj,dynCall_iiiiiijj,_asyncify_start_unwind,_asyncify_stop_unwind,_asyncify_start_rewind,_asyncify_stop_rewind;function assignWasmExports(wasmExports){Module["_main"]=_main=wasmExports["Ua"];Module["_switchToEarthMode"]=_switchToEarthMode=wasmExports["Va"];Module["_switchToModelMode"]=_switchToModelMode=wasmExports["Wa"];Module["_resetCamera"]=_resetCamera=wasmExports["Xa"];Module["_toggleWireframe"]=_toggleWireframe=wasmExports["Ya"];Module["_getFPS"]=_getFPS=wasmExports["Za"];Module["_getTriangleCount"]=_getTriangleCount=wasmExports["_a"];Module["_getVertexCount"]=_getVertexCount=wasmExports["$a"];Module["_getCameraX"]=_getCameraX=wasmExports["ab"];Module["_getCameraY"]=_getCameraY=wasmExports["bb"];Module["_getCameraZ"]=_getCameraZ=wasmExports["cb"];Module["_getFOV"]=_getFOV=wasmExports["db"];Module["_getWireframeMode"]=_getWireframeMode=wasmExports["eb"];_free=wasmExports["fb"];_malloc=wasmExports["gb"];_emwgpuCreateBindGroup=wasmExports["hb"];_emwgpuCreateBindGroupLayout=wasmExports["ib"];_emwgpuCreateCommandBuffer=wasmExports["jb"];_emwgpuCreateCommandEncoder=wasmExports["kb"];_emwgpuCreateComputePassEncoder=wasmExports["lb"];_emwgpuCreateComputePipeline=wasmExports["mb"];_emwgpuCreatePipelineLayout=wasmExports["nb"];_emwgpuCreateQuerySet=wasmExports["ob"];_emwgpuCreateRenderBundle=wasmExports["pb"];_emwgpuCreateRenderBundleEncoder=wasmExports["qb"];_emwgpuCreateRenderPassEncoder=wasmExports["rb"];_emwgpuCreateRenderPipeline=wasmExports["sb"];_emwgpuCreateSampler=wasmExports["tb"];_emwgpuCreateSurface=wasmExports["ub"];_emwgpuCreateTexture=wasmExports["vb"];_emwgpuCreateTextureView=wasmExports["wb"];_emwgpuCreateAdapter=wasmExports["xb"];_emwgpuCreateBuffer=wasmExports["yb"];_emwgpuCreateDevice=wasmExports["zb"];_emwgpuCreateQueue=wasmExports["Ab"];_emwgpuCreateShaderModule=wasmExports["Bb"];_emwgpuOnDeviceLostCompleted=wasmExports["Cb"];_emwgpuOnRequestAdapterCompleted=wasmExports["Db"];_emwgpuOnRequestDeviceCompleted=wasmExports["Eb"];_emwgpuOnUncapturedError=wasmExports["Fb"];__emscripten_stack_restore=wasmExports["Gb"];__emscripten_stack_alloc=wasmExports["Hb"];_emscripten_stack_get_current=wasmExports["Ib"];dynCalls["v"]=dynCall_v=wasmExports["Jb"];dynCalls["viiiii"]=dynCall_viiiii=wasmExports["Kb"];dynCalls["viii"]=dynCall_viii=wasmExports["Lb"];dynCalls["vidd"]=dynCall_vidd=wasmExports["Mb"];dynCalls["viiii"]=dynCall_viiii=wasmExports["Nb"];dynCalls["ii"]=dynCall_ii=wasmExports["Ob"];dynCalls["vi"]=dynCall_vi=wasmExports["Pb"];dynCalls["iiiiii"]=dynCall_iiiiii=wasmExports["Qb"];dynCalls["viiiiii"]=dynCall_viiiiii=wasmExports["Rb"];dynCalls["iiiiiii"]=dynCall_iiiiiii=wasmExports["Sb"];dynCalls["iiii"]=dynCall_iiii=wasmExports["Tb"];dynCalls["vii"]=dynCall_vii=wasmExports["Ub"];dynCalls["iii"]=dynCall_iii=wasmExports["Vb"];dynCalls["iiiiiiii"]=dynCall_iiiiiiii=wasmExports["Wb"];dynCalls["iiiii"]=dynCall_iiiii=wasmExports["Xb"];dynCalls["viji"]=dynCall_viji=wasmExports["Yb"];dynCalls["jiji"]=dynCall_jiji=wasmExports["Zb"];dynCalls["iidiiii"]=dynCall_iidiiii=wasmExports["_b"];dynCalls["viijii"]=dynCall_viijii=wasmExports["$b"];dynCalls["iiiiiiiii"]=dynCall_iiiiiiiii=wasmExports["ac"];dynCalls["iiiiij"]=dynCall_iiiiij=wasmExports["bc"];dynCalls["iiiiid"]=dynCall_iiiiid=wasmExports["cc"];dynCalls["iiiiijj"]=dynCall_iiiiijj=wasmExports["dc"];dynCalls["iiiiiijj"]=dynCall_iiiiiijj=wasmExports["ec"];_asyncify_start_unwind=wasmExports["fc"];_asyncify_stop_unwind=wasmExports["gc"];_asyncify_start_rewind=wasmExports["hc"];_asyncify_stop_rewind=wasmExports["ic"]}var wasmImports={ha:ImGui_ImplGlfw_EmscriptenOpenURL,n:___cxa_throw,s:___syscall_fcntl64,T:___syscall_ioctl,U:___syscall_openat,X:__abort_js,p:__embind_register_bigint,M:__embind_register_bool,K:__embind_register_emval,o:__embind_register_float,c:__embind_register_integer,b:__embind_register_memory_view,L:__embind_register_std_string,i:__embind_register_std_wstring,N:__embind_register_void,O:__tzset_js,Q:_clock_time_get,ea:_emscripten_fetch_free,ba:_emscripten_has_asyncify,ga:_emscripten_is_main_browser_thread,P:_emscripten_resize_heap,Ra:_emscripten_set_main_loop,x:_emscripten_sleep,fa:_emscripten_start_fetch,aa:_emwgpuAdapterRequestDevice,da:_emwgpuBufferDestroy,a:_emwgpuDelete,$:_emwgpuDeviceCreateBuffer,_:_emwgpuDeviceCreateShaderModule,ca:_emwgpuDeviceDestroy,Y:_emwgpuGetPreferredFormat,Z:_emwgpuInstanceRequestAdapter,V:_environ_get,W:_environ_sizes_get,q:_fd_close,S:_fd_read,R:_fd_seek,r:_fd_write,d:_glfwCreateStandardCursor,Ha:_glfwCreateWindow,ia:_glfwGetClipboardString,I:_glfwGetCursorPos,f:_glfwGetFramebufferSize,oa:_glfwGetInputMode,la:_glfwGetJoystickAxes,ka:_glfwGetJoystickButtons,e:_glfwGetKey,J:_glfwGetTime,qa:_glfwGetWindowSize,h:_glfwGetWindowUserPointer,Qa:_glfwInit,Pa:_glfwPollEvents,sa:_glfwSetCharCallback,ja:_glfwSetClipboardString,na:_glfwSetCursor,va:_glfwSetCursorEnterCallback,pa:_glfwSetCursorPos,v:_glfwSetCursorPosCallback,w:_glfwSetErrorCallback,ta:_glfwSetFramebufferSizeCallback,ma:_glfwSetInputMode,ua:_glfwSetKeyCallback,ra:_glfwSetMonitorCallback,u:_glfwSetMouseButtonCallback,t:_glfwSetScrollCallback,wa:_glfwSetWindowFocusCallback,Ba:_glfwSetWindowUserPointer,H:_glfwWindowHint,Oa:_glfwWindowShouldClose,Na:_wgpuAdapterGetLimits,Ma:_wgpuCommandEncoderBeginRenderPass,La:_wgpuCommandEncoderFinish,m:_wgpuDeviceCreateBindGroup,l:_wgpuDeviceCreateBindGroupLayout,Ka:_wgpuDeviceCreateCommandEncoder,G:_wgpuDeviceCreatePipelineLayout,F:_wgpuDeviceCreateRenderPipeline,E:_wgpuDeviceCreateSampler,D:_wgpuDeviceCreateTexture,Ja:_wgpuInstanceCreateSurface,Ia:_wgpuQueueSubmit,g:_wgpuQueueWriteBuffer,C:_wgpuQueueWriteTexture,Ga:_wgpuRenderPassEncoderDraw,B:_wgpuRenderPassEncoderDrawIndexed,Fa:_wgpuRenderPassEncoderEnd,k:_wgpuRenderPassEncoderSetBindGroup,Ea:_wgpuRenderPassEncoderSetBlendConstant,A:_wgpuRenderPassEncoderSetIndexBuffer,z:_wgpuRenderPassEncoderSetPipeline,Da:_wgpuRenderPassEncoderSetScissorRect,y:_wgpuRenderPassEncoderSetVertexBuffer,Ca:_wgpuRenderPassEncoderSetViewport,Aa:_wgpuSurfaceConfigure,za:_wgpuSurfaceGetCurrentTexture,ya:_wgpuSurfaceUnconfigure,j:_wgpuTextureCreateView,xa:_wgpuTextureDestroy};var wasmExports;createWasm();function applySignatureConversions(wasmExports){wasmExports=Object.assign({},wasmExports);var makeWrapper_pp=f=>a0=>f(a0)>>>0;var makeWrapper_ppp=f=>(a0,a1)=>f(a0,a1)>>>0;var makeWrapper_p=f=>()=>f()>>>0;wasmExports["__getTypeName"]=makeWrapper_pp(wasmExports["__getTypeName"]);wasmExports["gb"]=makeWrapper_pp(wasmExports["gb"]);wasmExports["memalign"]=makeWrapper_ppp(wasmExports["memalign"]);wasmExports["Hb"]=makeWrapper_pp(wasmExports["Hb"]);wasmExports["Ib"]=makeWrapper_p(wasmExports["Ib"]);return wasmExports}function callMain(args=[]){var entryFunction=_main;args.unshift(thisProgram);var argc=args.length;var argv=stackAlloc((argc+1)*4);var argv_ptr=argv;args.forEach(arg=>{HEAPU32[argv_ptr>>>2>>>0]=stringToUTF8OnStack(arg);argv_ptr+=4});HEAPU32[argv_ptr>>>2>>>0]=0;try{var ret=entryFunction(argc,argv);exitJS(ret,true);return ret}catch(e){return handleException(e)}}function run(args=arguments_){if(runDependencies>0){dependenciesFulfilled=run;return}preRun();if(runDependencies>0){dependenciesFulfilled=run;return}function doRun(){Module["calledRun"]=true;if(ABORT)return;initRuntime();preMain();Module["onRuntimeInitialized"]?.();var noInitialRun=Module["noInitialRun"]||false;if(!noInitialRun)callMain(args);postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(()=>{setTimeout(()=>Module["setStatus"](""),1);doRun()},1)}else{doRun()}}function preInit(){if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].shift()()}}}preInit();run();
