#pragma once

#include <USG_Backend/RenderBackend.h>
#include <memory>
#include <vector>
#include <unordered_map>

// Vulkan headers
#include <vulkan/vulkan.h>

#ifdef USG_PLATFORM_DESKTOP
#include <GLFW/glfw3.h>
#endif

namespace USG
{
    // 前向声明
    class VulkanDevice;
    class VulkanSwapchain;
    class VulkanBackend;

    /**
     * @brief Vulkan缓冲区实现
     */
    class VulkanBuffer : public BackendBuffer
    {
    public:
        VulkanBuffer(VkDevice device, VkBuffer buffer, VkDeviceMemory memory, uint64_t size, BufferUsage usage, MemoryProperty memoryProperty);
        ~VulkanBuffer() override;

        uint64_t getSize() const override { return _size; }
        BufferUsage getUsage() const override { return _usage; }
        MemoryProperty getMemoryProperties() const override { return _memoryProperty; }

        // Vulkan特有的方法
        void *map();
        void unmap();
        void updateData(const void *data, size_t size, size_t offset = 0);
        VkBuffer getVkBuffer() const { return _buffer; }

    private:
        VkDevice _device;
        VkBuffer _buffer;
        VkDeviceMemory _memory;
        uint64_t _size;
        BufferUsage _usage;
        MemoryProperty _memoryProperty;
        void *_mappedData = nullptr;
    };

    /**
     * @brief Vulkan着色器实现
     */
    class VulkanShader : public BackendShader
    {
    public:
        VulkanShader(VkDevice device, VkShaderModule module, ShaderStage stage);
        ~VulkanShader() override;

        ShaderStage getStage() const override { return _stage; }
        std::string getEntryPoint() const override { return "main"; }
        VkShaderModule getShaderModule() const { return _module; }

    private:
        VkDevice _device;
        VkShaderModule _module;
        ShaderStage _stage;
    };

    /**
     * @brief Vulkan管线实现
     */
    class VulkanPipeline : public BackendPipeline
    {
    public:
        VulkanPipeline(VkDevice device, VkPipeline pipeline, VkPipelineLayout layout);
        ~VulkanPipeline() override;

        bool isComputePipeline() const override { return false; } // 目前只支持图形管线
        VkPipeline getVkPipeline() const { return _pipeline; }
        VkPipelineLayout getVkPipelineLayout() const { return _layout; }

    private:
        VkDevice _device;
        VkPipeline _pipeline;
        VkPipelineLayout _layout;
    };

    /**
     * @brief Vulkan命令列表实现
     */
    class VulkanCommandList : public BackendCommandList
    {
    public:
        VulkanCommandList(VkDevice device, VkCommandBuffer commandBuffer, VulkanBackend *backend);
        ~VulkanCommandList() override;

        void begin() override;
        void end() override;
        void reset() override;
        void beginRenderPass(const RenderPassDesc &desc) override;
        void endRenderPass() override;
        void setPipeline(BackendPipeline *pipeline) override;
        void setVertexBuffer(BackendBuffer *buffer, uint32_t slot, size_t offset = 0) override;
        void setIndexBuffer(BackendBuffer *buffer, IndexFormat format, size_t offset = 0) override;
        void setDescriptorSet(BackendDescriptorSet *descriptorSet, uint32_t slot) override;
        void draw(uint32_t vertexCount, uint32_t instanceCount = 1, uint32_t firstVertex = 0, uint32_t firstInstance = 0) override;
        void drawIndexed(uint32_t indexCount, uint32_t instanceCount = 1, uint32_t firstIndex = 0, int32_t vertexOffset = 0, uint32_t firstInstance = 0) override;
        void dispatch(uint32_t groupCountX, uint32_t groupCountY = 1, uint32_t groupCountZ = 1) override;
        void barrier(const BarrierDesc &barrier) override;
        void pushDebugGroup(const std::string &name) override;
        void popDebugGroup() override;
        void insertDebugMarker(const std::string &name) override;

        VkCommandBuffer getVkCommandBuffer() const { return _commandBuffer; }
        VkCommandBuffer getCommandBuffer() const { return _commandBuffer; }

    private:
        VkDevice _device;
        VkCommandBuffer _commandBuffer;
        VkRenderPass _currentRenderPass = VK_NULL_HANDLE;
        bool _isRecording = false;
        VulkanBackend *_backend;
    };

    /**
     * @brief Vulkan渲染后端实现
     *
     * 基于Vulkan API的渲染后端实现
     */
    class VulkanBackend : public RenderBackend
    {
    public:
        VulkanBackend();
        ~VulkanBackend() override;

        // RenderBackend接口实现
        bool initialize(const BackendConfig &config) override;
        void cleanup() override;
        BackendType getBackendType() const override { return BackendType::Vulkan; }
        std::string getBackendName() const override { return "Vulkan"; }

        // 资源创建
        BackendBuffer *createBuffer(const BufferDesc &desc) override;
        BackendTexture *createTexture(const TextureDesc &desc) override;
        BackendShader *createShader(const ShaderDesc &desc) override;
        BackendPipeline *createPipeline(const PipelineDesc &desc) override;
        BackendDescriptorSet *createDescriptorSet() override;
        BackendCommandList *createCommandList() override;
        BackendFence *createFence() override;
        BackendSemaphore *createSemaphore() override;

        // 命令执行
        void executeCommandList(BackendCommandList *cmdList,
                                const std::vector<BackendSemaphore *> &waitSemaphores = {},
                                const std::vector<BackendSemaphore *> &signalSemaphores = {},
                                BackendFence *fence = nullptr) override;

        // 资源销毁
        void destroyBuffer(BackendBuffer *buffer) override;
        void destroyTexture(BackendTexture *texture) override;
        void destroyShader(BackendShader *shader) override;
        void destroyPipeline(BackendPipeline *pipeline) override;
        void destroyDescriptorSet(BackendDescriptorSet *descriptorSet) override;
        void destroyCommandList(BackendCommandList *cmdList) override;
        void destroyFence(BackendFence *fence) override;
        void destroySemaphore(BackendSemaphore *semaphore) override;

        // 缓冲区数据更新
        void updateBuffer(BackendBuffer *buffer, const void *data, size_t size, size_t offset = 0);

        // 其他抽象方法
        BackendDevice *getDevice() override { return nullptr; }
        void beginFrame() override;
        void endFrame() override;
        void present() override;
        void waitIdle() override {}
        void setErrorCallback(std::function<void(const std::string &)> callback) override {}
        void setDebugCallback(std::function<void(const std::string &)> callback) override {}

        // Getter方法供VulkanCommandList使用
        VkRenderPass getRenderPass() const { return _renderPass; }
        VkFramebuffer getCurrentFramebuffer() const
        {
            return _imageIndex < _swapchainFramebuffers.size() ? _swapchainFramebuffers[_imageIndex] : VK_NULL_HANDLE;
        }
        VkExtent2D getSwapchainExtent() const { return _swapchainExtent; }

    private:
        // Vulkan初始化相关方法
        bool createInstance();
        bool selectPhysicalDevice();
        bool createLogicalDevice();
        bool createSwapchain(const BackendConfig &config);
        bool createRenderPass();
        bool createFramebuffers();
        bool createCommandPool();
        bool createSyncObjects();
        void printDeviceInfo();

        // 辅助方法
        uint32_t findMemoryType(uint32_t typeFilter, VkMemoryPropertyFlags properties);
        VkFormat findSupportedFormat(const std::vector<VkFormat> &candidates, VkImageTiling tiling, VkFormatFeatureFlags features);

        // SPIR-V着色器代码
        std::vector<uint32_t> getDefaultVertexShaderSPIRV();
        std::vector<uint32_t> getDefaultFragmentShaderSPIRV();

        // Vulkan对象
        VkInstance _instance = VK_NULL_HANDLE;
        VkPhysicalDevice _physicalDevice = VK_NULL_HANDLE;
        VkDevice _device = VK_NULL_HANDLE;
        VkQueue _graphicsQueue = VK_NULL_HANDLE;
        VkQueue _presentQueue = VK_NULL_HANDLE;
        VkSurfaceKHR _surface = VK_NULL_HANDLE;
        VkSwapchainKHR _swapchain = VK_NULL_HANDLE;
        VkRenderPass _renderPass = VK_NULL_HANDLE;
        VkCommandPool _commandPool = VK_NULL_HANDLE;

        // 交换链相关
        std::vector<VkImage> _swapchainImages;
        std::vector<VkImageView> _swapchainImageViews;
        std::vector<VkFramebuffer> _swapchainFramebuffers;
        VkFormat _swapchainImageFormat;
        VkExtent2D _swapchainExtent;

        // 同步对象
        std::vector<VkSemaphore> _imageAvailableSemaphores;
        std::vector<VkSemaphore> _renderFinishedSemaphores;
        std::vector<VkFence> _inFlightFences;

        // 队列族索引
        uint32_t _graphicsQueueFamily = UINT32_MAX;
        uint32_t _presentQueueFamily = UINT32_MAX;

        // 当前帧索引
        uint32_t _currentFrame = 0;
        uint32_t _imageIndex = 0;
        static const int MAX_FRAMES_IN_FLIGHT = 2;

        // 窗口相关
#ifdef USG_PLATFORM_DESKTOP
        GLFWwindow *_window = nullptr;
#endif

        // 状态
        bool _initialized = false;
        uint32_t _nextResourceId = 0;
    };

} // namespace USG
