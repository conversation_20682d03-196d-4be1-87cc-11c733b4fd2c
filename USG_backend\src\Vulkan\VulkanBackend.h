#pragma once

#include <USG_Backend/RenderBackend.h>
#include <memory>

namespace USG
{

    /**
     * @brief Vulkan渲染后端实现
     *
     * 基于Vulkan API的渲染后端实现
     */
    class VulkanBackend : public RenderBackend
    {
    public:
        VulkanBackend();
        ~VulkanBackend() override;

        // RenderBackend接口实现
        bool initialize(const BackendConfig &config) override;
        void cleanup() override;
        BackendType getBackendType() const override { return BackendType::Vulkan; }
        std::string getBackendName() const override { return "Vulkan"; }

        // 资源创建
        BackendBuffer *createBuffer(const BufferDesc &desc) override;
        BackendTexture *createTexture(const TextureDesc &desc) override;
        BackendShader *createShader(const ShaderDesc &desc) override;
        BackendPipeline *createPipeline(const PipelineDesc &desc) override;
        BackendDescriptorSet *createDescriptorSet() override;
        BackendCommandList *createCommandList() override;
        BackendFence *createFence() override;
        BackendSemaphore *createSemaphore() override;

        // 命令执行
        void executeCommandList(BackendCommandList *cmdList,
                                const std::vector<BackendSemaphore *> &waitSemaphores = {},
                                const std::vector<BackendSemaphore *> &signalSemaphores = {},
                                BackendFence *fence = nullptr) override;

        // 资源销毁
        void destroyBuffer(BackendBuffer *buffer) override;
        void destroyTexture(BackendTexture *texture) override;
        void destroyShader(BackendShader *shader) override;
        void destroyPipeline(BackendPipeline *pipeline) override;
        void destroyDescriptorSet(BackendDescriptorSet *descriptorSet) override;
        void destroyCommandList(BackendCommandList *cmdList) override;
        void destroyFence(BackendFence *fence) override;
        void destroySemaphore(BackendSemaphore *semaphore) override;

        // 其他抽象方法
        BackendDevice *getDevice() override { return nullptr; }
        void beginFrame() override {}
        void endFrame() override {}
        void present() override {}
        void waitIdle() override {}
        void setErrorCallback(std::function<void(const std::string &)> callback) override {}
        void setDebugCallback(std::function<void(const std::string &)> callback) override {}

    private:
        bool _initialized = false;
    };

} // namespace USG
