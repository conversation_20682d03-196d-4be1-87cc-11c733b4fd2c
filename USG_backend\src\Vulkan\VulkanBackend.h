#pragma once

#include <USG_Backend/RenderBackend.h>
#include <memory>

namespace USG
{

    /**
     * @brief Vulkan命令列表实现
     */
    class VulkanCommandList : public BackendCommandList
    {
    public:
        VulkanCommandList() = default;
        ~VulkanCommandList() override = default;

        void begin() override;
        void end() override;
        void reset() override;
        void beginRenderPass(const RenderPassDesc &desc) override;
        void endRenderPass() override;
        void setPipeline(BackendPipeline *pipeline) override;
        void setVertexBuffer(BackendBuffer *buffer, uint32_t slot, size_t offset = 0) override;
        void setIndexBuffer(BackendBuffer *buffer, IndexFormat format, size_t offset = 0) override;
        void setDescriptorSet(BackendDescriptorSet *descriptorSet, uint32_t slot) override;
        void draw(uint32_t vertexCount, uint32_t instanceCount = 1, uint32_t firstVertex = 0, uint32_t firstInstance = 0) override;
        void drawIndexed(uint32_t indexCount, uint32_t instanceCount = 1, uint32_t firstIndex = 0, int32_t vertexOffset = 0, uint32_t firstInstance = 0) override;
        void dispatch(uint32_t groupCountX, uint32_t groupCountY = 1, uint32_t groupCountZ = 1) override;
        void barrier(const BarrierDesc &barrier) override;
        void pushDebugGroup(const std::string &name) override;
        void popDebugGroup() override;
        void insertDebugMarker(const std::string &name) override;
    };

    /**
     * @brief Vulkan渲染后端实现
     *
     * 基于Vulkan API的渲染后端实现
     */
    class VulkanBackend : public RenderBackend
    {
    public:
        VulkanBackend();
        ~VulkanBackend() override;

        // RenderBackend接口实现
        bool initialize(const BackendConfig &config) override;
        void cleanup() override;
        BackendType getBackendType() const override { return BackendType::Vulkan; }
        std::string getBackendName() const override { return "Vulkan"; }

        // 资源创建
        BackendBuffer *createBuffer(const BufferDesc &desc) override;
        BackendTexture *createTexture(const TextureDesc &desc) override;
        BackendShader *createShader(const ShaderDesc &desc) override;
        BackendPipeline *createPipeline(const PipelineDesc &desc) override;
        BackendDescriptorSet *createDescriptorSet() override;
        BackendCommandList *createCommandList() override;
        BackendFence *createFence() override;
        BackendSemaphore *createSemaphore() override;

        // 命令执行
        void executeCommandList(BackendCommandList *cmdList,
                                const std::vector<BackendSemaphore *> &waitSemaphores = {},
                                const std::vector<BackendSemaphore *> &signalSemaphores = {},
                                BackendFence *fence = nullptr) override;

        // 资源销毁
        void destroyBuffer(BackendBuffer *buffer) override;
        void destroyTexture(BackendTexture *texture) override;
        void destroyShader(BackendShader *shader) override;
        void destroyPipeline(BackendPipeline *pipeline) override;
        void destroyDescriptorSet(BackendDescriptorSet *descriptorSet) override;
        void destroyCommandList(BackendCommandList *cmdList) override;
        void destroyFence(BackendFence *fence) override;
        void destroySemaphore(BackendSemaphore *semaphore) override;

        // 其他抽象方法
        BackendDevice *getDevice() override { return nullptr; }
        void beginFrame() override;
        void endFrame() override;
        void present() override;
        void waitIdle() override {}
        void setErrorCallback(std::function<void(const std::string &)> callback) override {}
        void setDebugCallback(std::function<void(const std::string &)> callback) override {}

    private:
        bool _initialized = false;
        uint32_t _nextResourceId = 0;
    };

} // namespace USG
