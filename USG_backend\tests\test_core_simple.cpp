/**
 * @file test_core_simple.cpp
 * @brief USG Backend核心功能简单测试
 */

#include <USG_Backend/BackendFactory.h>
#include <USG_Backend/RenderBackend.h>
#include <USG_Backend/BackendTypes.h>
#include <iostream>
#include <cassert>

using namespace USG;

void testBackendFactory() {
    std::cout << "=== 测试后端工厂 ===" << std::endl;
    
    auto& factory = BackendFactory::getInstance();
    
    // 检查WebGPU后端是否可用
    bool webgpuAvailable = factory.isBackendAvailable(BackendType::WebGPU);
    std::cout << "WebGPU后端可用: " << (webgpuAvailable ? "是" : "否") << std::endl;
    
    // 获取所有可用后端
    auto availableBackends = factory.getAvailableBackends();
    std::cout << "可用后端数量: " << availableBackends.size() << std::endl;
    
    for (auto backend : availableBackends) {
        std::cout << "  - 后端类型: " << static_cast<int>(backend) << std::endl;
    }
    
    // 获取默认后端类型
    auto defaultType = factory.getDefaultBackendType();
    std::cout << "默认后端类型: " << static_cast<int>(defaultType) << std::endl;
    
    std::cout << "后端工厂测试完成" << std::endl;
}

void testBackendManager() {
    std::cout << "\n=== 测试后端管理器 ===" << std::endl;
    
    auto& manager = BackendManager::getInstance();
    
    // 检查初始状态
    bool initialized = manager.isInitialized();
    std::cout << "管理器初始化状态: " << (initialized ? "已初始化" : "未初始化") << std::endl;
    
    // 获取当前后端类型
    auto currentType = manager.getCurrentBackendType();
    std::cout << "当前后端类型: " << static_cast<int>(currentType) << std::endl;
    
    // 获取当前后端名称
    auto currentName = manager.getCurrentBackendName();
    std::cout << "当前后端名称: " << currentName << std::endl;
    
    std::cout << "后端管理器测试完成" << std::endl;
}

void testBackendTypes() {
    std::cout << "\n=== 测试后端类型 ===" << std::endl;
    
    // 测试后端类型枚举
    std::cout << "WebGPU类型值: " << static_cast<int>(BackendType::WebGPU) << std::endl;
    std::cout << "Vulkan类型值: " << static_cast<int>(BackendType::Vulkan) << std::endl;
    std::cout << "OpenGL类型值: " << static_cast<int>(BackendType::OpenGL) << std::endl;
    std::cout << "Unknown类型值: " << static_cast<int>(BackendType::Unknown) << std::endl;
    
    // 测试着色器阶段
    std::cout << "顶点着色器阶段: " << static_cast<int>(ShaderStage::Vertex) << std::endl;
    std::cout << "片段着色器阶段: " << static_cast<int>(ShaderStage::Fragment) << std::endl;
    std::cout << "计算着色器阶段: " << static_cast<int>(ShaderStage::Compute) << std::endl;
    
    // 测试缓冲区用法
    std::cout << "顶点缓冲区用法: " << static_cast<int>(BufferUsage::Vertex) << std::endl;
    std::cout << "索引缓冲区用法: " << static_cast<int>(BufferUsage::Index) << std::endl;
    std::cout << "统一缓冲区用法: " << static_cast<int>(BufferUsage::Uniform) << std::endl;
    
    std::cout << "后端类型测试完成" << std::endl;
}

void testBackendConfig() {
    std::cout << "\n=== 测试后端配置 ===" << std::endl;
    
    // 创建默认配置
    BackendConfig config;
    std::cout << "默认配置:" << std::endl;
    std::cout << "  启用验证: " << (config.enableValidation ? "是" : "否") << std::endl;
    std::cout << "  启用调试标记: " << (config.enableDebugMarkers ? "是" : "否") << std::endl;
    std::cout << "  应用程序名称: " << config.applicationName << std::endl;
    std::cout << "  应用程序版本: " << config.applicationVersion << std::endl;
    
    // 修改配置
    config.enableValidation = true;
    config.enableDebugMarkers = true;
    config.applicationName = "USG Backend Test";
    config.applicationVersion = 100;
    
    std::cout << "修改后配置:" << std::endl;
    std::cout << "  启用验证: " << (config.enableValidation ? "是" : "否") << std::endl;
    std::cout << "  启用调试标记: " << (config.enableDebugMarkers ? "是" : "否") << std::endl;
    std::cout << "  应用程序名称: " << config.applicationName << std::endl;
    std::cout << "  应用程序版本: " << config.applicationVersion << std::endl;
    
    std::cout << "后端配置测试完成" << std::endl;
}

void testDeviceCapabilities() {
    std::cout << "\n=== 测试设备能力 ===" << std::endl;
    
    // 创建设备能力结构
    DeviceCapabilities caps;
    caps.maxTextureSize2D = 8192;
    caps.maxTextureSize3D = 2048;
    caps.maxColorAttachments = 8;
    caps.supportsComputeShaders = true;
    caps.supportsGeometryShaders = false;
    
    std::cout << "设备能力:" << std::endl;
    std::cout << "  最大2D纹理尺寸: " << caps.maxTextureSize2D << std::endl;
    std::cout << "  最大3D纹理尺寸: " << caps.maxTextureSize3D << std::endl;
    std::cout << "  最大颜色附件数: " << caps.maxColorAttachments << std::endl;
    std::cout << "  支持计算着色器: " << (caps.supportsComputeShaders ? "是" : "否") << std::endl;
    std::cout << "  支持几何着色器: " << (caps.supportsGeometryShaders ? "是" : "否") << std::endl;
    
    std::cout << "设备能力测试完成" << std::endl;
}

int main() {
  system("chcp 65001"); // 设置控制台编码为UTF-8
    std::cout << "USG Backend 核心功能测试开始" << std::endl;
    std::cout << "========================================" << std::endl;
    
    try {
        testBackendFactory();
        testBackendManager();
        testBackendTypes();
        testBackendConfig();
        testDeviceCapabilities();
        
        std::cout << "\n========================================" << std::endl;
        std::cout << "所有测试完成！" << std::endl;
        
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    catch (...) {
        std::cerr << "测试过程中发生未知异常" << std::endl;
        return 1;
    }
}
