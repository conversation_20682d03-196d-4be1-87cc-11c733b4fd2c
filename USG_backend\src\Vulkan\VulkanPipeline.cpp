#include "VulkanBackend.h"
#include <iostream>

namespace USG
{

  // VulkanShader实现
  VulkanShader::VulkanShader(VkDevice device, VkShaderModule module, ShaderStage stage)
      : _device(device), _module(module), _stage(stage)
  {
    std::cout << "[VulkanShader] Created shader module for stage " << (int)stage << std::endl;
  }

  VulkanShader::~VulkanShader()
  {
    if (_module != VK_NULL_HANDLE)
    {
      vkDestroyShaderModule(_device, _module, nullptr);
      std::cout << "[VulkanShader] Destroyed shader module" << std::endl;
    }
  }

  // VulkanPipeline实现
  VulkanPipeline::VulkanPipeline(VkDevice device, VkPipeline pipeline, VkPipelineLayout layout)
      : _device(device), _pipeline(pipeline), _layout(layout)
  {
    std::cout << "[VulkanPipeline] Created graphics pipeline" << std::endl;
  }

  VulkanPipeline::~VulkanPipeline()
  {
    if (_pipeline != VK_NULL_HANDLE)
    {
      vkDestroyPipeline(_device, _pipeline, nullptr);
      std::cout << "[VulkanPipeline] Destroyed pipeline" << std::endl;
    }

    if (_layout != VK_NULL_HANDLE)
    {
      vkDestroyPipelineLayout(_device, _layout, nullptr);
      std::cout << "[VulkanPipeline] Destroyed pipeline layout" << std::endl;
    }
  }

} // namespace USG
