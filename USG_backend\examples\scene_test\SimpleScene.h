#pragma once

#include <USG_Backend/BackendTypes.h>
#include <memory>
#include <vector>
#include <string>

#ifdef USG_HAS_VSG
// VSG前向声明
namespace vsg {
    class Node;
    class Group;
    class MatrixTransform;
    class Geometry;
    class StateGroup;
}
#endif

namespace USG {

    // 前向声明
    class RenderBackend;
    class BackendBuffer;
    class BackendTexture;
    class BackendShader;
    class BackendPipeline;
    class BackendCommandList;

    /**
     * @brief 简单测试场景
     * 
     * 创建一个包含基本几何体的测试场景，用于验证不同后端的渲染效果
     */
    class SimpleScene {
    public:
        /**
         * @brief 场景对象类型
         */
        enum class ObjectType {
            Cube,
            Sphere,
            Plane,
            Triangle,
            Teapot
        };

        /**
         * @brief 场景对象
         */
        struct SceneObject {
            ObjectType type;
            std::string name;
            float position[3] = {0.0f, 0.0f, 0.0f};
            float rotation[3] = {0.0f, 0.0f, 0.0f};
            float scale[3] = {1.0f, 1.0f, 1.0f};
            float color[4] = {1.0f, 1.0f, 1.0f, 1.0f};
            bool visible = true;
            
            // 渲染资源
            std::shared_ptr<BackendBuffer> vertexBuffer;
            std::shared_ptr<BackendBuffer> indexBuffer;
            std::shared_ptr<BackendTexture> texture;
            uint32_t indexCount = 0;
            uint32_t vertexCount = 0;
        };

        /**
         * @brief 场景配置
         */
        struct SceneConfig {
            float clearColor[4] = {0.2f, 0.3f, 0.4f, 1.0f};
            bool enableLighting = true;
            bool enableTextures = true;
            bool enableWireframe = false;
            
            // 相机配置
            float cameraPosition[3] = {0.0f, 0.0f, 5.0f};
            float cameraTarget[3] = {0.0f, 0.0f, 0.0f};
            float cameraUp[3] = {0.0f, 1.0f, 0.0f};
            float fov = 45.0f;
            float nearPlane = 0.1f;
            float farPlane = 100.0f;
            
            // 光照配置
            float lightPosition[3] = {2.0f, 2.0f, 2.0f};
            float lightColor[3] = {1.0f, 1.0f, 1.0f};
            float ambientColor[3] = {0.2f, 0.2f, 0.2f};
        };

    public:
        SimpleScene();
        ~SimpleScene();

        /**
         * @brief 初始化场景
         * @param backend 渲染后端
         * @param config 场景配置
         * @return 是否成功
         */
        bool initialize(RenderBackend* backend, const SceneConfig& config = {});

        /**
         * @brief 清理场景
         */
        void cleanup();

        /**
         * @brief 添加场景对象
         * @param type 对象类型
         * @param name 对象名称
         * @return 对象索引
         */
        int addObject(ObjectType type, const std::string& name = "");

        /**
         * @brief 移除场景对象
         * @param index 对象索引
         */
        void removeObject(int index);

        /**
         * @brief 获取场景对象
         * @param index 对象索引
         * @return 场景对象指针
         */
        SceneObject* getObject(int index);

        /**
         * @brief 获取场景对象（通过名称）
         * @param name 对象名称
         * @return 场景对象指针
         */
        SceneObject* getObject(const std::string& name);

        /**
         * @brief 更新场景
         * @param deltaTime 时间增量
         */
        void update(double deltaTime);

        /**
         * @brief 渲染场景
         * @param commandList 命令列表
         * @param viewMatrix 视图矩阵
         * @param projMatrix 投影矩阵
         * @return 是否成功
         */
        bool render(BackendCommandList* commandList, const float viewMatrix[16], const float projMatrix[16]);

        /**
         * @brief 设置场景配置
         * @param config 场景配置
         */
        void setSceneConfig(const SceneConfig& config);

        /**
         * @brief 获取场景配置
         * @return 场景配置
         */
        const SceneConfig& getSceneConfig() const { return _config; }

        /**
         * @brief 获取场景对象列表
         * @return 场景对象列表
         */
        const std::vector<SceneObject>& getObjects() const { return _objects; }

        /**
         * @brief 切换渲染后端
         * @param newBackend 新的渲染后端
         * @return 是否成功
         */
        bool switchBackend(RenderBackend* newBackend);

#ifdef USG_HAS_VSG
        /**
         * @brief 转换为VSG场景图
         * @return VSG场景根节点
         */
        std::shared_ptr<vsg::Node> toVSGScene();

        /**
         * @brief 从VSG场景图创建
         * @param vsgRoot VSG场景根节点
         * @return 是否成功
         */
        bool fromVSGScene(std::shared_ptr<vsg::Node> vsgRoot);
#endif

        /**
         * @brief 获取渲染统计信息
         */
        struct RenderStats {
            uint32_t objectCount = 0;
            uint32_t visibleObjectCount = 0;
            uint32_t triangleCount = 0;
            uint32_t drawCalls = 0;
        };

        /**
         * @brief 获取渲染统计信息
         * @return 渲染统计信息
         */
        const RenderStats& getRenderStats() const { return _renderStats; }

    private:
        // 内部方法
        bool createShaders();
        bool createPipeline();
        bool createGeometry(ObjectType type, SceneObject& object);
        bool createCubeGeometry(SceneObject& object);
        bool createSphereGeometry(SceneObject& object);
        bool createPlaneGeometry(SceneObject& object);
        bool createTriangleGeometry(SceneObject& object);
        
        void updateMatrices();
        void calculateMVPMatrix(const SceneObject& object, const float viewMatrix[16], 
                               const float projMatrix[16], float mvpMatrix[16]);

    private:
        // 渲染后端
        RenderBackend* _backend = nullptr;

        // 场景配置
        SceneConfig _config;

        // 场景对象
        std::vector<SceneObject> _objects;

        // 渲染资源
        std::shared_ptr<BackendShader> _vertexShader;
        std::shared_ptr<BackendShader> _fragmentShader;
        std::shared_ptr<BackendPipeline> _pipeline;
        std::shared_ptr<BackendBuffer> _uniformBuffer;

        // 统计信息
        RenderStats _renderStats;

        // 状态
        bool _initialized = false;
        double _totalTime = 0.0;
    };

} // namespace USG
