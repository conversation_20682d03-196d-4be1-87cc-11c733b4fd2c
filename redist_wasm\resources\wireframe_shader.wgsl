/**
 * Wireframe shader for WebGPU
 * This shader creates a wireframe effect by using barycentric coordinates
 */

struct VertexInput {
    @location(0) position: vec3f,
    @location(1) normal: vec3f,
    @location(2) color: vec3f,
    @location(3) uv: vec2f,
}

struct VertexOutput {
    @builtin(position) position: vec4f,
    @location(0) color: vec3f,
    @location(1) normal: vec3f,
    @location(2) uv: vec2f,
    @location(3) worldPos: vec3f,
}

struct Uniforms {
    projectionMatrix: mat4x4f,
    viewMatrix: mat4x4f,
    modelMatrix: mat4x4f,
    color: vec4f,
    time: f32,
    lightDirection: vec3f,
}

@group(0) @binding(0) var<uniform> uMyUniforms: Uniforms;

@vertex
fn vs_main(in: VertexInput) -> VertexOutput {
    var out: VertexOutput;
    
    let worldPosition = uMyUniforms.modelMatrix * vec4f(in.position, 1.0);
    out.position = uMyUniforms.projectionMatrix * uMyUniforms.viewMatrix * worldPosition;
    out.worldPos = worldPosition.xyz;
    out.color = in.color;
    out.normal = (uMyUniforms.modelMatrix * vec4f(in.normal, 0.0)).xyz;
    out.uv = in.uv;
    
    return out;
}

@fragment
fn fs_main(in: VertexOutput) -> @location(0) vec4f {
    // 线框效果：使用UV坐标创建网格线
    let gridSize = 20.0;
    let lineWidth = 0.05;
    
    let grid = abs(fract(in.uv * gridSize) - 0.5) / fwidth(in.uv * gridSize);
    let line = min(grid.x, grid.y);
    let wireframe = 1.0 - min(line, 1.0);
    
    // 基础颜色
    let baseColor = vec3f(0.2, 0.6, 1.0);
    let wireColor = vec3f(1.0, 1.0, 1.0);
    
    // 混合线框和基础颜色
    let finalColor = mix(baseColor * 0.1, wireColor, wireframe);
    
    return vec4f(finalColor, 1.0);
}
