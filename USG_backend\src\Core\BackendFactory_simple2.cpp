#include <USG_Backend/BackendFactory_simple.h>
#include <iostream>
#include <algorithm>

namespace USG {

BackendFactory& BackendFactory::getInstance() {
    static BackendFactory instance;
    return instance;
}

bool BackendFactory::isBackendAvailable(BackendType type) const {
    return std::find(_availableBackends.begin(), _availableBackends.end(), type) != _availableBackends.end();
}

bool BackendFactory::isBackendAvailable(const std::string& name) const {
    return std::find(_availableBackendNames.begin(), _availableBackendNames.end(), name) != _availableBackendNames.end();
}

std::vector<BackendType> BackendFactory::getAvailableBackends() const {
    return _availableBackends;
}

std::vector<std::string> BackendFactory::getAvailableBackendNames() const {
    return _availableBackendNames;
}

BackendType BackendFactory::getDefaultBackendType() const {
    return _defaultBackendType;
}

void BackendFactory::setDefaultBackendType(BackendType type) {
    _defaultBackendType = type;
    std::cout << "[BackendFactory] Set default backend type to: " << backendTypeToString(type) << std::endl;
}

BackendType BackendFactory::detectBestBackend() const {
    // 按优先级检测最佳后端
    std::vector<BackendType> priorities = {
        BackendType::WebGPU,
        BackendType::Vulkan,
        BackendType::DirectX12,
        BackendType::OpenGL
    };
    
    for (BackendType type : priorities) {
        if (isBackendAvailable(type)) {
            std::cout << "[BackendFactory] Detected best backend: " << backendTypeToString(type) << std::endl;
            return type;
        }
    }
    
    std::cout << "[BackendFactory] No suitable backend found" << std::endl;
    return BackendType::Unknown;
}

void BackendFactory::clear() {
    _availableBackends.clear();
    _availableBackendNames.clear();
    std::cout << "[BackendFactory] Cleared all available backends" << std::endl;
}

// BackendManager实现
BackendManager& BackendManager::getInstance() {
    static BackendManager instance;
    return instance;
}

bool BackendManager::initialize(BackendType type, const BackendConfig& config) {
    if (_initialized) {
        std::cout << "[BackendManager] Already initialized with " << backendTypeToString(_currentBackendType) << std::endl;
        return true;
    }
    
    std::cout << "[BackendManager] Initializing with backend type: " << backendTypeToString(type) << std::endl;
    
    // 检查后端是否可用
    if (!BackendFactory::getInstance().isBackendAvailable(type)) {
        std::cerr << "[BackendManager] Backend type not available: " << backendTypeToString(type) << std::endl;
        return false;
    }
    
    // 模拟初始化过程
    _currentBackendType = type;
    _initialized = true;
    
    std::cout << "[BackendManager] Successfully initialized with " << backendTypeToString(type) << " (simulated)" << std::endl;
    
    // 输出配置信息
    if (!config.applicationName.empty()) {
        std::cout << "[BackendManager] Application: " << config.applicationName << std::endl;
    }
    if (config.enableValidation) {
        std::cout << "[BackendManager] Validation enabled" << std::endl;
    }
    if (config.enableDebugMarkers) {
        std::cout << "[BackendManager] Debug markers enabled" << std::endl;
    }
    
    return true;
}

bool BackendManager::initialize(const std::string& name, const BackendConfig& config) {
    if (_initialized) {
        std::cout << "[BackendManager] Already initialized" << std::endl;
        return true;
    }
    
    std::cout << "[BackendManager] Initializing with backend: " << name << std::endl;
    
    // 检查后端是否可用
    if (!BackendFactory::getInstance().isBackendAvailable(name)) {
        std::cerr << "[BackendManager] Backend not available: " << name << std::endl;
        return false;
    }
    
    // 根据名称确定类型
    BackendType type = BackendType::Unknown;
    if (name == "WebGPU") {
        type = BackendType::WebGPU;
    } else if (name == "Vulkan") {
        type = BackendType::Vulkan;
    } else if (name == "OpenGL") {
        type = BackendType::OpenGL;
    } else if (name == "DirectX12") {
        type = BackendType::DirectX12;
    } else if (name == "Metal") {
        type = BackendType::Metal;
    }
    
    return initialize(type, config);
}

void BackendManager::cleanup() {
    if (!_initialized) {
        return;
    }
    
    std::cout << "[BackendManager] Cleaning up " << backendTypeToString(_currentBackendType) << " backend..." << std::endl;
    
    _currentBackendType = BackendType::Unknown;
    _initialized = false;
    
    std::cout << "[BackendManager] Cleanup completed" << std::endl;
}

bool BackendManager::switchBackend(BackendType type, const BackendConfig& config) {
    std::cout << "[BackendManager] Switching from " << backendTypeToString(_currentBackendType) 
              << " to " << backendTypeToString(type) << std::endl;
    
    // 清理当前后端
    cleanup();
    
    // 初始化新后端
    return initialize(type, config);
}

bool BackendManager::switchBackend(const std::string& name, const BackendConfig& config) {
    std::cout << "[BackendManager] Switching to backend: " << name << std::endl;
    
    // 清理当前后端
    cleanup();
    
    // 初始化新后端
    return initialize(name, config);
}

bool BackendManager::isInitialized() const {
    return _initialized;
}

BackendType BackendManager::getCurrentBackendType() const {
    return _currentBackendType;
}

std::string BackendManager::getCurrentBackendName() const {
    return backendTypeToString(_currentBackendType);
}

void BackendManager::setErrorCallback(std::function<void(const std::string&)> callback) {
    _errorCallback = callback;
    std::cout << "[BackendManager] Error callback set" << std::endl;
}

void BackendManager::setDebugCallback(std::function<void(const std::string&)> callback) {
    _debugCallback = callback;
    std::cout << "[BackendManager] Debug callback set" << std::endl;
}

} // namespace USG
