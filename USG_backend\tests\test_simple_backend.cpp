/**
 * @file test_simple_backend.cpp
 * @brief 简单的USG Backend测试程序
 */

#include <USG_Backend/BackendTypes.h>
#include <USG_Backend/BackendFactory_simple.h>
#include <iostream>
#include <cassert>

using namespace USG;

void testBackendTypes()
{
    std::cout << "=== 测试后端类型 ===" << std::endl;

    // 测试后端类型转换
    std::cout << "WebGPU: " << backendTypeToString(BackendType::WebGPU) << std::endl;
    std::cout << "Vulkan: " << backendTypeToString(BackendType::Vulkan) << std::endl;
    std::cout << "OpenGL: " << backendTypeToString(BackendType::OpenGL) << std::endl;

    // 测试纹理格式
    std::cout << "RGBA8_UNorm: " << textureFormatToString(TextureFormat::RGBA8_UNorm) << std::endl;
    std::cout << "Depth32_Float: " << textureFormatToString(TextureFormat::Depth32_Float) << std::endl;

    // 测试格式属性
    uint32_t rgba8Size = getTextureFormatBytesPerPixel(TextureFormat::RGBA8_UNorm);
    std::cout << "RGBA8_UNorm 字节数: " << rgba8Size << std::endl;
    assert(rgba8Size == 4);

    bool isDepth = isDepthFormat(TextureFormat::Depth32_Float);
    std::cout << "Depth32_Float 是深度格式: " << (isDepth ? "是" : "否") << std::endl;
    assert(isDepth == true);

    bool isCompressed = isCompressedFormat(TextureFormat::BC1_RGBA_UNorm);
    std::cout << "BC1_RGBA_UNorm 是压缩格式: " << (isCompressed ? "是" : "否") << std::endl;
    assert(isCompressed == true);

    std::cout << "后端类型测试通过!" << std::endl
              << std::endl;
}

void testBufferDesc()
{
    std::cout << "=== 测试缓冲区描述符 ===" << std::endl;

    BufferDesc desc;
    desc.size = 1024;
    desc.usage = BufferUsage::Vertex | BufferUsage::TransferDst;
    desc.memoryProperties = MemoryProperty::DeviceLocal | MemoryProperty::HostVisible;
    desc.label = "Test Buffer";

    std::cout << "缓冲区大小: " << desc.size << " 字节" << std::endl;
    std::cout << "缓冲区标签: " << desc.label << std::endl;

    // 测试位运算
    bool hasVertexUsage = (desc.usage & BufferUsage::Vertex) != BufferUsage::None;
    bool hasUniformUsage = (desc.usage & BufferUsage::Uniform) != BufferUsage::None;

    std::cout << "包含顶点用途: " << (hasVertexUsage ? "是" : "否") << std::endl;
    std::cout << "包含Uniform用途: " << (hasUniformUsage ? "是" : "否") << std::endl;

    assert(hasVertexUsage == true);
    assert(hasUniformUsage == false);

    std::cout << "缓冲区描述符测试通过!" << std::endl
              << std::endl;
}

void testTextureDesc()
{
    std::cout << "=== 测试纹理描述符 ===" << std::endl;

    TextureDesc desc;
    desc.width = 512;
    desc.height = 512;
    desc.depth = 1;
    desc.mipLevels = 9; // log2(512) + 1
    desc.arrayLayers = 1;
    desc.format = TextureFormat::RGBA8_UNorm;
    desc.usage = TextureUsage::Sampled | TextureUsage::RenderTarget;
    desc.sampleCount = 1;
    desc.label = "Test Texture";

    std::cout << "纹理尺寸: " << desc.width << "x" << desc.height << std::endl;
    std::cout << "Mip级别: " << desc.mipLevels << std::endl;
    std::cout << "纹理格式: " << textureFormatToString(desc.format) << std::endl;
    std::cout << "纹理标签: " << desc.label << std::endl;

    // 测试用途标志
    bool hasSampledUsage = (desc.usage & TextureUsage::Sampled) != TextureUsage::None;
    bool hasStorageUsage = (desc.usage & TextureUsage::Storage) != TextureUsage::None;

    std::cout << "包含采样用途: " << (hasSampledUsage ? "是" : "否") << std::endl;
    std::cout << "包含存储用途: " << (hasStorageUsage ? "是" : "否") << std::endl;

    assert(hasSampledUsage == true);
    assert(hasStorageUsage == false);

    std::cout << "纹理描述符测试通过!" << std::endl
              << std::endl;
}

void testBackendFactory()
{
    std::cout << "=== 测试后端工厂 ===" << std::endl;

    auto &factory = BackendFactory::getInstance();

    // 测试默认后端类型
    BackendType defaultType = factory.getDefaultBackendType();
    std::cout << "默认后端类型: " << backendTypeToString(defaultType) << std::endl;

    // 设置新的默认后端类型
    factory.setDefaultBackendType(BackendType::Vulkan);
    BackendType newDefaultType = factory.getDefaultBackendType();
    std::cout << "新默认后端类型: " << backendTypeToString(newDefaultType) << std::endl;
    assert(newDefaultType == BackendType::Vulkan);

    // 恢复原来的默认类型
    factory.setDefaultBackendType(defaultType);

    // 测试最佳后端检测
    BackendType bestType = factory.detectBestBackend();
    std::cout << "检测到的最佳后端: " << backendTypeToString(bestType) << std::endl;

    // 获取可用后端列表
    auto availableBackends = factory.getAvailableBackends();
    std::cout << "可用后端数量: " << availableBackends.size() << std::endl;

    auto availableNames = factory.getAvailableBackendNames();
    std::cout << "可用后端名称数量: " << availableNames.size() << std::endl;

    std::cout << "后端工厂测试通过!" << std::endl
              << std::endl;
}

void testDeviceCapabilities()
{
    std::cout << "=== 测试设备能力 ===" << std::endl;

    DeviceCapabilities caps;
    caps.maxTextureSize2D = 8192;
    caps.maxColorAttachments = 8;
    caps.maxVertexAttributes = 16;
    caps.supportsComputeShaders = true;
    caps.supportsGeometryShaders = false;
    caps.supportsTessellationShaders = false;

    std::cout << "最大2D纹理尺寸: " << caps.maxTextureSize2D << std::endl;
    std::cout << "最大颜色附件数: " << caps.maxColorAttachments << std::endl;
    std::cout << "最大顶点属性数: " << caps.maxVertexAttributes << std::endl;
    std::cout << "支持计算着色器: " << (caps.supportsComputeShaders ? "是" : "否") << std::endl;
    std::cout << "支持几何着色器: " << (caps.supportsGeometryShaders ? "是" : "否") << std::endl;
    std::cout << "支持细分着色器: " << (caps.supportsTessellationShaders ? "是" : "否") << std::endl;

    std::cout << "设备能力测试通过!" << std::endl
              << std::endl;
}

void testRenderPassDesc()
{
    std::cout << "=== 测试渲染通道描述符 ===" << std::endl;

    RenderPassDesc desc;
    desc.label = "Test Render Pass";

    // 添加颜色附件
    RenderPassDesc::ColorAttachment colorAttachment;
    colorAttachment.texture = nullptr;    // 在实际使用中会是有效的纹理指针
    colorAttachment.clearColor[0] = 0.2f; // R
    colorAttachment.clearColor[1] = 0.3f; // G
    colorAttachment.clearColor[2] = 0.4f; // B
    colorAttachment.clearColor[3] = 1.0f; // A
    colorAttachment.loadClear = true;
    colorAttachment.storeResult = true;

    desc.colorAttachments.push_back(colorAttachment);

    // 设置深度模板附件
    desc.depthStencilAttachment.texture = nullptr; // 在实际使用中会是有效的纹理指针
    desc.depthStencilAttachment.clearDepth = 1.0f;
    desc.depthStencilAttachment.clearStencil = 0;
    desc.depthStencilAttachment.loadClearDepth = true;
    desc.depthStencilAttachment.storeDepth = true;

    std::cout << "渲染通道标签: " << desc.label << std::endl;
    std::cout << "颜色附件数量: " << desc.colorAttachments.size() << std::endl;
    std::cout << "清除颜色: (" << colorAttachment.clearColor[0] << ", "
              << colorAttachment.clearColor[1] << ", "
              << colorAttachment.clearColor[2] << ", "
              << colorAttachment.clearColor[3] << ")" << std::endl;
    std::cout << "清除深度: " << desc.depthStencilAttachment.clearDepth << std::endl;

    std::cout << "渲染通道描述符测试通过!" << std::endl
              << std::endl;
}

int main()
{
    std::cout << "开始USG Backend简单测试..." << std::endl
              << std::endl;

    try
    {
        // 运行各项测试
        testBackendTypes();
        testBufferDesc();
        testTextureDesc();
        testBackendFactory();
        testDeviceCapabilities();
        testRenderPassDesc();

        std::cout << "========================================" << std::endl;
        std::cout << "所有测试通过! ✓" << std::endl;
        std::cout << "USG Backend核心组件工作正常" << std::endl;
        std::cout << "========================================" << std::endl;

        return 0;
    }
    catch (const std::exception &e)
    {
        std::cerr << "测试异常: " << e.what() << std::endl;
        return -1;
    }
    catch (...)
    {
        std::cerr << "未知测试异常" << std::endl;
        return -1;
    }
}

/**
 * 这个简单测试验证了USG Backend的核心类型系统和基础功能：
 *
 * 1. 后端类型枚举和字符串转换
 * 2. 纹理格式属性查询
 * 3. 缓冲区和纹理描述符
 * 4. 后端工厂基础功能
 * 5. 设备能力结构
 * 6. 渲染通道描述符
 *
 * 这些测试不依赖WebGPU库，可以验证核心架构的正确性。
 */
