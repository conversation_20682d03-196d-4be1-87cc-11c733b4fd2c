cmake_minimum_required(VERSION 3.20)

project(USG 
    VERSION 1.0.0
    DESCRIPTION "WebGPU Scene Graph - A modern scene graph library based on WebGPU"
    LANGUAGES CXX
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 编译选项
if(MSVC)
    add_compile_options(/utf-8 /W4)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -pedantic)
endif()

# 构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 选项
option(USG_BUILD_EXAMPLES "Build USG examples" ON)
option(USG_BUILD_TESTS "Build USG tests" ON)
option(USG_BUILD_DOCS "Build USG documentation" OFF)
option(USG_ENABLE_VALIDATION "Enable WebGPU validation layers" OFF)

# 平台检测
if(EMSCRIPTEN)
    set(USG_PLATFORM_WASM ON)
    message(STATUS "Building for WebAssembly")
else()
    set(USG_PLATFORM_DESKTOP ON)
    message(STATUS "Building for Desktop")
endif()

# 依赖项
include(FetchContent)

# WebGPU
if(USG_PLATFORM_WASM)
    # WebAssembly使用Emscripten的WebGPU
    set(WEBGPU_BACKEND "EmDawnWebGPU" CACHE STRING "WebGPU backend")
    FetchContent_Declare(
        emdawnwebgpu
        GIT_REPOSITORY https://github.com/eliemichel/emdawnwebgpu
        GIT_TAG main
    )
    FetchContent_MakeAvailable(emdawnwebgpu)
    set(WEBGPU_TARGET emdawnwebgpu)
else()
    # 桌面使用WebGPU-distribution
    set(WEBGPU_BACKEND "WGPU" CACHE STRING "WebGPU backend")
    FetchContent_Declare(
        webgpu
        GIT_REPOSITORY https://github.com/eliemichel/WebGPU-distribution
        GIT_TAG wgpu
    )
    FetchContent_MakeAvailable(webgpu)
    set(WEBGPU_TARGET webgpu)
endif()

# GLM数学库
FetchContent_Declare(
    glm
    GIT_REPOSITORY https://github.com/g-truc/glm
    GIT_TAG *******
)
FetchContent_MakeAvailable(glm)

# GLFW (仅桌面)
if(USG_PLATFORM_DESKTOP)
    FetchContent_Declare(
        glfw
        GIT_REPOSITORY https://github.com/glfw/glfw
        GIT_TAG 3.3.8
    )
    FetchContent_MakeAvailable(glfw)
    
    # GLFW3WebGPU
    FetchContent_Declare(
        glfw3webgpu
        GIT_REPOSITORY https://github.com/eliemichel/glfw3webgpu
        GIT_TAG main
    )
    FetchContent_MakeAvailable(glfw3webgpu)
endif()

# 源文件
set(USG_CORE_SOURCES
    src/Core/Object.cpp
)

set(USG_NODES_SOURCES
    src/Nodes/Node.cpp
    src/Nodes/Group.cpp
    src/Nodes/Transform.cpp
    src/Nodes/Geometry.cpp
)

set(USG_VISITORS_SOURCES
    src/Visitors/Visitor.cpp
    src/Visitors/UpdateVisitor.cpp
    src/Visitors/CullVisitor.cpp
    src/Visitors/RenderVisitor.cpp
)

set(USG_WEBGPU_SOURCES
    src/WebGPU/WebGPUDevice.cpp
    src/WebGPU/BufferManager.cpp
    src/WebGPU/TextureManager.cpp
    src/WebGPU/ShaderManager.cpp
)

set(USG_MATH_SOURCES
    src/Math/BoundingSphere.cpp
    src/Math/Math.cpp
)

set(USG_RENDERING_SOURCES
    src/Rendering/StateSet.cpp
    src/Rendering/RenderGraph.cpp
    src/Rendering/RenderPass.cpp
    src/Rendering/Pipeline.cpp
)

set(USG_RESOURCES_SOURCES
    src/Resources/ResourceManager.cpp
    src/Resources/AssetLoader.cpp
    src/Resources/TextureLoader.cpp
    src/Resources/ModelLoader.cpp
)

set(USG_UTILS_SOURCES
    src/Utils/Logger.cpp
    src/Utils/Timer.cpp
    src/Utils/FileUtils.cpp
)

# 所有源文件
set(USG_ALL_SOURCES
    ${USG_CORE_SOURCES}
    ${USG_NODES_SOURCES}
    ${USG_VISITORS_SOURCES}
    ${USG_WEBGPU_SOURCES}
    ${USG_MATH_SOURCES}
    ${USG_RENDERING_SOURCES}
    ${USG_RESOURCES_SOURCES}
    ${USG_UTILS_SOURCES}
)

# 头文件
set(USG_HEADERS
    include/USG/USG.h
    include/USG/Core/Object.h
    include/USG/Nodes/Node.h
    include/USG/Nodes/Group.h
    include/USG/Nodes/Transform.h
    include/USG/Nodes/Geometry.h
    include/USG/Visitors/Visitor.h
    include/USG/WebGPU/WebGPUDevice.h
    include/USG/Math/Math.h
    include/USG/Math/BoundingSphere.h
    include/USG/Rendering/StateSet.h
    include/USG/Rendering/RenderGraph.h
    include/USG/Resources/ResourceManager.h
    include/USG/Utils/Logger.h
)

# 创建USG库
add_library(USG ${USG_ALL_SOURCES} ${USG_HEADERS})

# 设置目标属性
set_target_properties(USG PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    PUBLIC_HEADER "${USG_HEADERS}"
)

# 包含目录
target_include_directories(USG
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# 链接库
target_link_libraries(USG
    PUBLIC
        ${WEBGPU_TARGET}
        glm::glm
)

if(USG_PLATFORM_DESKTOP)
    target_link_libraries(USG
        PUBLIC
            glfw
            glfw3webgpu
    )
endif()

# 编译定义
target_compile_definitions(USG
    PUBLIC
        USG_VERSION_MAJOR=${PROJECT_VERSION_MAJOR}
        USG_VERSION_MINOR=${PROJECT_VERSION_MINOR}
        USG_VERSION_PATCH=${PROJECT_VERSION_PATCH}
)

if(USG_PLATFORM_WASM)
    target_compile_definitions(USG PUBLIC USG_PLATFORM_WASM)
else()
    target_compile_definitions(USG PUBLIC USG_PLATFORM_DESKTOP)
endif()

if(USG_ENABLE_VALIDATION)
    target_compile_definitions(USG PUBLIC USG_ENABLE_VALIDATION)
endif()

# WebAssembly特定设置
if(USG_PLATFORM_WASM)
    target_compile_options(USG PRIVATE
        -sUSE_WEBGPU=1
        -sASYNCIFY
    )
    target_link_options(USG PRIVATE
        -sUSE_WEBGPU=1
        -sASYNCIFY
        -sALLOW_MEMORY_GROWTH=1
        -sEXPORTED_RUNTIME_METHODS=ccall,cwrap
    )
endif()

# 示例
if(USG_BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# 测试
if(USG_BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# 文档
if(USG_BUILD_DOCS)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        add_subdirectory(docs)
    else()
        message(WARNING "Doxygen not found, documentation will not be built")
    endif()
endif()

# 安装
include(GNUInstallDirs)

install(TARGETS USG
    EXPORT USGTargets
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    PUBLIC_HEADER DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/USG
)

install(DIRECTORY include/USG
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
    FILES_MATCHING PATTERN "*.h"
)

install(EXPORT USGTargets
    FILE USGTargets.cmake
    NAMESPACE USG::
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/USG
)

# 配置文件
include(CMakePackageConfigHelpers)

configure_package_config_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/cmake/USGConfig.cmake.in
    ${CMAKE_CURRENT_BINARY_DIR}/USGConfig.cmake
    INSTALL_DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/USG
)

write_basic_package_version_file(
    ${CMAKE_CURRENT_BINARY_DIR}/USGConfigVersion.cmake
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

install(FILES
    ${CMAKE_CURRENT_BINARY_DIR}/USGConfig.cmake
    ${CMAKE_CURRENT_BINARY_DIR}/USGConfigVersion.cmake
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/USG
)

# 打包
set(CPACK_PACKAGE_NAME "USG")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY ${PROJECT_DESCRIPTION})
set(CPACK_PACKAGE_VENDOR "USG Team")
set(CPACK_RESOURCE_FILE_LICENSE "${CMAKE_CURRENT_SOURCE_DIR}/LICENSE")
set(CPACK_RESOURCE_FILE_README "${CMAKE_CURRENT_SOURCE_DIR}/README.md")

include(CPack)
