#pragma once

#include "BackendTypes.h"
#include <memory>
#include <functional>

namespace USG
{

    /**
     * @brief 渲染后端抽象接口
     *
     * 定义了所有渲染后端必须实现的统一接口，支持WebGPU、Vulkan、OpenGL等多种后端。
     * 通过这个抽象层，上层应用可以透明地切换不同的渲染后端。
     */
    class RenderBackend
    {
    public:
        virtual ~RenderBackend() = default;

        /**
         * @brief 获取后端类型
         * @return 后端类型枚举
         */
        virtual BackendType getBackendType() const = 0;

        /**
         * @brief 获取后端名称
         * @return 后端名称字符串
         */
        virtual std::string getBackendName() const = 0;

        /**
         * @brief 初始化渲染后端
         * @param config 后端配置
         * @return 是否初始化成功
         */
        virtual bool initialize(const BackendConfig &config) = 0;

        /**
         * @brief 清理渲染后端
         */
        virtual void cleanup() = 0;

        /**
         * @brief 获取设备接口
         * @return 设备接口指针
         */
        virtual BackendDevice *getDevice() = 0;

        /**
         * @brief 开始帧渲染
         */
        virtual void beginFrame() = 0;

        /**
         * @brief 结束帧渲染
         */
        virtual void endFrame() = 0;

        /**
         * @brief 呈现渲染结果
         */
        virtual void present() = 0;

        /**
         * @brief 等待设备空闲
         */
        virtual void waitIdle() = 0;

        /**
         * @brief 创建缓冲区
         * @param desc 缓冲区描述符
         * @return 缓冲区对象指针
         */
        virtual BackendBuffer *createBuffer(const BufferDesc &desc) = 0;

        /**
         * @brief 创建纹理
         * @param desc 纹理描述符
         * @return 纹理对象指针
         */
        virtual BackendTexture *createTexture(const TextureDesc &desc) = 0;

        /**
         * @brief 创建着色器
         * @param desc 着色器描述符
         * @return 着色器对象指针
         */
        virtual BackendShader *createShader(const ShaderDesc &desc) = 0;

        /**
         * @brief 创建渲染管线
         * @param desc 管线描述符
         * @return 管线对象指针
         */
        virtual BackendPipeline *createPipeline(const PipelineDesc &desc) = 0;

        /**
         * @brief 创建描述符集
         * @return 描述符集对象指针
         */
        virtual BackendDescriptorSet *createDescriptorSet() = 0;

        /**
         * @brief 创建命令列表
         * @return 命令列表对象指针
         */
        virtual BackendCommandList *createCommandList() = 0;

        /**
         * @brief 创建围栏对象
         * @return 围栏对象指针
         */
        virtual BackendFence *createFence() = 0;

        /**
         * @brief 创建信号量对象
         * @return 信号量对象指针
         */
        virtual BackendSemaphore *createSemaphore() = 0;

        /**
         * @brief 执行命令列表
         * @param cmdList 命令列表
         * @param waitSemaphores 等待的信号量列表
         * @param signalSemaphores 要发信号的信号量列表
         * @param fence 完成时要发信号的围栏
         */
        virtual void executeCommandList(BackendCommandList *cmdList,
                                        const std::vector<BackendSemaphore *> &waitSemaphores = {},
                                        const std::vector<BackendSemaphore *> &signalSemaphores = {},
                                        BackendFence *fence = nullptr) = 0;

        /**
         * @brief 销毁缓冲区
         * @param buffer 缓冲区对象
         */
        virtual void destroyBuffer(BackendBuffer *buffer) = 0;

        /**
         * @brief 销毁纹理
         * @param texture 纹理对象
         */
        virtual void destroyTexture(BackendTexture *texture) = 0;

        /**
         * @brief 销毁着色器
         * @param shader 着色器对象
         */
        virtual void destroyShader(BackendShader *shader) = 0;

        /**
         * @brief 销毁管线
         * @param pipeline 管线对象
         */
        virtual void destroyPipeline(BackendPipeline *pipeline) = 0;

        /**
         * @brief 销毁描述符集
         * @param descriptorSet 描述符集对象
         */
        virtual void destroyDescriptorSet(BackendDescriptorSet *descriptorSet) = 0;

        /**
         * @brief 销毁命令列表
         * @param cmdList 命令列表对象
         */
        virtual void destroyCommandList(BackendCommandList *cmdList) = 0;

        /**
         * @brief 销毁围栏
         * @param fence 围栏对象
         */
        virtual void destroyFence(BackendFence *fence) = 0;

        /**
         * @brief 销毁信号量
         * @param semaphore 信号量对象
         */
        virtual void destroySemaphore(BackendSemaphore *semaphore) = 0;

        /**
         * @brief 设置错误回调函数
         * @param callback 错误回调函数
         */
        virtual void setErrorCallback(std::function<void(const std::string &)> callback) = 0;

        /**
         * @brief 设置调试回调函数
         * @param callback 调试回调函数
         */
        virtual void setDebugCallback(std::function<void(const std::string &)> callback) = 0;
    };

    /**
     * @brief 后端设备抽象接口
     */
    class BackendDevice
    {
    public:
        virtual ~BackendDevice() = default;

        /**
         * @brief 获取设备名称
         * @return 设备名称
         */
        virtual std::string getDeviceName() const = 0;

        /**
         * @brief 获取设备能力
         * @return 设备能力结构
         */
        virtual DeviceCapabilities getCapabilities() const = 0;

        /**
         * @brief 映射缓冲区内存
         * @param buffer 缓冲区对象
         * @param offset 偏移量
         * @param size 大小
         * @return 映射的内存指针
         */
        virtual void *mapBuffer(BackendBuffer *buffer, size_t offset, size_t size) = 0;

        /**
         * @brief 取消映射缓冲区内存
         * @param buffer 缓冲区对象
         */
        virtual void unmapBuffer(BackendBuffer *buffer) = 0;

        /**
         * @brief 更新缓冲区数据
         * @param buffer 缓冲区对象
         * @param data 数据指针
         * @param size 数据大小
         * @param offset 偏移量
         */
        virtual void updateBuffer(BackendBuffer *buffer, const void *data, size_t size, size_t offset = 0) = 0;

        /**
         * @brief 更新纹理数据
         * @param texture 纹理对象
         * @param data 数据指针
         * @param region 更新区域
         */
        virtual void updateTexture(BackendTexture *texture, const void *data, const TextureRegion &region) = 0;

        /**
         * @brief 生成纹理Mipmap
         * @param texture 纹理对象
         */
        virtual void generateMipmaps(BackendTexture *texture) = 0;
    };

    /**
     * @brief 后端命令列表抽象接口
     */
    class BackendCommandList
    {
    public:
        virtual ~BackendCommandList() = default;

        /**
         * @brief 开始记录命令
         */
        virtual void begin() = 0;

        /**
         * @brief 结束记录命令
         */
        virtual void end() = 0;

        /**
         * @brief 重置命令列表
         */
        virtual void reset() = 0;

        /**
         * @brief 开始渲染通道
         * @param desc 渲染通道描述符
         */
        virtual void beginRenderPass(const RenderPassDesc &desc) = 0;

        /**
         * @brief 结束渲染通道
         */
        virtual void endRenderPass() = 0;

        /**
         * @brief 设置渲染管线
         * @param pipeline 管线对象
         */
        virtual void setPipeline(BackendPipeline *pipeline) = 0;

        /**
         * @brief 设置顶点缓冲区
         * @param buffer 缓冲区对象
         * @param slot 绑定槽位
         * @param offset 偏移量
         */
        virtual void setVertexBuffer(BackendBuffer *buffer, uint32_t slot, size_t offset = 0) = 0;

        /**
         * @brief 设置索引缓冲区
         * @param buffer 缓冲区对象
         * @param format 索引格式
         * @param offset 偏移量
         */
        virtual void setIndexBuffer(BackendBuffer *buffer, IndexFormat format, size_t offset = 0) = 0;

        /**
         * @brief 设置描述符集
         * @param descriptorSet 描述符集对象
         * @param slot 绑定槽位
         */
        virtual void setDescriptorSet(BackendDescriptorSet *descriptorSet, uint32_t slot) = 0;

        /**
         * @brief 绘制命令
         * @param vertexCount 顶点数量
         * @param instanceCount 实例数量
         * @param firstVertex 第一个顶点索引
         * @param firstInstance 第一个实例索引
         */
        virtual void draw(uint32_t vertexCount, uint32_t instanceCount = 1, uint32_t firstVertex = 0, uint32_t firstInstance = 0) = 0;

        /**
         * @brief 索引绘制命令
         * @param indexCount 索引数量
         * @param instanceCount 实例数量
         * @param firstIndex 第一个索引
         * @param vertexOffset 顶点偏移
         * @param firstInstance 第一个实例索引
         */
        virtual void drawIndexed(uint32_t indexCount, uint32_t instanceCount = 1, uint32_t firstIndex = 0, int32_t vertexOffset = 0, uint32_t firstInstance = 0) = 0;

        /**
         * @brief 计算调度命令
         * @param groupCountX X方向工作组数量
         * @param groupCountY Y方向工作组数量
         * @param groupCountZ Z方向工作组数量
         */
        virtual void dispatch(uint32_t groupCountX, uint32_t groupCountY = 1, uint32_t groupCountZ = 1) = 0;

        /**
         * @brief 插入内存屏障
         * @param barrier 屏障描述符
         */
        virtual void barrier(const BarrierDesc &barrier) = 0;

        /**
         * @brief 推入调试组
         * @param name 调试组名称
         */
        virtual void pushDebugGroup(const std::string &name) = 0;

        /**
         * @brief 弹出调试组
         */
        virtual void popDebugGroup() = 0;

        /**
         * @brief 插入调试标记
         * @param name 标记名称
         */
        virtual void insertDebugMarker(const std::string &name) = 0;
    };

    /**
     * @brief 后端缓冲区抽象接口
     */
    class BackendBuffer
    {
    public:
        virtual ~BackendBuffer() = default;

        /**
         * @brief 获取缓冲区大小
         * @return 缓冲区大小（字节）
         */
        virtual uint64_t getSize() const = 0;

        /**
         * @brief 获取缓冲区用途
         * @return 缓冲区用途标志
         */
        virtual BufferUsage getUsage() const = 0;

        /**
         * @brief 获取内存属性
         * @return 内存属性标志
         */
        virtual MemoryProperty getMemoryProperties() const = 0;
    };

    /**
     * @brief 后端纹理抽象接口
     */
    class BackendTexture
    {
    public:
        virtual ~BackendTexture() = default;

        /**
         * @brief 获取纹理宽度
         * @return 纹理宽度
         */
        virtual uint32_t getWidth() const = 0;

        /**
         * @brief 获取纹理高度
         * @return 纹理高度
         */
        virtual uint32_t getHeight() const = 0;

        /**
         * @brief 获取纹理深度
         * @return 纹理深度
         */
        virtual uint32_t getDepth() const = 0;

        /**
         * @brief 获取Mip级别数量
         * @return Mip级别数量
         */
        virtual uint32_t getMipLevels() const = 0;

        /**
         * @brief 获取数组层数量
         * @return 数组层数量
         */
        virtual uint32_t getArrayLayers() const = 0;

        /**
         * @brief 获取纹理格式
         * @return 纹理格式
         */
        virtual TextureFormat getFormat() const = 0;

        /**
         * @brief 获取纹理用途
         * @return 纹理用途标志
         */
        virtual TextureUsage getUsage() const = 0;
    };

    /**
     * @brief 后端着色器抽象接口
     */
    class BackendShader
    {
    public:
        virtual ~BackendShader() = default;

        /**
         * @brief 获取着色器阶段
         * @return 着色器阶段
         */
        virtual ShaderStage getStage() const = 0;

        /**
         * @brief 获取入口点名称
         * @return 入口点名称
         */
        virtual std::string getEntryPoint() const = 0;
    };

    /**
     * @brief 后端管线抽象接口
     */
    class BackendPipeline
    {
    public:
        virtual ~BackendPipeline() = default;

        /**
         * @brief 获取管线类型（图形或计算）
         * @return 管线类型
         */
        virtual bool isComputePipeline() const = 0;
    };

    /**
     * @brief 后端描述符集抽象接口
     */
    class BackendDescriptorSet
    {
    public:
        virtual ~BackendDescriptorSet() = default;

        /**
         * @brief 绑定缓冲区到描述符集
         * @param binding 绑定点
         * @param buffer 缓冲区对象
         * @param offset 偏移量
         * @param size 大小
         */
        virtual void bindBuffer(uint32_t binding, BackendBuffer *buffer, size_t offset = 0, size_t size = 0) = 0;

        /**
         * @brief 绑定纹理到描述符集
         * @param binding 绑定点
         * @param texture 纹理对象
         */
        virtual void bindTexture(uint32_t binding, BackendTexture *texture) = 0;

        /**
         * @brief 更新描述符集
         */
        virtual void update() = 0;
    };

    /**
     * @brief 后端围栏抽象接口
     */
    class BackendFence
    {
    public:
        virtual ~BackendFence() = default;

        /**
         * @brief 等待围栏
         * @param timeoutNs 超时时间（纳秒）
         * @return 是否成功等待
         */
        virtual bool wait(uint64_t timeoutNs = UINT64_MAX) = 0;

        /**
         * @brief 重置围栏
         */
        virtual void reset() = 0;

        /**
         * @brief 检查围栏状态
         * @return 是否已发信号
         */
        virtual bool isSignaled() = 0;
    };

    /**
     * @brief 后端信号量抽象接口
     */
    class BackendSemaphore
    {
    public:
        virtual ~BackendSemaphore() = default;
    };

} // namespace USG
