/*
 * Copyright © 2015 <PERSON><PERSON><PERSON>
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice (including the
 * next paragraph) shall be included in all copies or substantial
 * portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT.  IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
 * BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
 * ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

#include "wayland-client-core.h"
#include "wayland-server-core.h"

#ifdef WL_DISPLAY_SYNC
#error including wayland-client-core.h imported protocol symbols!
#endif
#ifdef WL_DISPLAY_ERROR
#error including wayland-server-core.h imported protocol symbols!
#endif

#ifdef WAYLAND_CLIENT_H
#error including wayland-client-core.h included the non-core header!
#endif
#ifdef WAYLAND_SERVER_H
#error including wayland-server-core.h included the non-core header!
#endif

#include "wayland-client.h"
#include "wayland-server.h"

#ifndef WL_DISPLAY_SYNC
#error including wayland-client.h did not import protocol symbols!
#endif
#ifndef WL_DISPLAY_ERROR
#error including wayland-server.h did not import protocol symbols!
#endif

int main(int argc, char **argv) { return 0; }
