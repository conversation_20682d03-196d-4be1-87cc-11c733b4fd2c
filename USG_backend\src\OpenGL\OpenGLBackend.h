#pragma once

#include <USG_Backend/RenderBackend.h>
#include <memory>
#include <vector>
#include <iostream>

// OpenGL headers
#ifdef _WIN32
#include <windows.h>
#include <GL/gl.h>
#include <GL/glext.h>
#else
#include <GL/gl.h>
#include <GL/glext.h>
#endif

// OpenGL function pointers (简化版本)
#ifndef GL_ARRAY_BUFFER
#define GL_ARRAY_BUFFER 0x8892
#endif
#ifndef GL_ELEMENT_ARRAY_BUFFER
#define GL_ELEMENT_ARRAY_BUFFER 0x8893
#endif
#ifndef GL_STATIC_DRAW
#define GL_STATIC_DRAW 0x88E4
#endif
#ifndef GL_VERTEX_SHADER
#define GL_VERTEX_SHADER 0x8B31
#endif
#ifndef GL_FRAGMENT_SHADER
#define GL_FRAGMENT_SHADER 0x8B30
#endif
#ifndef GL_COMPILE_STATUS
#define GL_COMPILE_STATUS 0x8B81
#endif

// 简化的OpenGL函数声明（在实际项目中应该使用GLAD或类似的加载器）
extern "C"
{
    void glGenBuffers(int n, unsigned int *buffers);
    void glBindBuffer(unsigned int target, unsigned int buffer);
    void glBufferData(unsigned int target, size_t size, const void *data, unsigned int usage);
    void glDeleteBuffers(int n, const unsigned int *buffers);
    unsigned int glCreateShader(unsigned int type);
    void glShaderSource(unsigned int shader, int count, const char *const *string, const int *length);
    void glCompileShader(unsigned int shader);
    void glGetShaderiv(unsigned int shader, unsigned int pname, int *params);
    void glGetShaderInfoLog(unsigned int shader, int bufSize, int *length, char *infoLog);
    void glDeleteShader(unsigned int shader);
    unsigned int glCreateProgram(void);
    void glDeleteProgram(unsigned int program);
    void glUseProgram(unsigned int program);
    void glEnableVertexAttribArray(unsigned int index);
    void glVertexAttribPointer(unsigned int index, int size, unsigned int type, unsigned char normalized, int stride, const void *pointer);
    void glGenTextures(int n, unsigned int *textures);
    void glBindTexture(unsigned int target, unsigned int texture);
    void glTexImage2D(unsigned int target, int level, int internalformat, int width, int height, int border, unsigned int format, unsigned int type, const void *pixels);
    void glTexParameteri(unsigned int target, unsigned int pname, int param);
    void glDeleteTextures(int n, const unsigned int *textures);
    void glDrawArrays(unsigned int mode, int first, int count);
    void glDrawElements(unsigned int mode, int count, unsigned int type, const void *indices);
}

namespace USG
{

    /**
     * @brief 简单的OpenGL渲染后端实现
     *
     * 提供基本的OpenGL渲染功能
     */
    class OpenGLBackend : public RenderBackend
    {
    public:
        OpenGLBackend();
        ~OpenGLBackend() override;

        // RenderBackend接口实现
        bool initialize(const BackendConfig &config) override;
        void cleanup() override;
        BackendType getBackendType() const override { return BackendType::OpenGL; }
        std::string getBackendName() const override { return "OpenGL"; }

        // 资源创建
        BackendBuffer *createBuffer(const BufferDesc &desc) override;
        BackendTexture *createTexture(const TextureDesc &desc) override;
        BackendShader *createShader(const ShaderDesc &desc) override;
        BackendPipeline *createPipeline(const PipelineDesc &desc) override;
        BackendDescriptorSet *createDescriptorSet() override;
        BackendCommandList *createCommandList() override;
        BackendFence *createFence() override;
        BackendSemaphore *createSemaphore() override;

        // 命令执行
        void executeCommandList(BackendCommandList *cmdList,
                                const std::vector<BackendSemaphore *> &waitSemaphores = {},
                                const std::vector<BackendSemaphore *> &signalSemaphores = {},
                                BackendFence *fence = nullptr) override;

        // 资源销毁
        void destroyBuffer(BackendBuffer *buffer) override;
        void destroyTexture(BackendTexture *texture) override;
        void destroyShader(BackendShader *shader) override;
        void destroyPipeline(BackendPipeline *pipeline) override;
        void destroyDescriptorSet(BackendDescriptorSet *descriptorSet) override;
        void destroyCommandList(BackendCommandList *cmdList) override;
        void destroyFence(BackendFence *fence) override;
        void destroySemaphore(BackendSemaphore *semaphore) override;

        // 其他抽象方法
        BackendDevice *getDevice() override { return nullptr; }
        void beginFrame() override;
        void endFrame() override;
        void present() override {}
        void waitIdle() override {}
        void setErrorCallback(std::function<void(const std::string &)> callback) override {}
        void setDebugCallback(std::function<void(const std::string &)> callback) override {}

    private:
        BackendConfig _config;
        bool _initialized = false;
        uint32_t _resourceCounter = 0;

        // OpenGL状态
        GLuint _currentProgram = 0;
        GLuint _currentVAO = 0;
        GLuint _currentVBO = 0;
        GLuint _currentEBO = 0;
    };

    // 简单的OpenGL资源类
    class OpenGLBuffer : public BackendBuffer
    {
    public:
        OpenGLBuffer(uint32_t id, const BufferDesc &desc);
        ~OpenGLBuffer() override;

        uint64_t getSize() const override { return _desc.size; }
        BufferUsage getUsage() const override { return _desc.usage; }
        MemoryProperty getMemoryProperties() const override { return _desc.memoryProperties; }

        uint32_t getId() const { return _id; }

    private:
        uint32_t _id;
        BufferDesc _desc;
    };

    class OpenGLTexture : public BackendTexture
    {
    public:
        OpenGLTexture(uint32_t id, const TextureDesc &desc);
        ~OpenGLTexture() override;

        uint32_t getWidth() const override { return _desc.width; }
        uint32_t getHeight() const override { return _desc.height; }
        uint32_t getDepth() const override { return _desc.depth; }
        uint32_t getMipLevels() const override { return _desc.mipLevels; }
        uint32_t getArrayLayers() const override { return _desc.arrayLayers; }
        TextureFormat getFormat() const override { return _desc.format; }
        TextureUsage getUsage() const override { return _desc.usage; }

        uint32_t getId() const { return _id; }

    private:
        uint32_t _id;
        TextureDesc _desc;
    };

    class OpenGLShader : public BackendShader
    {
    public:
        OpenGLShader(uint32_t id, const ShaderDesc &desc);
        ~OpenGLShader() override;

        ShaderStage getStage() const override { return _desc.stage; }
        std::string getEntryPoint() const override { return _desc.entryPoint; }

        uint32_t getId() const { return _id; }

    private:
        uint32_t _id;
        ShaderDesc _desc;
    };

    class OpenGLPipeline : public BackendPipeline
    {
    public:
        OpenGLPipeline(uint32_t id, const PipelineDesc &desc);
        ~OpenGLPipeline() override;

        bool isComputePipeline() const override { return _desc.isComputePipeline; }

        uint32_t getId() const { return _id; }

    private:
        uint32_t _id;
        PipelineDesc _desc;
    };

    class OpenGLDescriptorSet : public BackendDescriptorSet
    {
    public:
        OpenGLDescriptorSet();
        ~OpenGLDescriptorSet() override;

        void bindBuffer(uint32_t binding, BackendBuffer *buffer, size_t offset = 0, size_t size = 0) override {}
        void bindTexture(uint32_t binding, BackendTexture *texture) override {}
        void update() override {}
    };

    class OpenGLCommandList : public BackendCommandList
    {
    public:
        OpenGLCommandList();
        ~OpenGLCommandList() override;

        void begin() override {}
        void end() override {}
        void reset() override {}
        void beginRenderPass(const RenderPassDesc &desc) override;
        void endRenderPass() override {}
        void setPipeline(BackendPipeline *pipeline) override;
        void setVertexBuffer(BackendBuffer *buffer, uint32_t binding = 0, size_t offset = 0) override;
        void setIndexBuffer(BackendBuffer *buffer, IndexFormat format, uint64_t offset = 0) override;
        void setDescriptorSet(BackendDescriptorSet *descriptorSet, uint32_t set = 0) override {}
        void draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance) override;
        void drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t vertexOffset, uint32_t firstInstance) override;
        void dispatch(uint32_t groupCountX, uint32_t groupCountY, uint32_t groupCountZ) override {}
        void barrier(const BarrierDesc &barrier) override {}
        void pushDebugGroup(const std::string &name) override {}
        void popDebugGroup() override {}
        void insertDebugMarker(const std::string &name) override {}

    private:
        bool _inRenderPass = false;
        GLuint _currentProgram = 0;
        GLuint _currentVAO = 0;
    };

    class OpenGLFence : public BackendFence
    {
    public:
        OpenGLFence() = default;
        ~OpenGLFence() override = default;

        bool wait(uint64_t timeoutNs = UINT64_MAX) override { return true; }
        void reset() override {}
        bool isSignaled() override { return true; }
    };

    class OpenGLSemaphore : public BackendSemaphore
    {
    public:
        OpenGLSemaphore() = default;
        ~OpenGLSemaphore() override = default;
    };

} // namespace USG
