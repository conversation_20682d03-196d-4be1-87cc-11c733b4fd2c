#pragma once

// 使用GLM作为数学库基础
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtc/quaternion.hpp>
#include <glm/gtc/type_ptr.hpp>
#include <glm/gtx/transform.hpp>

namespace usg {

// 基础数学类型别名
using vec2 = glm::vec2;
using vec3 = glm::vec3;
using vec4 = glm::vec4;

using ivec2 = glm::ivec2;
using ivec3 = glm::ivec3;
using ivec4 = glm::ivec4;

using uvec2 = glm::uvec2;
using uvec3 = glm::uvec3;
using uvec4 = glm::uvec4;

using mat2 = glm::mat2;
using mat3 = glm::mat3;
using mat4 = glm::mat4;

using quat = glm::quat;

// 常用数学常量
namespace math {
    constexpr float PI = 3.14159265358979323846f;
    constexpr float TWO_PI = 2.0f * PI;
    constexpr float HALF_PI = 0.5f * PI;
    constexpr float DEG_TO_RAD = PI / 180.0f;
    constexpr float RAD_TO_DEG = 180.0f / PI;
    constexpr float EPSILON = 1e-6f;
}

// 数学工具函数
namespace math {
    
    /**
     * @brief 角度转弧度
     * @param degrees 角度
     * @return 弧度
     */
    inline float radians(float degrees) {
        return degrees * DEG_TO_RAD;
    }
    
    /**
     * @brief 弧度转角度
     * @param radians 弧度
     * @return 角度
     */
    inline float degrees(float radians) {
        return radians * RAD_TO_DEG;
    }
    
    /**
     * @brief 线性插值
     * @param a 起始值
     * @param b 结束值
     * @param t 插值参数 [0, 1]
     * @return 插值结果
     */
    template<typename T>
    inline T lerp(const T& a, const T& b, float t) {
        return a + t * (b - a);
    }
    
    /**
     * @brief 球面线性插值
     * @param a 起始四元数
     * @param b 结束四元数
     * @param t 插值参数 [0, 1]
     * @return 插值结果
     */
    inline quat slerp(const quat& a, const quat& b, float t) {
        return glm::slerp(a, b, t);
    }
    
    /**
     * @brief 限制值在指定范围内
     * @param value 值
     * @param min_val 最小值
     * @param max_val 最大值
     * @return 限制后的值
     */
    template<typename T>
    inline T clamp(const T& value, const T& min_val, const T& max_val) {
        return glm::clamp(value, min_val, max_val);
    }
    
    /**
     * @brief 计算两点距离
     * @param a 点A
     * @param b 点B
     * @return 距离
     */
    inline float distance(const vec3& a, const vec3& b) {
        return glm::distance(a, b);
    }
    
    /**
     * @brief 计算向量长度
     * @param v 向量
     * @return 长度
     */
    inline float length(const vec3& v) {
        return glm::length(v);
    }
    
    /**
     * @brief 归一化向量
     * @param v 向量
     * @return 归一化后的向量
     */
    inline vec3 normalize(const vec3& v) {
        return glm::normalize(v);
    }
    
    /**
     * @brief 向量点积
     * @param a 向量A
     * @param b 向量B
     * @return 点积
     */
    inline float dot(const vec3& a, const vec3& b) {
        return glm::dot(a, b);
    }
    
    /**
     * @brief 向量叉积
     * @param a 向量A
     * @param b 向量B
     * @return 叉积
     */
    inline vec3 cross(const vec3& a, const vec3& b) {
        return glm::cross(a, b);
    }
    
    /**
     * @brief 创建平移矩阵
     * @param translation 平移向量
     * @return 平移矩阵
     */
    inline mat4 translate(const vec3& translation) {
        return glm::translate(translation);
    }
    
    /**
     * @brief 创建旋转矩阵
     * @param angle 旋转角度（弧度）
     * @param axis 旋转轴
     * @return 旋转矩阵
     */
    inline mat4 rotate(float angle, const vec3& axis) {
        return glm::rotate(angle, axis);
    }
    
    /**
     * @brief 创建缩放矩阵
     * @param scale 缩放向量
     * @return 缩放矩阵
     */
    inline mat4 scale(const vec3& scale) {
        return glm::scale(scale);
    }
    
    /**
     * @brief 创建视图矩阵
     * @param eye 眼睛位置
     * @param center 目标位置
     * @param up 上方向
     * @return 视图矩阵
     */
    inline mat4 lookAt(const vec3& eye, const vec3& center, const vec3& up) {
        return glm::lookAt(eye, center, up);
    }
    
    /**
     * @brief 创建透视投影矩阵
     * @param fovy 视野角度（弧度）
     * @param aspect 宽高比
     * @param near 近平面距离
     * @param far 远平面距离
     * @return 透视投影矩阵
     */
    inline mat4 perspective(float fovy, float aspect, float near, float far) {
        return glm::perspective(fovy, aspect, near, far);
    }
    
    /**
     * @brief 创建正交投影矩阵
     * @param left 左边界
     * @param right 右边界
     * @param bottom 下边界
     * @param top 上边界
     * @param near 近平面距离
     * @param far 远平面距离
     * @return 正交投影矩阵
     */
    inline mat4 ortho(float left, float right, float bottom, float top, float near, float far) {
        return glm::ortho(left, right, bottom, top, near, far);
    }
    
    /**
     * @brief 矩阵求逆
     * @param m 矩阵
     * @return 逆矩阵
     */
    inline mat4 inverse(const mat4& m) {
        return glm::inverse(m);
    }
    
    /**
     * @brief 矩阵转置
     * @param m 矩阵
     * @return 转置矩阵
     */
    inline mat4 transpose(const mat4& m) {
        return glm::transpose(m);
    }
    
    /**
     * @brief 从四元数创建旋转矩阵
     * @param q 四元数
     * @return 旋转矩阵
     */
    inline mat4 mat4_cast(const quat& q) {
        return glm::mat4_cast(q);
    }
    
    /**
     * @brief 从旋转矩阵提取四元数
     * @param m 旋转矩阵
     * @return 四元数
     */
    inline quat quat_cast(const mat4& m) {
        return glm::quat_cast(m);
    }
    
    /**
     * @brief 检查浮点数是否相等
     * @param a 浮点数A
     * @param b 浮点数B
     * @param epsilon 误差范围
     * @return 是否相等
     */
    inline bool equal(float a, float b, float epsilon = EPSILON) {
        return std::abs(a - b) < epsilon;
    }
    
    /**
     * @brief 检查向量是否相等
     * @param a 向量A
     * @param b 向量B
     * @param epsilon 误差范围
     * @return 是否相等
     */
    inline bool equal(const vec3& a, const vec3& b, float epsilon = EPSILON) {
        return glm::all(glm::lessThan(glm::abs(a - b), vec3(epsilon)));
    }
    
} // namespace math

} // namespace usg
