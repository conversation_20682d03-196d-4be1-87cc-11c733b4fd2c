# USG设计讨论记录

## 项目背景

基于当前WebGPU地球渲染项目的成功实践，我们提出了USG (WebGPU Scene Graph) 框架设计，旨在创建一个现代化的场景图渲染引擎，能够平替VSG和OSG，同时充分发挥WebGPU的跨平台优势。

## 设计目标

### 核心目标
1. **API兼容性**: 提供与VSG/OSG相似的API接口，便于现有代码迁移
2. **跨平台支持**: 统一支持桌面端(Windows/Linux/macOS)和WebAssembly平台
3. **现代化架构**: 基于现代C++17/20特性和WebGPU API设计
4. **高性能**: 充分利用WebGPU的现代图形特性和并行计算能力

### 技术特点
- **场景图模式**: 层次化的节点树结构组织3D场景
- **访问者模式**: 通过Visitor实现场景遍历和操作
- **智能指针**: 自动内存管理和引用计数
- **渲染图**: 现代化的渲染管线组织和优化
- **异步加载**: 支持异步资源加载和流式处理

## 架构设计

### 分层架构
```
┌─────────────────────────────────────────────────────────┐
│                   应用层 (Application Layer)              │
│  Application, Viewer, Scene Manager, Camera Controller   │
├─────────────────────────────────────────────────────────┤
│                  场景图层 (Scene Graph Layer)             │
│    Root Node, Group Node, Transform Node, Geometry Node  │
├─────────────────────────────────────────────────────────┤
│                 访问者系统 (Visitor System)               │
│   Cull Visitor, Render Visitor, Update Visitor          │
├─────────────────────────────────────────────────────────┤
│                渲染抽象层 (Render Abstraction)            │
│   Render Graph, Command Buffer, Render Pass, Pipeline    │
├─────────────────────────────────────────────────────────┤
│               WebGPU封装层 (WebGPU Wrapper)               │
│   Device Manager, Buffer Manager, Texture Manager       │
├─────────────────────────────────────────────────────────┤
│                资源管理 (Resource Management)             │
│   Resource Cache, Asset Loader, Memory Pool              │
└─────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. 对象模型 (Object Model)
- **Object**: 基础对象类，提供类型信息、引用计数、序列化支持
- **ref_ptr**: 智能指针类型，自动引用计数管理
- **make_ref**: 便利函数，创建智能指针对象

#### 2. 场景图节点 (Scene Graph Nodes)
- **Node**: 场景图节点基类，支持访问者模式和包围盒计算
- **Group**: 组节点，可以包含子节点
- **Transform**: 变换节点，支持矩阵变换
- **Geometry**: 几何节点，包含渲染数据

#### 3. 访问者系统 (Visitor System)
- **Visitor**: 访问者基类，定义遍历接口
- **UpdateVisitor**: 更新访问者，处理动画和动态内容
- **CullVisitor**: 裁剪访问者，执行视锥体裁剪
- **RenderVisitor**: 渲染访问者，生成渲染命令

#### 4. WebGPU封装 (WebGPU Wrapper)
- **WebGPUDevice**: 设备管理，封装WebGPU设备和队列
- **BufferManager**: 缓冲区管理，统一管理GPU缓冲区
- **TextureManager**: 纹理管理，处理纹理创建和缓存
- **ShaderManager**: 着色器管理，编译和缓存着色器

## 兼容性设计

### VSG兼容性
```cpp
// USG原生API
auto root = usg::make_ref<usg::Group>();
auto transform = usg::make_ref<usg::Transform>();

// VSG兼容性
namespace vsg = usg;
auto root = vsg::Group::create();
auto transform = vsg::Transform::create();
```

### OSG兼容性
```cpp
// OSG风格API
namespace osg = usg;
osg::ref_ptr<osg::Group> root = new osg::Group;
osg::ref_ptr<osg::Transform> transform = new osg::Transform;
root->addChild(transform);
```

## 地球渲染系统重构

### 当前系统问题
- **单体化设计**: Application类承担过多职责
- **紧耦合**: 渲染逻辑与业务逻辑混合
- **缺乏抽象**: 直接使用WebGPU API，缺少中间抽象层
- **有限扩展性**: 难以支持复杂场景图和多种渲染模式

### USG重构方案
```cpp
class EarthNode : public usg::Transform {
public:
    void updateRotation(double deltaTime);
    void setTileSystem(ref_ptr<TileSystem> tileSystem);
    void setRadius(double radius);
    
private:
    ref_ptr<TileSystem> _tileSystem;
    ref_ptr<SphereGeometry> _sphereGeometry;
    double _radius = 6371000.0;
};

class TileSystem : public usg::Object {
public:
    void loadTile(int x, int y, int z);
    void updateLOD(const vec3& viewPoint);
    void cullTiles(const Frustum& frustum);
    
private:
    std::unordered_map<TileKey, ref_ptr<Tile>> _tiles;
    ref_ptr<TileLoader> _loader;
};
```

## 实施计划

### 阶段一: 基础框架 (4周)
- [ ] 实现Object模型和ref_ptr智能指针系统
- [ ] 创建Node层次结构和基本场景图
- [ ] 实现Visitor模式和基本遍历
- [ ] 建立WebGPU设备抽象层

### 阶段二: 渲染系统 (6周)
- [ ] 实现StateSet和状态管理
- [ ] 创建渲染管线和命令缓冲区
- [ ] 实现CullVisitor和RenderVisitor
- [ ] 建立资源管理系统

### 阶段三: 高级功能 (6周)
- [ ] 光照和着色器系统
- [ ] 纹理和材质高级功能
- [ ] 动画和变换系统
- [ ] 性能优化和批处理

### 阶段四: 兼容性和优化 (4周)
- [ ] VSG/OSG API兼容层
- [ ] 性能测试和优化
- [ ] 文档和示例
- [ ] 单元测试覆盖

## 技术优势

### 架构优势
- **模块化设计**: 清晰的职责分离和接口抽象
- **可扩展性**: 支持插件式扩展和自定义节点
- **性能优化**: 基于现代图形API的高性能设计
- **内存安全**: 智能指针和RAII管理

### WebGPU优势
- **现代API**: 基于现代图形API设计理念
- **跨平台**: 统一的API支持多平台
- **高性能**: 低开销的GPU访问
- **并行计算**: 内置计算着色器支持

### 兼容性优势
- **VSG接口兼容**: 可以作为VSG的直接替代
- **OSG接口兼容**: 支持OSG风格的API调用
- **渐进式迁移**: 支持逐步迁移现有代码
- **WebAssembly优化**: 针对Web环境的特殊优化

## 风险评估

### 技术风险
- **WebGPU成熟度**: WebGPU仍在发展中，API可能变化
- **性能对比**: 需要验证与原生Vulkan/OpenGL的性能差异
- **兼容性复杂度**: 完全兼容VSG/OSG API可能增加复杂度

### 缓解措施
- **渐进式开发**: 分阶段实施，及时调整
- **性能基准**: 建立性能测试基准，持续监控
- **API抽象**: 通过抽象层隔离WebGPU API变化
- **社区参与**: 积极参与WebGPU社区，跟踪标准发展

## 结论

USG框架设计提供了一个现代化、高性能、跨平台的场景图渲染引擎解决方案。通过充分利用WebGPU的优势，同时保持与现有VSG/OSG生态的兼容性，USG能够为3D图形应用开发提供强大而灵活的基础设施。

该设计不仅解决了当前地球渲染系统的架构问题，还为未来的扩展和优化奠定了坚实的基础。通过分阶段的实施计划，我们可以逐步构建一个完整、可靠、高性能的WebGPU场景图引擎。
