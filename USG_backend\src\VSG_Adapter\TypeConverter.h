#pragma once

#include <USG_Backend/BackendTypes.h>
#include <VSG_Adapter/VSG_Device.h>

namespace USG {

/**
 * @brief 类型转换器类
 * 
 * 负责在Vulkan API类型和USG Backend类型之间进行转换
 */
class TypeConverter {
public:
    TypeConverter() = default;
    ~TypeConverter() = default;
    
    // Vulkan到USG的转换
    BufferDesc convertVkBufferCreateInfo(const VkBufferCreateInfo* pCreateInfo);
    TextureDesc convertVkImageCreateInfo(const VkImageCreateInfo* pCreateInfo);
    PipelineDesc convertVkGraphicsPipelineCreateInfo(const VkGraphicsPipelineCreateInfo* pCreateInfo);
    ShaderDesc convertVkShaderModuleCreateInfo(const VkShaderModuleCreateInfo* pCreateInfo, ShaderStage stage);
    
    // 枚举转换
    BufferUsage convertVkBufferUsage(VkBufferUsageFlags usage);
    TextureUsage convertVkImageUsage(VkImageUsageFlags usage);
    TextureFormat convertVkFormat(uint32_t format);
    IndexFormat convertVkIndexType(VkIndexType indexType);
    PrimitiveTopology convertVkPrimitiveTopology(uint32_t topology);
    CompareFunction convertVkCompareOp(uint32_t compareOp);
    BlendOperation convertVkBlendOp(uint32_t blendOp);
    BlendFactor convertVkBlendFactor(uint32_t blendFactor);
    MemoryProperty convertVkMemoryPropertyFlags(VkMemoryPropertyFlags flags);
    
    // USG到Vulkan的转换（用于查询和验证）
    VkBufferUsageFlags convertToVkBufferUsage(BufferUsage usage);
    VkImageUsageFlags convertToVkImageUsage(TextureUsage usage);
    uint32_t convertToVkFormat(TextureFormat format);
    VkIndexType convertToVkIndexType(IndexFormat format);
    
private:
    // 辅助方法
    uint32_t calculateMipLevels(uint32_t width, uint32_t height, uint32_t depth = 1);
    bool isDepthStencilFormat(uint32_t format);
    bool isCompressedFormat(uint32_t format);
    uint32_t getFormatBlockSize(uint32_t format);
};

} // namespace USG
