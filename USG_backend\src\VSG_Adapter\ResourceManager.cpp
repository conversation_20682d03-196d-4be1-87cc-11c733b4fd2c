#include "ResourceManager.h"
#include <iostream>

namespace USG {

ResourceManager::ResourceManager() {
    std::cout << "[ResourceManager] Created resource manager" << std::endl;
}

ResourceManager::~ResourceManager() {
    cleanup();
    std::cout << "[ResourceManager] Destroyed resource manager" << std::endl;
}

// 缓冲区管理
void ResourceManager::registerBuffer(void* handle, BackendBuffer* buffer) {
    registerResource(_buffers, handle, buffer);
}

void ResourceManager::unregisterBuffer(void* handle) {
    unregisterResource(_buffers, handle);
}

BackendBuffer* ResourceManager::getBuffer(void* handle) {
    return getResource(_buffers, handle);
}

// 纹理管理
void ResourceManager::registerTexture(void* handle, BackendTexture* texture) {
    registerResource(_textures, handle, texture);
}

void ResourceManager::unregisterTexture(void* handle) {
    unregisterResource(_textures, handle);
}

BackendTexture* ResourceManager::getTexture(void* handle) {
    return getResource(_textures, handle);
}

// 管线管理
void ResourceManager::registerPipeline(void* handle, BackendPipeline* pipeline) {
    registerResource(_pipelines, handle, pipeline);
}

void ResourceManager::unregisterPipeline(void* handle) {
    unregisterResource(_pipelines, handle);
}

BackendPipeline* ResourceManager::getPipeline(void* handle) {
    return getResource(_pipelines, handle);
}

// 着色器管理
void ResourceManager::registerShader(void* handle, BackendShader* shader) {
    registerResource(_shaders, handle, shader);
}

void ResourceManager::unregisterShader(void* handle) {
    unregisterResource(_shaders, handle);
}

BackendShader* ResourceManager::getShader(void* handle) {
    return getResource(_shaders, handle);
}

// 描述符集管理
void ResourceManager::registerDescriptorSet(void* handle, BackendDescriptorSet* descriptorSet) {
    registerResource(_descriptorSets, handle, descriptorSet);
}

void ResourceManager::unregisterDescriptorSet(void* handle) {
    unregisterResource(_descriptorSets, handle);
}

BackendDescriptorSet* ResourceManager::getDescriptorSet(void* handle) {
    return getResource(_descriptorSets, handle);
}

// 同步对象管理
void ResourceManager::registerFence(void* handle, BackendFence* fence) {
    registerResource(_fences, handle, fence);
}

void ResourceManager::unregisterFence(void* handle) {
    unregisterResource(_fences, handle);
}

BackendFence* ResourceManager::getFence(void* handle) {
    return getResource(_fences, handle);
}

void ResourceManager::registerSemaphore(void* handle, BackendSemaphore* semaphore) {
    registerResource(_semaphores, handle, semaphore);
}

void ResourceManager::unregisterSemaphore(void* handle) {
    unregisterResource(_semaphores, handle);
}

BackendSemaphore* ResourceManager::getSemaphore(void* handle) {
    return getResource(_semaphores, handle);
}

// 命令列表管理
void ResourceManager::registerCommandList(void* handle, BackendCommandList* commandList) {
    registerResource(_commandLists, handle, commandList);
}

void ResourceManager::unregisterCommandList(void* handle) {
    unregisterResource(_commandLists, handle);
}

BackendCommandList* ResourceManager::getCommandList(void* handle) {
    return getResource(_commandLists, handle);
}

// 统计信息
size_t ResourceManager::getBufferCount() const {
    std::lock_guard<std::mutex> lock(_mutex);
    return _buffers.size();
}

size_t ResourceManager::getTextureCount() const {
    std::lock_guard<std::mutex> lock(_mutex);
    return _textures.size();
}

size_t ResourceManager::getPipelineCount() const {
    std::lock_guard<std::mutex> lock(_mutex);
    return _pipelines.size();
}

size_t ResourceManager::getShaderCount() const {
    std::lock_guard<std::mutex> lock(_mutex);
    return _shaders.size();
}

size_t ResourceManager::getDescriptorSetCount() const {
    std::lock_guard<std::mutex> lock(_mutex);
    return _descriptorSets.size();
}

size_t ResourceManager::getFenceCount() const {
    std::lock_guard<std::mutex> lock(_mutex);
    return _fences.size();
}

size_t ResourceManager::getSemaphoreCount() const {
    std::lock_guard<std::mutex> lock(_mutex);
    return _semaphores.size();
}

size_t ResourceManager::getCommandListCount() const {
    std::lock_guard<std::mutex> lock(_mutex);
    return _commandLists.size();
}

void ResourceManager::cleanup() {
    std::lock_guard<std::mutex> lock(_mutex);
    
    std::cout << "[ResourceManager] Cleaning up resources..." << std::endl;
    
    // 清理所有映射表
    _buffers.clear();
    _textures.clear();
    _pipelines.clear();
    _shaders.clear();
    _descriptorSets.clear();
    _fences.clear();
    _semaphores.clear();
    _commandLists.clear();
    
    std::cout << "[ResourceManager] All resources cleaned up" << std::endl;
}

void ResourceManager::printStatistics() const {
    std::lock_guard<std::mutex> lock(_mutex);
    
    std::cout << "[ResourceManager] Resource Statistics:" << std::endl;
    std::cout << "  Buffers: " << _buffers.size() << std::endl;
    std::cout << "  Textures: " << _textures.size() << std::endl;
    std::cout << "  Pipelines: " << _pipelines.size() << std::endl;
    std::cout << "  Shaders: " << _shaders.size() << std::endl;
    std::cout << "  Descriptor Sets: " << _descriptorSets.size() << std::endl;
    std::cout << "  Fences: " << _fences.size() << std::endl;
    std::cout << "  Semaphores: " << _semaphores.size() << std::endl;
    std::cout << "  Command Lists: " << _commandLists.size() << std::endl;
    
    size_t totalResources = _buffers.size() + _textures.size() + _pipelines.size() + 
                           _shaders.size() + _descriptorSets.size() + _fences.size() + 
                           _semaphores.size() + _commandLists.size();
    std::cout << "  Total Resources: " << totalResources << std::endl;
}

} // namespace USG
