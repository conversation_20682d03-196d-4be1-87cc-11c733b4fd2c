#include "VulkanBackend.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

namespace USG
{

  VulkanBuffer::VulkanBuffer(VkDevice device, VkBuffer buffer, VkDeviceMemory memory, uint64_t size, BufferUsage usage, MemoryProperty memoryProperty)
      : _device(device), _buffer(buffer), _memory(memory), _size(size), _usage(usage), _memoryProperty(memoryProperty)
  {
    std::cout << "[VulkanBuffer] Created buffer with size: " << size << " bytes" << std::endl;
  }

  VulkanBuffer::~VulkanBuffer()
  {
    if (_mappedData)
    {
      unmap();
    }

    if (_buffer != VK_NULL_HANDLE)
    {
      vkDestroyBuffer(_device, _buffer, nullptr);
    }

    if (_memory != VK_NULL_HANDLE)
    {
      vkFreeMemory(_device, _memory, nullptr);
    }

    std::cout << "[VulkanBuffer] Destroyed buffer" << std::endl;
  }

  void *VulkanBuffer::map()
  {
    if (_mappedData)
    {
      return _mappedData;
    }

    if (vkMapMemory(_device, _memory, 0, _size, 0, &_mappedData) != VK_SUCCESS)
    {
      throw std::runtime_error("Failed to map buffer memory");
    }

    return _mappedData;
  }

  void VulkanBuffer::unmap()
  {
    if (_mappedData)
    {
      vkUnmapMemory(_device, _memory);
      _mappedData = nullptr;
    }
  }

  void VulkanBuffer::updateData(const void *data, size_t size, size_t offset)
  {
    if (offset + size > _size)
    {
      throw std::runtime_error("Buffer update out of bounds");
    }

    void *mappedData = map();
    std::memcpy(static_cast<char *>(mappedData) + offset, data, size);

    // 如果内存不是HOST_COHERENT，需要flush
    // 这里简化处理，假设内存是coherent的
  }

} // namespace USG
