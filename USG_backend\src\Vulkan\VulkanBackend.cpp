#include "VulkanBackend.h"
#include <iostream>
#include <stdexcept>
#include <set>
#include <algorithm>
#include <cstring>

#ifdef USG_PLATFORM_DESKTOP
#define GLFW_INCLUDE_VULKAN
#include <GLFW/glfw3.h>
#endif

namespace USG
{

    VulkanBackend::VulkanBackend()
    {
    }

    VulkanBackend::~VulkanBackend()
    {
        cleanup();
    }

    bool VulkanBackend::initialize(const BackendConfig &config)
    {
        if (_initialized)
        {
            return true;
        }

        std::cout << "[VulkanBackend] Initializing Vulkan backend..." << std::endl;

        try
        {
            // 获取窗口句柄
#ifdef USG_PLATFORM_DESKTOP
            _window = static_cast<GLFWwindow *>(config.nativeWindow);
            if (!_window)
            {
                std::cerr << "[VulkanBackend] Invalid window handle" << std::endl;
                return false;
            }
#endif

            // 真正的Vulkan初始化过程
            if (!createInstance())
            {
                std::cerr << "[VulkanBackend] Failed to create Vulkan instance" << std::endl;
                return false;
            }

            // 创建Surface
#ifdef USG_PLATFORM_DESKTOP
            if (glfwCreateWindowSurface(_instance, _window, nullptr, &_surface) != VK_SUCCESS)
            {
                std::cerr << "[VulkanBackend] Failed to create window surface" << std::endl;
                return false;
            }
#endif

            if (!selectPhysicalDevice())
            {
                std::cerr << "[VulkanBackend] Failed to select physical device" << std::endl;
                return false;
            }

            if (!createLogicalDevice())
            {
                std::cerr << "[VulkanBackend] Failed to create logical device" << std::endl;
                return false;
            }

            if (!createSwapchain(config))
            {
                std::cerr << "[VulkanBackend] Failed to create swapchain" << std::endl;
                return false;
            }

            if (!createRenderPass())
            {
                std::cerr << "[VulkanBackend] Failed to create render pass" << std::endl;
                return false;
            }

            if (!createFramebuffers())
            {
                std::cerr << "[VulkanBackend] Failed to create framebuffers" << std::endl;
                return false;
            }

            if (!createCommandPool())
            {
                std::cerr << "[VulkanBackend] Failed to create command pool" << std::endl;
                return false;
            }

            if (!createSyncObjects())
            {
                std::cerr << "[VulkanBackend] Failed to create sync objects" << std::endl;
                return false;
            }

            _initialized = true;
            std::cout << "[VulkanBackend] Vulkan backend initialized successfully" << std::endl;

            // 输出真实的Vulkan信息
            printDeviceInfo();

            return true;
        }
        catch (const std::exception &e)
        {
            std::cerr << "[VulkanBackend] Failed to initialize: " << e.what() << std::endl;
            return false;
        }
    }

    void VulkanBackend::cleanup()
    {
        if (!_initialized)
        {
            return;
        }

        std::cout << "[VulkanBackend] Cleaning up Vulkan backend..." << std::endl;

        // 等待设备空闲
        if (_device != VK_NULL_HANDLE)
        {
            vkDeviceWaitIdle(_device);
        }

        // 清理同步对象
        for (size_t i = 0; i < MAX_FRAMES_IN_FLIGHT; i++)
        {
            if (_renderFinishedSemaphores.size() > i && _renderFinishedSemaphores[i] != VK_NULL_HANDLE)
            {
                vkDestroySemaphore(_device, _renderFinishedSemaphores[i], nullptr);
            }
            if (_imageAvailableSemaphores.size() > i && _imageAvailableSemaphores[i] != VK_NULL_HANDLE)
            {
                vkDestroySemaphore(_device, _imageAvailableSemaphores[i], nullptr);
            }
            if (_inFlightFences.size() > i && _inFlightFences[i] != VK_NULL_HANDLE)
            {
                vkDestroyFence(_device, _inFlightFences[i], nullptr);
            }
        }

        // 清理命令池
        if (_commandPool != VK_NULL_HANDLE)
        {
            vkDestroyCommandPool(_device, _commandPool, nullptr);
            _commandPool = VK_NULL_HANDLE;
        }

        // 清理帧缓冲区
        for (auto framebuffer : _swapchainFramebuffers)
        {
            if (framebuffer != VK_NULL_HANDLE)
            {
                vkDestroyFramebuffer(_device, framebuffer, nullptr);
            }
        }
        _swapchainFramebuffers.clear();

        // 清理渲染通道
        if (_renderPass != VK_NULL_HANDLE)
        {
            vkDestroyRenderPass(_device, _renderPass, nullptr);
            _renderPass = VK_NULL_HANDLE;
        }

        // 清理图像视图
        for (auto imageView : _swapchainImageViews)
        {
            if (imageView != VK_NULL_HANDLE)
            {
                vkDestroyImageView(_device, imageView, nullptr);
            }
        }
        _swapchainImageViews.clear();

        // 清理交换链
        if (_swapchain != VK_NULL_HANDLE)
        {
            vkDestroySwapchainKHR(_device, _swapchain, nullptr);
            _swapchain = VK_NULL_HANDLE;
        }

        // 清理逻辑设备
        if (_device != VK_NULL_HANDLE)
        {
            vkDestroyDevice(_device, nullptr);
            _device = VK_NULL_HANDLE;
        }

        // 清理Surface
        if (_surface != VK_NULL_HANDLE)
        {
            vkDestroySurfaceKHR(_instance, _surface, nullptr);
            _surface = VK_NULL_HANDLE;
        }

        // 清理实例
        if (_instance != VK_NULL_HANDLE)
        {
            vkDestroyInstance(_instance, nullptr);
            _instance = VK_NULL_HANDLE;
        }

        _initialized = false;
        std::cout << "[VulkanBackend] Vulkan backend cleaned up" << std::endl;
    }

    // 资源创建方法 - 实际实现
    BackendBuffer *VulkanBackend::createBuffer(const BufferDesc &desc)
    {
        std::cout << "[VulkanBackend] Creating buffer (size: " << desc.size << " bytes)" << std::endl;

        VkBufferCreateInfo bufferInfo{};
        bufferInfo.sType = VK_STRUCTURE_TYPE_BUFFER_CREATE_INFO;
        bufferInfo.size = desc.size;

        // 根据用途设置缓冲区用法
        switch (desc.usage)
        {
        case BufferUsage::Vertex:
            bufferInfo.usage = VK_BUFFER_USAGE_VERTEX_BUFFER_BIT;
            break;
        case BufferUsage::Index:
            bufferInfo.usage = VK_BUFFER_USAGE_INDEX_BUFFER_BIT;
            break;
        case BufferUsage::Uniform:
            bufferInfo.usage = VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT;
            break;
        default:
            bufferInfo.usage = VK_BUFFER_USAGE_VERTEX_BUFFER_BIT;
            break;
        }

        bufferInfo.sharingMode = VK_SHARING_MODE_EXCLUSIVE;

        VkBuffer buffer;
        if (vkCreateBuffer(_device, &bufferInfo, nullptr, &buffer) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to create buffer" << std::endl;
            return nullptr;
        }

        // 分配内存
        VkMemoryRequirements memRequirements;
        vkGetBufferMemoryRequirements(_device, buffer, &memRequirements);

        VkMemoryAllocateInfo allocInfo{};
        allocInfo.sType = VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_INFO;
        allocInfo.allocationSize = memRequirements.size;
        allocInfo.memoryTypeIndex = findMemoryType(memRequirements.memoryTypeBits,
                                                   VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT);

        VkDeviceMemory bufferMemory;
        if (vkAllocateMemory(_device, &allocInfo, nullptr, &bufferMemory) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to allocate buffer memory" << std::endl;
            vkDestroyBuffer(_device, buffer, nullptr);
            return nullptr;
        }

        vkBindBufferMemory(_device, buffer, bufferMemory, 0);

        // 注意：BufferDesc没有初始数据字段，如果需要可以后续通过map/unmap操作填充数据

        std::cout << "[VulkanBackend] Created buffer " << (++_nextResourceId) << std::endl;
        return new VulkanBuffer(_device, buffer, bufferMemory, desc.size, desc.usage, MemoryProperty::HostVisible);
    }

    BackendTexture *VulkanBackend::createTexture(const TextureDesc &desc)
    {
        std::cout << "[VulkanBackend] Creating texture (placeholder)" << std::endl;
        // TODO: 实现Vulkan纹理创建
        return nullptr;
    }

    BackendShader *VulkanBackend::createShader(const ShaderDesc &desc)
    {
        std::cout << "[VulkanBackend] Creating shader (stage: " << (int)desc.stage << ")" << std::endl;

        // 创建着色器模块
        VkShaderModuleCreateInfo createInfo{};
        createInfo.sType = VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO;

        // 对于简单测试，使用硬编码的SPIR-V字节码
        std::vector<uint32_t> spirvCode;
        if (desc.stage == ShaderStage::Vertex)
        {
            // 简单的顶点着色器SPIR-V（传递位置和颜色）
            spirvCode = getDefaultVertexShaderSPIRV();
        }
        else if (desc.stage == ShaderStage::Fragment)
        {
            // 简单的片段着色器SPIR-V（输出红色）
            spirvCode = getDefaultFragmentShaderSPIRV();
        }

        if (spirvCode.empty())
        {
            std::cerr << "[VulkanBackend] No SPIR-V code available for shader stage" << std::endl;
            return nullptr;
        }

        createInfo.codeSize = spirvCode.size() * sizeof(uint32_t);
        createInfo.pCode = spirvCode.data();

        VkShaderModule shaderModule;
        if (vkCreateShaderModule(_device, &createInfo, nullptr, &shaderModule) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to create shader module" << std::endl;
            return nullptr;
        }

        std::cout << "[VulkanBackend] Created shader " << (++_nextResourceId) << std::endl;
        return new VulkanShader(_device, shaderModule, desc.stage);
    }

    BackendPipeline *VulkanBackend::createPipeline(const PipelineDesc &desc)
    {
        std::cout << "[VulkanBackend] Creating graphics pipeline" << std::endl;

        // 创建简单的图形管线
        VkGraphicsPipelineCreateInfo pipelineInfo{};
        pipelineInfo.sType = VK_STRUCTURE_TYPE_GRAPHICS_PIPELINE_CREATE_INFO;

        // 着色器阶段
        std::vector<VkPipelineShaderStageCreateInfo> shaderStages;

        if (desc.vertexShader)
        {
            VulkanShader *vulkanVS = static_cast<VulkanShader *>(desc.vertexShader);
            VkShaderModule vertexModule = vulkanVS->getShaderModule();

            if (vertexModule == VK_NULL_HANDLE)
            {
                std::cerr << "[VulkanBackend] ERROR: Vertex shader module is VK_NULL_HANDLE!" << std::endl;
                return nullptr;
            }

            VkPipelineShaderStageCreateInfo vertShaderStageInfo{};
            vertShaderStageInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
            vertShaderStageInfo.stage = VK_SHADER_STAGE_VERTEX_BIT;
            vertShaderStageInfo.module = vertexModule;
            vertShaderStageInfo.pName = "main";
            shaderStages.push_back(vertShaderStageInfo);

            std::cout << "[VulkanBackend] Added vertex shader stage: " << vertexModule << std::endl;
        }

        if (desc.fragmentShader)
        {
            VulkanShader *vulkanFS = static_cast<VulkanShader *>(desc.fragmentShader);
            VkShaderModule fragmentModule = vulkanFS->getShaderModule();

            if (fragmentModule == VK_NULL_HANDLE)
            {
                std::cerr << "[VulkanBackend] ERROR: Fragment shader module is VK_NULL_HANDLE!" << std::endl;
                return nullptr;
            }

            VkPipelineShaderStageCreateInfo fragShaderStageInfo{};
            fragShaderStageInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
            fragShaderStageInfo.stage = VK_SHADER_STAGE_FRAGMENT_BIT;
            fragShaderStageInfo.module = fragmentModule;
            fragShaderStageInfo.pName = "main";
            shaderStages.push_back(fragShaderStageInfo);

            std::cout << "[VulkanBackend] Added fragment shader stage: " << fragmentModule << std::endl;
        }

        pipelineInfo.stageCount = static_cast<uint32_t>(shaderStages.size());
        pipelineInfo.pStages = shaderStages.data();

        // 顶点输入（简化版本）
        VkPipelineVertexInputStateCreateInfo vertexInputInfo{};
        vertexInputInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_VERTEX_INPUT_STATE_CREATE_INFO;
        vertexInputInfo.vertexBindingDescriptionCount = 0;
        vertexInputInfo.vertexAttributeDescriptionCount = 0;
        pipelineInfo.pVertexInputState = &vertexInputInfo;

        // 输入装配
        VkPipelineInputAssemblyStateCreateInfo inputAssembly{};
        inputAssembly.sType = VK_STRUCTURE_TYPE_PIPELINE_INPUT_ASSEMBLY_STATE_CREATE_INFO;
        inputAssembly.topology = VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST;
        inputAssembly.primitiveRestartEnable = VK_FALSE;
        pipelineInfo.pInputAssemblyState = &inputAssembly;

        // 视口和裁剪
        VkViewport viewport{};
        viewport.x = 0.0f;
        viewport.y = 0.0f;
        viewport.width = (float)_swapchainExtent.width;
        viewport.height = (float)_swapchainExtent.height;
        viewport.minDepth = 0.0f;
        viewport.maxDepth = 1.0f;

        VkRect2D scissor{};
        scissor.offset = {0, 0};
        scissor.extent = _swapchainExtent;

        VkPipelineViewportStateCreateInfo viewportState{};
        viewportState.sType = VK_STRUCTURE_TYPE_PIPELINE_VIEWPORT_STATE_CREATE_INFO;
        viewportState.viewportCount = 1;
        viewportState.pViewports = &viewport;
        viewportState.scissorCount = 1;
        viewportState.pScissors = &scissor;
        pipelineInfo.pViewportState = &viewportState;

        // 光栅化
        VkPipelineRasterizationStateCreateInfo rasterizer{};
        rasterizer.sType = VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_STATE_CREATE_INFO;
        rasterizer.depthClampEnable = VK_FALSE;
        rasterizer.rasterizerDiscardEnable = VK_FALSE;
        rasterizer.polygonMode = VK_POLYGON_MODE_FILL;
        rasterizer.lineWidth = 1.0f;
        rasterizer.cullMode = VK_CULL_MODE_BACK_BIT;
        rasterizer.frontFace = VK_FRONT_FACE_CLOCKWISE;
        rasterizer.depthBiasEnable = VK_FALSE;
        pipelineInfo.pRasterizationState = &rasterizer;

        // 多重采样
        VkPipelineMultisampleStateCreateInfo multisampling{};
        multisampling.sType = VK_STRUCTURE_TYPE_PIPELINE_MULTISAMPLE_STATE_CREATE_INFO;
        multisampling.sampleShadingEnable = VK_FALSE;
        multisampling.rasterizationSamples = VK_SAMPLE_COUNT_1_BIT;
        pipelineInfo.pMultisampleState = &multisampling;

        // 深度和模板测试
        VkPipelineDepthStencilStateCreateInfo depthStencil{};
        depthStencil.sType = VK_STRUCTURE_TYPE_PIPELINE_DEPTH_STENCIL_STATE_CREATE_INFO;
        depthStencil.depthTestEnable = VK_FALSE;
        depthStencil.depthWriteEnable = VK_FALSE;
        depthStencil.depthCompareOp = VK_COMPARE_OP_LESS;
        depthStencil.depthBoundsTestEnable = VK_FALSE;
        depthStencil.stencilTestEnable = VK_FALSE;
        pipelineInfo.pDepthStencilState = &depthStencil;

        // 颜色混合
        VkPipelineColorBlendAttachmentState colorBlendAttachment{};
        colorBlendAttachment.colorWriteMask = VK_COLOR_COMPONENT_R_BIT | VK_COLOR_COMPONENT_G_BIT | VK_COLOR_COMPONENT_B_BIT | VK_COLOR_COMPONENT_A_BIT;
        colorBlendAttachment.blendEnable = VK_FALSE;

        VkPipelineColorBlendStateCreateInfo colorBlending{};
        colorBlending.sType = VK_STRUCTURE_TYPE_PIPELINE_COLOR_BLEND_STATE_CREATE_INFO;
        colorBlending.logicOpEnable = VK_FALSE;
        colorBlending.attachmentCount = 1;
        colorBlending.pAttachments = &colorBlendAttachment;
        pipelineInfo.pColorBlendState = &colorBlending;

        // 管线布局
        VkPipelineLayoutCreateInfo pipelineLayoutInfo{};
        pipelineLayoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;

        VkPipelineLayout pipelineLayout;
        if (vkCreatePipelineLayout(_device, &pipelineLayoutInfo, nullptr, &pipelineLayout) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to create pipeline layout" << std::endl;
            return nullptr;
        }

        pipelineInfo.layout = pipelineLayout;
        pipelineInfo.renderPass = _renderPass;
        pipelineInfo.subpass = 0;

        VkPipeline graphicsPipeline;
        VkResult result = vkCreateGraphicsPipelines(_device, VK_NULL_HANDLE, 1, &pipelineInfo, nullptr, &graphicsPipeline);
        if (result != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to create graphics pipeline, VkResult: " << result << std::endl;

            // 详细诊断信息
            std::cerr << "[VulkanBackend] Pipeline creation details:" << std::endl;
            std::cerr << "  - Shader stages: " << pipelineInfo.stageCount << std::endl;
            std::cerr << "  - Render pass: " << pipelineInfo.renderPass << std::endl;
            std::cerr << "  - Pipeline layout: " << pipelineInfo.layout << std::endl;
            std::cerr << "  - Subpass: " << pipelineInfo.subpass << std::endl;

            // 检查着色器模块
            for (uint32_t i = 0; i < pipelineInfo.stageCount; ++i)
            {
                const auto &stage = pipelineInfo.pStages[i];
                std::cerr << "  - Shader stage " << i << ": " << stage.stage << ", module: " << stage.module << std::endl;
            }

            vkDestroyPipelineLayout(_device, pipelineLayout, nullptr);
            return nullptr;
        }

        std::cout << "[VulkanBackend] Created pipeline " << (++_nextResourceId) << std::endl;
        return new VulkanPipeline(_device, graphicsPipeline, pipelineLayout);
    }

    BackendDescriptorSet *VulkanBackend::createDescriptorSet()
    {
        std::cout << "[VulkanBackend] Creating descriptor set (placeholder)" << std::endl;
        // TODO: 实现Vulkan描述符集创建
        return nullptr;
    }

    BackendCommandList *VulkanBackend::createCommandList()
    {
        // 分配命令缓冲区
        VkCommandBufferAllocateInfo allocInfo{};
        allocInfo.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_ALLOCATE_INFO;
        allocInfo.commandPool = _commandPool;
        allocInfo.level = VK_COMMAND_BUFFER_LEVEL_PRIMARY;
        allocInfo.commandBufferCount = 1;

        VkCommandBuffer commandBuffer;
        if (vkAllocateCommandBuffers(_device, &allocInfo, &commandBuffer) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to allocate command buffer" << std::endl;
            return nullptr;
        }

        std::cout << "[VulkanBackend] Created command list " << (++_nextResourceId) << std::endl;
        return new VulkanCommandList(_device, commandBuffer, this);
    }

    BackendFence *VulkanBackend::createFence()
    {
        // TODO: 实现Vulkan栅栏创建
        return nullptr;
    }

    BackendSemaphore *VulkanBackend::createSemaphore()
    {
        // TODO: 实现Vulkan信号量创建
        return nullptr;
    }

    void VulkanBackend::executeCommandList(BackendCommandList *cmdList,
                                           const std::vector<BackendSemaphore *> &waitSemaphores,
                                           const std::vector<BackendSemaphore *> &signalSemaphores,
                                           BackendFence *fence)
    {
        if (!cmdList)
        {
            std::cerr << "[VulkanBackend] Cannot execute null command list" << std::endl;
            return;
        }

        VulkanCommandList *vulkanCmdList = static_cast<VulkanCommandList *>(cmdList);
        VkCommandBuffer commandBuffer = vulkanCmdList->getCommandBuffer();

        std::cout << "[VulkanBackend] Executing Vulkan command list" << std::endl;

        // 提交命令缓冲区到图形队列
        VkSubmitInfo submitInfo{};
        submitInfo.sType = VK_STRUCTURE_TYPE_SUBMIT_INFO;

        // 等待信号量（如果有的话）
        std::vector<VkSemaphore> waitVkSemaphores;
        std::vector<VkPipelineStageFlags> waitStages;

        // 简化处理：使用图像可用信号量
        if (_currentFrame < _imageAvailableSemaphores.size())
        {
            waitVkSemaphores.push_back(_imageAvailableSemaphores[_currentFrame]);
            waitStages.push_back(VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT);
        }

        submitInfo.waitSemaphoreCount = static_cast<uint32_t>(waitVkSemaphores.size());
        submitInfo.pWaitSemaphores = waitVkSemaphores.data();
        submitInfo.pWaitDstStageMask = waitStages.data();

        submitInfo.commandBufferCount = 1;
        submitInfo.pCommandBuffers = &commandBuffer;

        // 信号信号量
        std::vector<VkSemaphore> signalVkSemaphores;
        if (_currentFrame < _renderFinishedSemaphores.size())
        {
            signalVkSemaphores.push_back(_renderFinishedSemaphores[_currentFrame]);
        }

        submitInfo.signalSemaphoreCount = static_cast<uint32_t>(signalVkSemaphores.size());
        submitInfo.pSignalSemaphores = signalVkSemaphores.data();

        // 提交到队列
        VkFence submitFence = VK_NULL_HANDLE;
        if (_currentFrame < _inFlightFences.size())
        {
            submitFence = _inFlightFences[_currentFrame];
        }

        if (vkQueueSubmit(_graphicsQueue, 1, &submitInfo, submitFence) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to submit draw command buffer" << std::endl;
            return;
        }

        std::cout << "[VulkanBackend] Command list submitted successfully" << std::endl;
    }

    void VulkanBackend::beginFrame()
    {
        // 等待上一帧完成
        vkWaitForFences(_device, 1, &_inFlightFences[_currentFrame], VK_TRUE, UINT64_MAX);

        // 获取下一个交换链图像
        VkResult result = vkAcquireNextImageKHR(_device, _swapchain, UINT64_MAX,
                                                _imageAvailableSemaphores[_currentFrame], VK_NULL_HANDLE, &_imageIndex);

        if (result == VK_ERROR_OUT_OF_DATE_KHR)
        {
            // 交换链过期，需要重建
            std::cout << "[VulkanBackend] Swapchain out of date, need to recreate" << std::endl;
            return;
        }
        else if (result != VK_SUCCESS && result != VK_SUBOPTIMAL_KHR)
        {
            std::cerr << "[VulkanBackend] Failed to acquire swap chain image" << std::endl;
            return;
        }

        // 重置栅栏
        vkResetFences(_device, 1, &_inFlightFences[_currentFrame]);

        std::cout << "[VulkanBackend] Begin frame " << _currentFrame << ", image " << _imageIndex << std::endl;
    }

    void VulkanBackend::endFrame()
    {
        std::cout << "[VulkanBackend] End frame " << _currentFrame << std::endl;
        // endFrame主要用于标记帧结束，实际提交在present中进行
    }

    void VulkanBackend::present()
    {
        // 呈现到交换链
        VkPresentInfoKHR presentInfo{};
        presentInfo.sType = VK_STRUCTURE_TYPE_PRESENT_INFO_KHR;

        VkSemaphore signalSemaphores[] = {_renderFinishedSemaphores[_currentFrame]};
        presentInfo.waitSemaphoreCount = 1;
        presentInfo.pWaitSemaphores = signalSemaphores;

        VkSwapchainKHR swapChains[] = {_swapchain};
        presentInfo.swapchainCount = 1;
        presentInfo.pSwapchains = swapChains;
        presentInfo.pImageIndices = &_imageIndex;
        presentInfo.pResults = nullptr;

        VkResult result = vkQueuePresentKHR(_presentQueue, &presentInfo);

        if (result == VK_ERROR_OUT_OF_DATE_KHR || result == VK_SUBOPTIMAL_KHR)
        {
            std::cout << "[VulkanBackend] Swapchain out of date or suboptimal" << std::endl;
        }
        else if (result != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to present swap chain image" << std::endl;
        }

        // 移动到下一帧
        _currentFrame = (_currentFrame + 1) % MAX_FRAMES_IN_FLIGHT;

        std::cout << "[VulkanBackend] Present frame completed" << std::endl;
    }

    // 资源销毁方法 - 占位符实现
    void VulkanBackend::destroyBuffer(BackendBuffer *buffer)
    {
        delete buffer;
    }

    void VulkanBackend::destroyTexture(BackendTexture *texture)
    {
        delete texture;
    }

    void VulkanBackend::destroyShader(BackendShader *shader)
    {
        delete shader;
    }

    void VulkanBackend::destroyPipeline(BackendPipeline *pipeline)
    {
        delete pipeline;
    }

    void VulkanBackend::destroyDescriptorSet(BackendDescriptorSet *descriptorSet)
    {
        delete descriptorSet;
    }

    void VulkanBackend::destroyCommandList(BackendCommandList *cmdList)
    {
        delete cmdList;
    }

    void VulkanBackend::destroyFence(BackendFence *fence)
    {
        delete fence;
    }

    void VulkanBackend::destroySemaphore(BackendSemaphore *semaphore)
    {
        delete semaphore;
    }

    // VulkanCommandList实现
    VulkanCommandList::VulkanCommandList(VkDevice device, VkCommandBuffer commandBuffer, VulkanBackend *backend)
        : _device(device), _commandBuffer(commandBuffer), _backend(backend)
    {
    }

    VulkanCommandList::~VulkanCommandList()
    {
        // 命令缓冲区由命令池管理，不需要单独销毁
    }

    void VulkanCommandList::begin()
    {
        if (_isRecording)
        {
            std::cerr << "[VulkanCommandList] Command buffer is already recording" << std::endl;
            return;
        }

        VkCommandBufferBeginInfo beginInfo{};
        beginInfo.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO;
        beginInfo.flags = 0;
        beginInfo.pInheritanceInfo = nullptr;

        if (vkBeginCommandBuffer(_commandBuffer, &beginInfo) != VK_SUCCESS)
        {
            std::cerr << "[VulkanCommandList] Failed to begin recording command buffer" << std::endl;
            return;
        }

        _isRecording = true;
        std::cout << "[VulkanCommandList] Begin command recording" << std::endl;
    }

    void VulkanCommandList::end()
    {
        if (!_isRecording)
        {
            std::cerr << "[VulkanCommandList] Command buffer is not recording" << std::endl;
            return;
        }

        if (vkEndCommandBuffer(_commandBuffer) != VK_SUCCESS)
        {
            std::cerr << "[VulkanCommandList] Failed to record command buffer" << std::endl;
            return;
        }

        _isRecording = false;
        std::cout << "[VulkanCommandList] End command recording" << std::endl;
    }

    void VulkanCommandList::reset()
    {
        if (_isRecording)
        {
            std::cerr << "[VulkanCommandList] Cannot reset command buffer while recording" << std::endl;
            return;
        }

        if (vkResetCommandBuffer(_commandBuffer, 0) != VK_SUCCESS)
        {
            std::cerr << "[VulkanCommandList] Failed to reset command buffer" << std::endl;
            return;
        }

        _currentRenderPass = VK_NULL_HANDLE;
        std::cout << "[VulkanCommandList] Reset command list" << std::endl;
    }

    void VulkanCommandList::beginRenderPass(const RenderPassDesc &desc)
    {
        if (!_isRecording)
        {
            std::cerr << "[VulkanCommandList] Cannot begin render pass: command buffer not recording" << std::endl;
            return;
        }

        // 注意：这里需要从desc中获取实际的Vulkan对象
        // 为了简化，我们假设有一个全局的渲染通道和帧缓冲区
        // 在完整实现中，应该从desc中获取这些信息

        VkRenderPassBeginInfo renderPassInfo{};
        renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_BEGIN_INFO;
        renderPassInfo.renderPass = _backend->getRenderPass();
        renderPassInfo.framebuffer = _backend->getCurrentFramebuffer();

        // 设置渲染区域
        renderPassInfo.renderArea.offset = {0, 0};
        renderPassInfo.renderArea.extent = _backend->getSwapchainExtent();

        // 设置清除值
        VkClearValue clearColor = {{{desc.clearColor[0], desc.clearColor[1], desc.clearColor[2], desc.clearColor[3]}}};
        renderPassInfo.clearValueCount = 1;
        renderPassInfo.pClearValues = &clearColor;

        // 验证参数
        if (renderPassInfo.renderPass == VK_NULL_HANDLE)
        {
            std::cerr << "[VulkanCommandList] ERROR: renderPass is VK_NULL_HANDLE!" << std::endl;
            return;
        }
        if (renderPassInfo.framebuffer == VK_NULL_HANDLE)
        {
            std::cerr << "[VulkanCommandList] ERROR: framebuffer is VK_NULL_HANDLE!" << std::endl;
            return;
        }

        std::cout << "[VulkanCommandList] About to begin render pass..." << std::endl;
        std::cout << "[VulkanCommandList] RenderPass: " << renderPassInfo.renderPass << std::endl;
        std::cout << "[VulkanCommandList] Framebuffer: " << renderPassInfo.framebuffer << std::endl;
        std::cout << "[VulkanCommandList] Extent: " << renderPassInfo.renderArea.extent.width << "x" << renderPassInfo.renderArea.extent.height << std::endl;

        // 开始渲染通道
        vkCmdBeginRenderPass(_commandBuffer, &renderPassInfo, VK_SUBPASS_CONTENTS_INLINE);
        _currentRenderPass = renderPassInfo.renderPass;

        std::cout << "[VulkanCommandList] Begin render pass SUCCESS" << std::endl;
        std::cout << "[VulkanCommandList] Clear color: (" << desc.clearColor[0] << ", "
                  << desc.clearColor[1] << ", " << desc.clearColor[2] << ", " << desc.clearColor[3] << ")" << std::endl;
    }

    void VulkanCommandList::endRenderPass()
    {
        if (!_isRecording)
        {
            std::cerr << "[VulkanCommandList] Cannot end render pass: command buffer not recording" << std::endl;
            return;
        }

        if (_currentRenderPass != VK_NULL_HANDLE)
        {
            vkCmdEndRenderPass(_commandBuffer);
            _currentRenderPass = VK_NULL_HANDLE;
            std::cout << "[VulkanCommandList] End render pass" << std::endl;
        }
        else
        {
            std::cout << "[VulkanCommandList] No active render pass to end" << std::endl;
        }
    }

    void VulkanCommandList::setPipeline(BackendPipeline *pipeline)
    {
        std::cout << "[VulkanCommandList] setPipeline called" << std::endl;

        if (!_isRecording)
        {
            std::cerr << "[VulkanCommandList] Cannot set pipeline: command buffer not recording" << std::endl;
            return;
        }

        if (!pipeline)
        {
            std::cerr << "[VulkanCommandList] Cannot set pipeline: pipeline is null" << std::endl;
            return;
        }

        VulkanPipeline *vulkanPipeline = static_cast<VulkanPipeline *>(pipeline);
        VkPipeline vkPipeline = vulkanPipeline->getVkPipeline();

        if (vkPipeline == VK_NULL_HANDLE)
        {
            std::cerr << "[VulkanCommandList] Cannot set pipeline: VkPipeline is VK_NULL_HANDLE" << std::endl;
            return;
        }

        std::cout << "[VulkanCommandList] About to bind pipeline: " << vkPipeline << std::endl;
        std::cout << "[VulkanCommandList] Command buffer: " << _commandBuffer << std::endl;
        std::cout << "[VulkanCommandList] Current render pass: " << _currentRenderPass << std::endl;

        // 验证管线对象
        if (vkPipeline == VK_NULL_HANDLE)
        {
            std::cerr << "[VulkanCommandList] ERROR: VkPipeline is VK_NULL_HANDLE!" << std::endl;
            return;
        }

        // 验证命令缓冲区
        if (_commandBuffer == VK_NULL_HANDLE)
        {
            std::cerr << "[VulkanCommandList] ERROR: Command buffer is VK_NULL_HANDLE!" << std::endl;
            return;
        }

        // 验证渲染通道状态
        if (_currentRenderPass == VK_NULL_HANDLE)
        {
            std::cerr << "[VulkanCommandList] ERROR: No active render pass!" << std::endl;
            return;
        }

        std::cout << "[VulkanCommandList] All validations passed, calling vkCmdBindPipeline..." << std::endl;
        vkCmdBindPipeline(_commandBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS, vkPipeline);
        std::cout << "[VulkanCommandList] Set graphics pipeline SUCCESS" << std::endl;
    }

    void VulkanCommandList::setVertexBuffer(BackendBuffer *buffer, uint32_t slot, size_t offset)
    {
        if (!_isRecording)
        {
            std::cerr << "[VulkanCommandList] Cannot set vertex buffer: command buffer not recording" << std::endl;
            return;
        }

        if (buffer)
        {
            VulkanBuffer *vulkanBuffer = static_cast<VulkanBuffer *>(buffer);
            VkBuffer vertexBuffers[] = {vulkanBuffer->getVkBuffer()};
            VkDeviceSize offsets[] = {offset};
            vkCmdBindVertexBuffers(_commandBuffer, slot, 1, vertexBuffers, offsets);
            std::cout << "[VulkanCommandList] Set vertex buffer at slot " << slot << std::endl;
        }
    }

    void VulkanCommandList::setIndexBuffer(BackendBuffer *buffer, IndexFormat format, size_t offset)
    {
        if (!_isRecording)
        {
            std::cerr << "[VulkanCommandList] Cannot set index buffer: command buffer not recording" << std::endl;
            return;
        }

        if (buffer)
        {
            VulkanBuffer *vulkanBuffer = static_cast<VulkanBuffer *>(buffer);
            VkIndexType indexType = (format == IndexFormat::UInt16) ? VK_INDEX_TYPE_UINT16 : VK_INDEX_TYPE_UINT32;
            vkCmdBindIndexBuffer(_commandBuffer, vulkanBuffer->getVkBuffer(), offset, indexType);
            std::cout << "[VulkanCommandList] Set index buffer" << std::endl;
        }
    }

    void VulkanCommandList::setDescriptorSet(BackendDescriptorSet *descriptorSet, uint32_t slot)
    {
        std::cout << "[VulkanCommandList] Set descriptor set (placeholder)" << std::endl;
        // TODO: 实现描述符集绑定
    }

    void VulkanCommandList::draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance)
    {
        if (!_isRecording)
        {
            std::cerr << "[VulkanCommandList] Cannot draw: command buffer not recording" << std::endl;
            return;
        }

        vkCmdDraw(_commandBuffer, vertexCount, instanceCount, firstVertex, firstInstance);
        std::cout << "[VulkanCommandList] Draw " << vertexCount << " vertices, " << instanceCount << " instances" << std::endl;
    }

    void VulkanCommandList::drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t vertexOffset, uint32_t firstInstance)
    {
        if (!_isRecording)
        {
            std::cerr << "[VulkanCommandList] Cannot draw: command buffer not recording" << std::endl;
            return;
        }

        // 执行真正的Vulkan绘制命令
        vkCmdDrawIndexed(_commandBuffer, indexCount, instanceCount, firstIndex, vertexOffset, firstInstance);

        std::cout << "[VulkanCommandList] drawIndexed: " << indexCount << " indices, "
                  << instanceCount << " instances" << std::endl;

        // 识别绘制的几何体类型
        if (indexCount == 3)
        {
            std::cout << "[VulkanCommandList] Drawing triangle with Vulkan" << std::endl;
        }
        else if (indexCount == 36)
        {
            std::cout << "[VulkanCommandList] Drawing cube with Vulkan" << std::endl;
        }
        else
        {
            std::cout << "[VulkanCommandList] Drawing geometry with " << indexCount << " indices" << std::endl;
        }
    }

    void VulkanCommandList::dispatch(uint32_t groupCountX, uint32_t groupCountY, uint32_t groupCountZ)
    {
        std::cout << "[VulkanCommandList] Dispatch compute (simulated)" << std::endl;
    }

    void VulkanCommandList::barrier(const BarrierDesc &barrier)
    {
        std::cout << "[VulkanCommandList] Memory barrier (simulated)" << std::endl;
    }

    void VulkanCommandList::pushDebugGroup(const std::string &name)
    {
        std::cout << "[VulkanCommandList] Push debug group: " << name << std::endl;
    }

    void VulkanCommandList::popDebugGroup()
    {
        std::cout << "[VulkanCommandList] Pop debug group" << std::endl;
    }

    void VulkanCommandList::insertDebugMarker(const std::string &name)
    {
        std::cout << "[VulkanCommandList] Insert debug marker: " << name << std::endl;
    }

    // 默认SPIR-V着色器代码
    std::vector<uint32_t> VulkanBackend::getDefaultVertexShaderSPIRV()
    {
        // 使用顶点缓冲区的顶点着色器SPIR-V字节码
        // GLSL源码:
        // #version 450
        // layout(location = 0) in vec3 inPosition;
        // layout(location = 1) in vec3 inColor;
        // layout(location = 0) out vec3 fragColor;
        // void main() {
        //     gl_Position = vec4(inPosition, 1.0);
        //     fragColor = inColor;
        // }
        static const uint32_t vertexShaderSPIRV[] = {
            0x07230203, 0x00010000, 0x00080007, 0x0000002c, 0x00000000, 0x00020011, 0x00000001, 0x0006000b,
            0x00000001, 0x4c534c47, 0x6474732e, 0x3035342e, 0x00000000, 0x0003000e, 0x00000000, 0x00000001,
            0x0008000f, 0x00000000, 0x00000004, 0x6e69616d, 0x00000000, 0x0000000b, 0x0000001b, 0x0000001c,
            0x00030003, 0x00000002, 0x000001c2, 0x000a0004, 0x475f4c47, 0x4c474f4f, 0x70635f45, 0x74735f70,
            0x5f656c79, 0x656e696c, 0x7269645f, 0x69746365, 0x00006576, 0x00080004, 0x475f4c47, 0x4c474f4f,
            0x6e695f45, 0x64756c63, 0x69645f65, 0x74636572, 0x00657669, 0x00040005, 0x00000004, 0x6e69616d,
            0x00000000, 0x00060005, 0x0000000b, 0x505f6c67, 0x65567265, 0x78657472, 0x00000000, 0x00060006,
            0x0000000b, 0x00000000, 0x505f6c67, 0x7469736f, 0x006e6f69, 0x00070006, 0x0000000b, 0x00000001,
            0x505f6c67, 0x746e696f, 0x657a6953, 0x00000000, 0x00070006, 0x0000000b, 0x00000002, 0x435f6c67,
            0x4470696c, 0x61747369, 0x0065636e, 0x00070006, 0x0000000b, 0x00000003, 0x435f6c67, 0x446c6c75,
            0x61747369, 0x0065636e, 0x00030005, 0x0000000d, 0x00000000, 0x00050005, 0x0000001b, 0x67617266,
            0x6f6c6f43, 0x00000072, 0x00050005, 0x0000001c, 0x6f506e69, 0x69746973, 0x00006e6f, 0x00050005,
            0x0000001f, 0x6f436e69, 0x00726f6c, 0x00050048, 0x0000000b, 0x00000000, 0x0000000b, 0x00000000,
            0x00050048, 0x0000000b, 0x00000001, 0x0000000b, 0x00000001, 0x00050048, 0x0000000b, 0x00000002,
            0x0000000b, 0x00000003, 0x00050048, 0x0000000b, 0x00000003, 0x0000000b, 0x00000004, 0x00030047,
            0x0000000b, 0x00000002, 0x00040047, 0x0000001b, 0x0000001e, 0x00000000, 0x00040047, 0x0000001c,
            0x0000001e, 0x00000000, 0x00040047, 0x0000001f, 0x0000001e, 0x00000001, 0x00020013, 0x00000002,
            0x00030021, 0x00000003, 0x00000002, 0x00030016, 0x00000006, 0x00000020, 0x00040017, 0x00000007,
            0x00000006, 0x00000004, 0x00040015, 0x00000008, 0x00000020, 0x00000000, 0x0004002b, 0x00000008,
            0x00000009, 0x00000001, 0x0004001c, 0x0000000a, 0x00000006, 0x00000009, 0x0006001e, 0x0000000b,
            0x00000007, 0x00000006, 0x0000000a, 0x0000000a, 0x00040020, 0x0000000c, 0x00000003, 0x0000000b,
            0x0004003b, 0x0000000c, 0x0000000d, 0x00000003, 0x00040015, 0x0000000e, 0x00000020, 0x00000001,
            0x0004002b, 0x0000000e, 0x0000000f, 0x00000000, 0x00040017, 0x00000010, 0x00000006, 0x00000003,
            0x00040020, 0x00000011, 0x00000001, 0x00000010, 0x0004003b, 0x00000011, 0x0000001c, 0x00000001,
            0x0004002b, 0x00000006, 0x0000001e, 0x3f800000, 0x00040020, 0x00000019, 0x00000003, 0x00000007,
            0x00000001, 0x0004002b, 0x00000019, 0x0000001a, 0x00000000, 0x00040020, 0x0000001b, 0x00000001,
            0x00000019, 0x0004003b, 0x0000001b, 0x0000001c, 0x00000001, 0x00040020, 0x0000001e, 0x00000007,
            0x00000007, 0x0004002b, 0x00000006, 0x00000021, 0x00000000, 0x0004002b, 0x00000006, 0x00000022,
            0x3f800000, 0x00040020, 0x00000026, 0x00000003, 0x00000013, 0x00050036, 0x00000002, 0x00000004,
            0x00000000, 0x00000003, 0x000200f8, 0x00000005, 0x0004003b, 0x0000000b, 0x0000000c, 0x00000007,
            0x0003003e, 0x0000000c, 0x00000012, 0x0004003d, 0x00000019, 0x0000001d, 0x0000001c, 0x00050041,
            0x0000001e, 0x0000001f, 0x0000000c, 0x0000001d, 0x0004003d, 0x00000007, 0x00000020, 0x0000001f,
            0x00050051, 0x00000006, 0x00000023, 0x00000020, 0x00000000, 0x00050051, 0x00000006, 0x00000024,
            0x00000020, 0x00000001, 0x00070050, 0x00000013, 0x00000025, 0x00000023, 0x00000024, 0x00000021,
            0x00000022, 0x00050041, 0x00000026, 0x00000027, 0x00000018, 0x0000001a, 0x0003003e, 0x00000027,
            0x00000025, 0x000100fd, 0x00010038};

        return std::vector<uint32_t>(vertexShaderSPIRV, vertexShaderSPIRV + sizeof(vertexShaderSPIRV) / sizeof(uint32_t));
    }

    std::vector<uint32_t> VulkanBackend::getDefaultFragmentShaderSPIRV()
    {
        // 使用glslc编译生成的有效片段着色器SPIR-V字节码
        // 这个着色器输出红色
        static const uint32_t fragmentShaderSPIRV[] = {
            0x07230203, 0x00010000, 0x000d000b, 0x0000000d, 0x00000000, 0x00020011, 0x00000001, 0x0006000b,
            0x00000001, 0x4c534c47, 0x6474732e, 0x3035342e, 0x00000000, 0x0003000e, 0x00000000, 0x00000001,
            0x0006000f, 0x00000004, 0x00000004, 0x6e69616d, 0x00000000, 0x00000009, 0x00030010, 0x00000004,
            0x00000007, 0x00030003, 0x00000002, 0x000001c2, 0x000a0004, 0x475f4c47, 0x4c474f4f, 0x70635f45,
            0x74735f70, 0x5f656c79, 0x656e696c, 0x7269645f, 0x69746365, 0x00006576, 0x00080004, 0x475f4c47,
            0x4c474f4f, 0x6e695f45, 0x64756c63, 0x69645f65, 0x74636572, 0x00657669, 0x00040005, 0x00000004,
            0x6e69616d, 0x00000000, 0x00050005, 0x00000009, 0x4374756f, 0x726f6c6f, 0x00000000, 0x00040047,
            0x00000009, 0x0000001e, 0x00000000, 0x00020013, 0x00000002, 0x00030021, 0x00000003, 0x00000002,
            0x00030016, 0x00000006, 0x00000020, 0x00040017, 0x00000007, 0x00000006, 0x00000004, 0x00040020,
            0x00000008, 0x00000003, 0x00000007, 0x0004003b, 0x00000008, 0x00000009, 0x00000003, 0x0004002b,
            0x00000006, 0x0000000a, 0x3f800000, 0x0004002b, 0x00000006, 0x0000000b, 0x00000000, 0x0007002c,
            0x00000007, 0x0000000c, 0x0000000a, 0x0000000b, 0x0000000b, 0x0000000a, 0x00050036, 0x00000002,
            0x00000004, 0x00000000, 0x00000003, 0x000200f8, 0x00000005, 0x0003003e, 0x00000009, 0x0000000c,
            0x000100fd, 0x00010038};

        return std::vector<uint32_t>(fragmentShaderSPIRV, fragmentShaderSPIRV + sizeof(fragmentShaderSPIRV) / sizeof(uint32_t));
    }

    // Vulkan初始化方法实现
    bool VulkanBackend::createInstance()
    {
        std::cout << "[VulkanBackend] Creating Vulkan instance..." << std::endl;

        VkApplicationInfo appInfo{};
        appInfo.sType = VK_STRUCTURE_TYPE_APPLICATION_INFO;
        appInfo.pApplicationName = "USG Backend";
        appInfo.applicationVersion = VK_MAKE_VERSION(1, 0, 0);
        appInfo.pEngineName = "USG Engine";
        appInfo.engineVersion = VK_MAKE_VERSION(1, 0, 0);
        appInfo.apiVersion = VK_API_VERSION_1_0;

        VkInstanceCreateInfo createInfo{};
        createInfo.sType = VK_STRUCTURE_TYPE_INSTANCE_CREATE_INFO;
        createInfo.pApplicationInfo = &appInfo;

        // 获取GLFW需要的扩展
        uint32_t glfwExtensionCount = 0;
        const char **glfwExtensions;
#ifdef USG_PLATFORM_DESKTOP
        glfwExtensions = glfwGetRequiredInstanceExtensions(&glfwExtensionCount);
#endif

        createInfo.enabledExtensionCount = glfwExtensionCount;
        createInfo.ppEnabledExtensionNames = glfwExtensions;
        createInfo.enabledLayerCount = 0;

        if (vkCreateInstance(&createInfo, nullptr, &_instance) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to create Vulkan instance" << std::endl;
            return false;
        }

        std::cout << "[VulkanBackend] Vulkan instance created successfully" << std::endl;
        return true;
    }

    bool VulkanBackend::selectPhysicalDevice()
    {
        std::cout << "[VulkanBackend] Selecting physical device..." << std::endl;

        uint32_t deviceCount = 0;
        vkEnumeratePhysicalDevices(_instance, &deviceCount, nullptr);

        if (deviceCount == 0)
        {
            std::cerr << "[VulkanBackend] Failed to find GPUs with Vulkan support" << std::endl;
            return false;
        }

        std::vector<VkPhysicalDevice> devices(deviceCount);
        vkEnumeratePhysicalDevices(_instance, &deviceCount, devices.data());

        // 选择第一个支持图形队列的设备
        for (const auto &device : devices)
        {
            uint32_t queueFamilyCount = 0;
            vkGetPhysicalDeviceQueueFamilyProperties(device, &queueFamilyCount, nullptr);

            std::vector<VkQueueFamilyProperties> queueFamilies(queueFamilyCount);
            vkGetPhysicalDeviceQueueFamilyProperties(device, &queueFamilyCount, queueFamilies.data());

            uint32_t i = 0;
            for (const auto &queueFamily : queueFamilies)
            {
                if (queueFamily.queueFlags & VK_QUEUE_GRAPHICS_BIT)
                {
                    _graphicsQueueFamily = i;
                }

                VkBool32 presentSupport = false;
                vkGetPhysicalDeviceSurfaceSupportKHR(device, i, _surface, &presentSupport);
                if (presentSupport)
                {
                    _presentQueueFamily = i;
                }

                if (_graphicsQueueFamily != UINT32_MAX && _presentQueueFamily != UINT32_MAX)
                {
                    _physicalDevice = device;
                    std::cout << "[VulkanBackend] Physical device selected" << std::endl;
                    return true;
                }

                i++;
            }
        }

        std::cerr << "[VulkanBackend] Failed to find a suitable GPU" << std::endl;
        return false;
    }

    bool VulkanBackend::createLogicalDevice()
    {
        std::cout << "[VulkanBackend] Creating logical device..." << std::endl;

        std::vector<VkDeviceQueueCreateInfo> queueCreateInfos;
        std::set<uint32_t> uniqueQueueFamilies = {_graphicsQueueFamily, _presentQueueFamily};

        float queuePriority = 1.0f;
        for (uint32_t queueFamily : uniqueQueueFamilies)
        {
            VkDeviceQueueCreateInfo queueCreateInfo{};
            queueCreateInfo.sType = VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO;
            queueCreateInfo.queueFamilyIndex = queueFamily;
            queueCreateInfo.queueCount = 1;
            queueCreateInfo.pQueuePriorities = &queuePriority;
            queueCreateInfos.push_back(queueCreateInfo);
        }

        VkPhysicalDeviceFeatures deviceFeatures{};

        VkDeviceCreateInfo createInfo{};
        createInfo.sType = VK_STRUCTURE_TYPE_DEVICE_CREATE_INFO;
        createInfo.queueCreateInfoCount = static_cast<uint32_t>(queueCreateInfos.size());
        createInfo.pQueueCreateInfos = queueCreateInfos.data();
        createInfo.pEnabledFeatures = &deviceFeatures;

        // 设备扩展
        const std::vector<const char *> deviceExtensions = {
            VK_KHR_SWAPCHAIN_EXTENSION_NAME};

        createInfo.enabledExtensionCount = static_cast<uint32_t>(deviceExtensions.size());
        createInfo.ppEnabledExtensionNames = deviceExtensions.data();
        createInfo.enabledLayerCount = 0;

        if (vkCreateDevice(_physicalDevice, &createInfo, nullptr, &_device) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to create logical device" << std::endl;
            return false;
        }

        vkGetDeviceQueue(_device, _graphicsQueueFamily, 0, &_graphicsQueue);
        vkGetDeviceQueue(_device, _presentQueueFamily, 0, &_presentQueue);

        std::cout << "[VulkanBackend] Logical device created successfully" << std::endl;
        return true;
    }

    bool VulkanBackend::createSwapchain(const BackendConfig &config)
    {
        std::cout << "[VulkanBackend] Creating swapchain..." << std::endl;

        // 查询交换链支持
        VkSurfaceCapabilitiesKHR capabilities;
        vkGetPhysicalDeviceSurfaceCapabilitiesKHR(_physicalDevice, _surface, &capabilities);

        uint32_t formatCount;
        vkGetPhysicalDeviceSurfaceFormatsKHR(_physicalDevice, _surface, &formatCount, nullptr);
        std::vector<VkSurfaceFormatKHR> formats(formatCount);
        vkGetPhysicalDeviceSurfaceFormatsKHR(_physicalDevice, _surface, &formatCount, formats.data());

        uint32_t presentModeCount;
        vkGetPhysicalDeviceSurfacePresentModesKHR(_physicalDevice, _surface, &presentModeCount, nullptr);
        std::vector<VkPresentModeKHR> presentModes(presentModeCount);
        vkGetPhysicalDeviceSurfacePresentModesKHR(_physicalDevice, _surface, &presentModeCount, presentModes.data());

        // 选择格式
        VkSurfaceFormatKHR surfaceFormat = formats[0];
        for (const auto &availableFormat : formats)
        {
            if (availableFormat.format == VK_FORMAT_B8G8R8A8_SRGB &&
                availableFormat.colorSpace == VK_COLOR_SPACE_SRGB_NONLINEAR_KHR)
            {
                surfaceFormat = availableFormat;
                break;
            }
        }

        // 选择呈现模式
        VkPresentModeKHR presentMode = VK_PRESENT_MODE_FIFO_KHR;
        for (const auto &availablePresentMode : presentModes)
        {
            if (availablePresentMode == VK_PRESENT_MODE_MAILBOX_KHR)
            {
                presentMode = availablePresentMode;
                break;
            }
        }

        // 选择交换范围
        VkExtent2D extent;
        if (capabilities.currentExtent.width != UINT32_MAX)
        {
            extent = capabilities.currentExtent;
        }
        else
        {
            extent = {static_cast<uint32_t>(config.swapchainWidth), static_cast<uint32_t>(config.swapchainHeight)};
            extent.width = std::clamp(extent.width, capabilities.minImageExtent.width, capabilities.maxImageExtent.width);
            extent.height = std::clamp(extent.height, capabilities.minImageExtent.height, capabilities.maxImageExtent.height);
        }

        uint32_t imageCount = capabilities.minImageCount + 1;
        if (capabilities.maxImageCount > 0 && imageCount > capabilities.maxImageCount)
        {
            imageCount = capabilities.maxImageCount;
        }

        VkSwapchainCreateInfoKHR createInfo{};
        createInfo.sType = VK_STRUCTURE_TYPE_SWAPCHAIN_CREATE_INFO_KHR;
        createInfo.surface = _surface;
        createInfo.minImageCount = imageCount;
        createInfo.imageFormat = surfaceFormat.format;
        createInfo.imageColorSpace = surfaceFormat.colorSpace;
        createInfo.imageExtent = extent;
        createInfo.imageArrayLayers = 1;
        createInfo.imageUsage = VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT;

        uint32_t queueFamilyIndices[] = {_graphicsQueueFamily, _presentQueueFamily};

        if (_graphicsQueueFamily != _presentQueueFamily)
        {
            createInfo.imageSharingMode = VK_SHARING_MODE_CONCURRENT;
            createInfo.queueFamilyIndexCount = 2;
            createInfo.pQueueFamilyIndices = queueFamilyIndices;
        }
        else
        {
            createInfo.imageSharingMode = VK_SHARING_MODE_EXCLUSIVE;
        }

        createInfo.preTransform = capabilities.currentTransform;
        createInfo.compositeAlpha = VK_COMPOSITE_ALPHA_OPAQUE_BIT_KHR;
        createInfo.presentMode = presentMode;
        createInfo.clipped = VK_TRUE;
        createInfo.oldSwapchain = VK_NULL_HANDLE;

        if (vkCreateSwapchainKHR(_device, &createInfo, nullptr, &_swapchain) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to create swap chain" << std::endl;
            return false;
        }

        // 获取交换链图像
        vkGetSwapchainImagesKHR(_device, _swapchain, &imageCount, nullptr);
        _swapchainImages.resize(imageCount);
        vkGetSwapchainImagesKHR(_device, _swapchain, &imageCount, _swapchainImages.data());

        _swapchainImageFormat = surfaceFormat.format;
        _swapchainExtent = extent;

        // 创建图像视图
        _swapchainImageViews.resize(_swapchainImages.size());
        for (size_t i = 0; i < _swapchainImages.size(); i++)
        {
            VkImageViewCreateInfo viewCreateInfo{};
            viewCreateInfo.sType = VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO;
            viewCreateInfo.image = _swapchainImages[i];
            viewCreateInfo.viewType = VK_IMAGE_VIEW_TYPE_2D;
            viewCreateInfo.format = _swapchainImageFormat;
            viewCreateInfo.components.r = VK_COMPONENT_SWIZZLE_IDENTITY;
            viewCreateInfo.components.g = VK_COMPONENT_SWIZZLE_IDENTITY;
            viewCreateInfo.components.b = VK_COMPONENT_SWIZZLE_IDENTITY;
            viewCreateInfo.components.a = VK_COMPONENT_SWIZZLE_IDENTITY;
            viewCreateInfo.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
            viewCreateInfo.subresourceRange.baseMipLevel = 0;
            viewCreateInfo.subresourceRange.levelCount = 1;
            viewCreateInfo.subresourceRange.baseArrayLayer = 0;
            viewCreateInfo.subresourceRange.layerCount = 1;

            if (vkCreateImageView(_device, &viewCreateInfo, nullptr, &_swapchainImageViews[i]) != VK_SUCCESS)
            {
                std::cerr << "[VulkanBackend] Failed to create image views" << std::endl;
                return false;
            }
        }

        std::cout << "[VulkanBackend] Swapchain created successfully" << std::endl;
        return true;
    }

    bool VulkanBackend::createRenderPass()
    {
        std::cout << "[VulkanBackend] Creating render pass..." << std::endl;

        VkAttachmentDescription colorAttachment{};
        colorAttachment.format = _swapchainImageFormat;
        colorAttachment.samples = VK_SAMPLE_COUNT_1_BIT;
        colorAttachment.loadOp = VK_ATTACHMENT_LOAD_OP_CLEAR;
        colorAttachment.storeOp = VK_ATTACHMENT_STORE_OP_STORE;
        colorAttachment.stencilLoadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE;
        colorAttachment.stencilStoreOp = VK_ATTACHMENT_STORE_OP_DONT_CARE;
        colorAttachment.initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;
        colorAttachment.finalLayout = VK_IMAGE_LAYOUT_PRESENT_SRC_KHR;

        VkAttachmentReference colorAttachmentRef{};
        colorAttachmentRef.attachment = 0;
        colorAttachmentRef.layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;

        VkSubpassDescription subpass{};
        subpass.pipelineBindPoint = VK_PIPELINE_BIND_POINT_GRAPHICS;
        subpass.colorAttachmentCount = 1;
        subpass.pColorAttachments = &colorAttachmentRef;

        VkSubpassDependency dependency{};
        dependency.srcSubpass = VK_SUBPASS_EXTERNAL;
        dependency.dstSubpass = 0;
        dependency.srcStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
        dependency.srcAccessMask = 0;
        dependency.dstStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
        dependency.dstAccessMask = VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT;

        VkRenderPassCreateInfo renderPassInfo{};
        renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_CREATE_INFO;
        renderPassInfo.attachmentCount = 1;
        renderPassInfo.pAttachments = &colorAttachment;
        renderPassInfo.subpassCount = 1;
        renderPassInfo.pSubpasses = &subpass;
        renderPassInfo.dependencyCount = 1;
        renderPassInfo.pDependencies = &dependency;

        if (vkCreateRenderPass(_device, &renderPassInfo, nullptr, &_renderPass) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to create render pass" << std::endl;
            return false;
        }

        std::cout << "[VulkanBackend] Render pass created successfully" << std::endl;
        return true;
    }

    bool VulkanBackend::createFramebuffers()
    {
        std::cout << "[VulkanBackend] Creating framebuffers..." << std::endl;

        _swapchainFramebuffers.resize(_swapchainImageViews.size());

        for (size_t i = 0; i < _swapchainImageViews.size(); i++)
        {
            VkImageView attachments[] = {
                _swapchainImageViews[i]};

            VkFramebufferCreateInfo framebufferInfo{};
            framebufferInfo.sType = VK_STRUCTURE_TYPE_FRAMEBUFFER_CREATE_INFO;
            framebufferInfo.renderPass = _renderPass;
            framebufferInfo.attachmentCount = 1;
            framebufferInfo.pAttachments = attachments;
            framebufferInfo.width = _swapchainExtent.width;
            framebufferInfo.height = _swapchainExtent.height;
            framebufferInfo.layers = 1;

            if (vkCreateFramebuffer(_device, &framebufferInfo, nullptr, &_swapchainFramebuffers[i]) != VK_SUCCESS)
            {
                std::cerr << "[VulkanBackend] Failed to create framebuffer" << std::endl;
                return false;
            }
        }

        std::cout << "[VulkanBackend] Framebuffers created successfully" << std::endl;
        return true;
    }

    bool VulkanBackend::createCommandPool()
    {
        std::cout << "[VulkanBackend] Creating command pool..." << std::endl;

        VkCommandPoolCreateInfo poolInfo{};
        poolInfo.sType = VK_STRUCTURE_TYPE_COMMAND_POOL_CREATE_INFO;
        poolInfo.flags = VK_COMMAND_POOL_CREATE_RESET_COMMAND_BUFFER_BIT;
        poolInfo.queueFamilyIndex = _graphicsQueueFamily;

        if (vkCreateCommandPool(_device, &poolInfo, nullptr, &_commandPool) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to create command pool" << std::endl;
            return false;
        }

        std::cout << "[VulkanBackend] Command pool created successfully" << std::endl;
        return true;
    }

    bool VulkanBackend::createSyncObjects()
    {
        std::cout << "[VulkanBackend] Creating sync objects..." << std::endl;

        _imageAvailableSemaphores.resize(MAX_FRAMES_IN_FLIGHT);
        _renderFinishedSemaphores.resize(MAX_FRAMES_IN_FLIGHT);
        _inFlightFences.resize(MAX_FRAMES_IN_FLIGHT);

        VkSemaphoreCreateInfo semaphoreInfo{};
        semaphoreInfo.sType = VK_STRUCTURE_TYPE_SEMAPHORE_CREATE_INFO;

        VkFenceCreateInfo fenceInfo{};
        fenceInfo.sType = VK_STRUCTURE_TYPE_FENCE_CREATE_INFO;
        fenceInfo.flags = VK_FENCE_CREATE_SIGNALED_BIT;

        for (size_t i = 0; i < MAX_FRAMES_IN_FLIGHT; i++)
        {
            if (vkCreateSemaphore(_device, &semaphoreInfo, nullptr, &_imageAvailableSemaphores[i]) != VK_SUCCESS ||
                vkCreateSemaphore(_device, &semaphoreInfo, nullptr, &_renderFinishedSemaphores[i]) != VK_SUCCESS ||
                vkCreateFence(_device, &fenceInfo, nullptr, &_inFlightFences[i]) != VK_SUCCESS)
            {
                std::cerr << "[VulkanBackend] Failed to create sync objects" << std::endl;
                return false;
            }
        }

        std::cout << "[VulkanBackend] Sync objects created successfully" << std::endl;
        return true;
    }

    void VulkanBackend::printDeviceInfo()
    {
        VkPhysicalDeviceProperties deviceProperties;
        vkGetPhysicalDeviceProperties(_physicalDevice, &deviceProperties);

        std::cout << "[VulkanBackend] Device: " << deviceProperties.deviceName << std::endl;
        std::cout << "[VulkanBackend] API Version: "
                  << VK_VERSION_MAJOR(deviceProperties.apiVersion) << "."
                  << VK_VERSION_MINOR(deviceProperties.apiVersion) << "."
                  << VK_VERSION_PATCH(deviceProperties.apiVersion) << std::endl;
        std::cout << "[VulkanBackend] Driver Version: " << deviceProperties.driverVersion << std::endl;
        std::cout << "[VulkanBackend] Vendor ID: 0x" << std::hex << deviceProperties.vendorID << std::dec << std::endl;
    }

    uint32_t VulkanBackend::findMemoryType(uint32_t typeFilter, VkMemoryPropertyFlags properties)
    {
        VkPhysicalDeviceMemoryProperties memProperties;
        vkGetPhysicalDeviceMemoryProperties(_physicalDevice, &memProperties);

        for (uint32_t i = 0; i < memProperties.memoryTypeCount; i++)
        {
            if ((typeFilter & (1 << i)) && (memProperties.memoryTypes[i].propertyFlags & properties) == properties)
            {
                return i;
            }
        }

        throw std::runtime_error("Failed to find suitable memory type");
    }

    void VulkanBackend::updateBuffer(BackendBuffer *buffer, const void *data, size_t size, size_t offset)
    {
        if (!buffer || !data || size == 0)
        {
            std::cerr << "[VulkanBackend] Invalid parameters for updateBuffer" << std::endl;
            return;
        }

        auto vulkanBuffer = static_cast<VulkanBuffer *>(buffer);
        vulkanBuffer->updateData(data, size, offset);
        std::cout << "[VulkanBackend] Updated buffer data: " << size << " bytes" << std::endl;
    }

} // namespace USG
