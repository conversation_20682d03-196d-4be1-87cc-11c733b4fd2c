#include "VulkanBackend.h"
#include <iostream>
#include <stdexcept>
#include <set>
#include <algorithm>
#include <cstring>

#ifdef USG_PLATFORM_DESKTOP
#define GLFW_INCLUDE_VULKAN
#include <GLFW/glfw3.h>
#endif

namespace USG
{

    VulkanBackend::VulkanBackend()
    {
    }

    VulkanBackend::~VulkanBackend()
    {
        cleanup();
    }

    bool VulkanBackend::initialize(const BackendConfig &config)
    {
        if (_initialized)
        {
            return true;
        }

        std::cout << "[VulkanBackend] Initializing Vulkan backend..." << std::endl;

        try
        {
            // 获取窗口句柄
#ifdef USG_PLATFORM_DESKTOP
            _window = static_cast<GLFWwindow *>(config.windowHandle);
            if (!_window)
            {
                std::cerr << "[VulkanBackend] Invalid window handle" << std::endl;
                return false;
            }
#endif

            // 真正的Vulkan初始化过程
            if (!createInstance())
            {
                std::cerr << "[VulkanBackend] Failed to create Vulkan instance" << std::endl;
                return false;
            }

            // 创建Surface
#ifdef USG_PLATFORM_DESKTOP
            if (glfwCreateWindowSurface(_instance, _window, nullptr, &_surface) != VK_SUCCESS)
            {
                std::cerr << "[VulkanBackend] Failed to create window surface" << std::endl;
                return false;
            }
#endif

            if (!selectPhysicalDevice())
            {
                std::cerr << "[VulkanBackend] Failed to select physical device" << std::endl;
                return false;
            }

            if (!createLogicalDevice())
            {
                std::cerr << "[VulkanBackend] Failed to create logical device" << std::endl;
                return false;
            }

            if (!createSwapchain(config))
            {
                std::cerr << "[VulkanBackend] Failed to create swapchain" << std::endl;
                return false;
            }

            if (!createRenderPass())
            {
                std::cerr << "[VulkanBackend] Failed to create render pass" << std::endl;
                return false;
            }

            if (!createFramebuffers())
            {
                std::cerr << "[VulkanBackend] Failed to create framebuffers" << std::endl;
                return false;
            }

            if (!createCommandPool())
            {
                std::cerr << "[VulkanBackend] Failed to create command pool" << std::endl;
                return false;
            }

            if (!createSyncObjects())
            {
                std::cerr << "[VulkanBackend] Failed to create sync objects" << std::endl;
                return false;
            }

            _initialized = true;
            std::cout << "[VulkanBackend] Vulkan backend initialized successfully" << std::endl;

            // 输出真实的Vulkan信息
            printDeviceInfo();

            return true;
        }
        catch (const std::exception &e)
        {
            std::cerr << "[VulkanBackend] Failed to initialize: " << e.what() << std::endl;
            return false;
        }
    }

    void VulkanBackend::cleanup()
    {
        if (!_initialized)
        {
            return;
        }

        std::cout << "[VulkanBackend] Cleaning up Vulkan backend..." << std::endl;

        // 等待设备空闲
        if (_device != VK_NULL_HANDLE)
        {
            vkDeviceWaitIdle(_device);
        }

        // 清理同步对象
        for (size_t i = 0; i < MAX_FRAMES_IN_FLIGHT; i++)
        {
            if (_renderFinishedSemaphores.size() > i && _renderFinishedSemaphores[i] != VK_NULL_HANDLE)
            {
                vkDestroySemaphore(_device, _renderFinishedSemaphores[i], nullptr);
            }
            if (_imageAvailableSemaphores.size() > i && _imageAvailableSemaphores[i] != VK_NULL_HANDLE)
            {
                vkDestroySemaphore(_device, _imageAvailableSemaphores[i], nullptr);
            }
            if (_inFlightFences.size() > i && _inFlightFences[i] != VK_NULL_HANDLE)
            {
                vkDestroyFence(_device, _inFlightFences[i], nullptr);
            }
        }

        // 清理命令池
        if (_commandPool != VK_NULL_HANDLE)
        {
            vkDestroyCommandPool(_device, _commandPool, nullptr);
            _commandPool = VK_NULL_HANDLE;
        }

        // 清理帧缓冲区
        for (auto framebuffer : _swapchainFramebuffers)
        {
            if (framebuffer != VK_NULL_HANDLE)
            {
                vkDestroyFramebuffer(_device, framebuffer, nullptr);
            }
        }
        _swapchainFramebuffers.clear();

        // 清理渲染通道
        if (_renderPass != VK_NULL_HANDLE)
        {
            vkDestroyRenderPass(_device, _renderPass, nullptr);
            _renderPass = VK_NULL_HANDLE;
        }

        // 清理图像视图
        for (auto imageView : _swapchainImageViews)
        {
            if (imageView != VK_NULL_HANDLE)
            {
                vkDestroyImageView(_device, imageView, nullptr);
            }
        }
        _swapchainImageViews.clear();

        // 清理交换链
        if (_swapchain != VK_NULL_HANDLE)
        {
            vkDestroySwapchainKHR(_device, _swapchain, nullptr);
            _swapchain = VK_NULL_HANDLE;
        }

        // 清理逻辑设备
        if (_device != VK_NULL_HANDLE)
        {
            vkDestroyDevice(_device, nullptr);
            _device = VK_NULL_HANDLE;
        }

        // 清理Surface
        if (_surface != VK_NULL_HANDLE)
        {
            vkDestroySurfaceKHR(_instance, _surface, nullptr);
            _surface = VK_NULL_HANDLE;
        }

        // 清理实例
        if (_instance != VK_NULL_HANDLE)
        {
            vkDestroyInstance(_instance, nullptr);
            _instance = VK_NULL_HANDLE;
        }

        _initialized = false;
        std::cout << "[VulkanBackend] Vulkan backend cleaned up" << std::endl;
    }

    // 资源创建方法 - 占位符实现
    BackendBuffer *VulkanBackend::createBuffer(const BufferDesc &desc)
    {
        // TODO: 实现Vulkan缓冲区创建
        return nullptr;
    }

    BackendTexture *VulkanBackend::createTexture(const TextureDesc &desc)
    {
        // TODO: 实现Vulkan纹理创建
        return nullptr;
    }

    BackendShader *VulkanBackend::createShader(const ShaderDesc &desc)
    {
        // TODO: 实现Vulkan着色器创建
        return nullptr;
    }

    BackendPipeline *VulkanBackend::createPipeline(const PipelineDesc &desc)
    {
        // TODO: 实现Vulkan管线创建
        return nullptr;
    }

    BackendDescriptorSet *VulkanBackend::createDescriptorSet()
    {
        // TODO: 实现Vulkan描述符集创建
        return nullptr;
    }

    BackendCommandList *VulkanBackend::createCommandList()
    {
        // 分配命令缓冲区
        VkCommandBufferAllocateInfo allocInfo{};
        allocInfo.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_ALLOCATE_INFO;
        allocInfo.commandPool = _commandPool;
        allocInfo.level = VK_COMMAND_BUFFER_LEVEL_PRIMARY;
        allocInfo.commandBufferCount = 1;

        VkCommandBuffer commandBuffer;
        if (vkAllocateCommandBuffers(_device, &allocInfo, &commandBuffer) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to allocate command buffer" << std::endl;
            return nullptr;
        }

        std::cout << "[VulkanBackend] Created command list " << (++_nextResourceId) << std::endl;
        return new VulkanCommandList(_device, commandBuffer);
    }

    BackendFence *VulkanBackend::createFence()
    {
        // TODO: 实现Vulkan栅栏创建
        return nullptr;
    }

    BackendSemaphore *VulkanBackend::createSemaphore()
    {
        // TODO: 实现Vulkan信号量创建
        return nullptr;
    }

    void VulkanBackend::executeCommandList(BackendCommandList *cmdList,
                                           const std::vector<BackendSemaphore *> &waitSemaphores,
                                           const std::vector<BackendSemaphore *> &signalSemaphores,
                                           BackendFence *fence)
    {
        // TODO: 实现Vulkan命令列表执行
        std::cout << "[VulkanBackend] Executing command list (simulated)" << std::endl;
    }

    void VulkanBackend::beginFrame()
    {
        // 等待上一帧完成
        vkWaitForFences(_device, 1, &_inFlightFences[_currentFrame], VK_TRUE, UINT64_MAX);

        // 获取下一个交换链图像
        VkResult result = vkAcquireNextImageKHR(_device, _swapchain, UINT64_MAX,
                                                _imageAvailableSemaphores[_currentFrame], VK_NULL_HANDLE, &_imageIndex);

        if (result == VK_ERROR_OUT_OF_DATE_KHR)
        {
            // 交换链过期，需要重建
            std::cout << "[VulkanBackend] Swapchain out of date, need to recreate" << std::endl;
            return;
        }
        else if (result != VK_SUCCESS && result != VK_SUBOPTIMAL_KHR)
        {
            std::cerr << "[VulkanBackend] Failed to acquire swap chain image" << std::endl;
            return;
        }

        // 重置栅栏
        vkResetFences(_device, 1, &_inFlightFences[_currentFrame]);

        std::cout << "[VulkanBackend] Begin frame " << _currentFrame << ", image " << _imageIndex << std::endl;
    }

    void VulkanBackend::endFrame()
    {
        std::cout << "[VulkanBackend] End frame " << _currentFrame << std::endl;
        // endFrame主要用于标记帧结束，实际提交在present中进行
    }

    void VulkanBackend::present()
    {
        // 呈现到交换链
        VkPresentInfoKHR presentInfo{};
        presentInfo.sType = VK_STRUCTURE_TYPE_PRESENT_INFO_KHR;

        VkSemaphore signalSemaphores[] = {_renderFinishedSemaphores[_currentFrame]};
        presentInfo.waitSemaphoreCount = 1;
        presentInfo.pWaitSemaphores = signalSemaphores;

        VkSwapchainKHR swapChains[] = {_swapchain};
        presentInfo.swapchainCount = 1;
        presentInfo.pSwapchains = swapChains;
        presentInfo.pImageIndices = &_imageIndex;
        presentInfo.pResults = nullptr;

        VkResult result = vkQueuePresentKHR(_presentQueue, &presentInfo);

        if (result == VK_ERROR_OUT_OF_DATE_KHR || result == VK_SUBOPTIMAL_KHR)
        {
            std::cout << "[VulkanBackend] Swapchain out of date or suboptimal" << std::endl;
        }
        else if (result != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to present swap chain image" << std::endl;
        }

        // 移动到下一帧
        _currentFrame = (_currentFrame + 1) % MAX_FRAMES_IN_FLIGHT;

        std::cout << "[VulkanBackend] Present frame completed" << std::endl;
    }

    // 资源销毁方法 - 占位符实现
    void VulkanBackend::destroyBuffer(BackendBuffer *buffer)
    {
        delete buffer;
    }

    void VulkanBackend::destroyTexture(BackendTexture *texture)
    {
        delete texture;
    }

    void VulkanBackend::destroyShader(BackendShader *shader)
    {
        delete shader;
    }

    void VulkanBackend::destroyPipeline(BackendPipeline *pipeline)
    {
        delete pipeline;
    }

    void VulkanBackend::destroyDescriptorSet(BackendDescriptorSet *descriptorSet)
    {
        delete descriptorSet;
    }

    void VulkanBackend::destroyCommandList(BackendCommandList *cmdList)
    {
        delete cmdList;
    }

    void VulkanBackend::destroyFence(BackendFence *fence)
    {
        delete fence;
    }

    void VulkanBackend::destroySemaphore(BackendSemaphore *semaphore)
    {
        delete semaphore;
    }

    // VulkanCommandList实现
    VulkanCommandList::VulkanCommandList(VkDevice device, VkCommandBuffer commandBuffer)
        : _device(device), _commandBuffer(commandBuffer)
    {
    }

    VulkanCommandList::~VulkanCommandList()
    {
        // 命令缓冲区由命令池管理，不需要单独销毁
    }

    void VulkanCommandList::begin()
    {
        if (_isRecording)
        {
            std::cerr << "[VulkanCommandList] Command buffer is already recording" << std::endl;
            return;
        }

        VkCommandBufferBeginInfo beginInfo{};
        beginInfo.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO;
        beginInfo.flags = 0;
        beginInfo.pInheritanceInfo = nullptr;

        if (vkBeginCommandBuffer(_commandBuffer, &beginInfo) != VK_SUCCESS)
        {
            std::cerr << "[VulkanCommandList] Failed to begin recording command buffer" << std::endl;
            return;
        }

        _isRecording = true;
        std::cout << "[VulkanCommandList] Begin command recording" << std::endl;
    }

    void VulkanCommandList::end()
    {
        if (!_isRecording)
        {
            std::cerr << "[VulkanCommandList] Command buffer is not recording" << std::endl;
            return;
        }

        if (vkEndCommandBuffer(_commandBuffer) != VK_SUCCESS)
        {
            std::cerr << "[VulkanCommandList] Failed to record command buffer" << std::endl;
            return;
        }

        _isRecording = false;
        std::cout << "[VulkanCommandList] End command recording" << std::endl;
    }

    void VulkanCommandList::reset()
    {
        if (_isRecording)
        {
            std::cerr << "[VulkanCommandList] Cannot reset command buffer while recording" << std::endl;
            return;
        }

        if (vkResetCommandBuffer(_commandBuffer, 0) != VK_SUCCESS)
        {
            std::cerr << "[VulkanCommandList] Failed to reset command buffer" << std::endl;
            return;
        }

        _currentRenderPass = VK_NULL_HANDLE;
        std::cout << "[VulkanCommandList] Reset command list" << std::endl;
    }

    void VulkanCommandList::beginRenderPass(const RenderPassDesc &desc)
    {
        if (!_isRecording)
        {
            std::cerr << "[VulkanCommandList] Cannot begin render pass: command buffer not recording" << std::endl;
            return;
        }

        // 注意：这里需要从desc中获取实际的Vulkan对象
        // 为了简化，我们假设有一个全局的渲染通道和帧缓冲区
        // 在完整实现中，应该从desc中获取这些信息

        VkRenderPassBeginInfo renderPassInfo{};
        renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_BEGIN_INFO;
        // renderPassInfo.renderPass = desc.renderPass; // 需要从desc获取
        // renderPassInfo.framebuffer = desc.framebuffer; // 需要从desc获取

        // 设置渲染区域
        renderPassInfo.renderArea.offset = {0, 0};
        // renderPassInfo.renderArea.extent = desc.extent; // 需要从desc获取

        // 设置清除值
        VkClearValue clearColor = {{{desc.clearColor[0], desc.clearColor[1], desc.clearColor[2], desc.clearColor[3]}}};
        renderPassInfo.clearValueCount = 1;
        renderPassInfo.pClearValues = &clearColor;

        // 由于我们没有完整的desc实现，这里只是记录状态
        // vkCmdBeginRenderPass(_commandBuffer, &renderPassInfo, VK_SUBPASS_CONTENTS_INLINE);

        std::cout << "[VulkanCommandList] Begin render pass (simplified implementation)" << std::endl;
        std::cout << "[VulkanCommandList] Clear color: (" << desc.clearColor[0] << ", "
                  << desc.clearColor[1] << ", " << desc.clearColor[2] << ", " << desc.clearColor[3] << ")" << std::endl;
    }

    void VulkanCommandList::endRenderPass()
    {
        std::cout << "[VulkanCommandList] End render pass" << std::endl;
    }

    void VulkanCommandList::setPipeline(BackendPipeline *pipeline)
    {
        std::cout << "[VulkanCommandList] Set pipeline (simulated)" << std::endl;
    }

    void VulkanCommandList::setVertexBuffer(BackendBuffer *buffer, uint32_t slot, size_t offset)
    {
        std::cout << "[VulkanCommandList] Set vertex buffer (simulated)" << std::endl;
    }

    void VulkanCommandList::setIndexBuffer(BackendBuffer *buffer, IndexFormat format, size_t offset)
    {
        std::cout << "[VulkanCommandList] Set index buffer (simulated)" << std::endl;
    }

    void VulkanCommandList::setDescriptorSet(BackendDescriptorSet *descriptorSet, uint32_t slot)
    {
        std::cout << "[VulkanCommandList] Set descriptor set (simulated)" << std::endl;
    }

    void VulkanCommandList::draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance)
    {
        std::cout << "[VulkanCommandList] Draw " << vertexCount << " vertices (simulated)" << std::endl;
    }

    void VulkanCommandList::drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t vertexOffset, uint32_t firstInstance)
    {
        if (!_isRecording)
        {
            std::cerr << "[VulkanCommandList] Cannot draw: command buffer not recording" << std::endl;
            return;
        }

        // 执行真正的Vulkan绘制命令
        vkCmdDrawIndexed(_commandBuffer, indexCount, instanceCount, firstIndex, vertexOffset, firstInstance);

        std::cout << "[VulkanCommandList] drawIndexed: " << indexCount << " indices, "
                  << instanceCount << " instances" << std::endl;

        // 识别绘制的几何体类型
        if (indexCount == 3)
        {
            std::cout << "[VulkanCommandList] Drawing triangle with Vulkan" << std::endl;
        }
        else if (indexCount == 36)
        {
            std::cout << "[VulkanCommandList] Drawing cube with Vulkan" << std::endl;
        }
        else
        {
            std::cout << "[VulkanCommandList] Drawing geometry with " << indexCount << " indices" << std::endl;
        }
    }

    void VulkanCommandList::dispatch(uint32_t groupCountX, uint32_t groupCountY, uint32_t groupCountZ)
    {
        std::cout << "[VulkanCommandList] Dispatch compute (simulated)" << std::endl;
    }

    void VulkanCommandList::barrier(const BarrierDesc &barrier)
    {
        std::cout << "[VulkanCommandList] Memory barrier (simulated)" << std::endl;
    }

    void VulkanCommandList::pushDebugGroup(const std::string &name)
    {
        std::cout << "[VulkanCommandList] Push debug group: " << name << std::endl;
    }

    void VulkanCommandList::popDebugGroup()
    {
        std::cout << "[VulkanCommandList] Pop debug group" << std::endl;
    }

    void VulkanCommandList::insertDebugMarker(const std::string &name)
    {
        std::cout << "[VulkanCommandList] Insert debug marker: " << name << std::endl;
    }

    // Vulkan初始化方法实现
    bool VulkanBackend::createInstance()
    {
        std::cout << "[VulkanBackend] Creating Vulkan instance..." << std::endl;

        VkApplicationInfo appInfo{};
        appInfo.sType = VK_STRUCTURE_TYPE_APPLICATION_INFO;
        appInfo.pApplicationName = "USG Backend";
        appInfo.applicationVersion = VK_MAKE_VERSION(1, 0, 0);
        appInfo.pEngineName = "USG Engine";
        appInfo.engineVersion = VK_MAKE_VERSION(1, 0, 0);
        appInfo.apiVersion = VK_API_VERSION_1_0;

        VkInstanceCreateInfo createInfo{};
        createInfo.sType = VK_STRUCTURE_TYPE_INSTANCE_CREATE_INFO;
        createInfo.pApplicationInfo = &appInfo;

        // 获取GLFW需要的扩展
        uint32_t glfwExtensionCount = 0;
        const char **glfwExtensions;
#ifdef USG_PLATFORM_DESKTOP
        glfwExtensions = glfwGetRequiredInstanceExtensions(&glfwExtensionCount);
#endif

        createInfo.enabledExtensionCount = glfwExtensionCount;
        createInfo.ppEnabledExtensionNames = glfwExtensions;
        createInfo.enabledLayerCount = 0;

        if (vkCreateInstance(&createInfo, nullptr, &_instance) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to create Vulkan instance" << std::endl;
            return false;
        }

        std::cout << "[VulkanBackend] Vulkan instance created successfully" << std::endl;
        return true;
    }

    bool VulkanBackend::selectPhysicalDevice()
    {
        std::cout << "[VulkanBackend] Selecting physical device..." << std::endl;

        uint32_t deviceCount = 0;
        vkEnumeratePhysicalDevices(_instance, &deviceCount, nullptr);

        if (deviceCount == 0)
        {
            std::cerr << "[VulkanBackend] Failed to find GPUs with Vulkan support" << std::endl;
            return false;
        }

        std::vector<VkPhysicalDevice> devices(deviceCount);
        vkEnumeratePhysicalDevices(_instance, &deviceCount, devices.data());

        // 选择第一个支持图形队列的设备
        for (const auto &device : devices)
        {
            uint32_t queueFamilyCount = 0;
            vkGetPhysicalDeviceQueueFamilyProperties(device, &queueFamilyCount, nullptr);

            std::vector<VkQueueFamilyProperties> queueFamilies(queueFamilyCount);
            vkGetPhysicalDeviceQueueFamilyProperties(device, &queueFamilyCount, queueFamilies.data());

            uint32_t i = 0;
            for (const auto &queueFamily : queueFamilies)
            {
                if (queueFamily.queueFlags & VK_QUEUE_GRAPHICS_BIT)
                {
                    _graphicsQueueFamily = i;
                }

                VkBool32 presentSupport = false;
                vkGetPhysicalDeviceSurfaceSupportKHR(device, i, _surface, &presentSupport);
                if (presentSupport)
                {
                    _presentQueueFamily = i;
                }

                if (_graphicsQueueFamily != UINT32_MAX && _presentQueueFamily != UINT32_MAX)
                {
                    _physicalDevice = device;
                    std::cout << "[VulkanBackend] Physical device selected" << std::endl;
                    return true;
                }

                i++;
            }
        }

        std::cerr << "[VulkanBackend] Failed to find a suitable GPU" << std::endl;
        return false;
    }

    bool VulkanBackend::createLogicalDevice()
    {
        std::cout << "[VulkanBackend] Creating logical device..." << std::endl;

        std::vector<VkDeviceQueueCreateInfo> queueCreateInfos;
        std::set<uint32_t> uniqueQueueFamilies = {_graphicsQueueFamily, _presentQueueFamily};

        float queuePriority = 1.0f;
        for (uint32_t queueFamily : uniqueQueueFamilies)
        {
            VkDeviceQueueCreateInfo queueCreateInfo{};
            queueCreateInfo.sType = VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO;
            queueCreateInfo.queueFamilyIndex = queueFamily;
            queueCreateInfo.queueCount = 1;
            queueCreateInfo.pQueuePriorities = &queuePriority;
            queueCreateInfos.push_back(queueCreateInfo);
        }

        VkPhysicalDeviceFeatures deviceFeatures{};

        VkDeviceCreateInfo createInfo{};
        createInfo.sType = VK_STRUCTURE_TYPE_DEVICE_CREATE_INFO;
        createInfo.queueCreateInfoCount = static_cast<uint32_t>(queueCreateInfos.size());
        createInfo.pQueueCreateInfos = queueCreateInfos.data();
        createInfo.pEnabledFeatures = &deviceFeatures;

        // 设备扩展
        const std::vector<const char *> deviceExtensions = {
            VK_KHR_SWAPCHAIN_EXTENSION_NAME};

        createInfo.enabledExtensionCount = static_cast<uint32_t>(deviceExtensions.size());
        createInfo.ppEnabledExtensionNames = deviceExtensions.data();
        createInfo.enabledLayerCount = 0;

        if (vkCreateDevice(_physicalDevice, &createInfo, nullptr, &_device) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to create logical device" << std::endl;
            return false;
        }

        vkGetDeviceQueue(_device, _graphicsQueueFamily, 0, &_graphicsQueue);
        vkGetDeviceQueue(_device, _presentQueueFamily, 0, &_presentQueue);

        std::cout << "[VulkanBackend] Logical device created successfully" << std::endl;
        return true;
    }

    bool VulkanBackend::createSwapchain(const BackendConfig &config)
    {
        std::cout << "[VulkanBackend] Creating swapchain..." << std::endl;

        // 查询交换链支持
        VkSurfaceCapabilitiesKHR capabilities;
        vkGetPhysicalDeviceSurfaceCapabilitiesKHR(_physicalDevice, _surface, &capabilities);

        uint32_t formatCount;
        vkGetPhysicalDeviceSurfaceFormatsKHR(_physicalDevice, _surface, &formatCount, nullptr);
        std::vector<VkSurfaceFormatKHR> formats(formatCount);
        vkGetPhysicalDeviceSurfaceFormatsKHR(_physicalDevice, _surface, &formatCount, formats.data());

        uint32_t presentModeCount;
        vkGetPhysicalDeviceSurfacePresentModesKHR(_physicalDevice, _surface, &presentModeCount, nullptr);
        std::vector<VkPresentModeKHR> presentModes(presentModeCount);
        vkGetPhysicalDeviceSurfacePresentModesKHR(_physicalDevice, _surface, &presentModeCount, presentModes.data());

        // 选择格式
        VkSurfaceFormatKHR surfaceFormat = formats[0];
        for (const auto &availableFormat : formats)
        {
            if (availableFormat.format == VK_FORMAT_B8G8R8A8_SRGB &&
                availableFormat.colorSpace == VK_COLOR_SPACE_SRGB_NONLINEAR_KHR)
            {
                surfaceFormat = availableFormat;
                break;
            }
        }

        // 选择呈现模式
        VkPresentModeKHR presentMode = VK_PRESENT_MODE_FIFO_KHR;
        for (const auto &availablePresentMode : presentModes)
        {
            if (availablePresentMode == VK_PRESENT_MODE_MAILBOX_KHR)
            {
                presentMode = availablePresentMode;
                break;
            }
        }

        // 选择交换范围
        VkExtent2D extent;
        if (capabilities.currentExtent.width != UINT32_MAX)
        {
            extent = capabilities.currentExtent;
        }
        else
        {
            extent = {static_cast<uint32_t>(config.swapchainWidth), static_cast<uint32_t>(config.swapchainHeight)};
            extent.width = std::clamp(extent.width, capabilities.minImageExtent.width, capabilities.maxImageExtent.width);
            extent.height = std::clamp(extent.height, capabilities.minImageExtent.height, capabilities.maxImageExtent.height);
        }

        uint32_t imageCount = capabilities.minImageCount + 1;
        if (capabilities.maxImageCount > 0 && imageCount > capabilities.maxImageCount)
        {
            imageCount = capabilities.maxImageCount;
        }

        VkSwapchainCreateInfoKHR createInfo{};
        createInfo.sType = VK_STRUCTURE_TYPE_SWAPCHAIN_CREATE_INFO_KHR;
        createInfo.surface = _surface;
        createInfo.minImageCount = imageCount;
        createInfo.imageFormat = surfaceFormat.format;
        createInfo.imageColorSpace = surfaceFormat.colorSpace;
        createInfo.imageExtent = extent;
        createInfo.imageArrayLayers = 1;
        createInfo.imageUsage = VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT;

        uint32_t queueFamilyIndices[] = {_graphicsQueueFamily, _presentQueueFamily};

        if (_graphicsQueueFamily != _presentQueueFamily)
        {
            createInfo.imageSharingMode = VK_SHARING_MODE_CONCURRENT;
            createInfo.queueFamilyIndexCount = 2;
            createInfo.pQueueFamilyIndices = queueFamilyIndices;
        }
        else
        {
            createInfo.imageSharingMode = VK_SHARING_MODE_EXCLUSIVE;
        }

        createInfo.preTransform = capabilities.currentTransform;
        createInfo.compositeAlpha = VK_COMPOSITE_ALPHA_OPAQUE_BIT_KHR;
        createInfo.presentMode = presentMode;
        createInfo.clipped = VK_TRUE;
        createInfo.oldSwapchain = VK_NULL_HANDLE;

        if (vkCreateSwapchainKHR(_device, &createInfo, nullptr, &_swapchain) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to create swap chain" << std::endl;
            return false;
        }

        // 获取交换链图像
        vkGetSwapchainImagesKHR(_device, _swapchain, &imageCount, nullptr);
        _swapchainImages.resize(imageCount);
        vkGetSwapchainImagesKHR(_device, _swapchain, &imageCount, _swapchainImages.data());

        _swapchainImageFormat = surfaceFormat.format;
        _swapchainExtent = extent;

        // 创建图像视图
        _swapchainImageViews.resize(_swapchainImages.size());
        for (size_t i = 0; i < _swapchainImages.size(); i++)
        {
            VkImageViewCreateInfo viewCreateInfo{};
            viewCreateInfo.sType = VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO;
            viewCreateInfo.image = _swapchainImages[i];
            viewCreateInfo.viewType = VK_IMAGE_VIEW_TYPE_2D;
            viewCreateInfo.format = _swapchainImageFormat;
            viewCreateInfo.components.r = VK_COMPONENT_SWIZZLE_IDENTITY;
            viewCreateInfo.components.g = VK_COMPONENT_SWIZZLE_IDENTITY;
            viewCreateInfo.components.b = VK_COMPONENT_SWIZZLE_IDENTITY;
            viewCreateInfo.components.a = VK_COMPONENT_SWIZZLE_IDENTITY;
            viewCreateInfo.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
            viewCreateInfo.subresourceRange.baseMipLevel = 0;
            viewCreateInfo.subresourceRange.levelCount = 1;
            viewCreateInfo.subresourceRange.baseArrayLayer = 0;
            viewCreateInfo.subresourceRange.layerCount = 1;

            if (vkCreateImageView(_device, &viewCreateInfo, nullptr, &_swapchainImageViews[i]) != VK_SUCCESS)
            {
                std::cerr << "[VulkanBackend] Failed to create image views" << std::endl;
                return false;
            }
        }

        std::cout << "[VulkanBackend] Swapchain created successfully" << std::endl;
        return true;
    }

    bool VulkanBackend::createRenderPass()
    {
        std::cout << "[VulkanBackend] Creating render pass..." << std::endl;

        VkAttachmentDescription colorAttachment{};
        colorAttachment.format = _swapchainImageFormat;
        colorAttachment.samples = VK_SAMPLE_COUNT_1_BIT;
        colorAttachment.loadOp = VK_ATTACHMENT_LOAD_OP_CLEAR;
        colorAttachment.storeOp = VK_ATTACHMENT_STORE_OP_STORE;
        colorAttachment.stencilLoadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE;
        colorAttachment.stencilStoreOp = VK_ATTACHMENT_STORE_OP_DONT_CARE;
        colorAttachment.initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;
        colorAttachment.finalLayout = VK_IMAGE_LAYOUT_PRESENT_SRC_KHR;

        VkAttachmentReference colorAttachmentRef{};
        colorAttachmentRef.attachment = 0;
        colorAttachmentRef.layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;

        VkSubpassDescription subpass{};
        subpass.pipelineBindPoint = VK_PIPELINE_BIND_POINT_GRAPHICS;
        subpass.colorAttachmentCount = 1;
        subpass.pColorAttachments = &colorAttachmentRef;

        VkSubpassDependency dependency{};
        dependency.srcSubpass = VK_SUBPASS_EXTERNAL;
        dependency.dstSubpass = 0;
        dependency.srcStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
        dependency.srcAccessMask = 0;
        dependency.dstStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
        dependency.dstAccessMask = VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT;

        VkRenderPassCreateInfo renderPassInfo{};
        renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_CREATE_INFO;
        renderPassInfo.attachmentCount = 1;
        renderPassInfo.pAttachments = &colorAttachment;
        renderPassInfo.subpassCount = 1;
        renderPassInfo.pSubpasses = &subpass;
        renderPassInfo.dependencyCount = 1;
        renderPassInfo.pDependencies = &dependency;

        if (vkCreateRenderPass(_device, &renderPassInfo, nullptr, &_renderPass) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to create render pass" << std::endl;
            return false;
        }

        std::cout << "[VulkanBackend] Render pass created successfully" << std::endl;
        return true;
    }

    bool VulkanBackend::createFramebuffers()
    {
        std::cout << "[VulkanBackend] Creating framebuffers..." << std::endl;

        _swapchainFramebuffers.resize(_swapchainImageViews.size());

        for (size_t i = 0; i < _swapchainImageViews.size(); i++)
        {
            VkImageView attachments[] = {
                _swapchainImageViews[i]};

            VkFramebufferCreateInfo framebufferInfo{};
            framebufferInfo.sType = VK_STRUCTURE_TYPE_FRAMEBUFFER_CREATE_INFO;
            framebufferInfo.renderPass = _renderPass;
            framebufferInfo.attachmentCount = 1;
            framebufferInfo.pAttachments = attachments;
            framebufferInfo.width = _swapchainExtent.width;
            framebufferInfo.height = _swapchainExtent.height;
            framebufferInfo.layers = 1;

            if (vkCreateFramebuffer(_device, &framebufferInfo, nullptr, &_swapchainFramebuffers[i]) != VK_SUCCESS)
            {
                std::cerr << "[VulkanBackend] Failed to create framebuffer" << std::endl;
                return false;
            }
        }

        std::cout << "[VulkanBackend] Framebuffers created successfully" << std::endl;
        return true;
    }

    bool VulkanBackend::createCommandPool()
    {
        std::cout << "[VulkanBackend] Creating command pool..." << std::endl;

        VkCommandPoolCreateInfo poolInfo{};
        poolInfo.sType = VK_STRUCTURE_TYPE_COMMAND_POOL_CREATE_INFO;
        poolInfo.flags = VK_COMMAND_POOL_CREATE_RESET_COMMAND_BUFFER_BIT;
        poolInfo.queueFamilyIndex = _graphicsQueueFamily;

        if (vkCreateCommandPool(_device, &poolInfo, nullptr, &_commandPool) != VK_SUCCESS)
        {
            std::cerr << "[VulkanBackend] Failed to create command pool" << std::endl;
            return false;
        }

        std::cout << "[VulkanBackend] Command pool created successfully" << std::endl;
        return true;
    }

    bool VulkanBackend::createSyncObjects()
    {
        std::cout << "[VulkanBackend] Creating sync objects..." << std::endl;

        _imageAvailableSemaphores.resize(MAX_FRAMES_IN_FLIGHT);
        _renderFinishedSemaphores.resize(MAX_FRAMES_IN_FLIGHT);
        _inFlightFences.resize(MAX_FRAMES_IN_FLIGHT);

        VkSemaphoreCreateInfo semaphoreInfo{};
        semaphoreInfo.sType = VK_STRUCTURE_TYPE_SEMAPHORE_CREATE_INFO;

        VkFenceCreateInfo fenceInfo{};
        fenceInfo.sType = VK_STRUCTURE_TYPE_FENCE_CREATE_INFO;
        fenceInfo.flags = VK_FENCE_CREATE_SIGNALED_BIT;

        for (size_t i = 0; i < MAX_FRAMES_IN_FLIGHT; i++)
        {
            if (vkCreateSemaphore(_device, &semaphoreInfo, nullptr, &_imageAvailableSemaphores[i]) != VK_SUCCESS ||
                vkCreateSemaphore(_device, &semaphoreInfo, nullptr, &_renderFinishedSemaphores[i]) != VK_SUCCESS ||
                vkCreateFence(_device, &fenceInfo, nullptr, &_inFlightFences[i]) != VK_SUCCESS)
            {
                std::cerr << "[VulkanBackend] Failed to create sync objects" << std::endl;
                return false;
            }
        }

        std::cout << "[VulkanBackend] Sync objects created successfully" << std::endl;
        return true;
    }

    void VulkanBackend::printDeviceInfo()
    {
        VkPhysicalDeviceProperties deviceProperties;
        vkGetPhysicalDeviceProperties(_physicalDevice, &deviceProperties);

        std::cout << "[VulkanBackend] Device: " << deviceProperties.deviceName << std::endl;
        std::cout << "[VulkanBackend] API Version: "
                  << VK_VERSION_MAJOR(deviceProperties.apiVersion) << "."
                  << VK_VERSION_MINOR(deviceProperties.apiVersion) << "."
                  << VK_VERSION_PATCH(deviceProperties.apiVersion) << std::endl;
        std::cout << "[VulkanBackend] Driver Version: " << deviceProperties.driverVersion << std::endl;
        std::cout << "[VulkanBackend] Vendor ID: 0x" << std::hex << deviceProperties.vendorID << std::dec << std::endl;
    }

    uint32_t VulkanBackend::findMemoryType(uint32_t typeFilter, VkMemoryPropertyFlags properties)
    {
        VkPhysicalDeviceMemoryProperties memProperties;
        vkGetPhysicalDeviceMemoryProperties(_physicalDevice, &memProperties);

        for (uint32_t i = 0; i < memProperties.memoryTypeCount; i++)
        {
            if ((typeFilter & (1 << i)) && (memProperties.memoryTypes[i].propertyFlags & properties) == properties)
            {
                return i;
            }
        }

        throw std::runtime_error("Failed to find suitable memory type");
    }

} // namespace USG
