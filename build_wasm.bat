@echo off
rem Build and publish WebAssembly version of LearnWebGPU
rem Usage: double-click or run from PowerShell / cmd

setlocal

rem ----------------------------------------------------------------
rem 1) Setup emsdk environment (adjust path if you installed elsewhere)
call "C:\dev\emsdk\emsdk_env.bat"
if %errorlevel% neq 0 (
  echo [ERROR] Could not locate emsdk_env.bat.  Please verify emsdk installation path. & goto :eof
)

rem ----------------------------------------------------------------
rem 2) Configure (uses Ninja generator; falls back to default if Ninja not found)
if exist build_wasm (
  rmdir /s /q build_wasm
)

set CMAKE_GENERATOR=Ninja
where ninja >nul 2>&1 || set CMAKE_GENERATOR="Unix Makefiles"

echo [INFO] Configuring CMake for WebAssembly...
emcmake cmake -S . -B build_wasm -G %CMAKE_GENERATOR% ^
  -DCMAKE_BUILD_TYPE=Release ^
  -DDEV_MODE=OFF ^
  -DWEBGPU_BACKEND=EMDAWNWEBGPU
if %errorlevel% neq 0 (
  echo [ERROR] CMake configuration failed. & goto :eof
)

rem ----------------------------------------------------------------
rem 3) Build
cmake --build build_wasm --config Release -j %NUMBER_OF_PROCESSORS%
if %errorlevel% neq 0 (
  echo [ERROR] Build failed. & goto :eof
)

rem ----------------------------------------------------------------
rem 4) Copy artefacts to redist_wasm
if not exist redist_wasm mkdir redist_wasm
xcopy /y /d build_wasm\App.html redist_wasm\
xcopy /y /d build_wasm\App.js   redist_wasm\
xcopy /y /d build_wasm\App.wasm redist_wasm\
robocopy resources redist_wasm\resources /mir

if %errorlevel% lss 8 (
  echo [SUCCESS] WebAssembly artefacts published to redist_wasm\
) else (
  echo [WARNING] robocopy returned code %errorlevel%, please check copied resources.
)

endlocal 