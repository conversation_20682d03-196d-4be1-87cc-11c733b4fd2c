#include "WebGPUBackend.h"

namespace USG {

// WebGPUPipeline实现
WebGPUPipeline::WebGPUPipeline(WGPURenderPipeline pipeline, const PipelineDesc& desc)
    : _renderPipeline(pipeline), _computePipeline(nullptr), _isComputePipeline(false), _desc(desc) {
}

WebGPUPipeline::WebGPUPipeline(WGPUComputePipeline pipeline, const PipelineDesc& desc)
    : _renderPipeline(nullptr), _computePipeline(pipeline), _isComputePipeline(true), _desc(desc) {
}

WebGPUPipeline::~WebGPUPipeline() {
    if (_renderPipeline) {
        wgpuRenderPipelineRelease(_renderPipeline);
    }
    if (_computePipeline) {
        wgpuComputePipelineRelease(_computePipeline);
    }
}

} // namespace USG
