# USG Backend 自动化编译和测试脚本
# 功能：编译项目、复制DLL、运行测试

param(
    [string]$BuildType = "Release",
    [switch]$Clean = $false,
    [switch]$Test = $true,
    [switch]$Verbose = $false
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" "Green"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" "Red"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ️  $Message" "Cyan"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠️  $Message" "Yellow"
}

# 获取脚本目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = $ScriptDir
$BuildDir = Join-Path $ProjectRoot "build_test"

Write-Info "USG Backend 自动化编译和测试"
Write-Info "项目根目录: $ProjectRoot"
Write-Info "构建目录: $BuildDir"
Write-Info "构建类型: $BuildType"

# 检查必要的工具
Write-Info "检查构建工具..."

# 检查CMake
try {
    $cmakeVersion = cmake --version | Select-Object -First 1
    Write-Success "找到 CMake: $cmakeVersion"
} catch {
    Write-Error "未找到 CMake，请安装 CMake"
    exit 1
}

# 检查Visual Studio构建工具
try {
    $msbuildPath = Get-Command msbuild -ErrorAction SilentlyContinue
    if ($msbuildPath) {
        Write-Success "找到 MSBuild: $($msbuildPath.Source)"
    } else {
        Write-Warning "未找到 MSBuild，尝试使用 Visual Studio Developer Command Prompt"
    }
} catch {
    Write-Warning "MSBuild 检查失败"
}

# 清理构建目录（如果需要）
if ($Clean -and (Test-Path $BuildDir)) {
    Write-Info "清理构建目录..."
    Remove-Item -Recurse -Force $BuildDir
    Write-Success "构建目录已清理"
}

# 创建构建目录
if (-not (Test-Path $BuildDir)) {
    Write-Info "创建构建目录..."
    New-Item -ItemType Directory -Path $BuildDir | Out-Null
    Write-Success "构建目录已创建"
}

# 进入构建目录
Set-Location $BuildDir

try {
    # 配置项目
    Write-Info "配置 CMake 项目..."
    $cmakeArgs = @(
        ".."
        "-DCMAKE_BUILD_TYPE=$BuildType"
        "-DUSG_BUILD_SCENE_TEST=ON"
        "-DUSG_BUILD_WEBGPU_BACKEND=ON"
        "-DUSG_BUILD_VULKAN_BACKEND=ON"
    )
    
    if ($Verbose) {
        $cmakeArgs += "--verbose"
    }
    
    & cmake @cmakeArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Error "CMake 配置失败"
        exit 1
    }
    Write-Success "CMake 配置完成"

    # 编译项目
    Write-Info "编译项目..."
    $buildArgs = @(
        "--build", "."
        "--config", $BuildType
        "--target", "scene_test_app"
    )
    
    if ($Verbose) {
        $buildArgs += "--verbose"
    }
    
    & cmake @buildArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Error "编译失败"
        exit 1
    }
    Write-Success "编译完成"

    # 检查生成的文件
    $exePath = Join-Path $BuildDir "$BuildType\scene_test_app.exe"
    if (Test-Path $exePath) {
        Write-Success "找到可执行文件: $exePath"
    } else {
        Write-Error "未找到可执行文件: $exePath"
        exit 1
    }

    # 检查DLL文件
    $dllPath = Join-Path $BuildDir "$BuildType\wgpu_native.dll"
    if (Test-Path $dllPath) {
        Write-Success "找到 wgpu_native.dll: $dllPath"
    } else {
        Write-Warning "未找到 wgpu_native.dll，WebGPU 后端可能无法工作"
    }

    # 运行测试（如果启用）
    if ($Test) {
        Write-Info "准备运行测试..."
        Write-Info "可执行文件路径: $exePath"
        
        # 显示控制说明
        Write-Info "程序控制说明:"
        Write-Info "  • 数字键 1: 切换到 WebGPU 后端"
        Write-Info "  • 数字键 2: 切换到 Vulkan 后端"  
        Write-Info "  • 数字键 3: 切换到 OpenGL 后端"
        Write-Info "  • ESC: 退出程序"
        Write-Info ""
        Write-Info "程序将在5秒后启动..."
        Start-Sleep -Seconds 5
        
        # 启动程序
        Write-Info "启动测试程序..."
        try {
            & $exePath
            Write-Success "程序正常退出"
        } catch {
            Write-Error "程序运行出错: $($_.Exception.Message)"
        }
    }

} finally {
    # 返回原目录
    Set-Location $ProjectRoot
}

Write-Success "构建和测试完成！"
