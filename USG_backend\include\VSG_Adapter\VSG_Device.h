#pragma once

#include <USG_Backend/RenderBackend.h>
#include <USG_Backend/BackendTypes.h>
#include <memory>
#include <unordered_map>

// Vulkan类型定义（用于VSG兼容性）
#ifndef VK_DEFINE_HANDLE
#define VK_DEFINE_HANDLE(object) typedef struct object##_T *object;
#endif

VK_DEFINE_HANDLE(VkDevice)
VK_DEFINE_HANDLE(VkPhysicalDevice)
VK_DEFINE_HANDLE(VkInstance)
VK_DEFINE_HANDLE(VkQueue)
VK_DEFINE_HANDLE(VkBuffer)
VK_DEFINE_HANDLE(VkImage)
VK_DEFINE_HANDLE(VkImageView)
VK_DEFINE_HANDLE(VkSampler)
VK_DEFINE_HANDLE(VkShaderModule)
VK_DEFINE_HANDLE(VkPipeline)
VK_DEFINE_HANDLE(VkPipelineLayout)
VK_DEFINE_HANDLE(VkRenderPass)
VK_DEFINE_HANDLE(VkDescriptorSet)
VK_DEFINE_HANDLE(VkDescriptorSetLayout)
VK_DEFINE_HANDLE(VkDescriptorPool)
VK_DEFINE_HANDLE(VkDeviceMemory)
VK_DEFINE_HANDLE(VkSemaphore)
VK_DEFINE_HANDLE(VkFence)
VK_DEFINE_HANDLE(VkCommandBuffer)
VK_DEFINE_HANDLE(VkQueryPool)

// Vulkan枚举和结构体（简化版本）
typedef enum VkResult
{
    VK_SUCCESS = 0,
    VK_NOT_READY = 1,
    VK_TIMEOUT = 2,
    VK_ERROR_OUT_OF_HOST_MEMORY = -1,
    VK_ERROR_OUT_OF_DEVICE_MEMORY = -2,
    VK_ERROR_INITIALIZATION_FAILED = -3,
    VK_ERROR_DEVICE_LOST = -4,
    VK_ERROR_MEMORY_MAP_FAILED = -5,
    VK_ERROR_LAYER_NOT_PRESENT = -6,
    VK_ERROR_EXTENSION_NOT_PRESENT = -7,
    VK_ERROR_FEATURE_NOT_PRESENT = -8,
    VK_ERROR_INCOMPATIBLE_DRIVER = -9,
    VK_ERROR_TOO_MANY_OBJECTS = -10,
    VK_ERROR_FORMAT_NOT_SUPPORTED = -11
} VkResult;

typedef uint32_t VkFlags;
typedef VkFlags VkBufferUsageFlags;
typedef VkFlags VkImageUsageFlags;
typedef VkFlags VkMemoryPropertyFlags;
typedef VkFlags VkCommandBufferResetFlags;
typedef VkFlags VkQueryControlFlags;
typedef VkFlags VkPipelineStageFlags;
typedef VkFlags VkDependencyFlags;

typedef enum VkBufferUsageFlagBits
{
    VK_BUFFER_USAGE_TRANSFER_SRC_BIT = 0x00000001,
    VK_BUFFER_USAGE_TRANSFER_DST_BIT = 0x00000002,
    VK_BUFFER_USAGE_UNIFORM_TEXEL_BUFFER_BIT = 0x00000004,
    VK_BUFFER_USAGE_STORAGE_TEXEL_BUFFER_BIT = 0x00000008,
    VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT = 0x00000010,
    VK_BUFFER_USAGE_STORAGE_BUFFER_BIT = 0x00000020,
    VK_BUFFER_USAGE_INDEX_BUFFER_BIT = 0x00000040,
    VK_BUFFER_USAGE_VERTEX_BUFFER_BIT = 0x00000080,
    VK_BUFFER_USAGE_INDIRECT_BUFFER_BIT = 0x00000100
} VkBufferUsageFlagBits;

typedef enum VkImageUsageFlagBits
{
    VK_IMAGE_USAGE_TRANSFER_SRC_BIT = 0x00000001,
    VK_IMAGE_USAGE_TRANSFER_DST_BIT = 0x00000002,
    VK_IMAGE_USAGE_SAMPLED_BIT = 0x00000004,
    VK_IMAGE_USAGE_STORAGE_BIT = 0x00000008,
    VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT = 0x00000010,
    VK_IMAGE_USAGE_DEPTH_STENCIL_ATTACHMENT_BIT = 0x00000020
} VkImageUsageFlagBits;

typedef enum VkSharingMode
{
    VK_SHARING_MODE_EXCLUSIVE = 0,
    VK_SHARING_MODE_CONCURRENT = 1
} VkSharingMode;

typedef enum VkIndexType
{
    VK_INDEX_TYPE_UINT16 = 0,
    VK_INDEX_TYPE_UINT32 = 1
} VkIndexType;

typedef enum VkPipelineBindPoint
{
    VK_PIPELINE_BIND_POINT_GRAPHICS = 0,
    VK_PIPELINE_BIND_POINT_COMPUTE = 1
} VkPipelineBindPoint;

typedef enum VkSubpassContents
{
    VK_SUBPASS_CONTENTS_INLINE = 0,
    VK_SUBPASS_CONTENTS_SECONDARY_COMMAND_BUFFERS = 1
} VkSubpassContents;

typedef enum VkPipelineStageFlagBits
{
    VK_PIPELINE_STAGE_TOP_OF_PIPE_BIT = 0x00000001,
    VK_PIPELINE_STAGE_DRAW_INDIRECT_BIT = 0x00000002,
    VK_PIPELINE_STAGE_VERTEX_INPUT_BIT = 0x00000004,
    VK_PIPELINE_STAGE_VERTEX_SHADER_BIT = 0x00000008,
    VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT = 0x00000080,
    VK_PIPELINE_STAGE_EARLY_FRAGMENT_TESTS_BIT = 0x00000100,
    VK_PIPELINE_STAGE_LATE_FRAGMENT_TESTS_BIT = 0x00000200,
    VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT = 0x00000400,
    VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT = 0x00000800,
    VK_PIPELINE_STAGE_TRANSFER_BIT = 0x00001000,
    VK_PIPELINE_STAGE_BOTTOM_OF_PIPE_BIT = 0x00002000,
    VK_PIPELINE_STAGE_HOST_BIT = 0x00004000,
    VK_PIPELINE_STAGE_ALL_GRAPHICS_BIT = 0x00008000,
    VK_PIPELINE_STAGE_ALL_COMMANDS_BIT = 0x00010000
} VkPipelineStageFlagBits;

typedef uint64_t VkDeviceSize;
typedef uint32_t VkBool32;

// 简化的Vulkan结构体
struct VkBufferCreateInfo
{
    uint32_t sType;
    const void *pNext;
    VkFlags flags;
    VkDeviceSize size;
    VkBufferUsageFlags usage;
    VkSharingMode sharingMode;
    uint32_t queueFamilyIndexCount;
    const uint32_t *pQueueFamilyIndices;
};

struct VkImageCreateInfo
{
    uint32_t sType;
    const void *pNext;
    VkFlags flags;
    uint32_t imageType;
    uint32_t format;
    struct
    {
        uint32_t width;
        uint32_t height;
        uint32_t depth;
    } extent;
    uint32_t mipLevels;
    uint32_t arrayLayers;
    uint32_t samples;
    uint32_t tiling;
    VkImageUsageFlags usage;
    VkSharingMode sharingMode;
    uint32_t queueFamilyIndexCount;
    const uint32_t *pQueueFamilyIndices;
    uint32_t initialLayout;
};

struct VkMemoryAllocateInfo
{
    uint32_t sType;
    const void *pNext;
    VkDeviceSize allocationSize;
    uint32_t memoryTypeIndex;
};

// 其他必要的结构体声明
struct VkImageViewCreateInfo;
struct VkSamplerCreateInfo;
struct VkShaderModuleCreateInfo;
struct VkPipelineLayoutCreateInfo;
struct VkRenderPassCreateInfo;
struct VkGraphicsPipelineCreateInfo;
struct VkDescriptorSetLayoutCreateInfo;
struct VkDescriptorPoolCreateInfo;
struct VkDescriptorSetAllocateInfo;
struct VkSemaphoreCreateInfo;
struct VkFenceCreateInfo;
struct VkCommandBufferBeginInfo;
struct VkRenderPassBeginInfo;
struct VkSubmitInfo;
struct VkBufferCopy;
struct VkImageCopy;
struct VkBufferImageCopy;
struct VkMemoryBarrier;
struct VkBufferMemoryBarrier;
struct VkImageMemoryBarrier;

#define VK_NULL_HANDLE nullptr

namespace USG
{

    // 前向声明
    class TypeConverter;
    class ResourceManager;

    /**
     * @brief VSG Device适配器类
     *
     * 提供与VSG Device完全兼容的API接口，内部转发到USG Backend。
     * 实现零侵入的VSG到WebGPU后端的透明替换。
     */
    class VSG_Device
    {
    public:
        /**
         * @brief 构造函数
         * @param backendType 后端类型，默认为WebGPU
         */
        explicit VSG_Device(BackendType backendType = BackendType::WebGPU);

        /**
         * @brief 析构函数
         */
        ~VSG_Device();

        // 禁止拷贝和移动
        VSG_Device(const VSG_Device &) = delete;
        VSG_Device &operator=(const VSG_Device &) = delete;
        VSG_Device(VSG_Device &&) = delete;
        VSG_Device &operator=(VSG_Device &&) = delete;

        /**
         * @brief 初始化设备
         * @param config 后端配置
         * @return 是否初始化成功
         */
        bool initialize(const BackendConfig &config = {});

        /**
         * @brief 清理设备
         */
        void cleanup();

        // ========== VSG兼容的Vulkan API接口 ==========

        // 缓冲区管理
        VkResult createBuffer(const VkBufferCreateInfo *pCreateInfo, VkBuffer *pBuffer);
        void destroyBuffer(VkBuffer buffer);
        VkResult bindBufferMemory(VkBuffer buffer, VkDeviceMemory memory, VkDeviceSize memoryOffset);

        // 图像管理
        VkResult createImage(const VkImageCreateInfo *pCreateInfo, VkImage *pImage);
        void destroyImage(VkImage image);
        VkResult bindImageMemory(VkImage image, VkDeviceMemory memory, VkDeviceSize memoryOffset);
        VkResult createImageView(const VkImageViewCreateInfo *pCreateInfo, VkImageView *pImageView);
        void destroyImageView(VkImageView imageView);

        // 采样器管理
        VkResult createSampler(const VkSamplerCreateInfo *pCreateInfo, VkSampler *pSampler);
        void destroySampler(VkSampler sampler);

        // 着色器管理
        VkResult createShaderModule(const VkShaderModuleCreateInfo *pCreateInfo, VkShaderModule *pShaderModule);
        void destroyShaderModule(VkShaderModule shaderModule);

        // 管线管理
        VkResult createPipelineLayout(const VkPipelineLayoutCreateInfo *pCreateInfo, VkPipelineLayout *pPipelineLayout);
        void destroyPipelineLayout(VkPipelineLayout pipelineLayout);
        VkResult createRenderPass(const VkRenderPassCreateInfo *pCreateInfo, VkRenderPass *pRenderPass);
        void destroyRenderPass(VkRenderPass renderPass);
        VkResult createGraphicsPipelines(uint32_t createInfoCount, const VkGraphicsPipelineCreateInfo *pCreateInfos, VkPipeline *pPipelines);
        void destroyPipeline(VkPipeline pipeline);

        // 描述符管理
        VkResult createDescriptorSetLayout(const VkDescriptorSetLayoutCreateInfo *pCreateInfo, VkDescriptorSetLayout *pSetLayout);
        void destroyDescriptorSetLayout(VkDescriptorSetLayout descriptorSetLayout);
        VkResult createDescriptorPool(const VkDescriptorPoolCreateInfo *pCreateInfo, VkDescriptorPool *pDescriptorPool);
        void destroyDescriptorPool(VkDescriptorPool descriptorPool);
        VkResult allocateDescriptorSets(const VkDescriptorSetAllocateInfo *pAllocateInfo, VkDescriptorSet *pDescriptorSets);

        // 内存管理
        VkResult allocateMemory(const VkMemoryAllocateInfo *pAllocateInfo, VkDeviceMemory *pMemory);
        void freeMemory(VkDeviceMemory memory);
        VkResult mapMemory(VkDeviceMemory memory, VkDeviceSize offset, VkDeviceSize size, void **ppData);
        void unmapMemory(VkDeviceMemory memory);

        // 队列操作
        void getDeviceQueue(uint32_t queueFamilyIndex, uint32_t queueIndex, VkQueue *pQueue);
        VkResult queueSubmit(VkQueue queue, uint32_t submitCount, const VkSubmitInfo *pSubmits, VkFence fence);
        VkResult queueWaitIdle(VkQueue queue);
        VkResult deviceWaitIdle();

        // 同步对象
        VkResult createSemaphore(const VkSemaphoreCreateInfo *pCreateInfo, VkSemaphore *pSemaphore);
        void destroySemaphore(VkSemaphore semaphore);
        VkResult createFence(const VkFenceCreateInfo *pCreateInfo, VkFence *pFence);
        void destroyFence(VkFence fence);
        VkResult waitForFences(uint32_t fenceCount, const VkFence *pFences, VkBool32 waitAll, uint64_t timeout);
        VkResult resetFences(uint32_t fenceCount, const VkFence *pFences);

        // ========== USG Backend接口 ==========

        /**
         * @brief 获取后端实例
         * @return 后端实例指针
         */
        RenderBackend *getBackend() const { return _backend.get(); }

        /**
         * @brief 获取后端类型
         * @return 后端类型
         */
        BackendType getBackendType() const;

        /**
         * @brief 检查是否已初始化
         * @return 是否已初始化
         */
        bool isInitialized() const { return _initialized; }

        /**
         * @brief 设置调试模式
         * @param enabled 是否启用调试
         */
        void setDebugEnabled(bool enabled) { _debugEnabled = enabled; }

        /**
         * @brief 检查是否启用调试
         * @return 是否启用调试
         */
        bool isDebugEnabled() const { return _debugEnabled; }

    private:
        // 后端实例
        std::unique_ptr<RenderBackend> _backend;
        std::unique_ptr<TypeConverter> _converter;
        std::unique_ptr<ResourceManager> _resourceManager;

        // 状态管理
        bool _initialized = false;
        bool _debugEnabled = false;
        BackendType _backendType;

        // 句柄计数器
        std::atomic<uint64_t> _handleCounter{0x1000};
        std::atomic<uint64_t> _memoryHandleCounter{0x2000};

        // 资源映射表
        std::unordered_map<VkBuffer, BackendBuffer *> _bufferMap;
        std::unordered_map<VkImage, BackendTexture *> _imageMap;
        std::unordered_map<VkPipeline, BackendPipeline *> _pipelineMap;
        std::unordered_map<VkShaderModule, BackendShader *> _shaderMap;
        std::unordered_map<VkDescriptorSet, BackendDescriptorSet *> _descriptorSetMap;
        std::unordered_map<VkFence, BackendFence *> _fenceMap;
        std::unordered_map<VkSemaphore, BackendSemaphore *> _semaphoreMap;

        // 内部辅助方法
        VkBuffer createVkHandle();
        VkImage createVkImageHandle();
        VkPipeline createVkPipelineHandle();
        VkShaderModule createVkShaderHandle();
        VkDescriptorSet createVkDescriptorSetHandle();
        VkFence createVkFenceHandle();
        VkSemaphore createVkSemaphoreHandle();

        void registerBuffer(VkBuffer handle, BackendBuffer *buffer);
        void registerImage(VkImage handle, BackendTexture *texture);
        void registerPipeline(VkPipeline handle, BackendPipeline *pipeline);
        void registerShader(VkShaderModule handle, BackendShader *shader);
        void registerDescriptorSet(VkDescriptorSet handle, BackendDescriptorSet *descriptorSet);
        void registerFence(VkFence handle, BackendFence *fence);
        void registerSemaphore(VkSemaphore handle, BackendSemaphore *semaphore);

        BackendBuffer *getBackendBuffer(VkBuffer handle);
        BackendTexture *getBackendTexture(VkImage handle);
        BackendPipeline *getBackendPipeline(VkPipeline handle);
        BackendShader *getBackendShader(VkShaderModule handle);
        BackendDescriptorSet *getBackendDescriptorSet(VkDescriptorSet handle);
        BackendFence *getBackendFence(VkFence handle);
        BackendSemaphore *getBackendSemaphore(VkSemaphore handle);

        void unregisterBuffer(VkBuffer handle);
        void unregisterImage(VkImage handle);
        void unregisterPipeline(VkPipeline handle);
        void unregisterShader(VkShaderModule handle);
        void unregisterDescriptorSet(VkDescriptorSet handle);
        void unregisterFence(VkFence handle);
        void unregisterSemaphore(VkSemaphore handle);

        // 调试输出
        void logCall(const std::string &function, const std::string &details = "") const;
        void logError(const std::string &function, const std::string &error) const;
    };

} // namespace USG
