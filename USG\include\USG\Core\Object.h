#pragma once

#include <string>
#include <atomic>
#include <type_traits>

namespace usg {

// 前向声明
class Serializer;
class Deserializer;

/**
 * @brief 基础对象类，所有USG对象的根类
 * 
 * 提供基础的对象模型功能：
 * - 类型信息和RTTI
 * - 引用计数内存管理
 * - 序列化支持
 */
class Object {
public:
    Object() = default;
    virtual ~Object() = default;
    
    // 禁止拷贝，允许移动
    Object(const Object&) = delete;
    Object& operator=(const Object&) = delete;
    Object(Object&&) = default;
    Object& operator=(Object&&) = default;
    
    /**
     * @brief 获取类名
     * @return 类名字符串
     */
    virtual std::string className() const = 0;
    
    /**
     * @brief 检查是否为相同类型
     * @param obj 要比较的对象
     * @return 是否为相同类型
     */
    virtual bool isSameKindAs(const Object* obj) const {
        return obj && (className() == obj->className());
    }
    
    /**
     * @brief 序列化对象
     * @param serializer 序列化器
     */
    virtual void serialize(Serializer& serializer) const {}
    
    /**
     * @brief 反序列化对象
     * @param deserializer 反序列化器
     */
    virtual void deserialize(Deserializer& deserializer) {}
    
    /**
     * @brief 增加引用计数
     */
    void ref() const { 
        ++_refCount; 
    }
    
    /**
     * @brief 减少引用计数，计数为0时删除对象
     */
    void unref() const { 
        if (--_refCount == 0) {
            delete this; 
        }
    }
    
    /**
     * @brief 获取当前引用计数
     * @return 引用计数
     */
    int referenceCount() const { 
        return _refCount.load(); 
    }
    
protected:
    mutable std::atomic<int> _refCount{0};
};

/**
 * @brief 智能指针类型，提供自动引用计数管理
 * 
 * @tparam T 指向的对象类型，必须继承自Object
 */
template<typename T>
class ref_ptr {
    static_assert(std::is_base_of_v<Object, T>, "T must inherit from Object");
    
public:
    using element_type = T;
    
    /**
     * @brief 默认构造函数
     */
    ref_ptr() : _ptr(nullptr) {}
    
    /**
     * @brief 从原始指针构造
     * @param ptr 原始指针
     */
    explicit ref_ptr(T* ptr) : _ptr(ptr) { 
        if (_ptr) _ptr->ref(); 
    }
    
    /**
     * @brief 拷贝构造函数
     * @param rp 其他ref_ptr
     */
    ref_ptr(const ref_ptr& rp) : _ptr(rp._ptr) { 
        if (_ptr) _ptr->ref(); 
    }
    
    /**
     * @brief 移动构造函数
     * @param rp 其他ref_ptr
     */
    ref_ptr(ref_ptr&& rp) noexcept : _ptr(rp._ptr) { 
        rp._ptr = nullptr; 
    }
    
    /**
     * @brief 从兼容类型构造
     * @tparam U 兼容的类型
     * @param rp 其他ref_ptr
     */
    template<typename U>
    ref_ptr(const ref_ptr<U>& rp) : _ptr(rp.get()) {
        static_assert(std::is_convertible_v<U*, T*>, "U* must be convertible to T*");
        if (_ptr) _ptr->ref();
    }
    
    /**
     * @brief 析构函数
     */
    ~ref_ptr() { 
        if (_ptr) _ptr->unref(); 
    }
    
    /**
     * @brief 拷贝赋值操作符
     * @param rp 其他ref_ptr
     * @return 自身引用
     */
    ref_ptr& operator=(const ref_ptr& rp) {
        if (_ptr != rp._ptr) {
            T* old_ptr = _ptr;
            _ptr = rp._ptr;
            if (_ptr) _ptr->ref();
            if (old_ptr) old_ptr->unref();
        }
        return *this;
    }
    
    /**
     * @brief 移动赋值操作符
     * @param rp 其他ref_ptr
     * @return 自身引用
     */
    ref_ptr& operator=(ref_ptr&& rp) noexcept {
        if (this != &rp) {
            if (_ptr) _ptr->unref();
            _ptr = rp._ptr;
            rp._ptr = nullptr;
        }
        return *this;
    }
    
    /**
     * @brief 从原始指针赋值
     * @param ptr 原始指针
     * @return 自身引用
     */
    ref_ptr& operator=(T* ptr) {
        if (_ptr != ptr) {
            T* old_ptr = _ptr;
            _ptr = ptr;
            if (_ptr) _ptr->ref();
            if (old_ptr) old_ptr->unref();
        }
        return *this;
    }
    
    /**
     * @brief 解引用操作符
     * @return 对象引用
     */
    T& operator*() const { 
        return *_ptr; 
    }
    
    /**
     * @brief 成员访问操作符
     * @return 对象指针
     */
    T* operator->() const { 
        return _ptr; 
    }
    
    /**
     * @brief 获取原始指针
     * @return 原始指针
     */
    T* get() const { 
        return _ptr; 
    }
    
    /**
     * @brief 布尔转换操作符
     * @return 是否为非空指针
     */
    explicit operator bool() const { 
        return _ptr != nullptr; 
    }
    
    /**
     * @brief 重置指针
     * @param ptr 新的指针值
     */
    void reset(T* ptr = nullptr) {
        *this = ptr;
    }
    
    /**
     * @brief 释放指针所有权
     * @return 原始指针
     */
    T* release() {
        T* ptr = _ptr;
        _ptr = nullptr;
        return ptr;
    }
    
    /**
     * @brief 交换两个ref_ptr
     * @param other 另一个ref_ptr
     */
    void swap(ref_ptr& other) noexcept {
        std::swap(_ptr, other._ptr);
    }
    
private:
    T* _ptr;
    
    template<typename U>
    friend class ref_ptr;
};

/**
 * @brief 比较操作符
 */
template<typename T, typename U>
bool operator==(const ref_ptr<T>& lhs, const ref_ptr<U>& rhs) {
    return lhs.get() == rhs.get();
}

template<typename T, typename U>
bool operator!=(const ref_ptr<T>& lhs, const ref_ptr<U>& rhs) {
    return !(lhs == rhs);
}

template<typename T>
bool operator==(const ref_ptr<T>& lhs, std::nullptr_t) {
    return lhs.get() == nullptr;
}

template<typename T>
bool operator==(std::nullptr_t, const ref_ptr<T>& rhs) {
    return nullptr == rhs.get();
}

template<typename T>
bool operator!=(const ref_ptr<T>& lhs, std::nullptr_t) {
    return !(lhs == nullptr);
}

template<typename T>
bool operator!=(std::nullptr_t, const ref_ptr<T>& rhs) {
    return !(nullptr == rhs);
}

/**
 * @brief 创建ref_ptr的便利函数
 * @tparam T 对象类型
 * @tparam Args 构造参数类型
 * @param args 构造参数
 * @return ref_ptr<T>
 */
template<typename T, typename... Args>
ref_ptr<T> make_ref(Args&&... args) {
    return ref_ptr<T>(new T(std::forward<Args>(args)...));
}

/**
 * @brief 动态转换ref_ptr
 * @tparam T 目标类型
 * @tparam U 源类型
 * @param ptr 源ref_ptr
 * @return 转换后的ref_ptr
 */
template<typename T, typename U>
ref_ptr<T> dynamic_pointer_cast(const ref_ptr<U>& ptr) {
    return ref_ptr<T>(dynamic_cast<T*>(ptr.get()));
}

/**
 * @brief 静态转换ref_ptr
 * @tparam T 目标类型
 * @tparam U 源类型
 * @param ptr 源ref_ptr
 * @return 转换后的ref_ptr
 */
template<typename T, typename U>
ref_ptr<T> static_pointer_cast(const ref_ptr<U>& ptr) {
    return ref_ptr<T>(static_cast<T*>(ptr.get()));
}

} // namespace usg
