#pragma once

#include <USG/Math/Math.h>
#include <vector>

namespace usg {

/**
 * @brief 包围球类
 * 
 * 用于表示3D对象的包围球，支持：
 * - 球心和半径
 * - 包围球合并
 * - 变换操作
 * - 相交测试
 */
class BoundingSphere {
public:
    /**
     * @brief 默认构造函数，创建无效包围球
     */
    BoundingSphere() 
        : _center(0.0f, 0.0f, 0.0f)
        , _radius(-1.0f) {}
    
    /**
     * @brief 构造函数
     * @param center 球心
     * @param radius 半径
     */
    BoundingSphere(const vec3& center, float radius)
        : _center(center)
        , _radius(radius) {}
    
    /**
     * @brief 拷贝构造函数
     * @param bs 其他包围球
     */
    BoundingSphere(const BoundingSphere& bs) = default;
    
    /**
     * @brief 赋值操作符
     * @param bs 其他包围球
     * @return 自身引用
     */
    BoundingSphere& operator=(const BoundingSphere& bs) = default;
    
    /**
     * @brief 设置球心
     * @param center 球心
     */
    void setCenter(const vec3& center) { 
        _center = center; 
    }
    
    /**
     * @brief 获取球心
     * @return 球心
     */
    const vec3& getCenter() const { 
        return _center; 
    }
    
    /**
     * @brief 设置半径
     * @param radius 半径
     */
    void setRadius(float radius) { 
        _radius = radius; 
    }
    
    /**
     * @brief 获取半径
     * @return 半径
     */
    float getRadius() const { 
        return _radius; 
    }
    
    /**
     * @brief 检查包围球是否有效
     * @return 是否有效
     */
    bool valid() const { 
        return _radius >= 0.0f; 
    }
    
    /**
     * @brief 初始化为无效状态
     */
    void init() {
        _center = vec3(0.0f, 0.0f, 0.0f);
        _radius = -1.0f;
    }
    
    /**
     * @brief 设置包围球
     * @param center 球心
     * @param radius 半径
     */
    void set(const vec3& center, float radius) {
        _center = center;
        _radius = radius;
    }
    
    /**
     * @brief 扩展包围球以包含指定点
     * @param point 点
     */
    void expandBy(const vec3& point) {
        if (!valid()) {
            _center = point;
            _radius = 0.0f;
        } else {
            vec3 dv = point - _center;
            float distance = math::length(dv);
            if (distance > _radius) {
                float new_radius = (distance + _radius) * 0.5f;
                float ratio = (new_radius - _radius) / distance;
                _center += dv * ratio;
                _radius = new_radius;
            }
        }
    }
    
    /**
     * @brief 扩展包围球以包含另一个包围球
     * @param bs 另一个包围球
     */
    void expandBy(const BoundingSphere& bs) {
        if (!bs.valid()) return;
        
        if (!valid()) {
            *this = bs;
        } else {
            vec3 dv = bs._center - _center;
            float distance = math::length(dv);
            
            if (distance + bs._radius <= _radius) {
                // bs完全在当前球内
                return;
            }
            
            if (distance + _radius <= bs._radius) {
                // 当前球完全在bs内
                *this = bs;
                return;
            }
            
            // 需要合并两个球
            float new_radius = (distance + _radius + bs._radius) * 0.5f;
            float ratio = (new_radius - _radius) / distance;
            _center += dv * ratio;
            _radius = new_radius;
        }
    }
    
    /**
     * @brief 扩展包围球以包含多个点
     * @param points 点列表
     */
    void expandBy(const std::vector<vec3>& points) {
        for (const auto& point : points) {
            expandBy(point);
        }
    }
    
    /**
     * @brief 检查是否包含指定点
     * @param point 点
     * @return 是否包含
     */
    bool contains(const vec3& point) const {
        if (!valid()) return false;
        return math::distance(_center, point) <= _radius;
    }
    
    /**
     * @brief 检查是否与另一个包围球相交
     * @param bs 另一个包围球
     * @return 是否相交
     */
    bool intersects(const BoundingSphere& bs) const {
        if (!valid() || !bs.valid()) return false;
        float distance = math::distance(_center, bs._center);
        return distance <= (_radius + bs._radius);
    }
    
    /**
     * @brief 应用变换矩阵
     * @param matrix 变换矩阵
     * @return 变换后的包围球
     */
    BoundingSphere transform(const mat4& matrix) const {
        if (!valid()) return *this;
        
        // 变换球心
        vec4 transformed_center = matrix * vec4(_center, 1.0f);
        vec3 new_center = vec3(transformed_center) / transformed_center.w;
        
        // 计算新半径（考虑缩放）
        vec3 scale_vector = vec3(
            math::length(vec3(matrix[0])),
            math::length(vec3(matrix[1])),
            math::length(vec3(matrix[2]))
        );
        float max_scale = std::max({scale_vector.x, scale_vector.y, scale_vector.z});
        float new_radius = _radius * max_scale;
        
        return BoundingSphere(new_center, new_radius);
    }
    
    /**
     * @brief 计算到点的距离
     * @param point 点
     * @return 距离（负值表示点在球内）
     */
    float distanceTo(const vec3& point) const {
        if (!valid()) return -1.0f;
        return math::distance(_center, point) - _radius;
    }
    
    /**
     * @brief 计算到另一个包围球的距离
     * @param bs 另一个包围球
     * @return 距离（负值表示相交）
     */
    float distanceTo(const BoundingSphere& bs) const {
        if (!valid() || !bs.valid()) return -1.0f;
        return math::distance(_center, bs._center) - (_radius + bs._radius);
    }
    
    /**
     * @brief 获取包围球的体积
     * @return 体积
     */
    float volume() const {
        if (!valid()) return 0.0f;
        return (4.0f / 3.0f) * math::PI * _radius * _radius * _radius;
    }
    
    /**
     * @brief 获取包围球的表面积
     * @return 表面积
     */
    float surfaceArea() const {
        if (!valid()) return 0.0f;
        return 4.0f * math::PI * _radius * _radius;
    }
    
    /**
     * @brief 比较操作符
     * @param bs 另一个包围球
     * @return 是否相等
     */
    bool operator==(const BoundingSphere& bs) const {
        return math::equal(_center, bs._center) && 
               math::equal(_radius, bs._radius);
    }
    
    /**
     * @brief 比较操作符
     * @param bs 另一个包围球
     * @return 是否不相等
     */
    bool operator!=(const BoundingSphere& bs) const {
        return !(*this == bs);
    }
    
private:
    vec3 _center;   ///< 球心
    float _radius;  ///< 半径
};

/**
 * @brief 合并多个包围球
 * @param spheres 包围球列表
 * @return 合并后的包围球
 */
inline BoundingSphere mergeBoundingSpheres(const std::vector<BoundingSphere>& spheres) {
    BoundingSphere result;
    for (const auto& sphere : spheres) {
        result.expandBy(sphere);
    }
    return result;
}

/**
 * @brief 从点集计算包围球
 * @param points 点列表
 * @return 包围球
 */
inline BoundingSphere computeBoundingSphere(const std::vector<vec3>& points) {
    if (points.empty()) {
        return BoundingSphere();
    }
    
    // 简单实现：使用AABB中心和最大距离
    vec3 min_point = points[0];
    vec3 max_point = points[0];
    
    for (const auto& point : points) {
        min_point = glm::min(min_point, point);
        max_point = glm::max(max_point, point);
    }
    
    vec3 center = (min_point + max_point) * 0.5f;
    float max_distance = 0.0f;
    
    for (const auto& point : points) {
        float distance = math::distance(center, point);
        max_distance = std::max(max_distance, distance);
    }
    
    return BoundingSphere(center, max_distance);
}

} // namespace usg
