#pragma once

#include <USG/Core/Object.h>

namespace usg {

// 前向声明
class Node;
class Group;
class Transform;
class Geometry;

/**
 * @brief 访问者基类
 * 
 * 实现访问者模式，用于遍历和操作场景图。
 * 提供不同的遍历模式和访问接口。
 */
class Visitor {
public:
    /**
     * @brief 遍历模式
     */
    enum TraversalMode {
        TRAVERSE_NONE,              ///< 不遍历
        TRAVERSE_PARENTS,           ///< 遍历父节点
        TRAVERSE_ALL_CHILDREN,      ///< 遍历所有子节点
        TRAVERSE_ACTIVE_CHILDREN    ///< 遍历活跃子节点
    };
    
    /**
     * @brief 访问者类型
     */
    enum VisitorType {
        NODE_VISITOR = 0,
        UPDATE_VISITOR,
        CULL_VISITOR,
        RENDER_VISITOR,
        COMPUTE_VISITOR
    };
    
    Visitor(VisitorType type = NODE_VISITOR) 
        : _visitorType(type)
        , _traversalMode(TRAVERSE_ALL_CHILDREN) {}
    
    virtual ~Visitor() = default;
    
    /**
     * @brief 获取访问者类型
     * @return 访问者类型
     */
    VisitorType getVisitorType() const { 
        return _visitorType; 
    }
    
    /**
     * @brief 设置遍历模式
     * @param mode 遍历模式
     */
    void setTraversalMode(TraversalMode mode) { 
        _traversalMode = mode; 
    }
    
    /**
     * @brief 获取遍历模式
     * @return 遍历模式
     */
    TraversalMode getTraversalMode() const { 
        return _traversalMode; 
    }
    
    /**
     * @brief 设置节点掩码
     * @param mask 节点掩码
     */
    void setTraversalMask(unsigned int mask) { 
        _traversalMask = mask; 
    }
    
    /**
     * @brief 获取节点掩码
     * @return 节点掩码
     */
    unsigned int getTraversalMask() const { 
        return _traversalMask; 
    }
    
    /**
     * @brief 设置节点掩码要求
     * @param mask 节点掩码要求
     */
    void setNodeMaskOverride(unsigned int mask) { 
        _nodeMaskOverride = mask; 
    }
    
    /**
     * @brief 获取节点掩码要求
     * @return 节点掩码要求
     */
    unsigned int getNodeMaskOverride() const { 
        return _nodeMaskOverride; 
    }
    
    /**
     * @brief 检查节点是否应该被访问
     * @param node 节点
     * @return 是否应该访问
     */
    virtual bool validNodeMask(const Node& node) const;
    
    /**
     * @brief 访问节点（基类实现）
     * @param node 节点
     */
    virtual void visit(Node& node) {}
    
    /**
     * @brief 访问组节点
     * @param group 组节点
     */
    virtual void visit(Group& group) { 
        traverse(group); 
    }
    
    /**
     * @brief 访问变换节点
     * @param transform 变换节点
     */
    virtual void visit(Transform& transform) { 
        traverse(transform); 
    }
    
    /**
     * @brief 访问几何节点
     * @param geometry 几何节点
     */
    virtual void visit(Geometry& geometry) {}
    
    /**
     * @brief 应用访问者到节点
     * @param node 节点
     */
    void apply(Node& node) {
        if (validNodeMask(node)) {
            node.accept(*this);
        }
    }
    
    /**
     * @brief 遍历组节点的子节点
     * @param group 组节点
     */
    virtual void traverse(Group& group);
    
    /**
     * @brief 重置访问者状态
     */
    virtual void reset() {}
    
protected:
    VisitorType _visitorType;
    TraversalMode _traversalMode;
    unsigned int _traversalMask = 0xffffffff;
    unsigned int _nodeMaskOverride = 0x0;
};

/**
 * @brief 更新访问者
 * 
 * 用于更新场景图中的动态内容，如动画、物理模拟等。
 */
class UpdateVisitor : public Visitor {
public:
    UpdateVisitor() : Visitor(UPDATE_VISITOR) {}
    
    /**
     * @brief 设置帧时间
     * @param frameTime 帧时间
     */
    void setFrameTime(double frameTime) { 
        _frameTime = frameTime; 
    }
    
    /**
     * @brief 获取帧时间
     * @return 帧时间
     */
    double getFrameTime() const { 
        return _frameTime; 
    }
    
    /**
     * @brief 设置增量时间
     * @param deltaTime 增量时间
     */
    void setDeltaTime(double deltaTime) { 
        _deltaTime = deltaTime; 
    }
    
    /**
     * @brief 获取增量时间
     * @return 增量时间
     */
    double getDeltaTime() const { 
        return _deltaTime; 
    }
    
    void visit(Node& node) override;
    void visit(Transform& transform) override;
    
    void reset() override {
        _frameTime = 0.0;
        _deltaTime = 0.0;
    }
    
protected:
    double _frameTime = 0.0;
    double _deltaTime = 0.0;
};

/**
 * @brief 裁剪访问者
 * 
 * 用于视锥体裁剪，确定哪些对象在视野范围内需要渲染。
 */
class CullVisitor : public Visitor {
public:
    CullVisitor() : Visitor(CULL_VISITOR) {}
    
    /**
     * @brief 设置视图矩阵
     * @param view 视图矩阵
     */
    void setViewMatrix(const mat4& view) { 
        _viewMatrix = view; 
    }
    
    /**
     * @brief 获取视图矩阵
     * @return 视图矩阵
     */
    const mat4& getViewMatrix() const { 
        return _viewMatrix; 
    }
    
    /**
     * @brief 设置投影矩阵
     * @param projection 投影矩阵
     */
    void setProjectionMatrix(const mat4& projection) { 
        _projectionMatrix = projection; 
    }
    
    /**
     * @brief 获取投影矩阵
     * @return 投影矩阵
     */
    const mat4& getProjectionMatrix() const { 
        return _projectionMatrix; 
    }
    
    /**
     * @brief 获取当前模型视图矩阵
     * @return 模型视图矩阵
     */
    const mat4& getModelViewMatrix() const { 
        return _modelViewMatrix; 
    }
    
    void visit(Node& node) override;
    void visit(Group& group) override;
    void visit(Transform& transform) override;
    void visit(Geometry& geometry) override;
    
    /**
     * @brief 推入变换矩阵
     * @param matrix 变换矩阵
     */
    void pushModelViewMatrix(const mat4& matrix);
    
    /**
     * @brief 弹出变换矩阵
     */
    void popModelViewMatrix();
    
    /**
     * @brief 检查包围球是否在视锥体内
     * @param bs 包围球
     * @return 是否在视锥体内
     */
    bool isCulled(const BoundingSphere& bs) const;
    
    void reset() override {
        _viewMatrix = mat4(1.0f);
        _projectionMatrix = mat4(1.0f);
        _modelViewMatrix = mat4(1.0f);
        _matrixStack.clear();
    }
    
protected:
    mat4 _viewMatrix = mat4(1.0f);
    mat4 _projectionMatrix = mat4(1.0f);
    mat4 _modelViewMatrix = mat4(1.0f);
    std::vector<mat4> _matrixStack;
};

} // namespace usg
