# USG - WebGPU Scene Graph

## 项目概述

USG (WebGPU Scene Graph) 是一个基于WebGPU的现代化场景图渲染引擎，旨在提供与VSG和OSG兼容的API接口，同时充分利用WebGPU的跨平台优势。

## 设计目标

### 核心目标
- **API兼容性**: 提供与VSG/OSG相似的API接口，便于现有代码迁移
- **跨平台支持**: 统一支持桌面端(Windows/Linux/macOS)和WebAssembly平台
- **现代化架构**: 基于现代C++17/20特性和WebGPU API设计
- **高性能**: 充分利用WebGPU的现代图形特性和并行计算能力

### 技术特点
- **场景图模式**: 层次化的节点树结构组织3D场景
- **访问者模式**: 通过Visitor实现场景遍历和操作
- **智能指针**: 自动内存管理和引用计数
- **渲染图**: 现代化的渲染管线组织和优化
- **异步加载**: 支持异步资源加载和流式处理

## 架构设计

### 整体架构
```
USG/
├── Core/           # 核心基础设施
├── Nodes/          # 场景图节点
├── Visitors/       # 访问者系统
├── Rendering/      # 渲染抽象层
├── WebGPU/         # WebGPU封装层
├── Resources/      # 资源管理
├── Math/           # 数学库
├── Utils/          # 工具类
└── Examples/       # 示例代码
```

### 核心组件
1. **Object Model**: 基础对象模型，支持序列化和反射
2. **Scene Graph**: 场景图节点系统，支持变换和层次结构
3. **Visitor System**: 访问者模式，实现场景遍历和操作
4. **Render Graph**: 渲染图，优化渲染指令和资源管理
5. **WebGPU Wrapper**: WebGPU API封装，提供高级抽象

## 与VSG/OSG的兼容性

### API兼容性
- 提供相似的类名和接口设计
- 支持场景图的构建和操作
- 兼容的访问者模式
- 相似的状态管理机制

### 迁移路径
1. **头文件替换**: 简单的头文件包含替换
2. **命名空间映射**: 通过命名空间别名实现兼容
3. **适配器模式**: 为不兼容的API提供适配器
4. **渐进式迁移**: 支持逐步迁移现有代码

## 技术优势

### WebGPU优势
- **现代API**: 基于现代图形API设计理念
- **跨平台**: 统一的API支持多平台
- **高性能**: 低开销的GPU访问
- **并行计算**: 内置计算着色器支持

### 架构优势
- **模块化设计**: 清晰的职责分离和接口抽象
- **可扩展性**: 支持插件式扩展和自定义节点
- **内存安全**: 智能指针和RAII管理
- **现代C++**: 充分利用C++17/20特性

## 开发计划

### 阶段一: 基础框架 (4周)
- [ ] 核心对象模型和智能指针系统
- [ ] 基础场景图节点结构
- [ ] 访问者模式框架
- [ ] WebGPU设备抽象层

### 阶段二: 渲染系统 (6周)
- [ ] 状态管理和渲染状态
- [ ] 渲染管线和命令缓冲区
- [ ] 基础几何体和材质系统
- [ ] 资源管理和缓存

### 阶段三: 高级功能 (6周)
- [ ] 光照和着色器系统
- [ ] 纹理和材质高级功能
- [ ] 动画和变换系统
- [ ] 性能优化和批处理

### 阶段四: 兼容性和优化 (4周)
- [ ] VSG/OSG API兼容层
- [ ] 性能测试和优化
- [ ] 文档和示例
- [ ] 单元测试覆盖

## 使用示例

```cpp
#include <USG/USG.h>

int main() {
    // 创建设备和上下文
    auto device = usg::WebGPUDevice::create();
    auto viewer = usg::Viewer::create(device);
    
    // 创建场景图
    auto root = usg::Group::create();
    auto transform = usg::Transform::create();
    auto geometry = usg::Geometry::create();
    
    // 构建场景层次
    root->addChild(transform);
    transform->addChild(geometry);
    
    // 设置到查看器
    viewer->setSceneData(root);
    
    // 渲染循环
    while (viewer->isRunning()) {
        viewer->frame();
    }
    
    return 0;
}
```

## 贡献指南

### 代码规范
- 使用现代C++17/20特性
- 遵循RAII原则
- 使用智能指针管理内存
- 保持API的一致性和简洁性

### 测试要求
- 每个模块都需要单元测试
- 性能测试和基准测试
- 跨平台兼容性测试
- 内存泄漏检测

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
