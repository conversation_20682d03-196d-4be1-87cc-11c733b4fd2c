diff --git a/tools/fetch_dawn_dependencies.py b/tools/fetch_dawn_dependencies.py
index b5594dea6..8fc80e30a 100644
--- a/tools/fetch_dawn_dependencies.py
+++ b/tools/fetch_dawn_dependencies.py
@@ -88,7 +88,7 @@ parser.add_argument('-t',
                     action='store_true',
                     default=False,
                     help="""
-    Deprecated: Test dependencies are now always included.
+    Fetch dependencies needed for testing
     """)
 
 
@@ -97,27 +97,28 @@ def main(args):
     # Dependencies of dependencies are prefixed by their ancestors.
     required_submodules = [
         'third_party/abseil-cpp',
-        'third_party/dxc',
-        'third_party/dxheaders',
         'third_party/glfw',
         'third_party/jinja2',
         'third_party/khronos/EGL-Registry',
         'third_party/khronos/OpenGL-Registry',
         'third_party/libprotobuf-mutator/src',
-        'third_party/protobuf',
         'third_party/markupsafe',
         'third_party/glslang/src',
-        'third_party/google_benchmark/src',
-        'third_party/googletest',
         'third_party/spirv-headers/src',
         'third_party/spirv-tools/src',
         'third_party/vulkan-headers/src',
         'third_party/vulkan-loader/src',
         'third_party/vulkan-utility-libraries/src',
     ]
+
     if args.use_test_deps:
-        log("""WARNING: --use-test-deps argument deprecated. 
-            Test dependencies are now always included.""")
+        required_submodules += [
+            'third_party/googletest',
+            'third_party/google_benchmark/src',
+            'third_party/protobuf',
+            'third_party/dxc',
+            'third_party/dxheaders',
+        ]
 
     root_dir = Path(args.directory).resolve()
 
@@ -144,9 +145,9 @@ def process_dir(args, dir_path, required_submodules):
     variables = ldict.get('vars', {})
 
     if deps is None:
-        log(f"WARNING: DEPS file '{deps_path}' does not define a 'deps' variable"
+        log(f"ERROR: DEPS file '{deps_path}' does not define a 'deps' variable"
             )
-        return
+        exit(1)
 
     for submodule in required_submodules:
         if submodule not in deps:
