# OSG OpenGL接口分析

## 目标
分析OSG库中调用OpenGL API的关键位置，为WebGPU后端替换做准备。

## OSG核心渲染接口分析

### 1. 状态管理
```cpp
// OSG中的OpenGL状态管理
namespace osg {
    class State : public Referenced {
        // OpenGL状态缓存
        mutable GLint _currentActiveTextureUnit;
        mutable GLint _currentClientActiveTextureUnit;
        
        // 关键接口 - 需要WebGPU替换
        void glUseProgram(GLuint program);
        void glBindTexture(GLenum target, GLuint texture);
        void glBindBuffer(GLenum target, GLuint buffer);
        void glVertexAttribPointer(GLuint index, GLint size, GLenum type, GLboolean normalized, GLsizei stride, const void* pointer);
        void glDrawElements(GLenum mode, GLsizei count, GLenum type, const void* indices);
        void glDrawArrays(GLenum mode, GLint first, GLsizei count);
    };
}

// 跟踪点1: 状态设置
// 在OSG源码中添加调试输出
void State::glUseProgram(GLuint program) {
    std::cout << "[OSG_TRACE] State::glUseProgram() - program: " << program << std::endl;
    ::glUseProgram(program);
}
```

### 2. 几何体渲染
```cpp
// OSG中的几何体绘制
namespace osg {
    class Geometry : public Drawable {
        // 关键接口 - 需要WebGPU替换
        virtual void drawImplementation(RenderInfo& renderInfo) const;
        
        void drawVertexArraysImplementation(RenderInfo& renderInfo) const {
            State* state = renderInfo.getState();
            
            // 绑定顶点数组
            for (unsigned int unit = 0; unit < _vertexAttribList.size(); ++unit) {
                const Array* array = _vertexAttribList[unit].get();
                if (array && array->getBinding() == Array::BIND_PER_VERTEX) {
                    std::cout << "[OSG_TRACE] Binding vertex attribute " << unit << std::endl;
                    state->setVertexAttribPointer(unit, array);
                }
            }
            
            // 绘制调用
            const PrimitiveSet* primitiveset = getPrimitiveSet(0);
            std::cout << "[OSG_TRACE] Drawing primitive set - mode: " << primitiveset->getMode() 
                      << ", count: " << primitiveset->getNumIndices() << std::endl;
            primitiveset->draw(state, false);
        }
    };
}
```

### 3. 纹理管理
```cpp
// OSG中的纹理管理
namespace osg {
    class Texture : public StateAttribute {
        GLuint _textureObject;
        
        // 关键接口 - 需要WebGPU替换
        void apply(State& state) const override {
            std::cout << "[OSG_TRACE] Texture::apply() - texture object: " << _textureObject << std::endl;
            
            // 激活纹理单元
            state.setActiveTextureUnit(_textureUnit);
            
            // 绑定纹理
            state.glBindTexture(getTextureTarget(), _textureObject);
            
            // 设置纹理参数
            applyTexParameters(getTextureTarget(), state);
        }
        
        void compileGLObjects(State& state) const {
            std::cout << "[OSG_TRACE] Texture::compileGLObjects() - creating texture object" << std::endl;
            
            // 生成纹理对象
            glGenTextures(1, &_textureObject);
            
            // 上传纹理数据
            glBindTexture(getTextureTarget(), _textureObject);
            glTexImage2D(GL_TEXTURE_2D, 0, _internalFormat, _textureWidth, _textureHeight, 
                        0, _sourceFormat, _sourceType, _image->data());
        }
    };
}
```

### 4. 着色器程序
```cpp
// OSG中的着色器管理
namespace osg {
    class Program : public StateAttribute {
        GLuint _glProgramHandle;
        
        // 关键接口 - 需要WebGPU替换
        void apply(State& state) const override {
            std::cout << "[OSG_TRACE] Program::apply() - program handle: " << _glProgramHandle << std::endl;
            state.glUseProgram(_glProgramHandle);
        }
        
        void compileGLObjects(State& state) const {
            std::cout << "[OSG_TRACE] Program::compileGLObjects() - creating shader program" << std::endl;
            
            // 创建程序对象
            _glProgramHandle = glCreateProgram();
            
            // 编译和附加着色器
            for (ShaderList::const_iterator itr = _shaderList.begin(); itr != _shaderList.end(); ++itr) {
                Shader* shader = itr->get();
                shader->compileShader(state);
                glAttachShader(_glProgramHandle, shader->getShaderHandle());
            }
            
            // 链接程序
            glLinkProgram(_glProgramHandle);
        }
    };
    
    class Shader : public Object {
        GLuint _shaderHandle;
        
        void compileShader(State& state) const {
            std::cout << "[OSG_TRACE] Shader::compileShader() - type: " << _type << std::endl;
            
            // 创建着色器对象
            _shaderHandle = glCreateShader(_type);
            
            // 设置着色器源码
            const char* source = _shaderSource.c_str();
            glShaderSource(_shaderHandle, 1, &source, NULL);
            
            // 编译着色器
            glCompileShader(_shaderHandle);
        }
    };
}
```

### 5. 缓冲区对象
```cpp
// OSG中的缓冲区管理
namespace osg {
    class BufferObject : public Object {
        GLuint _glObjectID;
        
        // 关键接口 - 需要WebGPU替换
        void compileBuffer(State& state) const {
            std::cout << "[OSG_TRACE] BufferObject::compileBuffer() - size: " << _profile._size << std::endl;
            
            // 生成缓冲区对象
            glGenBuffers(1, &_glObjectID);
            
            // 绑定缓冲区
            glBindBuffer(_profile._target, _glObjectID);
            
            // 上传数据
            glBufferData(_profile._target, _profile._size, _profile._data, _profile._usage);
        }
        
        void bindBuffer(State& state) const {
            std::cout << "[OSG_TRACE] BufferObject::bindBuffer() - target: " << _profile._target 
                      << ", object: " << _glObjectID << std::endl;
            state.glBindBuffer(_profile._target, _glObjectID);
        }
    };
}
```

## OSG渲染流程跟踪

### 关键调用链分析
```cpp
// OSG典型渲染流程
void OSGRenderer::render() {
    // 1. 场景遍历 (保留)
    _sceneView->cull();
    
    // 2. 渲染状态设置 (需要替换)
    std::cout << "[OSG_TRACE] Setting up render state" << std::endl;
    State* state = _renderInfo.getState();
    
    // 3. 遍历渲染叶子节点 (需要替换)
    RenderLeaf* previous = NULL;
    for (RenderLeaf* leaf : _renderGraph._leaves) {
        std::cout << "[OSG_TRACE] Rendering leaf: " << leaf->_drawable->getName() << std::endl;
        
        // 应用状态变化
        leaf->_drawable->getStateSet()->apply(*state);
        
        // 绘制几何体
        leaf->_drawable->drawImplementation(_renderInfo);
        
        previous = leaf;
    }
    
    // 4. 交换缓冲区 (需要替换)
    std::cout << "[OSG_TRACE] Swapping buffers" << std::endl;
    _graphicsContext->swapBuffers();
}
```

## 需要替换的核心接口

### 1. OpenGL状态管理
- `glUseProgram()` → WebGPU Pipeline绑定
- `glBindTexture()` → WebGPU BindGroup设置
- `glBindBuffer()` → WebGPU Buffer绑定
- `glVertexAttribPointer()` → WebGPU顶点布局

### 2. OpenGL绘制调用
- `glDrawElements()` → WebGPU drawIndexed()
- `glDrawArrays()` → WebGPU draw()
- `glDrawElementsInstanced()` → WebGPU drawIndexed() with instances

### 3. OpenGL资源管理
- `glGenTextures()/glTexImage2D()` → WebGPU Texture创建
- `glGenBuffers()/glBufferData()` → WebGPU Buffer创建
- `glCreateProgram()/glLinkProgram()` → WebGPU RenderPipeline创建

### 4. OpenGL渲染状态
- `glViewport()` → WebGPU RenderPass viewport
- `glClearColor()/glClear()` → WebGPU RenderPass clear values
- `glEnable()/glDisable()` → WebGPU Pipeline state

## 跟踪方法

### 1. OSG状态跟踪
```cpp
// 在OSG State类中添加跟踪
class State : public Referenced {
public:
    void glUseProgram(GLuint program) {
        OSGTracer::logCall("glUseProgram", "program: " + std::to_string(program));
        ::glUseProgram(program);
    }
    
    void glBindTexture(GLenum target, GLuint texture) {
        OSGTracer::logCall("glBindTexture", "target: " + std::to_string(target) + 
                          ", texture: " + std::to_string(texture));
        ::glBindTexture(target, texture);
    }
    
    void glDrawElements(GLenum mode, GLsizei count, GLenum type, const void* indices) {
        OSGTracer::logCall("glDrawElements", "mode: " + std::to_string(mode) + 
                          ", count: " + std::to_string(count));
        ::glDrawElements(mode, count, type, indices);
    }
};
```

### 2. OSG绘制跟踪
```cpp
// 在OSG Drawable类中添加跟踪
class Drawable : public Object {
public:
    virtual void drawImplementation(RenderInfo& renderInfo) const {
        OSGTracer::logCall("Drawable::drawImplementation", 
                          "drawable: " + getName() + ", type: " + className());
        
        // 调用具体实现
        drawImplementationInternal(renderInfo);
    }
};
```

### 3. 运行时统计
```cpp
class OSGTracer {
public:
    static void logCall(const std::string& function, const std::string& details = "") {
        static std::unordered_map<std::string, int> callCounts;
        callCounts[function]++;
        
        std::cout << "[OSG_TRACE] " << function;
        if (!details.empty()) {
            std::cout << " - " << details;
        }
        std::cout << " (call #" << callCounts[function] << ")" << std::endl;
    }
    
    static void printStatistics() {
        std::cout << "\n[OSG_TRACE] Call Statistics:" << std::endl;
        for (const auto& pair : callCounts) {
            std::cout << "  " << pair.first << ": " << pair.second << " calls" << std::endl;
        }
    }
};
```

## 下一步行动

1. **在OSG源码中添加跟踪代码**
2. **运行典型OSG应用，收集调用日志**
3. **分析OpenGL调用模式和状态变化**
4. **设计WebGPU状态管理系统**
5. **实现OSG到WebGPU的适配层**
