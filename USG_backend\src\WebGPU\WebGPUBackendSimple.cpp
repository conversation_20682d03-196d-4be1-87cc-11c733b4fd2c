#define WEBGPU_CPP_IMPLEMENTATION
#include "WebGPUBackendSimple.h"
#include <iostream>
#include <cassert>
#include <string_view>

#define GLFW_INCLUDE_NONE
#define GLFW_EXPOSE_NATIVE_WIN32
#include <GLFW/glfw3.h>
#include <GLFW/glfw3native.h>
#include <windows.h>
#include <thread>
#include <chrono>

// 添加GLFW WebGPU扩展
#include "glfw3webgpu.h"

#ifdef USG_PLATFORM_DESKTOP
#include <GLFW/glfw3.h>
#ifdef _WIN32
#define GLFW_EXPOSE_NATIVE_WIN32
#include <GLFW/glfw3native.h>
#endif
#endif

namespace USG
{
    // 静态成员初始化
    WebGPUBackendSimple::RequestResult WebGPUBackendSimple::_adapterRequest;
    WebGPUBackendSimple::RequestResult WebGPUBackendSimple::_deviceRequest;

    WebGPUBackendSimple::WebGPUBackendSimple()
        : _initialized(false)
    {
        std::cout << "[WebGPUBackendSimple] Constructor called" << std::endl;
    }

    WebGPUBackendSimple::~WebGPUBackendSimple()
    {
        std::cout << "[WebGPUBackendSimple] Destructor called" << std::endl;
        cleanup();
    }

    bool WebGPUBackendSimple::initialize(const BackendConfig &config)
    {
        std::cout << "[WebGPUBackendSimple] Initializing WebGPU backend..." << std::endl;
        _config = config;

        if (!initializeWebGPU())
        {
            std::cerr << "[WebGPUBackendSimple] Failed to initialize WebGPU" << std::endl;
            return false;
        }

        _initialized = true;
        std::cout << "[WebGPUBackendSimple] WebGPU backend initialized successfully" << std::endl;
        return true;
    }

    void WebGPUBackendSimple::cleanup()
    {
        if (!_initialized)
            return;

        std::cout << "[WebGPUBackendSimple] Cleaning up WebGPU backend..." << std::endl;
        _initialized = false;
        std::cout << "[WebGPUBackendSimple] WebGPU backend cleaned up" << std::endl;
    }

    bool WebGPUBackendSimple::initializeWebGPU()
    {
        std::cout << "[WebGPUBackendSimple] *** WEBGPU INITIALIZATION DEBUG ***" << std::endl;
        std::cout << "[WebGPUBackendSimple] Creating WebGPU instance..." << std::endl;

        // 使用与工作的App.exe相同的实例创建方式
        wgpu::InstanceDescriptor instanceDesc = {};
        instanceDesc.setDefault();
        _instance = wgpu::createInstance(instanceDesc);

        if (!_instance)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create WebGPU instance" << std::endl;
            return false;
        }

        std::cout << "[WebGPUBackendSimple] ✅ WebGPU instance created successfully: " << _instance << std::endl;

        // 创建表面
        if (!createSurface(_config.nativeWindow))
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create surface" << std::endl;
            return false;
        }

        // 创建设备
        if (!createDevice())
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create device" << std::endl;
            return false;
        }

        // 配置交换链
        if (!createSwapchain(_config.swapchainWidth, _config.swapchainHeight))
        {
            std::cerr << "[WebGPUBackendSimple] Failed to configure swap chain" << std::endl;
            return false;
        }

        return true;
    }

    bool WebGPUBackendSimple::createSurface(void *nativeWindow)
    {
        std::cout << "[WebGPUBackendSimple] Creating WebGPU surface using GLFW..." << std::endl;

        if (!_instance || !nativeWindow)
        {
            std::cerr << "[WebGPUBackendSimple] No instance or native window available" << std::endl;
            return false;
        }

        // 使用GLFW的WebGPU表面创建函数（与工作的App.exe相同）
        GLFWwindow *window = static_cast<GLFWwindow *>(nativeWindow);

        std::cout << "[WebGPUBackendSimple] Using glfwCreateWindowWGPUSurface..." << std::endl;
        WGPUSurface wgpuSurface = glfwCreateWindowWGPUSurface(_instance, window);

        if (!wgpuSurface)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create surface with GLFW" << std::endl;
            return false;
        }

        // 直接使用C API的表面（wgpu-native使用C API）
        _surface = wgpu::Surface(wgpuSurface);

        std::cout << "[WebGPUBackendSimple] ✅ WebGPU surface created successfully using GLFW: " << _surface << std::endl;
        return true;
    }

    bool WebGPUBackendSimple::createDevice()
    {
        std::cout << "[WebGPUBackendSimple] Creating real WebGPU device..." << std::endl;

        if (!_instance)
        {
            std::cerr << "[WebGPUBackendSimple] No instance available for device creation" << std::endl;
            return false;
        }

        // 请求适配器 - 使用同步版本（像工作的App.exe一样）
        std::cout << "[WebGPUBackendSimple] Requesting adapter..." << std::endl;
        wgpu::RequestAdapterOptions adapterOptions = {};
        adapterOptions.setDefault();
        adapterOptions.compatibleSurface = _surface;
        adapterOptions.powerPreference = wgpu::PowerPreference::HighPerformance;

        _adapter = _instance.requestAdapter(adapterOptions);
        if (!_adapter)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to get adapter" << std::endl;
            return false;
        }

        std::cout << "[WebGPUBackendSimple] ✅ Got WebGPU adapter: " << _adapter << std::endl;

        // 请求设备 - 使用同步版本（与工作的App.exe相同）
        std::cout << "[WebGPUBackendSimple] Requesting device..." << std::endl;
        wgpu::DeviceDescriptor deviceDesc = {};
        deviceDesc.setDefault();
        deviceDesc.label = wgpu::StringView(std::string_view("WebGPU Device"));

        // 添加错误回调（与工作的App.exe相同）
        deviceDesc.uncapturedErrorCallbackInfo.callback =
            [](WGPUDevice const *device, WGPUErrorType type, WGPUStringView message, void *userdata1, void *userdata2)
        {
            std::cout << "[WebGPUBackendSimple] Device error: type " << type;
            if (message.data)
            {
                std::cout << " (message: " << std::string_view(message.data, message.length) << ")";
            }
            std::cout << std::endl;
        };

        // 添加设备丢失回调
        deviceDesc.deviceLostCallbackInfo.callback =
            [](WGPUDevice const *device, WGPUDeviceLostReason reason, WGPUStringView message, void *userdata1, void *userdata2)
        {
            std::cout << "[WebGPUBackendSimple] Device lost: reason " << reason;
            if (message.data)
            {
                std::cout << " (message: " << std::string_view(message.data, message.length) << ")";
            }
            std::cout << std::endl;
        };

        _device = _adapter.requestDevice(deviceDesc);
        if (!_device)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to get device" << std::endl;
            return false;
        }

        std::cout << "[WebGPUBackendSimple] ✅ Got WebGPU device: " << _device << std::endl;

        // 获取队列
        _queue = _device.getQueue();
        if (!_queue)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to get queue" << std::endl;
            return false;
        }

        std::cout << "[WebGPUBackendSimple] ✅ WebGPU device created successfully" << std::endl;
        return true;
    }

    bool WebGPUBackendSimple::createSwapchain(uint32_t width, uint32_t height)
    {
        std::cout << "[WebGPUBackendSimple] ⚠️ Skipping swapchain creation (temporary)" << std::endl;
        std::cout << "[WebGPUBackendSimple] Size: " << width << "x" << height << std::endl;
        return true;
    }

    void WebGPUBackendSimple::beginFrame()
    {
        std::cout << "[WebGPUBackendSimple] Begin frame" << std::endl;
    }

    void WebGPUBackendSimple::endFrame()
    {
        std::cout << "[WebGPUBackendSimple] End frame" << std::endl;
    }

    void WebGPUBackendSimple::present()
    {
        std::cout << "[WebGPUBackendSimple] Present frame" << std::endl;
    }

    void WebGPUBackendSimple::waitIdle()
    {
        // 空实现
    }

    // 资源创建方法 - 简化实现
    BackendBuffer *WebGPUBackendSimple::createBuffer(const BufferDesc &desc)
    {
        std::cout << "[WebGPUBackendSimple] Creating real WebGPU buffer (size: " << desc.size << " bytes)" << std::endl;

        if (!_device)
        {
            std::cerr << "[WebGPUBackendSimple] Device not available for buffer creation" << std::endl;
            return nullptr;
        }

        wgpu::BufferDescriptor bufferDesc = {};
        bufferDesc.setDefault();
        bufferDesc.label = wgpu::StringView(std::string_view(desc.label));
        bufferDesc.size = desc.size;
        bufferDesc.usage = convertBufferUsage(desc.usage);
        bufferDesc.mappedAtCreation = false;

        wgpu::Buffer buffer = _device.createBuffer(bufferDesc);
        if (!buffer)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create buffer" << std::endl;
            return nullptr;
        }

        std::cout << "[WebGPUBackendSimple] ✅ Created WebGPU buffer: " << desc.label << " (" << desc.size << " bytes)" << std::endl;
        return new WebGPUBufferSimple(buffer, desc);
    }

    BackendTexture *WebGPUBackendSimple::createTexture(const TextureDesc &desc)
    {
        std::cout << "[WebGPUBackendSimple] Creating texture" << std::endl;
        return nullptr;
    }

    BackendShader *WebGPUBackendSimple::createShader(const ShaderDesc &desc)
    {
        std::cout << "[WebGPUBackendSimple] Creating real WebGPU shader" << std::endl;

        if (!_device)
        {
            std::cout << "[WebGPUBackendSimple] ⚠️ Device not available, creating mock shader" << std::endl;
            // 创建一个虚拟着色器对象，使用空的模块
            wgpu::ShaderModule emptyModule = nullptr;
            return new WebGPUShaderSimple(emptyModule, desc);
        }

        std::string shaderCode;
        if (desc.stage == ShaderStage::Vertex)
        {
            shaderCode = getDefaultVertexShaderWGSL();
        }
        else if (desc.stage == ShaderStage::Fragment)
        {
            shaderCode = getDefaultFragmentShaderWGSL();
        }
        else
        {
            std::cerr << "[WebGPUBackendSimple] Unsupported shader stage" << std::endl;
            return nullptr;
        }

        wgpu::ShaderModuleDescriptor shaderDesc = {};
        shaderDesc.setDefault();
        shaderDesc.label = wgpu::StringView(std::string_view(desc.label));

        wgpu::ShaderSourceWGSL wgslDesc = {};
        wgslDesc.setDefault();
        wgslDesc.code = wgpu::StringView(std::string_view(shaderCode));
        shaderDesc.nextInChain = &wgslDesc.chain;

        wgpu::ShaderModule shaderModule = _device.createShaderModule(shaderDesc);
        if (!shaderModule)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create shader module" << std::endl;
            return nullptr;
        }

        std::cout << "[WebGPUBackendSimple] ✅ Created WebGPU shader: " << desc.label << std::endl;
        return new WebGPUShaderSimple(shaderModule, desc);
    }

    BackendPipeline *WebGPUBackendSimple::createPipeline(const PipelineDesc &desc)
    {
        std::cout << "[WebGPUBackendSimple] Creating real WebGPU pipeline" << std::endl;

        if (!_device)
        {
            std::cerr << "[WebGPUBackendSimple] Device not available for pipeline creation" << std::endl;
            return nullptr;
        }

        // 创建默认的顶点和片段着色器
        ShaderDesc vertexShaderDesc;
        vertexShaderDesc.stage = ShaderStage::Vertex;
        vertexShaderDesc.label = "DefaultVertexShader";

        ShaderDesc fragmentShaderDesc;
        fragmentShaderDesc.stage = ShaderStage::Fragment;
        fragmentShaderDesc.label = "DefaultFragmentShader";

        auto vertexShader = static_cast<WebGPUShaderSimple *>(createShader(vertexShaderDesc));
        auto fragmentShader = static_cast<WebGPUShaderSimple *>(createShader(fragmentShaderDesc));

        if (!vertexShader || !fragmentShader)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create shaders for pipeline" << std::endl;
            return nullptr;
        }

        // 创建渲染管线
        wgpu::RenderPipelineDescriptor pipelineDesc = {};
        pipelineDesc.setDefault();
        pipelineDesc.label = wgpu::StringView(std::string_view(desc.label));

        // 顶点阶段
        wgpu::VertexState vertexState = {};
        vertexState.setDefault();
        vertexState.module = vertexShader->getModule();
        vertexState.entryPoint = wgpu::StringView(std::string_view("vs_main"));

        // 顶点属性布局
        wgpu::VertexAttribute attributes[2] = {};
        attributes[0].format = wgpu::VertexFormat::Float32x3;
        attributes[0].offset = 0;
        attributes[0].shaderLocation = 0;

        attributes[1].format = wgpu::VertexFormat::Float32x3;
        attributes[1].offset = 12; // 3 * sizeof(float)
        attributes[1].shaderLocation = 1;

        wgpu::VertexBufferLayout bufferLayout = {};
        bufferLayout.arrayStride = 24; // 6 * sizeof(float)
        bufferLayout.stepMode = wgpu::VertexStepMode::Vertex;
        bufferLayout.attributeCount = 2;
        bufferLayout.attributes = attributes;

        vertexState.bufferCount = 1;
        vertexState.buffers = &bufferLayout;
        pipelineDesc.vertex = vertexState;

        // 片段阶段
        wgpu::FragmentState fragmentState = {};
        fragmentState.setDefault();
        fragmentState.module = fragmentShader->getModule();
        fragmentState.entryPoint = wgpu::StringView(std::string_view("fs_main"));

        wgpu::ColorTargetState colorTarget = {};
        colorTarget.format = _swapChainFormat;
        colorTarget.writeMask = wgpu::ColorWriteMask::All;

        fragmentState.targetCount = 1;
        fragmentState.targets = &colorTarget;
        pipelineDesc.fragment = &fragmentState;

        // 基本设置
        pipelineDesc.primitive.topology = wgpu::PrimitiveTopology::TriangleList;
        pipelineDesc.primitive.stripIndexFormat = wgpu::IndexFormat::Undefined;
        pipelineDesc.primitive.frontFace = wgpu::FrontFace::CCW;
        pipelineDesc.primitive.cullMode = wgpu::CullMode::None;

        // 多重采样
        pipelineDesc.multisample.count = 1;
        pipelineDesc.multisample.mask = ~0u;
        pipelineDesc.multisample.alphaToCoverageEnabled = false;

        // 创建管线布局
        wgpu::PipelineLayoutDescriptor layoutDesc = {};
        layoutDesc.setDefault();
        layoutDesc.label = wgpu::StringView(std::string_view("DefaultPipelineLayout"));
        wgpu::PipelineLayout layout = _device.createPipelineLayout(layoutDesc);
        pipelineDesc.layout = layout;

        wgpu::RenderPipeline pipeline = _device.createRenderPipeline(pipelineDesc);
        if (!pipeline)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create render pipeline" << std::endl;
            delete vertexShader;
            delete fragmentShader;
            return nullptr;
        }

        std::cout << "[WebGPUBackendSimple] ✅ Created WebGPU pipeline: " << desc.label << std::endl;
        return new WebGPUPipelineSimple(pipeline, desc);
    }

    BackendDescriptorSet *WebGPUBackendSimple::createDescriptorSet()
    {
        return new WebGPUDescriptorSetSimple(nullptr);
    }

    BackendCommandList *WebGPUBackendSimple::createCommandList()
    {
        std::cout << "[WebGPUBackendSimple] Creating command list with device: " << _device << std::endl;

        // 创建命令编码器
        wgpu::CommandEncoderDescriptor encoderDesc = {};
        encoderDesc.label = wgpu::StringView(std::string_view("Command Encoder"));
        wgpu::CommandEncoder encoder = _device.createCommandEncoder(encoderDesc);

        if (!encoder)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create command encoder" << std::endl;
            return nullptr;
        }

        std::cout << "[WebGPUBackendSimple] ✅ Created command encoder: " << encoder << std::endl;
        return new WebGPUCommandListSimple(encoder, this);
    }

    BackendFence *WebGPUBackendSimple::createFence()
    {
        return new WebGPUFenceSimple();
    }

    BackendSemaphore *WebGPUBackendSimple::createSemaphore()
    {
        return new WebGPUSemaphoreSimple();
    }

    void WebGPUBackendSimple::executeCommandList(BackendCommandList *cmdList,
                                                 const std::vector<BackendSemaphore *> &waitSemaphores,
                                                 const std::vector<BackendSemaphore *> &signalSemaphores,
                                                 BackendFence *fence)
    {
        std::cout << "[WebGPUBackendSimple] Executing command list" << std::endl;
    }

    void WebGPUBackendSimple::updateBuffer(BackendBuffer *buffer, const void *data, size_t size, size_t offset)
    {
        std::cout << "[WebGPUBackendSimple] Updating real buffer with " << size << " bytes" << std::endl;

        if (!buffer || !data || size == 0)
        {
            std::cerr << "[WebGPUBackendSimple] Invalid buffer update parameters" << std::endl;
            return;
        }

        auto webgpuBuffer = static_cast<WebGPUBufferSimple *>(buffer);
        wgpu::Buffer wgpuBuffer = webgpuBuffer->getBuffer();

        if (!wgpuBuffer)
        {
            std::cerr << "[WebGPUBackendSimple] Invalid WebGPU buffer" << std::endl;
            return;
        }

        // 使用队列写入数据
        _queue.writeBuffer(wgpuBuffer, offset, data, size);
        std::cout << "[WebGPUBackendSimple] ✅ Updated WebGPU buffer with " << size << " bytes" << std::endl;
    }

    // 类型转换方法
    wgpu::BufferUsage WebGPUBackendSimple::convertBufferUsage(BufferUsage usage)
    {
        wgpu::BufferUsage wgpuUsage = wgpu::BufferUsage::None;

        if ((usage & BufferUsage::Vertex) != BufferUsage::None)
            wgpuUsage = wgpuUsage | wgpu::BufferUsage::Vertex;
        if ((usage & BufferUsage::Index) != BufferUsage::None)
            wgpuUsage = wgpuUsage | wgpu::BufferUsage::Index;
        if ((usage & BufferUsage::Uniform) != BufferUsage::None)
            wgpuUsage = wgpuUsage | wgpu::BufferUsage::Uniform;
        if ((usage & BufferUsage::Storage) != BufferUsage::None)
            wgpuUsage = wgpuUsage | wgpu::BufferUsage::Storage;
        if ((usage & BufferUsage::TransferSrc) != BufferUsage::None)
            wgpuUsage = wgpuUsage | wgpu::BufferUsage::CopySrc;
        if ((usage & BufferUsage::TransferDst) != BufferUsage::None)
            wgpuUsage = wgpuUsage | wgpu::BufferUsage::CopyDst;

        // WebGPU要求：如果缓冲区需要被写入数据，必须包含CopyDst标志
        // 为了简化使用，我们自动为所有缓冲区添加CopyDst标志
        wgpuUsage = wgpuUsage | wgpu::BufferUsage::CopyDst;

        return wgpuUsage;
    }

    // 默认着色器代码
    std::string WebGPUBackendSimple::getDefaultVertexShaderWGSL()
    {
        return R"(
struct VertexInput {
    @location(0) position: vec3<f32>,
    @location(1) color: vec3<f32>,
}

struct VertexOutput {
    @builtin(position) clip_position: vec4<f32>,
    @location(0) color: vec3<f32>,
}

@vertex
fn vs_main(input: VertexInput) -> VertexOutput {
    var output: VertexOutput;
    output.clip_position = vec4<f32>(input.position, 1.0);
    output.color = input.color;
    return output;
}
)";
    }

    std::string WebGPUBackendSimple::getDefaultFragmentShaderWGSL()
    {
        return R"(
struct VertexOutput {
    @builtin(position) clip_position: vec4<f32>,
    @location(0) color: vec3<f32>,
}

@fragment
fn fs_main(input: VertexOutput) -> @location(0) vec4<f32> {
    return vec4<f32>(input.color, 1.0);
}
)";
    }

    // 资源销毁方法
    void WebGPUBackendSimple::destroyBuffer(BackendBuffer *buffer) { delete buffer; }
    void WebGPUBackendSimple::destroyTexture(BackendTexture *texture) { delete texture; }
    void WebGPUBackendSimple::destroyShader(BackendShader *shader) { delete shader; }
    void WebGPUBackendSimple::destroyPipeline(BackendPipeline *pipeline) { delete pipeline; }
    void WebGPUBackendSimple::destroyDescriptorSet(BackendDescriptorSet *descriptorSet) { delete descriptorSet; }
    void WebGPUBackendSimple::destroyCommandList(BackendCommandList *cmdList) { delete cmdList; }
    void WebGPUBackendSimple::destroyFence(BackendFence *fence) { delete fence; }
    void WebGPUBackendSimple::destroySemaphore(BackendSemaphore *semaphore) { delete semaphore; }

    // WebGPU资源类实现
    WebGPUBufferSimple::WebGPUBufferSimple(wgpu::Buffer buffer, const BufferDesc &desc)
        : _buffer(buffer), _desc(desc) {}

    WebGPUBufferSimple::~WebGPUBufferSimple() {}

    WebGPUTextureSimple::WebGPUTextureSimple(wgpu::Texture texture, const TextureDesc &desc)
        : _texture(texture), _desc(desc) {}

    WebGPUTextureSimple::~WebGPUTextureSimple() {}

    WebGPUShaderSimple::WebGPUShaderSimple(wgpu::ShaderModule module, const ShaderDesc &desc)
        : _module(module), _desc(desc) {}

    WebGPUShaderSimple::~WebGPUShaderSimple() {}

    WebGPUPipelineSimple::WebGPUPipelineSimple(wgpu::RenderPipeline pipeline, const PipelineDesc &desc)
        : _pipeline(pipeline), _desc(desc) {}

    WebGPUPipelineSimple::~WebGPUPipelineSimple() {}

    WebGPUDescriptorSetSimple::WebGPUDescriptorSetSimple(wgpu::BindGroup bindGroup)
        : _bindGroup(bindGroup) {}

    WebGPUDescriptorSetSimple::~WebGPUDescriptorSetSimple() {}

    // WebGPU CommandList 实现
    WebGPUCommandListSimple::WebGPUCommandListSimple(wgpu::CommandEncoder encoder, WebGPUBackendSimple *backend)
        : _encoder(encoder), _backend(backend) {}

    WebGPUCommandListSimple::~WebGPUCommandListSimple() {}

    void WebGPUCommandListSimple::beginRenderPass(const RenderPassDesc &desc)
    {
        std::cout << "[WebGPUCommandListSimple] Begin real render pass" << std::endl;

        if (!_encoder)
        {
            std::cerr << "[WebGPUCommandListSimple] No command encoder available" << std::endl;
            return;
        }

        // 获取当前交换链纹理
        wgpu::SurfaceTexture surfaceTexture;
        surfaceTexture.setDefault();
        _backend->getSurface().getCurrentTexture(&surfaceTexture);

        if (surfaceTexture.status != wgpu::SurfaceGetCurrentTextureStatus::SuccessOptimal &&
            surfaceTexture.status != wgpu::SurfaceGetCurrentTextureStatus::SuccessSuboptimal)
        {
            std::cerr << "[WebGPUCommandListSimple] Failed to get current texture" << std::endl;
            return;
        }

        wgpu::Texture texture(surfaceTexture.texture);
        wgpu::TextureView textureView = texture.createView();

        // 创建渲染通道描述符
        wgpu::RenderPassColorAttachment colorAttachment = {};
        colorAttachment.view = textureView;
        colorAttachment.resolveTarget = nullptr;
        colorAttachment.loadOp = wgpu::LoadOp::Clear;
        colorAttachment.storeOp = wgpu::StoreOp::Store;
        colorAttachment.clearValue = {0.0f, 0.0f, 0.0f, 1.0f}; // 黑色背景

        wgpu::RenderPassDescriptor renderPassDesc = {};
        renderPassDesc.setDefault();
        renderPassDesc.label = wgpu::StringView(std::string_view("MainRenderPass"));
        renderPassDesc.colorAttachmentCount = 1;
        renderPassDesc.colorAttachments = &colorAttachment;

        _renderPassEncoder = _encoder.beginRenderPass(renderPassDesc);
        std::cout << "[WebGPUCommandListSimple] ✅ Started WebGPU render pass" << std::endl;
    }

    void WebGPUCommandListSimple::endRenderPass()
    {
        std::cout << "[WebGPUCommandListSimple] End real render pass" << std::endl;

        if (_renderPassEncoder)
        {
            _renderPassEncoder.end();
            _renderPassEncoder = nullptr;
            std::cout << "[WebGPUCommandListSimple] ✅ Ended WebGPU render pass" << std::endl;
        }
    }

    void WebGPUCommandListSimple::setPipeline(BackendPipeline *pipeline)
    {
        std::cout << "[WebGPUCommandListSimple] Set real pipeline" << std::endl;

        if (!_renderPassEncoder || !pipeline)
        {
            std::cerr << "[WebGPUCommandListSimple] No render pass encoder or pipeline" << std::endl;
            return;
        }

        auto webgpuPipeline = static_cast<WebGPUPipelineSimple *>(pipeline);
        _currentPipeline = webgpuPipeline->getPipeline();
        _renderPassEncoder.setPipeline(_currentPipeline);
        std::cout << "[WebGPUCommandListSimple] ✅ Set WebGPU pipeline" << std::endl;
    }

    void WebGPUCommandListSimple::setVertexBuffer(BackendBuffer *buffer, uint32_t binding, size_t offset)
    {
        std::cout << "[WebGPUCommandListSimple] Set real vertex buffer" << std::endl;

        if (!_renderPassEncoder || !buffer)
        {
            std::cerr << "[WebGPUCommandListSimple] No render pass encoder or buffer" << std::endl;
            return;
        }

        auto webgpuBuffer = static_cast<WebGPUBufferSimple *>(buffer);
        _currentVertexBuffer = webgpuBuffer->getBuffer();
        _renderPassEncoder.setVertexBuffer(binding, _currentVertexBuffer, offset, WGPU_WHOLE_SIZE);
        std::cout << "[WebGPUCommandListSimple] ✅ Set WebGPU vertex buffer" << std::endl;
    }

    void WebGPUCommandListSimple::setIndexBuffer(BackendBuffer *buffer, IndexFormat format, uint64_t offset)
    {
        std::cout << "[WebGPUCommandListSimple] Set real index buffer" << std::endl;

        if (!_renderPassEncoder || !buffer)
        {
            std::cerr << "[WebGPUCommandListSimple] No render pass encoder or buffer" << std::endl;
            return;
        }

        auto webgpuBuffer = static_cast<WebGPUBufferSimple *>(buffer);
        _currentIndexBuffer = webgpuBuffer->getBuffer();

        wgpu::IndexFormat wgpuFormat = (format == IndexFormat::UInt16) ? wgpu::IndexFormat::Uint16 : wgpu::IndexFormat::Uint32;

        _renderPassEncoder.setIndexBuffer(_currentIndexBuffer, wgpuFormat, offset, WGPU_WHOLE_SIZE);
        std::cout << "[WebGPUCommandListSimple] ✅ Set WebGPU index buffer" << std::endl;
    }

    void WebGPUCommandListSimple::draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance)
    {
        std::cout << "[WebGPUCommandListSimple] Real draw (vertices: " << vertexCount << ", instances: " << instanceCount << ")" << std::endl;

        if (!_renderPassEncoder)
        {
            std::cerr << "[WebGPUCommandListSimple] No render pass encoder for draw" << std::endl;
            return;
        }

        _renderPassEncoder.draw(vertexCount, instanceCount, firstVertex, firstInstance);
        std::cout << "[WebGPUCommandListSimple] ✅ Executed WebGPU draw command" << std::endl;
    }

    void WebGPUCommandListSimple::drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t vertexOffset, uint32_t firstInstance)
    {
        std::cout << "[WebGPUCommandListSimple] Real draw indexed (indices: " << indexCount << ", instances: " << instanceCount << ")" << std::endl;

        if (!_renderPassEncoder)
        {
            std::cerr << "[WebGPUCommandListSimple] No render pass encoder for draw indexed" << std::endl;
            return;
        }

        _renderPassEncoder.drawIndexed(indexCount, instanceCount, firstIndex, vertexOffset, firstInstance);
        std::cout << "[WebGPUCommandListSimple] ✅ Executed WebGPU draw indexed command" << std::endl;
    }

} // namespace USG
