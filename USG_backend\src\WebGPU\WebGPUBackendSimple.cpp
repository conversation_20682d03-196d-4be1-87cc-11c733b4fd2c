#include "WebGPUBackendSimple.h"
#include <iostream>
#include <cassert>
#include <thread>
#include <chrono>
#include <cstring>

#ifdef USG_PLATFORM_DESKTOP
#include <GLFW/glfw3.h>
#ifdef _WIN32
#define GLFW_EXPOSE_NATIVE_WIN32
#include <GLFW/glfw3native.h>
#include <windows.h>
#endif
#endif

// 辅助函数：将C字符串转换为WGPUStringView
static WGPUStringView toWGPUStringView(const char *str)
{
    WGPUStringView view = {};
    if (str)
    {
        view.data = str;
        view.length = strlen(str);
    }
    else
    {
        view.data = nullptr;
        view.length = 0;
    }
    return view;
}

namespace USG
{

    // 静态成员初始化
    WebGPUBackendSimple::RequestResult WebGPUBackendSimple::_adapterRequest;
    WebGPUBackendSimple::RequestResult WebGPUBackendSimple::_deviceRequest;

    WebGPUBackendSimple::WebGPUBackendSimple()
    {
    }

    WebGPUBackendSimple::~WebGPUBackendSimple()
    {
        cleanup();
    }

    bool WebGPUBackendSimple::initialize(const BackendConfig &config)
    {
        if (_initialized)
        {
            return true;
        }

        _config = config;

        std::cout << "[WebGPUBackendSimple] Initializing WebGPU backend..." << std::endl;

        // 初始化WebGPU
        if (!initializeWebGPU())
        {
            std::cerr << "[WebGPUBackendSimple] Failed to initialize WebGPU" << std::endl;
            return false;
        }

        // 创建表面
        if (!createSurface(config.nativeWindow))
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create surface" << std::endl;
            return false;
        }

        // 创建设备
        if (!createDevice())
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create device" << std::endl;
            return false;
        }

        // 创建交换链
        if (!createSwapchain(config.swapchainWidth, config.swapchainHeight))
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create swapchain" << std::endl;
            return false;
        }

        _initialized = true;

        std::cout << "[WebGPUBackendSimple] WebGPU backend initialized successfully" << std::endl;
        return true;
    }

    void WebGPUBackendSimple::cleanup()
    {
        if (!_initialized)
        {
            return;
        }

        std::cout << "[WebGPUBackendSimple] Cleaning up WebGPU backend..." << std::endl;

        // Swapchain cleanup removed - using newer WebGPU API

        if (_surface)
        {
            wgpuSurfaceRelease(_surface);
            _surface = nullptr;
        }

        if (_queue)
        {
            wgpuQueueRelease(_queue);
            _queue = nullptr;
        }

        if (_device)
        {
            wgpuDeviceRelease(_device);
            _device = nullptr;
        }

        if (_adapter)
        {
            wgpuAdapterRelease(_adapter);
            _adapter = nullptr;
        }

        if (_instance)
        {
            wgpuInstanceRelease(_instance);
            _instance = nullptr;
        }

        _initialized = false;

        std::cout << "[WebGPUBackendSimple] WebGPU backend cleaned up" << std::endl;
    }

    bool WebGPUBackendSimple::initializeWebGPU()
    {
        // 创建WebGPU实例
        WGPUInstanceDescriptor instanceDesc = {};
        instanceDesc.nextInChain = nullptr;

        _instance = wgpuCreateInstance(&instanceDesc);
        if (!_instance)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create WebGPU instance" << std::endl;
            return false;
        }

        std::cout << "[WebGPUBackendSimple] Created WebGPU instance" << std::endl;
        return true;
    }

    bool WebGPUBackendSimple::createSurface(void *nativeWindow)
    {
#ifdef USG_PLATFORM_DESKTOP
        GLFWwindow *window = static_cast<GLFWwindow *>(nativeWindow);
        if (!window)
        {
            std::cerr << "[WebGPUBackendSimple] Invalid native window" << std::endl;
            return false;
        }

        // 创建平台特定的表面描述符
#ifdef _WIN32
        // Windows平台：使用Win32表面
        HWND hwnd = glfwGetWin32Window(window);
        if (!hwnd)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to get Win32 window handle" << std::endl;
            return false;
        }

        WGPUSurfaceSourceWindowsHWND win32SurfaceDesc = {};
        win32SurfaceDesc.chain.next = nullptr;
        win32SurfaceDesc.chain.sType = WGPUSType_SurfaceSourceWindowsHWND;
        win32SurfaceDesc.hinstance = GetModuleHandle(nullptr);
        win32SurfaceDesc.hwnd = hwnd;

        WGPUSurfaceDescriptor surfaceDesc = {};
        surfaceDesc.nextInChain = reinterpret_cast<WGPUChainedStruct *>(&win32SurfaceDesc);
        surfaceDesc.label = toWGPUStringView("Win32 Surface");
#else
        // 其他平台的表面创建（Linux/macOS）
        WGPUSurfaceDescriptor surfaceDesc = {};
        surfaceDesc.nextInChain = nullptr;
        surfaceDesc.label = toWGPUStringView("GLFW Surface");
#endif

        // 创建表面
        _surface = wgpuInstanceCreateSurface(_instance, &surfaceDesc);

        if (!_surface)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create surface" << std::endl;
            return false;
        }

        std::cout << "[WebGPUBackendSimple] Created surface successfully" << std::endl;
        return true;
#else
        std::cerr << "[WebGPUBackendSimple] Surface creation not supported on this platform" << std::endl;
        return false;
#endif
    }

    bool WebGPUBackendSimple::createDevice()
    {
        // 请求适配器
        WGPURequestAdapterOptions adapterOptions = {};
        adapterOptions.nextInChain = nullptr;
        adapterOptions.compatibleSurface = _surface;
        adapterOptions.powerPreference = WGPUPowerPreference_HighPerformance;
        adapterOptions.forceFallbackAdapter = false;

        _adapterRequest = {};

        // 设置回调信息
        WGPURequestAdapterCallbackInfo callbackInfo = {};
        callbackInfo.nextInChain = nullptr;
        callbackInfo.mode = WGPUCallbackMode_AllowProcessEvents;
        callbackInfo.callback = onAdapterRequestEnded;
        callbackInfo.userdata1 = nullptr;
        callbackInfo.userdata2 = nullptr;

        wgpuInstanceRequestAdapter(_instance, &adapterOptions, callbackInfo);

        // 等待适配器请求完成
        while (!_adapterRequest.completed)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
#ifdef USG_USE_WGPU_NATIVE
            wgpuInstanceProcessEvents(_instance);
#endif
        }

        if (!_adapterRequest.success || !_adapterRequest.adapter)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to get adapter: " << _adapterRequest.message << std::endl;
            return false;
        }

        _adapter = _adapterRequest.adapter;
        std::cout << "[WebGPUBackendSimple] Got adapter" << std::endl;

        // 请求设备
        WGPUDeviceDescriptor deviceDesc = {};
        deviceDesc.nextInChain = nullptr;
        deviceDesc.label = toWGPUStringView("USG WebGPU Device");
        deviceDesc.requiredFeatureCount = 0;
        deviceDesc.requiredFeatures = nullptr;
        deviceDesc.requiredLimits = nullptr;
        deviceDesc.defaultQueue.nextInChain = nullptr;
        deviceDesc.defaultQueue.label = toWGPUStringView("Default Queue");

        // 设置错误回调
        deviceDesc.uncapturedErrorCallbackInfo.nextInChain = nullptr;
        deviceDesc.uncapturedErrorCallbackInfo.callback = onDeviceError;
        deviceDesc.uncapturedErrorCallbackInfo.userdata1 = nullptr;
        deviceDesc.uncapturedErrorCallbackInfo.userdata2 = nullptr;

        _deviceRequest = {};

        // 设置设备请求回调信息
        WGPURequestDeviceCallbackInfo deviceCallbackInfo = {};
        deviceCallbackInfo.nextInChain = nullptr;
        deviceCallbackInfo.mode = WGPUCallbackMode_AllowProcessEvents;
        deviceCallbackInfo.callback = onDeviceRequestEnded;
        deviceCallbackInfo.userdata1 = nullptr;
        deviceCallbackInfo.userdata2 = nullptr;

        wgpuAdapterRequestDevice(_adapter, &deviceDesc, deviceCallbackInfo);

        // 等待设备请求完成
        while (!_deviceRequest.completed)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
#ifdef USG_USE_WGPU_NATIVE
            wgpuInstanceProcessEvents(_instance);
#endif
        }

        if (!_deviceRequest.success || !_deviceRequest.device)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to get device: " << _deviceRequest.message << std::endl;
            return false;
        }

        _device = _deviceRequest.device;
        _queue = wgpuDeviceGetQueue(_device);

        std::cout << "[WebGPUBackendSimple] Created device and queue" << std::endl;
        return true;
    }

    bool WebGPUBackendSimple::createSwapchain(uint32_t width, uint32_t height)
    {
        // 简化实现 - 不创建实际的交换链
        std::cout << "[WebGPUBackendSimple] Created virtual swapchain (" << width << "x" << height << ")" << std::endl;
        return true;
    }

    // 回调函数实现
    void WebGPUBackendSimple::onDeviceError(WGPUDevice const *device, WGPUErrorType type, WGPUStringView message, void *userdata1, void *userdata2)
    {
        if (message.data && message.length > 0)
        {
            std::string messageStr(message.data, message.length);
            std::cerr << "[WebGPUBackendSimple] Device error: " << messageStr << std::endl;
        }
        else
        {
            std::cerr << "[WebGPUBackendSimple] Device error (no message)" << std::endl;
        }
    }

    void WebGPUBackendSimple::onAdapterRequestEnded(WGPURequestAdapterStatus status, WGPUAdapter adapter, WGPUStringView message, void *userdata1, void *userdata2)
    {
        _adapterRequest.completed = true;
        _adapterRequest.success = (status == WGPURequestAdapterStatus_Success);
        _adapterRequest.adapter = adapter;
        if (message.data && message.length > 0)
        {
            _adapterRequest.message = std::string(message.data, message.length);
        }
    }

    void WebGPUBackendSimple::onDeviceRequestEnded(WGPURequestDeviceStatus status, WGPUDevice device, WGPUStringView message, void *userdata1, void *userdata2)
    {
        _deviceRequest.completed = true;
        _deviceRequest.success = (status == WGPURequestDeviceStatus_Success);
        _deviceRequest.device = device;
        if (message.data && message.length > 0)
        {
            _deviceRequest.message = std::string(message.data, message.length);
        }
    }

    // 资源创建方法 - 简化实现
    BackendBuffer *WebGPUBackendSimple::createBuffer(const BufferDesc &desc)
    {
        WGPUBufferDescriptor bufferDesc = {};
        bufferDesc.nextInChain = nullptr;
        bufferDesc.label = toWGPUStringView(desc.label.c_str());
        bufferDesc.size = desc.size;
        bufferDesc.usage = static_cast<WGPUBufferUsage>(desc.usage);
        bufferDesc.mappedAtCreation = false;

        WGPUBuffer buffer = wgpuDeviceCreateBuffer(_device, &bufferDesc);
        if (!buffer)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create buffer" << std::endl;
            return nullptr;
        }

        std::cout << "[WebGPUBackendSimple] Created buffer (size: " << desc.size << " bytes)" << std::endl;
        return new WebGPUBufferSimple(buffer, desc);
    }

    BackendTexture *WebGPUBackendSimple::createTexture(const TextureDesc &desc)
    {
        // 简化实现 - 返回nullptr表示暂不支持
        std::cout << "[WebGPUBackendSimple] Texture creation not implemented yet" << std::endl;
        return nullptr;
    }

    BackendShader *WebGPUBackendSimple::createShader(const ShaderDesc &desc)
    {
        std::cout << "[WebGPUBackendSimple] Creating shader (stage: " << static_cast<int>(desc.stage) << ")" << std::endl;

        // 获取默认的WGSL着色器代码
        std::string wgslCode;
        if (desc.stage == ShaderStage::Vertex)
        {
            wgslCode = getDefaultVertexShaderWGSL();
        }
        else if (desc.stage == ShaderStage::Fragment)
        {
            wgslCode = getDefaultFragmentShaderWGSL();
        }
        else
        {
            std::cerr << "[WebGPUBackendSimple] Unsupported shader stage: " << static_cast<int>(desc.stage) << std::endl;
            return nullptr;
        }

        // 创建着色器模块描述符
        WGPUShaderModuleWGSLDescriptor wgslDesc = {};
        wgslDesc.chain.next = nullptr;
        wgslDesc.chain.sType = WGPUSType_ShaderModuleWGSLDescriptor;
        wgslDesc.code = toWGPUStringView(wgslCode.c_str());

        WGPUShaderModuleDescriptor shaderDesc = {};
        shaderDesc.nextInChain = reinterpret_cast<WGPUChainedStruct *>(&wgslDesc);
        shaderDesc.label = toWGPUStringView("Default Shader");

        // 创建着色器模块
        WGPUShaderModule shaderModule = wgpuDeviceCreateShaderModule(_device, &shaderDesc);
        if (!shaderModule)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create shader module" << std::endl;
            return nullptr;
        }

        std::cout << "[WebGPUBackendSimple] Created shader module successfully" << std::endl;

        // 创建着色器对象
        auto shader = new WebGPUShaderSimple(shaderModule, desc);
        _resources.push_back(std::unique_ptr<BackendResource>(shader));

        std::cout << "[WebGPUBackendSimple] Created shader " << _resources.size() << std::endl;
        return shader;
    }

    BackendPipeline *WebGPUBackendSimple::createPipeline(const PipelineDesc &desc)
    {
        // 简化实现 - 返回nullptr表示暂不支持
        std::cout << "[WebGPUBackendSimple] Pipeline creation not implemented yet" << std::endl;
        return nullptr;
    }

    BackendDescriptorSet *WebGPUBackendSimple::createDescriptorSet()
    {
        // 简化实现 - 返回nullptr表示暂不支持
        std::cout << "[WebGPUBackendSimple] DescriptorSet creation not implemented yet" << std::endl;
        return nullptr;
    }

    BackendCommandList *WebGPUBackendSimple::createCommandList()
    {
        WGPUCommandEncoderDescriptor encoderDesc = {};
        encoderDesc.nextInChain = nullptr;
        encoderDesc.label = toWGPUStringView("Command Encoder");

        WGPUCommandEncoder encoder = wgpuDeviceCreateCommandEncoder(_device, &encoderDesc);
        if (!encoder)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create command encoder" << std::endl;
            return nullptr;
        }

        std::cout << "[WebGPUBackendSimple] Created command list" << std::endl;
        return new WebGPUCommandListSimple(encoder);
    }

    BackendFence *WebGPUBackendSimple::createFence()
    {
        return new WebGPUFenceSimple();
    }

    BackendSemaphore *WebGPUBackendSimple::createSemaphore()
    {
        return new WebGPUSemaphoreSimple();
    }

    void WebGPUBackendSimple::executeCommandList(BackendCommandList *cmdList,
                                                 const std::vector<BackendSemaphore *> &waitSemaphores,
                                                 const std::vector<BackendSemaphore *> &signalSemaphores,
                                                 BackendFence *fence)
    {
        // 简化实现
        std::cout << "[WebGPUBackendSimple] Executing command list" << std::endl;
    }

    // 资源销毁方法
    void WebGPUBackendSimple::destroyBuffer(BackendBuffer *buffer)
    {
        delete buffer;
    }

    void WebGPUBackendSimple::destroyTexture(BackendTexture *texture)
    {
        delete texture;
    }

    void WebGPUBackendSimple::destroyShader(BackendShader *shader)
    {
        delete shader;
    }

    void WebGPUBackendSimple::destroyPipeline(BackendPipeline *pipeline)
    {
        delete pipeline;
    }

    void WebGPUBackendSimple::destroyDescriptorSet(BackendDescriptorSet *descriptorSet)
    {
        delete descriptorSet;
    }

    void WebGPUBackendSimple::destroyCommandList(BackendCommandList *cmdList)
    {
        delete cmdList;
    }

    void WebGPUBackendSimple::destroyFence(BackendFence *fence)
    {
        delete fence;
    }

    void WebGPUBackendSimple::destroySemaphore(BackendSemaphore *semaphore)
    {
        delete semaphore;
    }

    // WebGPU资源类实现
    WebGPUBufferSimple::WebGPUBufferSimple(WGPUBuffer buffer, const BufferDesc &desc)
        : _buffer(buffer), _desc(desc)
    {
    }

    WebGPUBufferSimple::~WebGPUBufferSimple()
    {
        if (_buffer)
        {
            wgpuBufferRelease(_buffer);
        }
    }

    WebGPUTextureSimple::WebGPUTextureSimple(WGPUTexture texture, const TextureDesc &desc)
        : _texture(texture), _desc(desc)
    {
    }

    WebGPUTextureSimple::~WebGPUTextureSimple()
    {
        if (_texture)
        {
            wgpuTextureRelease(_texture);
        }
    }

    WebGPUShaderSimple::WebGPUShaderSimple(WGPUShaderModule module, const ShaderDesc &desc)
        : _module(module), _desc(desc)
    {
    }

    WebGPUShaderSimple::~WebGPUShaderSimple()
    {
        if (_module)
        {
            wgpuShaderModuleRelease(_module);
        }
    }

    // 默认WGSL着色器代码
    std::string WebGPUBackendSimple::getDefaultVertexShaderWGSL()
    {
        return R"(
@vertex
fn vs_main(@builtin(vertex_index) vertexIndex: u32) -> @builtin(position) vec4<f32> {
    // 根据顶点索引生成全屏三角形
    var pos = array<vec2<f32>, 3>(
        vec2<f32>(-1.0, -1.0),
        vec2<f32>( 3.0, -1.0),
        vec2<f32>(-1.0,  3.0)
    );

    return vec4<f32>(pos[vertexIndex], 0.0, 1.0);
}
)";
    }

    std::string WebGPUBackendSimple::getDefaultFragmentShaderWGSL()
    {
        return R"(
@fragment
fn fs_main() -> @location(0) vec4<f32> {
    return vec4<f32>(1.0, 0.0, 0.0, 1.0); // 红色
}
)";
    }

    WebGPUPipelineSimple::WebGPUPipelineSimple(WGPURenderPipeline pipeline, const PipelineDesc &desc)
        : _pipeline(pipeline), _desc(desc)
    {
    }

    WebGPUPipelineSimple::~WebGPUPipelineSimple()
    {
        if (_pipeline)
        {
            wgpuRenderPipelineRelease(_pipeline);
        }
    }

    WebGPUDescriptorSetSimple::WebGPUDescriptorSetSimple(WGPUBindGroup bindGroup)
        : _bindGroup(bindGroup)
    {
    }

    WebGPUDescriptorSetSimple::~WebGPUDescriptorSetSimple()
    {
        if (_bindGroup)
        {
            wgpuBindGroupRelease(_bindGroup);
        }
    }

    WebGPUCommandListSimple::WebGPUCommandListSimple(WGPUCommandEncoder encoder)
        : _encoder(encoder)
    {
    }

    WebGPUCommandListSimple::~WebGPUCommandListSimple()
    {
        if (_encoder)
        {
            wgpuCommandEncoderRelease(_encoder);
        }
    }

} // namespace USG
