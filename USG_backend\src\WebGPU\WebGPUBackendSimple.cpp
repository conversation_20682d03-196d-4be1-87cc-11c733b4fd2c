#include "WebGPUBackendSimple.h"
#include <iostream>
#include <cassert>
#include <thread>
#include <chrono>
#include <cstring>

#ifdef USG_PLATFORM_DESKTOP
#include <GLFW/glfw3.h>
#ifdef _WIN32
#define GLFW_EXPOSE_NATIVE_WIN32
#include <GLFW/glfw3native.h>
#include <windows.h>
#endif
#endif

// 辅助函数：将C字符串转换为WGPUStringView
static WGPUStringView toWGPUStringView(const char *str)
{
    WGPUStringView view = {};
    if (str)
    {
        view.data = str;
        view.length = strlen(str);
    }
    else
    {
        view.data = nullptr;
        view.length = 0;
    }
    return view;
}

namespace USG
{

    // 静态成员初始化
    WebGPUBackendSimple::RequestResult WebGPUBackendSimple::_adapterRequest;
    WebGPUBackendSimple::RequestResult WebGPUBackendSimple::_deviceRequest;

    WebGPUBackendSimple::WebGPUBackendSimple()
    {
    }

    WebGPUBackendSimple::~WebGPUBackendSimple()
    {
        cleanup();
    }

    bool WebGPUBackendSimple::initialize(const BackendConfig &config)
    {
        if (_initialized)
        {
            return true;
        }

        _config = config;

        std::cout << "[WebGPUBackendSimple] Initializing WebGPU backend..." << std::endl;

        // 初始化WebGPU
        if (!initializeWebGPU())
        {
            std::cerr << "[WebGPUBackendSimple] Failed to initialize WebGPU" << std::endl;
            return false;
        }

        // 创建表面
        if (!createSurface(config.nativeWindow))
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create surface" << std::endl;
            return false;
        }

        // 创建设备
        if (!createDevice())
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create device" << std::endl;
            return false;
        }

        // 创建交换链
        if (!createSwapchain(config.swapchainWidth, config.swapchainHeight))
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create swapchain" << std::endl;
            return false;
        }

        _initialized = true;

        std::cout << "[WebGPUBackendSimple] WebGPU backend initialized successfully" << std::endl;
        return true;
    }

    void WebGPUBackendSimple::cleanup()
    {
        if (!_initialized)
        {
            return;
        }

        std::cout << "[WebGPUBackendSimple] Cleaning up WebGPU backend..." << std::endl;

        // Swapchain cleanup removed - using newer WebGPU API

        if (_surface)
        {
            wgpuSurfaceRelease(_surface);
            _surface = nullptr;
        }

        if (_queue)
        {
            wgpuQueueRelease(_queue);
            _queue = nullptr;
        }

        if (_device)
        {
            wgpuDeviceRelease(_device);
            _device = nullptr;
        }

        if (_adapter)
        {
            wgpuAdapterRelease(_adapter);
            _adapter = nullptr;
        }

        if (_instance)
        {
            wgpuInstanceRelease(_instance);
            _instance = nullptr;
        }

        _initialized = false;

        std::cout << "[WebGPUBackendSimple] WebGPU backend cleaned up" << std::endl;
    }

    bool WebGPUBackendSimple::initializeWebGPU()
    {
        // 创建WebGPU实例
        WGPUInstanceDescriptor instanceDesc = {};
        instanceDesc.nextInChain = nullptr;

        _instance = wgpuCreateInstance(&instanceDesc);
        if (!_instance)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create WebGPU instance" << std::endl;
            return false;
        }

        std::cout << "[WebGPUBackendSimple] Created WebGPU instance" << std::endl;
        return true;
    }

    bool WebGPUBackendSimple::createSurface(void *nativeWindow)
    {
#ifdef USG_PLATFORM_DESKTOP
        GLFWwindow *window = static_cast<GLFWwindow *>(nativeWindow);
        if (!window)
        {
            std::cerr << "[WebGPUBackendSimple] Invalid native window" << std::endl;
            return false;
        }

        // 创建平台特定的表面描述符
#ifdef _WIN32
        // Windows平台：使用Win32表面
        HWND hwnd = glfwGetWin32Window(window);
        if (!hwnd)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to get Win32 window handle" << std::endl;
            return false;
        }

        WGPUSurfaceSourceWindowsHWND win32SurfaceDesc = {};
        win32SurfaceDesc.chain.next = nullptr;
        win32SurfaceDesc.chain.sType = WGPUSType_SurfaceSourceWindowsHWND;
        win32SurfaceDesc.hinstance = GetModuleHandle(nullptr);
        win32SurfaceDesc.hwnd = hwnd;

        WGPUSurfaceDescriptor surfaceDesc = {};
        surfaceDesc.nextInChain = reinterpret_cast<WGPUChainedStruct *>(&win32SurfaceDesc);
        surfaceDesc.label = toWGPUStringView("Win32 Surface");
#else
        // 其他平台的表面创建（Linux/macOS）
        WGPUSurfaceDescriptor surfaceDesc = {};
        surfaceDesc.nextInChain = nullptr;
        surfaceDesc.label = toWGPUStringView("GLFW Surface");
#endif

        // 创建表面
        _surface = wgpuInstanceCreateSurface(_instance, &surfaceDesc);

        if (!_surface)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create surface" << std::endl;
            return false;
        }

        std::cout << "[WebGPUBackendSimple] Created surface successfully" << std::endl;
        return true;
#else
        std::cerr << "[WebGPUBackendSimple] Surface creation not supported on this platform" << std::endl;
        return false;
#endif
    }

    bool WebGPUBackendSimple::createDevice()
    {
        // 请求适配器
        WGPURequestAdapterOptions adapterOptions = {};
        adapterOptions.nextInChain = nullptr;
        adapterOptions.compatibleSurface = _surface;
        adapterOptions.powerPreference = WGPUPowerPreference_HighPerformance;
        adapterOptions.forceFallbackAdapter = false;

        _adapterRequest = {};

        // 设置回调信息
        WGPURequestAdapterCallbackInfo callbackInfo = {};
        callbackInfo.nextInChain = nullptr;
        callbackInfo.mode = WGPUCallbackMode_AllowProcessEvents;
        callbackInfo.callback = onAdapterRequestEnded;
        callbackInfo.userdata1 = nullptr;
        callbackInfo.userdata2 = nullptr;

        wgpuInstanceRequestAdapter(_instance, &adapterOptions, callbackInfo);

        // 等待适配器请求完成
        while (!_adapterRequest.completed)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
#ifdef USG_USE_WGPU_NATIVE
            wgpuInstanceProcessEvents(_instance);
#endif
        }

        if (!_adapterRequest.success || !_adapterRequest.adapter)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to get adapter: " << _adapterRequest.message << std::endl;
            return false;
        }

        _adapter = _adapterRequest.adapter;
        std::cout << "[WebGPUBackendSimple] Got adapter" << std::endl;

        // 请求设备
        WGPUDeviceDescriptor deviceDesc = {};
        deviceDesc.nextInChain = nullptr;
        deviceDesc.label = toWGPUStringView("USG WebGPU Device");
        deviceDesc.requiredFeatureCount = 0;
        deviceDesc.requiredFeatures = nullptr;
        deviceDesc.requiredLimits = nullptr;
        deviceDesc.defaultQueue.nextInChain = nullptr;
        deviceDesc.defaultQueue.label = toWGPUStringView("Default Queue");

        // 设置错误回调
        deviceDesc.uncapturedErrorCallbackInfo.nextInChain = nullptr;
        deviceDesc.uncapturedErrorCallbackInfo.callback = onDeviceError;
        deviceDesc.uncapturedErrorCallbackInfo.userdata1 = nullptr;
        deviceDesc.uncapturedErrorCallbackInfo.userdata2 = nullptr;

        _deviceRequest = {};

        // 设置设备请求回调信息
        WGPURequestDeviceCallbackInfo deviceCallbackInfo = {};
        deviceCallbackInfo.nextInChain = nullptr;
        deviceCallbackInfo.mode = WGPUCallbackMode_AllowProcessEvents;
        deviceCallbackInfo.callback = onDeviceRequestEnded;
        deviceCallbackInfo.userdata1 = nullptr;
        deviceCallbackInfo.userdata2 = nullptr;

        wgpuAdapterRequestDevice(_adapter, &deviceDesc, deviceCallbackInfo);

        // 等待设备请求完成
        while (!_deviceRequest.completed)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
#ifdef USG_USE_WGPU_NATIVE
            wgpuInstanceProcessEvents(_instance);
#endif
        }

        if (!_deviceRequest.success || !_deviceRequest.device)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to get device: " << _deviceRequest.message << std::endl;
            return false;
        }

        _device = _deviceRequest.device;
        _queue = wgpuDeviceGetQueue(_device);

        std::cout << "[WebGPUBackendSimple] Created device and queue" << std::endl;
        return true;
    }

    bool WebGPUBackendSimple::createSwapchain(uint32_t width, uint32_t height)
    {
        std::cout << "[WebGPUBackendSimple] Creating swapchain (" << width << "x" << height << ")" << std::endl;

        // 配置Surface
        WGPUSurfaceConfiguration config = {};
        config.device = _device;
        config.format = _swapChainFormat;
        config.usage = WGPUTextureUsage_RenderAttachment;
        config.width = width;
        config.height = height;
        config.presentMode = WGPUPresentMode_Fifo;
        config.alphaMode = WGPUCompositeAlphaMode_Auto;

        wgpuSurfaceConfigure(_surface, &config);

        // 保存交换链尺寸
        _swapchainWidth = width;
        _swapchainHeight = height;

        std::cout << "[WebGPUBackendSimple] Surface configured successfully" << std::endl;
        return true;
    }

    // 回调函数实现
    void WebGPUBackendSimple::onDeviceError(WGPUDevice const *device, WGPUErrorType type, WGPUStringView message, void *userdata1, void *userdata2)
    {
        if (message.data && message.length > 0)
        {
            std::string messageStr(message.data, message.length);
            std::cerr << "[WebGPUBackendSimple] Device error: " << messageStr << std::endl;
        }
        else
        {
            std::cerr << "[WebGPUBackendSimple] Device error (no message)" << std::endl;
        }
    }

    void WebGPUBackendSimple::onAdapterRequestEnded(WGPURequestAdapterStatus status, WGPUAdapter adapter, WGPUStringView message, void *userdata1, void *userdata2)
    {
        _adapterRequest.completed = true;
        _adapterRequest.success = (status == WGPURequestAdapterStatus_Success);
        _adapterRequest.adapter = adapter;
        if (message.data && message.length > 0)
        {
            _adapterRequest.message = std::string(message.data, message.length);
        }
    }

    void WebGPUBackendSimple::onDeviceRequestEnded(WGPURequestDeviceStatus status, WGPUDevice device, WGPUStringView message, void *userdata1, void *userdata2)
    {
        _deviceRequest.completed = true;
        _deviceRequest.success = (status == WGPURequestDeviceStatus_Success);
        _deviceRequest.device = device;
        if (message.data && message.length > 0)
        {
            _deviceRequest.message = std::string(message.data, message.length);
        }
    }

    // 资源创建方法 - 简化实现
    BackendBuffer *WebGPUBackendSimple::createBuffer(const BufferDesc &desc)
    {
        WGPUBufferDescriptor bufferDesc = {};
        bufferDesc.nextInChain = nullptr;
        bufferDesc.label = toWGPUStringView(desc.label.c_str());
        bufferDesc.size = desc.size;
        bufferDesc.usage = convertBufferUsage(desc.usage);
        bufferDesc.mappedAtCreation = false;

        WGPUBuffer buffer = wgpuDeviceCreateBuffer(_device, &bufferDesc);
        if (!buffer)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create buffer" << std::endl;
            return nullptr;
        }

        std::cout << "[WebGPUBackendSimple] Created buffer (size: " << desc.size << " bytes)" << std::endl;
        return new WebGPUBufferSimple(buffer, desc);
    }

    BackendTexture *WebGPUBackendSimple::createTexture(const TextureDesc &desc)
    {
        // 简化实现 - 返回nullptr表示暂不支持
        std::cout << "[WebGPUBackendSimple] Texture creation not implemented yet" << std::endl;
        return nullptr;
    }

    BackendShader *WebGPUBackendSimple::createShader(const ShaderDesc &desc)
    {
        std::cout << "[WebGPUBackendSimple] Creating shader (stage: " << static_cast<int>(desc.stage) << ")" << std::endl;

        // 获取默认的WGSL着色器代码
        std::string wgslCode;
        if (desc.stage == ShaderStage::Vertex)
        {
            wgslCode = getDefaultVertexShaderWGSL();
        }
        else if (desc.stage == ShaderStage::Fragment)
        {
            wgslCode = getDefaultFragmentShaderWGSL();
        }
        else
        {
            std::cerr << "[WebGPUBackendSimple] Unsupported shader stage: " << static_cast<int>(desc.stage) << std::endl;
            return nullptr;
        }

        // 创建着色器模块描述符
        WGPUShaderSourceWGSL wgslDesc = {};
        wgslDesc.chain.next = nullptr;
        wgslDesc.chain.sType = WGPUSType_ShaderSourceWGSL;
        wgslDesc.code = toWGPUStringView(wgslCode.c_str());

        WGPUShaderModuleDescriptor shaderDesc = {};
        shaderDesc.nextInChain = reinterpret_cast<WGPUChainedStruct *>(&wgslDesc);
        shaderDesc.label = toWGPUStringView("Default Shader");

        // 创建着色器模块
        WGPUShaderModule shaderModule = wgpuDeviceCreateShaderModule(_device, &shaderDesc);
        if (!shaderModule)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create shader module" << std::endl;
            return nullptr;
        }

        std::cout << "[WebGPUBackendSimple] Created shader module successfully" << std::endl;

        // 创建着色器对象
        auto shader = new WebGPUShaderSimple(shaderModule, desc);

        std::cout << "[WebGPUBackendSimple] Created shader successfully" << std::endl;
        return shader;
    }

    BackendPipeline *WebGPUBackendSimple::createPipeline(const PipelineDesc &desc)
    {
        std::cout << "[WebGPUBackendSimple] Creating render pipeline" << std::endl;

        if (!desc.vertexShader || !desc.fragmentShader)
        {
            std::cerr << "[WebGPUBackendSimple] Both vertex and fragment shaders are required" << std::endl;
            return nullptr;
        }

        // 获取着色器模块
        WebGPUShaderSimple *vertexShader = static_cast<WebGPUShaderSimple *>(desc.vertexShader);
        WebGPUShaderSimple *fragmentShader = static_cast<WebGPUShaderSimple *>(desc.fragmentShader);

        // 创建渲染管线描述符
        WGPURenderPipelineDescriptor pipelineDesc = {};
        pipelineDesc.label = toWGPUStringView("Default Render Pipeline");

        // 顶点属性描述
        static WGPUVertexAttribute vertexAttributes[2] = {};

        // 位置属性 (location 0)
        vertexAttributes[0].format = WGPUVertexFormat_Float32x3;
        vertexAttributes[0].offset = 0;
        vertexAttributes[0].shaderLocation = 0;

        // 颜色属性 (location 1)
        vertexAttributes[1].format = WGPUVertexFormat_Float32x3;
        vertexAttributes[1].offset = 3 * sizeof(float);
        vertexAttributes[1].shaderLocation = 1;

        // 顶点缓冲区布局
        static WGPUVertexBufferLayout vertexBufferLayout = {};
        vertexBufferLayout.arrayStride = 6 * sizeof(float); // 3 floats for position + 3 floats for color
        vertexBufferLayout.stepMode = WGPUVertexStepMode_Vertex;
        vertexBufferLayout.attributeCount = 2;
        vertexBufferLayout.attributes = vertexAttributes;

        // 顶点阶段
        WGPUVertexState vertexState = {};
        vertexState.module = vertexShader->getModule();
        vertexState.entryPoint = toWGPUStringView("vs_main");
        vertexState.bufferCount = 1;
        vertexState.buffers = &vertexBufferLayout;

        // 片段阶段
        WGPUFragmentState fragmentState = {};
        fragmentState.module = fragmentShader->getModule();
        fragmentState.entryPoint = toWGPUStringView("fs_main");

        // 颜色目标
        WGPUColorTargetState colorTarget = {};
        colorTarget.format = _swapChainFormat;
        colorTarget.writeMask = WGPUColorWriteMask_All;

        fragmentState.targetCount = 1;
        fragmentState.targets = &colorTarget;

        // 原始状态
        WGPUPrimitiveState primitiveState = {};
        primitiveState.topology = WGPUPrimitiveTopology_TriangleList;
        primitiveState.stripIndexFormat = WGPUIndexFormat_Undefined;
        primitiveState.frontFace = WGPUFrontFace_CCW;
        primitiveState.cullMode = WGPUCullMode_None;

        // 多重采样状态
        WGPUMultisampleState multisampleState = {};
        multisampleState.count = 1;
        multisampleState.mask = ~0u;
        multisampleState.alphaToCoverageEnabled = WGPUOptionalBool_False;

        // 深度模板状态 - 禁用深度测试以确保渲染
        WGPUDepthStencilState depthStencilState = {};
        depthStencilState.format = WGPUTextureFormat_Undefined; // 不使用深度缓冲区
        depthStencilState.depthWriteEnabled = WGPUOptionalBool_False;
        depthStencilState.depthCompare = WGPUCompareFunction_Always;

        // 设置管线描述符
        pipelineDesc.vertex = vertexState;
        pipelineDesc.fragment = &fragmentState;
        pipelineDesc.primitive = primitiveState;
        pipelineDesc.multisample = multisampleState;
        pipelineDesc.depthStencil = nullptr; // 明确设置为nullptr，不使用深度测试

        // 创建渲染管线
        WGPURenderPipeline pipeline = wgpuDeviceCreateRenderPipeline(_device, &pipelineDesc);
        if (!pipeline)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create render pipeline" << std::endl;
            return nullptr;
        }

        std::cout << "[WebGPUBackendSimple] Created render pipeline successfully" << std::endl;

        // 创建管线对象
        auto pipelineObj = new WebGPUPipelineSimple(pipeline, desc);
        return pipelineObj;
    }

    BackendDescriptorSet *WebGPUBackendSimple::createDescriptorSet()
    {
        // 简化实现 - 返回nullptr表示暂不支持
        std::cout << "[WebGPUBackendSimple] DescriptorSet creation not implemented yet" << std::endl;
        return nullptr;
    }

    BackendCommandList *WebGPUBackendSimple::createCommandList()
    {
        WGPUCommandEncoderDescriptor encoderDesc = {};
        encoderDesc.nextInChain = nullptr;
        encoderDesc.label = toWGPUStringView("Command Encoder");

        WGPUCommandEncoder encoder = wgpuDeviceCreateCommandEncoder(_device, &encoderDesc);
        if (!encoder)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to create command encoder" << std::endl;
            return nullptr;
        }

        std::cout << "[WebGPUBackendSimple] Created command list" << std::endl;
        return new WebGPUCommandListSimple(encoder, this);
    }

    BackendFence *WebGPUBackendSimple::createFence()
    {
        return new WebGPUFenceSimple();
    }

    BackendSemaphore *WebGPUBackendSimple::createSemaphore()
    {
        return new WebGPUSemaphoreSimple();
    }

    void WebGPUBackendSimple::executeCommandList(BackendCommandList *cmdList,
                                                 const std::vector<BackendSemaphore *> &waitSemaphores,
                                                 const std::vector<BackendSemaphore *> &signalSemaphores,
                                                 BackendFence *fence)
    {
        // 简化实现
        std::cout << "[WebGPUBackendSimple] Executing command list" << std::endl;
    }

    void WebGPUBackendSimple::updateBuffer(BackendBuffer *buffer, const void *data, size_t size, size_t offset)
    {
        if (!buffer || !data || size == 0)
        {
            std::cerr << "[WebGPUBackendSimple] Invalid parameters for updateBuffer" << std::endl;
            return;
        }

        auto *webgpuBuffer = static_cast<WebGPUBufferSimple *>(buffer);
        WGPUBuffer wgpuBuffer = webgpuBuffer->getBuffer();

        if (!wgpuBuffer)
        {
            std::cerr << "[WebGPUBackendSimple] Invalid WebGPU buffer" << std::endl;
            return;
        }

        // 使用队列写入数据到缓冲区
        wgpuQueueWriteBuffer(_queue, wgpuBuffer, offset, data, size);

        std::cout << "[WebGPUBackendSimple] Updated buffer with " << size << " bytes at offset " << offset << std::endl;
    }

    // 资源销毁方法
    void WebGPUBackendSimple::destroyBuffer(BackendBuffer *buffer)
    {
        delete buffer;
    }

    void WebGPUBackendSimple::destroyTexture(BackendTexture *texture)
    {
        delete texture;
    }

    void WebGPUBackendSimple::destroyShader(BackendShader *shader)
    {
        delete shader;
    }

    void WebGPUBackendSimple::destroyPipeline(BackendPipeline *pipeline)
    {
        delete pipeline;
    }

    void WebGPUBackendSimple::destroyDescriptorSet(BackendDescriptorSet *descriptorSet)
    {
        delete descriptorSet;
    }

    void WebGPUBackendSimple::destroyCommandList(BackendCommandList *cmdList)
    {
        delete cmdList;
    }

    void WebGPUBackendSimple::destroyFence(BackendFence *fence)
    {
        delete fence;
    }

    void WebGPUBackendSimple::destroySemaphore(BackendSemaphore *semaphore)
    {
        delete semaphore;
    }

    // WebGPU资源类实现
    WebGPUBufferSimple::WebGPUBufferSimple(WGPUBuffer buffer, const BufferDesc &desc)
        : _buffer(buffer), _desc(desc)
    {
    }

    WebGPUBufferSimple::~WebGPUBufferSimple()
    {
        if (_buffer)
        {
            wgpuBufferRelease(_buffer);
        }
    }

    WebGPUTextureSimple::WebGPUTextureSimple(WGPUTexture texture, const TextureDesc &desc)
        : _texture(texture), _desc(desc)
    {
    }

    WebGPUTextureSimple::~WebGPUTextureSimple()
    {
        if (_texture)
        {
            wgpuTextureRelease(_texture);
        }
    }

    WebGPUShaderSimple::WebGPUShaderSimple(WGPUShaderModule module, const ShaderDesc &desc)
        : _module(module), _desc(desc)
    {
    }

    WebGPUShaderSimple::~WebGPUShaderSimple()
    {
        if (_module)
        {
            wgpuShaderModuleRelease(_module);
        }
    }

    // 默认WGSL着色器代码
    std::string WebGPUBackendSimple::getDefaultVertexShaderWGSL()
    {
        return R"(
struct VertexInput {
    @location(0) position: vec3<f32>,
    @location(1) color: vec3<f32>,
}

struct VertexOutput {
    @builtin(position) position: vec4<f32>,
    @location(0) color: vec3<f32>,
}

@vertex
fn vs_main(input: VertexInput) -> VertexOutput {
    var output: VertexOutput;
    // 直接使用顶点位置，确保在NDC空间内可见
    // 添加简单的变换以确保几何体可见
    var pos = input.position;
    // 缩放以确保在视口内
    pos = pos * 0.8;
    output.position = vec4<f32>(pos, 1.0);
    output.color = input.color;
    return output;
}
)";
    }

    std::string WebGPUBackendSimple::getDefaultFragmentShaderWGSL()
    {
        return R"(
struct FragmentInput {
    @location(0) color: vec3<f32>,
}

@fragment
fn fs_main(input: FragmentInput) -> @location(0) vec4<f32> {
    // 使用顶点颜色，增强亮度以确保可见
    return vec4<f32>(input.color * 1.2, 1.0);
}
)";
    }

    // 类型转换方法
    WGPUBufferUsage WebGPUBackendSimple::convertBufferUsage(BufferUsage usage)
    {
        WGPUBufferUsage wgpuUsage = WGPUBufferUsage_None;

        if (static_cast<uint32_t>(usage) & static_cast<uint32_t>(BufferUsage::Vertex))
        {
            wgpuUsage |= WGPUBufferUsage_Vertex;
            wgpuUsage |= WGPUBufferUsage_CopyDst; // 添加COPY_DST以支持数据上传
        }
        if (static_cast<uint32_t>(usage) & static_cast<uint32_t>(BufferUsage::Index))
        {
            wgpuUsage |= WGPUBufferUsage_Index;
            wgpuUsage |= WGPUBufferUsage_CopyDst; // 添加COPY_DST以支持数据上传
        }
        if (static_cast<uint32_t>(usage) & static_cast<uint32_t>(BufferUsage::Uniform))
        {
            wgpuUsage |= WGPUBufferUsage_Uniform;
            wgpuUsage |= WGPUBufferUsage_CopyDst; // 添加COPY_DST以支持数据上传
        }
        if (static_cast<uint32_t>(usage) & static_cast<uint32_t>(BufferUsage::Storage))
        {
            wgpuUsage |= WGPUBufferUsage_Storage;
            wgpuUsage |= WGPUBufferUsage_CopyDst; // 添加COPY_DST以支持数据上传
        }
        if (static_cast<uint32_t>(usage) & static_cast<uint32_t>(BufferUsage::Indirect))
        {
            wgpuUsage |= WGPUBufferUsage_Indirect;
        }
        if (static_cast<uint32_t>(usage) & static_cast<uint32_t>(BufferUsage::TransferSrc))
        {
            wgpuUsage |= WGPUBufferUsage_CopySrc;
        }
        if (static_cast<uint32_t>(usage) & static_cast<uint32_t>(BufferUsage::TransferDst))
        {
            wgpuUsage |= WGPUBufferUsage_CopyDst;
        }
        if (static_cast<uint32_t>(usage) & static_cast<uint32_t>(BufferUsage::QueryBuffer))
        {
            wgpuUsage |= WGPUBufferUsage_QueryResolve;
        }

        return wgpuUsage;
    }

    void WebGPUBackendSimple::beginFrame()
    {
        std::cout << "[WebGPUBackendSimple] Begin frame" << std::endl;

        // 获取当前交换链纹理
        WGPUSurfaceTexture surfaceTexture;
        wgpuSurfaceGetCurrentTexture(_surface, &surfaceTexture);

        if (surfaceTexture.status != WGPUSurfaceGetCurrentTextureStatus_SuccessOptimal &&
            surfaceTexture.status != WGPUSurfaceGetCurrentTextureStatus_SuccessSuboptimal)
        {
            std::cerr << "[WebGPUBackendSimple] Failed to get current texture: " << surfaceTexture.status << std::endl;
            return;
        }

        _currentTexture = surfaceTexture.texture;

        // 创建纹理视图
        WGPUTextureViewDescriptor viewDesc = {};
        viewDesc.label = toWGPUStringView("Surface texture view");
        viewDesc.format = _swapChainFormat;
        viewDesc.dimension = WGPUTextureViewDimension_2D;
        viewDesc.baseMipLevel = 0;
        viewDesc.mipLevelCount = 1;
        viewDesc.baseArrayLayer = 0;
        viewDesc.arrayLayerCount = 1;
        viewDesc.aspect = WGPUTextureAspect_All;

        _currentTextureView = wgpuTextureCreateView(_currentTexture, &viewDesc);

        std::cout << "[WebGPUBackendSimple] Frame begun successfully" << std::endl;
    }

    void WebGPUBackendSimple::endFrame()
    {
        std::cout << "[WebGPUBackendSimple] End frame" << std::endl;

        // 清理当前帧资源
        if (_currentTextureView)
        {
            wgpuTextureViewRelease(_currentTextureView);
            _currentTextureView = nullptr;
        }

        // 注意：_currentTexture 由交换链管理，不需要手动释放
        _currentTexture = nullptr;
    }

    void WebGPUBackendSimple::present()
    {
        std::cout << "[WebGPUBackendSimple] Present frame" << std::endl;

        // WebGPU的present是通过Surface自动处理的
        // 当我们释放纹理视图时，内容会自动呈现
        wgpuSurfacePresent(_surface);

        std::cout << "[WebGPUBackendSimple] Frame presented" << std::endl;
    }

    void WebGPUBackendSimple::waitIdle()
    {
        std::cout << "[WebGPUBackendSimple] Wait idle" << std::endl;
        // WebGPU没有直接的waitIdle，但我们可以等待设备
        // 这里暂时留空，因为WebGPU是异步的
    }

    WebGPUPipelineSimple::WebGPUPipelineSimple(WGPURenderPipeline pipeline, const PipelineDesc &desc)
        : _pipeline(pipeline), _desc(desc)
    {
    }

    WebGPUPipelineSimple::~WebGPUPipelineSimple()
    {
        if (_pipeline)
        {
            wgpuRenderPipelineRelease(_pipeline);
        }
    }

    WebGPUDescriptorSetSimple::WebGPUDescriptorSetSimple(WGPUBindGroup bindGroup)
        : _bindGroup(bindGroup)
    {
    }

    WebGPUDescriptorSetSimple::~WebGPUDescriptorSetSimple()
    {
        if (_bindGroup)
        {
            wgpuBindGroupRelease(_bindGroup);
        }
    }

    WebGPUCommandListSimple::WebGPUCommandListSimple(WGPUCommandEncoder encoder, WebGPUBackendSimple *backend)
        : _encoder(encoder), _backend(backend)
    {
    }

    WebGPUCommandListSimple::~WebGPUCommandListSimple()
    {
        if (_renderPassEncoder)
        {
            wgpuRenderPassEncoderRelease(_renderPassEncoder);
        }
        if (_encoder)
        {
            wgpuCommandEncoderRelease(_encoder);
        }
    }

    void WebGPUCommandListSimple::beginRenderPass(const RenderPassDesc &desc)
    {
        std::cout << "[WebGPUCommandListSimple] Begin render pass" << std::endl;

        // 获取当前纹理视图
        WGPUTextureView textureView = _backend->getCurrentTextureView();
        if (!textureView)
        {
            std::cerr << "[WebGPUCommandListSimple] No current texture view available" << std::endl;
            return;
        }

        // 创建颜色附件
        WGPURenderPassColorAttachment colorAttachment = {};
        colorAttachment.view = textureView;
        colorAttachment.resolveTarget = nullptr;
        colorAttachment.loadOp = WGPULoadOp_Clear;
        colorAttachment.storeOp = WGPUStoreOp_Store;
        colorAttachment.clearValue = {0.0f, 0.5f, 0.0f, 1.0f}; // 明亮的绿色背景，便于观察

        // 创建渲染通道描述符
        WGPURenderPassDescriptor renderPassDesc = {};
        renderPassDesc.label = toWGPUStringView("Main Render Pass");
        renderPassDesc.colorAttachmentCount = 1;
        renderPassDesc.colorAttachments = &colorAttachment;
        renderPassDesc.depthStencilAttachment = nullptr;

        // 开始渲染通道
        _renderPassEncoder = wgpuCommandEncoderBeginRenderPass(_encoder, &renderPassDesc);
        if (!_renderPassEncoder)
        {
            std::cerr << "[WebGPUCommandListSimple] Failed to begin render pass" << std::endl;
            return;
        }

        // 设置视口（WebGPU默认视口是整个渲染目标，但显式设置确保正确）
        wgpuRenderPassEncoderSetViewport(_renderPassEncoder,
                                         0.0f, 0.0f,                            // x, y
                                         (float)_backend->getSwapchainWidth(),  // width
                                         (float)_backend->getSwapchainHeight(), // height
                                         0.0f, 1.0f);                           // minDepth, maxDepth

        std::cout << "[WebGPUCommandListSimple] Render pass begun successfully with viewport: "
                  << _backend->getSwapchainWidth() << "x" << _backend->getSwapchainHeight()
                  << ", Clear color: GREEN (0.0, 0.5, 0.0, 1.0)" << std::endl;
    }

    void WebGPUCommandListSimple::endRenderPass()
    {
        std::cout << "[WebGPUCommandListSimple] End render pass" << std::endl;

        if (_renderPassEncoder)
        {
            wgpuRenderPassEncoderEnd(_renderPassEncoder);
            wgpuRenderPassEncoderRelease(_renderPassEncoder);
            _renderPassEncoder = nullptr;
            std::cout << "[WebGPUCommandListSimple] Render pass ended successfully" << std::endl;
        }
    }

    void WebGPUCommandListSimple::setPipeline(BackendPipeline *pipeline)
    {
        std::cout << "[WebGPUCommandListSimple] Set pipeline" << std::endl;

        if (!_renderPassEncoder)
        {
            std::cerr << "[WebGPUCommandListSimple] No active render pass for setPipeline" << std::endl;
            return;
        }

        if (pipeline)
        {
            // 转换为WebGPU管线对象
            auto *webgpuPipeline = static_cast<WebGPUPipelineSimple *>(pipeline);
            WGPURenderPipeline wgpuPipeline = webgpuPipeline->getPipeline();

            if (wgpuPipeline)
            {
                // 设置WebGPU渲染管线
                wgpuRenderPassEncoderSetPipeline(_renderPassEncoder, wgpuPipeline);
                _currentPipeline = wgpuPipeline;
                std::cout << "[WebGPUCommandListSimple] Pipeline set successfully" << std::endl;
            }
            else
            {
                std::cerr << "[WebGPUCommandListSimple] Invalid WebGPU pipeline" << std::endl;
            }
        }
        else
        {
            std::cerr << "[WebGPUCommandListSimple] Null pipeline provided" << std::endl;
        }
    }

    void WebGPUCommandListSimple::setVertexBuffer(BackendBuffer *buffer, uint32_t binding, size_t offset)
    {
        std::cout << "[WebGPUCommandListSimple] Set vertex buffer (binding: " << binding << ", offset: " << offset << ")" << std::endl;

        if (!_renderPassEncoder)
        {
            std::cerr << "[WebGPUCommandListSimple] No active render pass for setVertexBuffer" << std::endl;
            return;
        }

        if (buffer)
        {
            // 转换为WebGPU缓冲区对象
            auto *webgpuBuffer = static_cast<WebGPUBufferSimple *>(buffer);
            WGPUBuffer wgpuBuffer = webgpuBuffer->getBuffer();

            if (wgpuBuffer)
            {
                // 设置WebGPU顶点缓冲区
                wgpuRenderPassEncoderSetVertexBuffer(_renderPassEncoder, binding, wgpuBuffer, offset, webgpuBuffer->getSize() - offset);
                _currentVertexBuffer = wgpuBuffer;
                std::cout << "[WebGPUCommandListSimple] Vertex buffer set successfully" << std::endl;
            }
            else
            {
                std::cerr << "[WebGPUCommandListSimple] Invalid WebGPU vertex buffer" << std::endl;
            }
        }
        else
        {
            std::cerr << "[WebGPUCommandListSimple] Null vertex buffer provided" << std::endl;
        }
    }

    void WebGPUCommandListSimple::setIndexBuffer(BackendBuffer *buffer, IndexFormat format, uint64_t offset)
    {
        std::cout << "[WebGPUCommandListSimple] Set index buffer (format: " << static_cast<int>(format) << ", offset: " << offset << ")" << std::endl;

        if (!_renderPassEncoder)
        {
            std::cerr << "[WebGPUCommandListSimple] No active render pass for setIndexBuffer" << std::endl;
            return;
        }

        if (buffer)
        {
            // 转换为WebGPU缓冲区对象
            auto *webgpuBuffer = static_cast<WebGPUBufferSimple *>(buffer);
            WGPUBuffer wgpuBuffer = webgpuBuffer->getBuffer();

            if (wgpuBuffer)
            {
                // 转换索引格式
                WGPUIndexFormat wgpuFormat = WGPUIndexFormat_Uint16;
                if (format == IndexFormat::UInt32)
                {
                    wgpuFormat = WGPUIndexFormat_Uint32;
                }

                // 设置WebGPU索引缓冲区
                wgpuRenderPassEncoderSetIndexBuffer(_renderPassEncoder, wgpuBuffer, wgpuFormat, offset, webgpuBuffer->getSize() - offset);
                _currentIndexBuffer = wgpuBuffer;
                std::cout << "[WebGPUCommandListSimple] Index buffer set successfully" << std::endl;
            }
            else
            {
                std::cerr << "[WebGPUCommandListSimple] Invalid WebGPU index buffer" << std::endl;
            }
        }
        else
        {
            std::cerr << "[WebGPUCommandListSimple] Null index buffer provided" << std::endl;
        }
    }

    void WebGPUCommandListSimple::draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance)
    {
        std::cout << "[WebGPUCommandListSimple] Draw (vertices: " << vertexCount << ", instances: " << instanceCount << ")" << std::endl;

        if (!_renderPassEncoder)
        {
            std::cerr << "[WebGPUCommandListSimple] No active render pass for draw" << std::endl;
            return;
        }

        wgpuRenderPassEncoderDraw(_renderPassEncoder, vertexCount, instanceCount, firstVertex, firstInstance);
        std::cout << "[WebGPUCommandListSimple] Draw command executed" << std::endl;
    }

    void WebGPUCommandListSimple::drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t vertexOffset, uint32_t firstInstance)
    {
        std::cout << "[WebGPUCommandListSimple] Draw indexed (indices: " << indexCount << ", instances: " << instanceCount << ")" << std::endl;

        if (!_renderPassEncoder)
        {
            std::cerr << "[WebGPUCommandListSimple] No active render pass for drawIndexed" << std::endl;
            return;
        }

        wgpuRenderPassEncoderDrawIndexed(_renderPassEncoder, indexCount, instanceCount, firstIndex, vertexOffset, firstInstance);
        std::cout << "[WebGPUCommandListSimple] Draw indexed command executed" << std::endl;
    }

} // namespace USG
