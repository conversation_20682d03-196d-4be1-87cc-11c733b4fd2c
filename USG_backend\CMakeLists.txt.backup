cmake_minimum_required(VERSION 3.20)

project(USG_Backend_Simple
    VERSION 1.0.0
    DESCRIPTION "USG Backend - 简化测试版本"
    LANGUAGES CXX
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 编译选项
if(MSVC)
    add_compile_options(/utf-8 /W4)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -pedantic)
endif()

# 构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 简化的选项
option(USG_BUILD_CORE_ONLY "只构建核心库" ON)
option(USG_BUILD_SIMPLE_TEST "构建简单测试" ON)

# 核心库源文件
set(USG_CORE_SOURCES
    src/Core/BackendTypes.cpp
    src/Core/BackendFactory.cpp
)

set(USG_CORE_HEADERS
    include/USG_Backend/BackendTypes.h
    include/USG_Backend/RenderBackend.h
    include/USG_Backend/BackendFactory.h
)

# 创建核心库
add_library(USG_Backend_Core_Simple ${USG_CORE_SOURCES} ${USG_CORE_HEADERS})

# 设置核心库属性
set_target_properties(USG_Backend_Core_Simple PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

# 核心库包含目录
target_include_directories(USG_Backend_Core_Simple
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# 核心库编译定义
target_compile_definitions(USG_Backend_Core_Simple
    PUBLIC
        USG_BACKEND_VERSION_MAJOR=${PROJECT_VERSION_MAJOR}
        USG_BACKEND_VERSION_MINOR=${PROJECT_VERSION_MINOR}
        USG_BACKEND_VERSION_PATCH=${PROJECT_VERSION_PATCH}
)

# 简单测试
if(USG_BUILD_SIMPLE_TEST)
    # 创建简单测试可执行文件
    add_executable(test_simple_backend tests/test_simple_backend.cpp)
    
    # 链接库
    target_link_libraries(test_simple_backend
        PRIVATE
            USG_Backend_Core_Simple
    )
    
    # 包含目录
    target_include_directories(test_simple_backend
        PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}/include
            ${CMAKE_CURRENT_SOURCE_DIR}/src
    )
endif()

# 状态报告
message(STATUS "USG Backend Simple Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Platform: ${CMAKE_SYSTEM_NAME}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Core only: ${USG_BUILD_CORE_ONLY}")
message(STATUS "  Simple test: ${USG_BUILD_SIMPLE_TEST}")
