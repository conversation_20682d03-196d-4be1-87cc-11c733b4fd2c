#pragma once

#include <USG/Core/Object.h>
#include <USG/Math/BoundingSphere.h>
#include <vector>

namespace usg
{

    // 前向声明
    class Visitor;
    class StateSet;

    /**
     * @brief 场景图节点基类
     *
     * 所有场景图节点的基类，提供：
     * - 访问者模式支持
     * - 包围盒计算
     * - 状态管理
     * - 父子关系管理
     */
    class Node : public Object
    {
    public:
        Node() = default;
        virtual ~Node() = default;

        /**
         * @brief 接受访问者访问
         * @param visitor 访问者对象
         */
        virtual void accept(Visitor &visitor) = 0;

        /**
         * @brief 计算包围球
         * @return 包围球
         */
        virtual BoundingSphere computeBoundingSphere() const = 0;

        /**
         * @brief 获取包围球（带缓存）
         * @return 包围球
         */
        const BoundingSphere &getBoundingSphere() const
        {
            if (!_boundingSphereComputed)
            {
                _boundingSphere = computeBoundingSphere();
                _boundingSphereComputed = true;
            }
            return _boundingSphere;
        }

        /**
         * @brief 标记包围球需要重新计算
         */
        void dirtyBoundingSphere()
        {
            _boundingSphereComputed = false;
            // 通知父节点
            for (Node *parent : _parents)
            {
                parent->dirtyBoundingSphere();
            }
        }

        /**
         * @brief 设置状态集
         * @param stateSet 状态集
         */
        void setStateSet(ref_ptr<StateSet> stateSet)
        {
            _stateSet = stateSet;
        }

        /**
         * @brief 获取状态集
         * @return 状态集
         */
        ref_ptr<StateSet> getStateSet() const
        {
            return _stateSet;
        }

        /**
         * @brief 获取或创建状态集
         * @return 状态集
         */
        ref_ptr<StateSet> getOrCreateStateSet();

        /**
         * @brief 添加父节点
         * @param parent 父节点
         */
        void addParent(Node *parent)
        {
            if (parent && std::find(_parents.begin(), _parents.end(), parent) == _parents.end())
            {
                _parents.push_back(parent);
            }
        }

        /**
         * @brief 移除父节点
         * @param parent 父节点
         */
        void removeParent(Node *parent)
        {
            auto it = std::find(_parents.begin(), _parents.end(), parent);
            if (it != _parents.end())
            {
                _parents.erase(it);
            }
        }

        /**
         * @brief 获取父节点列表
         * @return 父节点列表
         */
        const std::vector<Node *> &getParents() const
        {
            return _parents;
        }

        /**
         * @brief 获取父节点数量
         * @return 父节点数量
         */
        size_t getNumParents() const
        {
            return _parents.size();
        }

        /**
         * @brief 获取指定索引的父节点
         * @param index 索引
         * @return 父节点
         */
        Node *getParent(size_t index) const
        {
            return index < _parents.size() ? _parents[index] : nullptr;
        }

        /**
         * @brief 检查是否有父节点
         * @return 是否有父节点
         */
        bool hasParents() const
        {
            return !_parents.empty();
        }

        /**
         * @brief 设置节点名称
         * @param name 节点名称
         */
        void setName(const std::string &name)
        {
            _name = name;
        }

        /**
         * @brief 获取节点名称
         * @return 节点名称
         */
        const std::string &getName() const
        {
            return _name;
        }

        /**
         * @brief 设置用户数据
         * @param userData 用户数据
         */
        void setUserData(ref_ptr<Object> userData)
        {
            _userData = userData;
        }

        /**
         * @brief 获取用户数据
         * @return 用户数据
         */
        ref_ptr<Object> getUserData() const
        {
            return _userData;
        }

        /**
         * @brief 设置节点掩码
         * @param mask 节点掩码
         */
        void setNodeMask(unsigned int mask)
        {
            _nodeMask = mask;
        }

        /**
         * @brief 获取节点掩码
         * @return 节点掩码
         */
        unsigned int getNodeMask() const
        {
            return _nodeMask;
        }

        /**
         * @brief 设置描述信息
         * @param descriptions 描述信息列表
         */
        void setDescriptions(const std::vector<std::string> &descriptions)
        {
            _descriptions = descriptions;
        }

        /**
         * @brief 获取描述信息
         * @return 描述信息列表
         */
        const std::vector<std::string> &getDescriptions() const
        {
            return _descriptions;
        }

        /**
         * @brief 添加描述信息
         * @param description 描述信息
         */
        void addDescription(const std::string &description)
        {
            _descriptions.push_back(description);
        }

        std::string className() const override
        {
            return "Node";
        }

    protected:
        // 状态管理
        ref_ptr<StateSet> _stateSet;

        // 层次结构
        std::vector<Node *> _parents;

        // 包围盒缓存
        mutable BoundingSphere _boundingSphere;
        mutable bool _boundingSphereComputed = false;

        // 节点属性
        std::string _name;
        ref_ptr<Object> _userData;
        unsigned int _nodeMask = 0xffffffff;
        std::vector<std::string> _descriptions;
    };

    /**
     * @brief 组节点，可以包含子节点
     */
    class Group : public Node
    {
    public:
        Group() = default;
        virtual ~Group() = default;

        void accept(Visitor &visitor) override;
        BoundingSphere computeBoundingSphere() const override;

        /**
         * @brief 添加子节点
         * @param child 子节点
         */
        virtual void addChild(ref_ptr<Node> child);

        /**
         * @brief 插入子节点
         * @param index 插入位置
         * @param child 子节点
         */
        virtual void insertChild(size_t index, ref_ptr<Node> child);

        /**
         * @brief 移除子节点
         * @param child 子节点
         * @return 是否成功移除
         */
        virtual bool removeChild(ref_ptr<Node> child);

        /**
         * @brief 移除指定索引的子节点
         * @param index 索引
         * @return 是否成功移除
         */
        virtual bool removeChild(size_t index);

        /**
         * @brief 移除多个子节点
         * @param start 起始索引
         * @param count 移除数量
         * @return 实际移除的数量
         */
        virtual size_t removeChildren(size_t start, size_t count);

        /**
         * @brief 替换子节点
         * @param oldChild 旧子节点
         * @param newChild 新子节点
         * @return 是否成功替换
         */
        virtual bool replaceChild(ref_ptr<Node> oldChild, ref_ptr<Node> newChild);

        /**
         * @brief 设置子节点
         * @param index 索引
         * @param child 子节点
         * @return 是否成功设置
         */
        virtual bool setChild(size_t index, ref_ptr<Node> child);

        /**
         * @brief 获取子节点数量
         * @return 子节点数量
         */
        size_t getNumChildren() const
        {
            return _children.size();
        }

        /**
         * @brief 获取指定索引的子节点
         * @param index 索引
         * @return 子节点
         */
        ref_ptr<Node> getChild(size_t index) const
        {
            return index < _children.size() ? _children[index] : nullptr;
        }

        /**
         * @brief 获取子节点列表
         * @return 子节点列表
         */
        const std::vector<ref_ptr<Node>> &getChildren() const
        {
            return _children;
        }

        /**
         * @brief 查找子节点索引
         * @param child 子节点
         * @return 索引，未找到返回size()
         */
        size_t getChildIndex(ref_ptr<Node> child) const;

        /**
         * @brief 检查是否包含指定子节点
         * @param child 子节点
         * @return 是否包含
         */
        bool containsNode(ref_ptr<Node> child) const;

        std::string className() const override
        {
            return "Group";
        }

    protected:
        std::vector<ref_ptr<Node>> _children;
    };

    /**
     * @brief 变换节点
     */
    class Transform : public Group
    {
    public:
        Transform() = default;
        explicit Transform(const mat4 &matrix) : _matrix(matrix) {}

        void accept(Visitor &visitor) override;
        BoundingSphere computeBoundingSphere() const override;

        /**
         * @brief 设置变换矩阵
         * @param matrix 变换矩阵
         */
        void setMatrix(const mat4 &matrix)
        {
            _matrix = matrix;
            dirtyBoundingSphere();
        }

        /**
         * @brief 获取变换矩阵
         * @return 变换矩阵
         */
        const mat4 &getMatrix() const
        {
            return _matrix;
        }

        /**
         * @brief 设置平移
         * @param translation 平移向量
         */
        void setTranslation(const vec3 &translation);

        /**
         * @brief 设置旋转
         * @param rotation 旋转四元数
         */
        void setRotation(const quat &rotation);

        /**
         * @brief 设置缩放
         * @param scale 缩放向量
         */
        void setScale(const vec3 &scale);

        /**
         * @brief 获取平移
         * @return 平移向量
         */
        vec3 getTranslation() const;

        /**
         * @brief 获取旋转
         * @return 旋转四元数
         */
        quat getRotation() const;

        /**
         * @brief 获取缩放
         * @return 缩放向量
         */
        vec3 getScale() const;

        std::string className() const override
        {
            return "Transform";
        }

    protected:
        mat4 _matrix = mat4(1.0f);
    };

} // namespace usg
