<?xml version='1.0' encoding='utf-8' ?>
<!DOCTYPE chapter PUBLIC "-//OASIS//DTD DocBook XML V4.5//EN"
"http://www.oasis-open.org/docbook/xml/4.5/docbookx.dtd" [
<!ENTITY % BOOK_ENTITIES SYSTEM "Wayland.ent">
%BOOK_ENTITIES;
]>

<preface>
  <title>Preface</title>

  <para>
  This document describes the (i) Wayland architecture, (ii) Wayland model of
  operation and (iii) its library API. Also, the Wayland protocol specification is shown
  in the Appendix. This document is aimed primarily at Wayland developers and
  those looking to program with it; it does not cover application development.
  </para>
  <para>
  There have been many contributors to this document and since this is only the
  first edition many errors are expected to be found. We appreciate
  corrections.
  </para>
  <literallayout>
Yours,

	the Wayland open-source community
	November 2012
  </literallayout>
</preface>
