cmake_minimum_required(VERSION 3.20)

project(USG_Backend_Core_Test
    VERSION 1.0.0
    DESCRIPTION "USG Backend - 核心测试版本"
    LANGUAGES CXX
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 编译选项
if(MSVC)
    add_compile_options(/utf-8 /W4)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -pedantic)
endif()

# 构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 核心库源文件
set(USG_CORE_SOURCES
    src/Core/BackendTypes.cpp
    src/Core/BackendFactory.cpp
)

set(USG_CORE_HEADERS
    include/USG_Backend/BackendTypes.h
    include/USG_Backend/RenderBackend.h
    include/USG_Backend/BackendFactory.h
    include/USG_Backend/BackendConfig.h
)

# 核心库源文件
set(USG_CORE_SOURCES
    src/Core/BackendFactory.cpp
    src/Core/BackendManager.cpp
    src/Core/TypeConverters.cpp
    src/Core/BackendTypes.cpp
)

set(USG_CORE_HEADERS
    include/USG_Backend/BackendTypes.h
    include/USG_Backend/RenderBackend.h
    include/USG_Backend/BackendFactory.h
    include/USG_Backend/BackendConfig.h
)

# WebGPU后端源文件
if(USG_BUILD_WEBGPU_BACKEND)
    set(USG_WEBGPU_SOURCES
        src/WebGPU/WebGPUBackend.cpp
        src/WebGPU/WebGPUAdapter.cpp
        src/WebGPU/WebGPUTypeConverter.cpp
        src/WebGPU/WebGPUShader.cpp
        src/WebGPU/WebGPUPipeline.cpp
        src/WebGPU/WebGPUDescriptorSet.cpp
        src/WebGPU/WebGPUCommandList.cpp
        src/WebGPU/WebGPUSync.cpp
        src/WebGPU/WebGPUDevice.cpp
    )

    set(USG_WEBGPU_HEADERS
        src/WebGPU/WebGPUBackend.h
        src/WebGPU/WebGPUAdapter.h
    )
endif()

# Vulkan后端源文件
if(USG_BUILD_VULKAN_BACKEND)
    set(USG_VULKAN_SOURCES
        src/Vulkan/VulkanBackend.cpp
    )
endif()

# VSG适配器源文件
if(USG_BUILD_VSG_ADAPTER)
    set(USG_VSG_SOURCES
        src/VSG_Adapter/VSG_Device.cpp
        src/VSG_Adapter/TypeConverter.cpp
        src/VSG_Adapter/ResourceManager.cpp
    )

    set(USG_VSG_HEADERS
        include/VSG_Adapter/VSG_Device.h
        src/VSG_Adapter/TypeConverter.h
        src/VSG_Adapter/ResourceManager.h
    )
endif()

# 创建核心库
add_library(USG_Backend_Core ${USG_CORE_SOURCES} ${USG_CORE_HEADERS})

# 设置核心库属性
set_target_properties(USG_Backend_Core PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    PUBLIC_HEADER "${USG_CORE_HEADERS}"
)

# 核心库包含目录
target_include_directories(USG_Backend_Core
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# 核心库编译定义
target_compile_definitions(USG_Backend_Core
    PUBLIC
        USG_BACKEND_VERSION_MAJOR=${PROJECT_VERSION_MAJOR}
        USG_BACKEND_VERSION_MINOR=${PROJECT_VERSION_MINOR}
        USG_BACKEND_VERSION_PATCH=${PROJECT_VERSION_PATCH}
)

if(USG_PLATFORM_WASM)
    target_compile_definitions(USG_Backend_Core PUBLIC USG_PLATFORM_WASM)
else()
    target_compile_definitions(USG_Backend_Core PUBLIC USG_PLATFORM_DESKTOP)
endif()

if(USG_ENABLE_VALIDATION)
    target_compile_definitions(USG_Backend_Core PUBLIC USG_ENABLE_VALIDATION)
endif()

if(USG_ENABLE_PROFILING)
    target_compile_definitions(USG_Backend_Core PUBLIC USG_ENABLE_PROFILING)
endif()

# WebGPU后端库
if(USG_BUILD_WEBGPU_BACKEND)
    add_library(USG_Backend_WebGPU ${USG_WEBGPU_SOURCES} ${USG_WEBGPU_HEADERS})
    
    target_link_libraries(USG_Backend_WebGPU
        PUBLIC
            USG_Backend_Core
            ${WEBGPU_TARGET}
    )
    
    if(USG_PLATFORM_DESKTOP)
        target_link_libraries(USG_Backend_WebGPU
            PUBLIC
                glfw
                glfw3webgpu
        )
    endif()
    
    target_include_directories(USG_Backend_WebGPU
        PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}/src
    )
    
    target_compile_definitions(USG_Backend_WebGPU
        PUBLIC
            USG_WEBGPU_BACKEND_AVAILABLE
            USG_WEBGPU_LIBRARY_TYPE="${WEBGPU_LIBRARY_TYPE}"
    )

    # 根据WebGPU库类型设置特定的编译定义
    if(WEBGPU_LIBRARY_TYPE STREQUAL "WGPU_NATIVE")
        target_compile_definitions(USG_Backend_WebGPU PUBLIC USG_USE_WGPU_NATIVE)
    elseif(WEBGPU_LIBRARY_TYPE STREQUAL "DAWN")
        target_compile_definitions(USG_Backend_WebGPU PUBLIC USG_USE_DAWN)
    elseif(WEBGPU_LIBRARY_TYPE STREQUAL "EMDAWN")
        target_compile_definitions(USG_Backend_WebGPU PUBLIC USG_USE_EMDAWN)
    elseif(WEBGPU_LIBRARY_TYPE STREQUAL "EMSCRIPTEN")
        target_compile_definitions(USG_Backend_WebGPU PUBLIC USG_USE_EMSCRIPTEN_WEBGPU)
    elseif(WEBGPU_LIBRARY_TYPE STREQUAL "WEBGPU_DISTRIBUTION")
        target_compile_definitions(USG_Backend_WebGPU PUBLIC USG_USE_WEBGPU_DISTRIBUTION)
    endif()
    
    # WebAssembly特定设置
    if(USG_PLATFORM_WASM)
        target_compile_options(USG_Backend_WebGPU PRIVATE
            -sUSE_WEBGPU=1
            -sASYNCIFY
        )
        target_link_options(USG_Backend_WebGPU PRIVATE
            -sUSE_WEBGPU=1
            -sASYNCIFY
            -sALLOW_MEMORY_GROWTH=1
        )
    endif()
endif()

# Vulkan后端库
if(USG_BUILD_VULKAN_BACKEND)
    add_library(USG_Backend_Vulkan ${USG_VULKAN_SOURCES})
    
    target_link_libraries(USG_Backend_Vulkan
        PUBLIC
            USG_Backend_Core
            Vulkan::Vulkan
    )
    
    target_compile_definitions(USG_Backend_Vulkan
        PUBLIC
            USG_VULKAN_BACKEND_AVAILABLE
    )
endif()

# VSG适配器库
if(USG_BUILD_VSG_ADAPTER)
    add_library(USG_Backend_VSG ${USG_VSG_SOURCES} ${USG_VSG_HEADERS})
    
    target_link_libraries(USG_Backend_VSG
        PUBLIC
            USG_Backend_Core
    )
    
    if(USG_BUILD_WEBGPU_BACKEND)
        target_link_libraries(USG_Backend_VSG PUBLIC USG_Backend_WebGPU)
    endif()
    
    if(USG_BUILD_VULKAN_BACKEND)
        target_link_libraries(USG_Backend_VSG PUBLIC USG_Backend_Vulkan)
    endif()
    
    target_include_directories(USG_Backend_VSG
        PUBLIC
            $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
            $<INSTALL_INTERFACE:include>
        PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}/src
    )
    
    target_compile_definitions(USG_Backend_VSG
        PUBLIC
            USG_VSG_ADAPTER_AVAILABLE
    )
    
    # 设置VSG适配器为共享库（用于动态替换）
    set_target_properties(USG_Backend_VSG PROPERTIES
        VERSION ${PROJECT_VERSION}
        SOVERSION ${PROJECT_VERSION_MAJOR}
        PUBLIC_HEADER "${USG_VSG_HEADERS}"
    )
endif()

# 创建总库（包含所有组件）
set(USG_ALL_TARGETS USG_Backend_Core)

if(USG_BUILD_WEBGPU_BACKEND)
    list(APPEND USG_ALL_TARGETS USG_Backend_WebGPU)
endif()

if(USG_BUILD_VULKAN_BACKEND)
    list(APPEND USG_ALL_TARGETS USG_Backend_Vulkan)
endif()

if(USG_BUILD_VSG_ADAPTER)
    list(APPEND USG_ALL_TARGETS USG_Backend_VSG)
endif()

# 示例
if(USG_BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# 测试
if(USG_BUILD_TESTS)
    enable_testing()

    # 创建测试可执行文件
    add_executable(test_webgpu_backend tests/test_webgpu_backend.cpp)

    # 链接库
    target_link_libraries(test_webgpu_backend
        PRIVATE
            USG_Backend_Core
    )

    if(USG_BUILD_WEBGPU_BACKEND)
        target_link_libraries(test_webgpu_backend PRIVATE USG_Backend_WebGPU)
    endif()

    if(USG_BUILD_VSG_ADAPTER)
        target_link_libraries(test_webgpu_backend PRIVATE USG_Backend_VSG)
    endif()

    # 包含目录
    target_include_directories(test_webgpu_backend
        PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}/include
            ${CMAKE_CURRENT_SOURCE_DIR}/src
    )

    # 添加测试
    add_test(NAME WebGPUBackendTest COMMAND test_webgpu_backend)

    # 设置测试属性
    set_tests_properties(WebGPUBackendTest PROPERTIES
        TIMEOUT 60
        LABELS "backend;webgpu"
    )
endif()

# 文档
if(USG_BUILD_DOCS)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        add_subdirectory(docs)
    else()
        message(WARNING "Doxygen not found, documentation will not be built")
    endif()
endif()

# 安装
include(GNUInstallDirs)

# 安装库
foreach(target ${USG_ALL_TARGETS})
    install(TARGETS ${target}
        EXPORT USG_BackendTargets
        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
        ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
        PUBLIC_HEADER DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/USG_Backend
    )
endforeach()

# 安装头文件
install(DIRECTORY include/USG_Backend
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
    FILES_MATCHING PATTERN "*.h"
)

if(USG_BUILD_VSG_ADAPTER)
    install(DIRECTORY include/VSG_Adapter
        DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
        FILES_MATCHING PATTERN "*.h"
    )
endif()

# 导出目标
install(EXPORT USG_BackendTargets
    FILE USG_BackendTargets.cmake
    NAMESPACE USG_Backend::
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/USG_Backend
)

# 配置文件
include(CMakePackageConfigHelpers)

configure_package_config_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/cmake/USG_BackendConfig.cmake.in
    ${CMAKE_CURRENT_BINARY_DIR}/USG_BackendConfig.cmake
    INSTALL_DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/USG_Backend
)

write_basic_package_version_file(
    ${CMAKE_CURRENT_BINARY_DIR}/USG_BackendConfigVersion.cmake
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

install(FILES
    ${CMAKE_CURRENT_BINARY_DIR}/USG_BackendConfig.cmake
    ${CMAKE_CURRENT_BINARY_DIR}/USG_BackendConfigVersion.cmake
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/USG_Backend
)

# 创建符号链接（用于动态替换）
if(USG_BUILD_VSG_ADAPTER AND USG_PLATFORM_DESKTOP)
    install(CODE "
        execute_process(COMMAND ${CMAKE_COMMAND} -E create_symlink
            libUSG_Backend_VSG.so
            \$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/${CMAKE_INSTALL_LIBDIR}/libvsg.so
        )
    ")
endif()

# 打包
set(CPACK_PACKAGE_NAME "USG_Backend")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY ${PROJECT_DESCRIPTION})
set(CPACK_PACKAGE_VENDOR "USG Backend Team")
set(CPACK_RESOURCE_FILE_LICENSE "${CMAKE_CURRENT_SOURCE_DIR}/LICENSE")
set(CPACK_RESOURCE_FILE_README "${CMAKE_CURRENT_SOURCE_DIR}/README.md")

include(CPack)

# 状态报告
message(STATUS "USG Backend Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Platform: ${CMAKE_SYSTEM_NAME}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  WebGPU backend: ${USG_BUILD_WEBGPU_BACKEND}")
message(STATUS "  Vulkan backend: ${USG_BUILD_VULKAN_BACKEND}")
message(STATUS "  OpenGL backend: ${USG_BUILD_OPENGL_BACKEND}")
message(STATUS "  VSG adapter: ${USG_BUILD_VSG_ADAPTER}")
message(STATUS "  OSG adapter: ${USG_BUILD_OSG_ADAPTER}")
message(STATUS "  Examples: ${USG_BUILD_EXAMPLES}")
message(STATUS "  Tests: ${USG_BUILD_TESTS}")
message(STATUS "  Validation: ${USG_ENABLE_VALIDATION}")
message(STATUS "  Profiling: ${USG_ENABLE_PROFILING}")
