cmake_minimum_required(VERSION 3.20)

project(USG_Backend
    VERSION 1.0.0
    DESCRIPTION "USG Backend - 统一渲染后端平台"
    LANGUAGES CXX
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 编译选项
if(MSVC)
    add_compile_options(/utf-8 /W4)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -pedantic)
endif()

# 构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 选项
option(USG_BUILD_WEBGPU_BACKEND "Build WebGPU backend" ON)
option(USG_BUILD_VULKAN_BACKEND "Build Vulkan backend" ON)
option(USG_BUILD_VSG_ADAPTER "Build VSG adapter" ON)
option(USG_BUILD_SCENE_TEST "Build scene test application" ON)
option(USG_USE_WGPU_NATIVE "Use wgpu-native for WebGPU" ON)

# 平台检测
if(EMSCRIPTEN)
    set(USG_PLATFORM_WASM ON)
    message(STATUS "Building for WebAssembly")
else()
    set(USG_PLATFORM_DESKTOP ON)
    message(STATUS "Building for Desktop")
endif()

# 依赖项
include(FetchContent)

# 查找VSG
if(USG_BUILD_VSG_ADAPTER)
    find_path(VSG_INCLUDE_DIR
        NAMES vsg/all.h
        PATHS
            ${CMAKE_CURRENT_SOURCE_DIR}/../osgearth_origin/VulkanSceneGraph/include
            F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/include
            /usr/local/include
            /usr/include
    )

    if(VSG_INCLUDE_DIR)
        message(STATUS "Found VSG headers: ${VSG_INCLUDE_DIR}")
        set(USG_HAS_VSG ON)
    else()
        message(STATUS "VSG not found, will build without VSG adapter")
        set(USG_BUILD_VSG_ADAPTER OFF)
    endif()
endif()

# WebGPU配置
if(USG_BUILD_WEBGPU_BACKEND)
    if(USG_USE_WGPU_NATIVE)
        set(WEBGPU_ROOT_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../webgpu/wgpu-native)
        if(NOT EXISTS ${WEBGPU_ROOT_DIR})
            set(WEBGPU_ROOT_DIR F:/cmo-dev/my_osgearth_web/LearnWebGPU-Code-step100-next2/webgpu/wgpu-native)
        endif()

        if(EXISTS ${WEBGPU_ROOT_DIR})
            message(STATUS "Using wgpu-native from: ${WEBGPU_ROOT_DIR}")
            set(WEBGPU_INCLUDE_DIR ${WEBGPU_ROOT_DIR}/include)
            set(WEBGPU_LIBRARY_DIR ${WEBGPU_ROOT_DIR}/lib)
            set(USG_HAS_WEBGPU ON)
        else()
            message(STATUS "wgpu-native not found, will build without WebGPU backend")
            set(USG_BUILD_WEBGPU_BACKEND OFF)
        endif()
    endif()
endif()

# Vulkan配置
if(USG_BUILD_VULKAN_BACKEND)
    find_package(Vulkan QUIET)
    if(Vulkan_FOUND)
        message(STATUS "Found Vulkan: ${Vulkan_LIBRARIES}")
        set(USG_HAS_VULKAN ON)
    else()
        message(STATUS "Vulkan not found, will build without Vulkan backend")
        set(USG_BUILD_VULKAN_BACKEND OFF)
    endif()
endif()

# GLFW (桌面平台)
if(USG_PLATFORM_DESKTOP)
    FetchContent_Declare(
        glfw
        GIT_REPOSITORY https://github.com/glfw/glfw
        GIT_TAG 3.3.8
    )
    FetchContent_MakeAvailable(glfw)
endif()

# 核心库源文件
set(USG_CORE_SOURCES
    src/Core/BackendTypes.cpp
    src/Core/BackendFactory.cpp
)

set(USG_CORE_HEADERS
    include/USG_Backend/BackendTypes.h
    include/USG_Backend/RenderBackend.h
    include/USG_Backend/BackendFactory.h
)

# WebGPU后端源文件
if(USG_BUILD_WEBGPU_BACKEND)
    set(USG_WEBGPU_SOURCES
        src/WebGPU/WebGPUBackend.cpp
        src/WebGPU/WebGPUAdapter.cpp
        src/WebGPU/WebGPUTypeConverter.cpp
        src/WebGPU/WebGPUShader.cpp
        src/WebGPU/WebGPUPipeline.cpp
        src/WebGPU/WebGPUDescriptorSet.cpp
        src/WebGPU/WebGPUCommandList.cpp
        src/WebGPU/WebGPUSync.cpp
        src/WebGPU/WebGPUDevice.cpp
    )

    set(USG_WEBGPU_HEADERS
        src/WebGPU/WebGPUBackend.h
        src/WebGPU/WebGPUAdapter.h
    )
endif()

# Vulkan后端源文件
if(USG_BUILD_VULKAN_BACKEND)
    set(USG_VULKAN_SOURCES
        src/Vulkan/VulkanBackend.cpp
        src/Vulkan/VulkanDevice.cpp
        src/Vulkan/VulkanBuffer.cpp
        src/Vulkan/VulkanTexture.cpp
        src/Vulkan/VulkanPipeline.cpp
        src/Vulkan/VulkanCommandList.cpp
    )

    set(USG_VULKAN_HEADERS
        src/Vulkan/VulkanBackend.h
    )
endif()

# VSG适配器源文件
if(USG_BUILD_VSG_ADAPTER)
    set(USG_VSG_SOURCES
        src/VSG_Adapter/VSG_Device.cpp
        src/VSG_Adapter/VSG_SceneRenderer.cpp
        src/VSG_Adapter/VSG_BackendBridge.cpp
        src/VSG_Adapter/TypeConverter.cpp
        src/VSG_Adapter/ResourceManager.cpp
    )

    set(USG_VSG_HEADERS
        include/VSG_Adapter/VSG_Device.h
        include/VSG_Adapter/VSG_SceneRenderer.h
        include/VSG_Adapter/VSG_BackendBridge.h
        src/VSG_Adapter/TypeConverter.h
        src/VSG_Adapter/ResourceManager.h
    )
endif()

# 创建核心库
add_library(USG_Backend_Core ${USG_CORE_SOURCES} ${USG_CORE_HEADERS})

# 设置核心库属性
set_target_properties(USG_Backend_Core PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

# 核心库包含目录
target_include_directories(USG_Backend_Core
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# 核心库编译定义
target_compile_definitions(USG_Backend_Core
    PUBLIC
        USG_BACKEND_VERSION_MAJOR=${PROJECT_VERSION_MAJOR}
        USG_BACKEND_VERSION_MINOR=${PROJECT_VERSION_MINOR}
        USG_BACKEND_VERSION_PATCH=${PROJECT_VERSION_PATCH}
)

# 平台定义
if(USG_PLATFORM_WASM)
    target_compile_definitions(USG_Backend_Core PUBLIC USG_PLATFORM_WASM)
else()
    target_compile_definitions(USG_Backend_Core PUBLIC USG_PLATFORM_DESKTOP)
endif()

# WebGPU后端库
if(USG_BUILD_WEBGPU_BACKEND)
    add_library(USG_Backend_WebGPU ${USG_WEBGPU_SOURCES} ${USG_WEBGPU_HEADERS})

    target_link_libraries(USG_Backend_WebGPU
        PUBLIC
            USG_Backend_Core
    )

    target_include_directories(USG_Backend_WebGPU
        PUBLIC
            ${WEBGPU_INCLUDE_DIR}
        PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}/src
    )

    target_compile_definitions(USG_Backend_WebGPU
        PUBLIC
            USG_WEBGPU_BACKEND_AVAILABLE
            USG_USE_WGPU_NATIVE
    )

    # 链接WebGPU库
    if(WIN32)
        find_library(WGPU_LIBRARY
            NAMES wgpu_native
            PATHS ${WEBGPU_LIBRARY_DIR}
            NO_DEFAULT_PATH
        )
        if(WGPU_LIBRARY)
            target_link_libraries(USG_Backend_WebGPU PUBLIC ${WGPU_LIBRARY})
        endif()
    endif()

    # 桌面平台链接GLFW
    if(USG_PLATFORM_DESKTOP)
        target_link_libraries(USG_Backend_WebGPU PUBLIC glfw)
    endif()
endif()

# Vulkan后端库
if(USG_BUILD_VULKAN_BACKEND)
    add_library(USG_Backend_Vulkan ${USG_VULKAN_SOURCES} ${USG_VULKAN_HEADERS})

    target_link_libraries(USG_Backend_Vulkan
        PUBLIC
            USG_Backend_Core
            Vulkan::Vulkan
    )

    target_include_directories(USG_Backend_Vulkan
        PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}/src
    )

    target_compile_definitions(USG_Backend_Vulkan
        PUBLIC
            USG_VULKAN_BACKEND_AVAILABLE
    )
endif()

# VSG适配器库
if(USG_BUILD_VSG_ADAPTER)
    add_library(USG_Backend_VSG ${USG_VSG_SOURCES} ${USG_VSG_HEADERS})

    target_link_libraries(USG_Backend_VSG
        PUBLIC
            USG_Backend_Core
    )

    target_include_directories(USG_Backend_VSG
        PUBLIC
            $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
            ${VSG_INCLUDE_DIR}
        PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}/src
    )

    target_compile_definitions(USG_Backend_VSG
        PUBLIC
            USG_VSG_ADAPTER_AVAILABLE
    )

    # 链接后端库
    if(USG_BUILD_WEBGPU_BACKEND)
        target_link_libraries(USG_Backend_VSG PUBLIC USG_Backend_WebGPU)
    endif()

    if(USG_BUILD_VULKAN_BACKEND)
        target_link_libraries(USG_Backend_VSG PUBLIC USG_Backend_Vulkan)
    endif()
endif()

# 测试程序
add_executable(test_core_backend tests/test_core_simple.cpp)

# 链接库
target_link_libraries(test_core_backend
    PRIVATE
        USG_Backend_Core
)

# 包含目录
target_include_directories(test_core_backend
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/include
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# 场景测试应用程序
if(USG_BUILD_SCENE_TEST)
    add_executable(scene_test_app
        examples/scene_test/main.cpp
        examples/scene_test/SceneTestApp.cpp
        examples/scene_test/SimpleScene.cpp
        examples/scene_test/BackendSwitcher.cpp
    )

    # 链接核心库
    target_link_libraries(scene_test_app
        PRIVATE
            USG_Backend_Core
    )

    # 链接后端库
    if(USG_BUILD_WEBGPU_BACKEND)
        target_link_libraries(scene_test_app PRIVATE USG_Backend_WebGPU)
    endif()

    if(USG_BUILD_VULKAN_BACKEND)
        target_link_libraries(scene_test_app PRIVATE USG_Backend_Vulkan)
    endif()

    if(USG_BUILD_VSG_ADAPTER)
        target_link_libraries(scene_test_app PRIVATE USG_Backend_VSG)
    endif()

    # 包含目录
    target_include_directories(scene_test_app
        PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}/include
            ${CMAKE_CURRENT_SOURCE_DIR}/src
            ${CMAKE_CURRENT_SOURCE_DIR}/examples/scene_test
    )

    # 桌面平台链接GLFW
    if(USG_PLATFORM_DESKTOP)
        target_link_libraries(scene_test_app PRIVATE glfw)
    endif()

    # 编译定义
    target_compile_definitions(scene_test_app PRIVATE
        USG_SCENE_TEST_APP
    )

    if(USG_BUILD_WEBGPU_BACKEND)
        target_compile_definitions(scene_test_app PRIVATE USG_HAS_WEBGPU)
    endif()

    if(USG_BUILD_VULKAN_BACKEND)
        target_compile_definitions(scene_test_app PRIVATE USG_HAS_VULKAN)
    endif()

    if(USG_BUILD_VSG_ADAPTER)
        target_compile_definitions(scene_test_app PRIVATE USG_HAS_VSG)
    endif()
endif()

# 状态报告
message(STATUS "USG Backend Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Platform: ${CMAKE_SYSTEM_NAME}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  WebGPU backend: ${USG_BUILD_WEBGPU_BACKEND}")
message(STATUS "  Vulkan backend: ${USG_BUILD_VULKAN_BACKEND}")
message(STATUS "  VSG adapter: ${USG_BUILD_VSG_ADAPTER}")
message(STATUS "  Scene test app: ${USG_BUILD_SCENE_TEST}")

if(USG_HAS_VSG)
    message(STATUS "  VSG include dir: ${VSG_INCLUDE_DIR}")
endif()

if(USG_HAS_WEBGPU)
    message(STATUS "  WebGPU root dir: ${WEBGPU_ROOT_DIR}")
endif()

if(USG_HAS_VULKAN)
    message(STATUS "  Vulkan found: ${Vulkan_FOUND}")
endif()
