#include "WebGPUAdapter.h"
#include <iostream>
#include <vector>
#include <cassert>

#ifdef USG_PLATFORM_DESKTOP
    #include <GLFW/glfw3.h>
    #ifdef USG_USE_WGPU_NATIVE
        #include <glfw3webgpu.h>
    #endif
#endif

#ifdef USG_PLATFORM_WASM
    #include <emscripten.h>
    #include <emscripten/html5.h>
#endif

namespace USG {

// 静态成员初始化
bool WebGPUAdapter::_initialized = false;
std::function<void(WGPUErrorType, const char*)> WebGPUAdapter::_errorCallback;

bool WebGPUAdapter::initialize() {
    if (_initialized) {
        return true;
    }
    
    std::cout << "[WebGPUAdapter] Initializing " << getImplementationName() << std::endl;
    
    bool success = false;
    
#if defined(USG_USE_WGPU_NATIVE)
    success = initializeWGPUNative();
#elif defined(USG_USE_DAWN)
    success = initializeDawn();
#elif defined(USG_USE_EMDAWN)
    success = initializeEmDawn();
#elif defined(USG_USE_EMSCRIPTEN_WEBGPU)
    success = initializeEmscripten();
#elif defined(USG_USE_WEBGPU_DISTRIBUTION)
    success = initializeWebGPUDistribution();
#else
    std::cerr << "[WebGPUAdapter] No WebGPU implementation selected" << std::endl;
    return false;
#endif
    
    if (success) {
        _initialized = true;
        std::cout << "[WebGPUAdapter] Successfully initialized " << getImplementationName() << std::endl;
    } else {
        std::cerr << "[WebGPUAdapter] Failed to initialize " << getImplementationName() << std::endl;
    }
    
    return success;
}

void WebGPUAdapter::cleanup() {
    if (!_initialized) {
        return;
    }
    
    std::cout << "[WebGPUAdapter] Cleaning up " << getImplementationName() << std::endl;
    
#if defined(USG_USE_DAWN)
    // Dawn特定清理
    dawnProcSetProcs(nullptr);
#endif
    
    _initialized = false;
    _errorCallback = nullptr;
}

WGPUInstance WebGPUAdapter::createInstance() {
    if (!_initialized) {
        std::cerr << "[WebGPUAdapter] Not initialized" << std::endl;
        return nullptr;
    }
    
    WGPUInstanceDescriptor instanceDesc = {};
    instanceDesc.nextInChain = nullptr;
    
#if defined(USG_USE_WGPU_NATIVE)
    return wgpuCreateInstance(&instanceDesc);
#elif defined(USG_USE_DAWN)
    return wgpuCreateInstance(&instanceDesc);
#elif defined(USG_USE_EMDAWN) || defined(USG_USE_WEBGPU_DISTRIBUTION)
    return wgpu::createInstance(instanceDesc);
#elif defined(USG_USE_EMSCRIPTEN_WEBGPU)
    // Emscripten使用全局实例
    return emscripten_webgpu_get_instance();
#else
    return nullptr;
#endif
}

void WebGPUAdapter::requestAdapter(WGPUInstance instance, 
                                  const WGPURequestAdapterOptions* options,
                                  std::function<void(WGPUAdapter)> callback) {
    if (!instance) {
        std::cerr << "[WebGPUAdapter] Invalid instance" << std::endl;
        callback(nullptr);
        return;
    }
    
    WGPURequestAdapterOptions defaultOptions = {};
    if (!options) {
        defaultOptions.powerPreference = WGPUPowerPreference_HighPerformance;
        options = &defaultOptions;
    }
    
#if defined(USG_USE_WGPU_NATIVE) || defined(USG_USE_DAWN)
    // 使用C API的异步请求
    struct CallbackData {
        std::function<void(WGPUAdapter)> callback;
    };
    
    auto* callbackData = new CallbackData{callback};
    
    wgpuInstanceRequestAdapter(instance, options,
        [](WGPURequestAdapterStatus status, WGPUAdapter adapter, char const* message, void* userdata) {
            auto* data = static_cast<CallbackData*>(userdata);
            if (status == WGPURequestAdapterStatus_Success) {
                data->callback(adapter);
            } else {
                std::cerr << "[WebGPUAdapter] Failed to request adapter: " << (message ? message : "Unknown error") << std::endl;
                data->callback(nullptr);
            }
            delete data;
        }, callbackData);
        
#elif defined(USG_USE_EMDAWN) || defined(USG_USE_WEBGPU_DISTRIBUTION)
    // 使用C++ API
    instance.requestAdapter(*options, [callback](wgpu::RequestAdapterStatus status, wgpu::Adapter adapter, char const* message) {
        if (status == wgpu::RequestAdapterStatus::Success) {
            callback(adapter);
        } else {
            std::cerr << "[WebGPUAdapter] Failed to request adapter: " << (message ? message : "Unknown error") << std::endl;
            callback(nullptr);
        }
    });
    
#elif defined(USG_USE_EMSCRIPTEN_WEBGPU)
    // Emscripten特定实现
    emscripten_webgpu_request_adapter_async(instance, options,
        [](WGPURequestAdapterStatus status, WGPUAdapter adapter, char const* message, void* userdata) {
            auto* cb = static_cast<std::function<void(WGPUAdapter)>*>(userdata);
            if (status == WGPURequestAdapterStatus_Success) {
                (*cb)(adapter);
            } else {
                std::cerr << "[WebGPUAdapter] Failed to request adapter: " << (message ? message : "Unknown error") << std::endl;
                (*cb)(nullptr);
            }
            delete cb;
        }, new std::function<void(WGPUAdapter)>(callback));
#endif
}

void WebGPUAdapter::requestDevice(WGPUAdapter adapter,
                                 const WGPUDeviceDescriptor* descriptor,
                                 std::function<void(WGPUDevice)> callback) {
    if (!adapter) {
        std::cerr << "[WebGPUAdapter] Invalid adapter" << std::endl;
        callback(nullptr);
        return;
    }
    
    WGPUDeviceDescriptor defaultDescriptor = {};
    if (!descriptor) {
        defaultDescriptor.nextInChain = nullptr;
        defaultDescriptor.label = "USG WebGPU Device";
        defaultDescriptor.requiredFeaturesCount = 0;
        defaultDescriptor.requiredFeatures = nullptr;
        defaultDescriptor.requiredLimits = nullptr;
        defaultDescriptor.defaultQueue.nextInChain = nullptr;
        defaultDescriptor.defaultQueue.label = "USG Default Queue";
        descriptor = &defaultDescriptor;
    }
    
#if defined(USG_USE_WGPU_NATIVE) || defined(USG_USE_DAWN)
    struct CallbackData {
        std::function<void(WGPUDevice)> callback;
    };
    
    auto* callbackData = new CallbackData{callback};
    
    wgpuAdapterRequestDevice(adapter, descriptor,
        [](WGPURequestDeviceStatus status, WGPUDevice device, char const* message, void* userdata) {
            auto* data = static_cast<CallbackData*>(userdata);
            if (status == WGPURequestDeviceStatus_Success) {
                data->callback(device);
            } else {
                std::cerr << "[WebGPUAdapter] Failed to request device: " << (message ? message : "Unknown error") << std::endl;
                data->callback(nullptr);
            }
            delete data;
        }, callbackData);
        
#elif defined(USG_USE_EMDAWN) || defined(USG_USE_WEBGPU_DISTRIBUTION)
    adapter.requestDevice(*descriptor, [callback](wgpu::RequestDeviceStatus status, wgpu::Device device, char const* message) {
        if (status == wgpu::RequestDeviceStatus::Success) {
            callback(device);
        } else {
            std::cerr << "[WebGPUAdapter] Failed to request device: " << (message ? message : "Unknown error") << std::endl;
            callback(nullptr);
        }
    });
    
#elif defined(USG_USE_EMSCRIPTEN_WEBGPU)
    emscripten_webgpu_request_device_async(adapter, descriptor,
        [](WGPURequestDeviceStatus status, WGPUDevice device, char const* message, void* userdata) {
            auto* cb = static_cast<std::function<void(WGPUDevice)>*>(userdata);
            if (status == WGPURequestDeviceStatus_Success) {
                (*cb)(device);
            } else {
                std::cerr << "[WebGPUAdapter] Failed to request device: " << (message ? message : "Unknown error") << std::endl;
                (*cb)(nullptr);
            }
            delete cb;
        }, new std::function<void(WGPUDevice)>(callback));
#endif
}

void WebGPUAdapter::setErrorCallback(std::function<void(WGPUErrorType, const char*)> callback) {
    _errorCallback = callback;
}

void WebGPUAdapter::processEvents() {
#if defined(USG_USE_WGPU_NATIVE)
    // wgpu-native可能需要轮询事件
    // 具体实现取决于版本
#elif defined(USG_USE_DAWN)
    // Dawn通常不需要显式事件处理
#elif defined(USG_USE_EMSCRIPTEN_WEBGPU)
    // Emscripten自动处理事件
#endif
}

bool WebGPUAdapter::isAvailable() {
#if defined(USG_PLATFORM_WASM)
    // 在WebAssembly中检查WebGPU支持
    return EM_ASM_INT({
        return typeof navigator !== 'undefined' && 
               typeof navigator.gpu !== 'undefined';
    });
#else
    // 在桌面平台，如果能初始化就认为可用
    return _initialized || initialize();
#endif
}

std::vector<std::string> WebGPUAdapter::getSupportedFeatures() {
    std::vector<std::string> features;
    
    // 基础特性
    features.push_back("basic-rendering");
    features.push_back("vertex-buffers");
    features.push_back("index-buffers");
    features.push_back("textures");
    features.push_back("samplers");
    
    // 根据实现添加特定特性
#if defined(USG_USE_WGPU_NATIVE) || defined(USG_USE_DAWN)
    features.push_back("compute-shaders");
    features.push_back("timestamp-queries");
    features.push_back("pipeline-statistics");
    features.push_back("texture-compression-bc");
#endif
    
    return features;
}

// 实现特定的初始化函数
bool WebGPUAdapter::initializeWGPUNative() {
#if defined(USG_USE_WGPU_NATIVE)
    std::cout << "[WebGPUAdapter] Initializing wgpu-native" << std::endl;
    // wgpu-native通常不需要特殊初始化
    return true;
#else
    return false;
#endif
}

bool WebGPUAdapter::initializeDawn() {
#if defined(USG_USE_DAWN)
    std::cout << "[WebGPUAdapter] Initializing Dawn" << std::endl;
    // 设置Dawn的过程表
    DawnProcTable procs = dawn::native::GetProcs();
    dawnProcSetProcs(&procs);
    return true;
#else
    return false;
#endif
}

bool WebGPUAdapter::initializeEmDawn() {
#if defined(USG_USE_EMDAWN)
    std::cout << "[WebGPUAdapter] Initializing EmDawn" << std::endl;
    // EmDawn通常不需要特殊初始化
    return true;
#else
    return false;
#endif
}

bool WebGPUAdapter::initializeEmscripten() {
#if defined(USG_USE_EMSCRIPTEN_WEBGPU)
    std::cout << "[WebGPUAdapter] Initializing Emscripten WebGPU" << std::endl;
    // 检查浏览器WebGPU支持
    bool supported = EM_ASM_INT({
        return typeof navigator !== 'undefined' && 
               typeof navigator.gpu !== 'undefined';
    });
    
    if (!supported) {
        std::cerr << "[WebGPUAdapter] WebGPU not supported in this browser" << std::endl;
        return false;
    }
    
    return true;
#else
    return false;
#endif
}

bool WebGPUAdapter::initializeWebGPUDistribution() {
#if defined(USG_USE_WEBGPU_DISTRIBUTION)
    std::cout << "[WebGPUAdapter] Initializing WebGPU-distribution" << std::endl;
    // WebGPU-distribution通常不需要特殊初始化
    return true;
#else
    return false;
#endif
}

} // namespace USG
