/**
 * @file main.cpp
 * @brief VSG地球渲染示例 - 使用USG Backend WebGPU后端
 * 
 * 这个示例展示了如何在现有VSG应用中无缝使用USG Backend，
 * 实现零侵入的WebGPU后端替换。
 */

// 标准VSG头文件 - 完全不需要修改
#include <vsg/all.h>
#include <iostream>
#include <chrono>

// USG Backend头文件 - 仅用于配置和调试
#include <USG_Backend/BackendFactory.h>
#include <VSG_Adapter/VSG_Device.h>

/**
 * @brief 地球节点类
 * 
 * 标准的VSG节点，展示地球自转动画
 */
class EarthNode : public vsg::Transform {
public:
    EarthNode() {
        setName("Earth");
        
        // 创建地球几何体
        createEarthGeometry();
        
        // 设置初始变换
        setMatrix(vsg::translate(0.0f, 0.0f, 0.0f));
    }
    
    /**
     * @brief 更新地球自转
     * @param deltaTime 时间增量
     */
    void updateRotation(double deltaTime) {
        static double totalTime = 0.0;
        totalTime += deltaTime;
        
        // 地球自转（加速显示）
        float rotationAngle = static_cast<float>(totalTime * 0.5);
        auto rotation = vsg::rotate(rotationAngle, vsg::vec3(0.0f, 1.0f, 0.0f));
        setMatrix(rotation);
    }
    
private:
    void createEarthGeometry() {
        // 创建球体几何体
        auto sphere = vsg::Sphere::create();
        sphere->radius = 1.0f;
        sphere->center = vsg::vec3(0.0f, 0.0f, 0.0f);
        
        // 创建几何体节点
        auto geometry = vsg::Geometry::create();
        geometry->assignArrays({sphere->vertices, sphere->normals, sphere->texcoords});
        geometry->assignIndices(sphere->indices);
        
        // 创建材质
        auto material = vsg::PhongMaterial::create();
        material->diffuse = vsg::vec4(0.2f, 0.6f, 1.0f, 1.0f);
        material->specular = vsg::vec4(0.8f, 0.8f, 0.8f, 1.0f);
        material->shininess = 32.0f;
        
        // 创建状态组
        auto stateGroup = vsg::StateGroup::create();
        stateGroup->add(material);
        stateGroup->addChild(geometry);
        
        addChild(stateGroup);
    }
};

/**
 * @brief 应用程序类
 */
class EarthApplication {
public:
    EarthApplication() = default;
    ~EarthApplication() = cleanup();
    
    /**
     * @brief 初始化应用程序
     * @return 是否成功
     */
    bool initialize() {
        std::cout << "=== USG Backend VSG地球渲染示例 ===" << std::endl;
        
        // 检查USG Backend可用性
        if (!checkUSGBackend()) {
            return false;
        }
        
        // 初始化VSG（内部会使用USG Backend）
        if (!initializeVSG()) {
            return false;
        }
        
        // 创建场景
        if (!createScene()) {
            return false;
        }
        
        // 创建查看器
        if (!createViewer()) {
            return false;
        }
        
        std::cout << "应用程序初始化成功！" << std::endl;
        return true;
    }
    
    /**
     * @brief 运行应用程序
     */
    void run() {
        std::cout << "开始渲染循环..." << std::endl;
        
        auto lastTime = std::chrono::high_resolution_clock::now();
        
        // 标准VSG渲染循环 - 完全不需要修改
        while (_viewer->advanceToNextFrame()) {
            // 计算时间增量
            auto currentTime = std::chrono::high_resolution_clock::now();
            auto deltaTime = std::chrono::duration<double>(currentTime - lastTime).count();
            lastTime = currentTime;
            
            // 更新地球自转
            if (_earthNode) {
                _earthNode->updateRotation(deltaTime);
            }
            
            // 标准VSG渲染流程
            _viewer->handleEvents();
            _viewer->update();
            _viewer->recordAndSubmit();
            _viewer->present();
            
            // 性能统计
            updatePerformanceStats(deltaTime);
        }
        
        std::cout << "渲染循环结束" << std::endl;
    }
    
private:
    bool checkUSGBackend() {
        std::cout << "检查USG Backend可用性..." << std::endl;
        
        // 检查WebGPU后端是否可用
        if (!USG::Backend::isAvailable(USG::BackendType::WebGPU)) {
            std::cerr << "错误: WebGPU后端不可用" << std::endl;
            return false;
        }
        
        std::cout << "✓ WebGPU后端可用" << std::endl;
        
        // 列出所有可用后端
        auto availableBackends = USG::Backend::getAvailable();
        std::cout << "可用后端: ";
        for (auto backend : availableBackends) {
            switch (backend) {
                case USG::BackendType::WebGPU:
                    std::cout << "WebGPU ";
                    break;
                case USG::BackendType::Vulkan:
                    std::cout << "Vulkan ";
                    break;
                case USG::BackendType::OpenGL:
                    std::cout << "OpenGL ";
                    break;
                default:
                    break;
            }
        }
        std::cout << std::endl;
        
        return true;
    }
    
    bool initializeVSG() {
        std::cout << "初始化VSG..." << std::endl;
        
        // 初始化USG Backend（可选，也可以通过环境变量或动态链接自动初始化）
        if (!USG::Backend::initialize(USG::BackendType::WebGPU)) {
            std::cerr << "错误: 无法初始化USG Backend" << std::endl;
            return false;
        }
        
        std::cout << "✓ USG Backend初始化成功" << std::endl;
        
        // 创建VSG设备（内部会使用USG Backend）
        _device = vsg::Device::create();
        if (!_device) {
            std::cerr << "错误: 无法创建VSG设备" << std::endl;
            return false;
        }
        
        // 检查是否使用了USG Backend
        if (auto* usgDevice = dynamic_cast<USG::VSG_Device*>(_device.get())) {
            std::cout << "✓ 使用USG Backend: " << usgDevice->getBackend()->getBackendName() << std::endl;
            usgDevice->setDebugEnabled(true);
        } else {
            std::cout << "⚠ 使用原生VSG后端" << std::endl;
        }
        
        return true;
    }
    
    bool createScene() {
        std::cout << "创建场景..." << std::endl;
        
        // 创建根节点
        _root = vsg::Group::create();
        _root->setName("Root");
        
        // 创建地球节点
        _earthNode = vsg::ref_ptr<EarthNode>(new EarthNode());
        
        // 添加光照
        auto light = vsg::DirectionalLight::create();
        light->direction = vsg::vec3(0.0f, -1.0f, -1.0f);
        light->color = vsg::vec3(1.0f, 1.0f, 1.0f);
        light->intensity = 1.0f;
        
        auto lightGroup = vsg::StateGroup::create();
        lightGroup->add(light);
        lightGroup->addChild(_earthNode);
        
        _root->addChild(lightGroup);
        
        std::cout << "✓ 场景创建成功，包含 " << _root->getNumChildren() << " 个子节点" << std::endl;
        return true;
    }
    
    bool createViewer() {
        std::cout << "创建查看器..." << std::endl;
        
        // 创建窗口特征
        auto windowTraits = vsg::WindowTraits::create();
        windowTraits->windowTitle = "USG Backend VSG地球渲染示例";
        windowTraits->width = 1024;
        windowTraits->height = 768;
        windowTraits->decoration = true;
        
        // 创建查看器
        _viewer = vsg::Viewer::create();
        
        // 创建窗口
        auto window = vsg::Window::create(windowTraits);
        if (!window) {
            std::cerr << "错误: 无法创建窗口" << std::endl;
            return false;
        }
        
        _viewer->addWindow(window);
        
        // 设置相机
        auto camera = vsg::Camera::create();
        auto perspective = vsg::Perspective::create();
        perspective->fieldOfViewY = vsg::radians(45.0);
        perspective->aspectRatio = static_cast<double>(windowTraits->width) / windowTraits->height;
        perspective->nearDistance = 0.1;
        perspective->farDistance = 100.0;
        camera->projectionMatrix = perspective;
        
        auto lookAt = vsg::LookAt::create();
        lookAt->eye = vsg::vec3(0.0f, 0.0f, 3.0f);
        lookAt->center = vsg::vec3(0.0f, 0.0f, 0.0f);
        lookAt->up = vsg::vec3(0.0f, 1.0f, 0.0f);
        camera->viewMatrix = lookAt;
        
        // 创建渲染图
        auto renderGraph = vsg::RenderGraph::create(window);
        renderGraph->camera = camera;
        renderGraph->addChild(_root);
        
        auto commandGraph = vsg::CommandGraph::create(window);
        commandGraph->addChild(renderGraph);
        
        _viewer->assignRecordAndSubmitTaskAndPresentation({commandGraph});
        
        // 编译场景图
        _viewer->compile();
        
        std::cout << "✓ 查看器创建成功" << std::endl;
        return true;
    }
    
    void updatePerformanceStats(double deltaTime) {
        static double totalTime = 0.0;
        static int frameCount = 0;
        static double lastReportTime = 0.0;
        
        totalTime += deltaTime;
        frameCount++;
        
        // 每秒报告一次性能统计
        if (totalTime - lastReportTime >= 1.0) {
            double fps = frameCount / (totalTime - lastReportTime);
            std::cout << "FPS: " << std::fixed << std::setprecision(1) << fps 
                      << ", 帧时间: " << std::setprecision(2) << (deltaTime * 1000.0) << "ms" << std::endl;
            
            frameCount = 0;
            lastReportTime = totalTime;
        }
    }
    
    void cleanup() {
        std::cout << "清理应用程序..." << std::endl;
        
        // 清理VSG资源
        _viewer.reset();
        _root.reset();
        _earthNode.reset();
        _device.reset();
        
        // 清理USG Backend
        USG::Backend::cleanup();
        
        std::cout << "✓ 应用程序清理完成" << std::endl;
    }
    
private:
    // VSG对象
    vsg::ref_ptr<vsg::Device> _device;
    vsg::ref_ptr<vsg::Viewer> _viewer;
    vsg::ref_ptr<vsg::Group> _root;
    vsg::ref_ptr<EarthNode> _earthNode;
};

/**
 * @brief 主函数
 */
int main(int argc, char* argv[]) {
    try {
        // 解析命令行参数
        bool enableDebug = false;
        for (int i = 1; i < argc; ++i) {
            if (std::string(argv[i]) == "--debug") {
                enableDebug = true;
            }
        }
        
        // 设置调试模式
        if (enableDebug) {
            std::cout << "启用调试模式" << std::endl;
            // 可以设置环境变量或其他调试选项
        }
        
        // 创建并运行应用程序
        EarthApplication app;
        
        if (!app.initialize()) {
            std::cerr << "应用程序初始化失败" << std::endl;
            return -1;
        }
        
        app.run();
        
        std::cout << "应用程序正常退出" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "异常: " << e.what() << std::endl;
        return -1;
    } catch (...) {
        std::cerr << "未知异常" << std::endl;
        return -1;
    }
}

/**
 * 编译和运行说明:
 * 
 * 1. 编译:
 *    mkdir build && cd build
 *    cmake .. -DUSG_BUILD_VSG_ADAPTER=ON -DUSG_BUILD_WEBGPU_BACKEND=ON
 *    make -j8
 * 
 * 2. 运行:
 *    ./vsg_earth
 *    或
 *    ./vsg_earth --debug  # 启用调试模式
 * 
 * 3. 动态替换运行:
 *    export LD_PRELOAD=/path/to/libUSG_Backend_VSG.so
 *    ./vsg_earth
 * 
 * 4. 预期输出:
 *    - 显示一个旋转的蓝色地球
 *    - 控制台输出USG Backend相关信息
 *    - 实时FPS和性能统计
 * 
 * 5. 验证WebGPU后端:
 *    - 检查控制台输出中的"使用USG Backend: WebGPU"
 *    - 观察渲染性能和质量
 *    - 确认跨平台兼容性
 */
