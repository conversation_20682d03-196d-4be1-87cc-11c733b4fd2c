/**
 * @file main.cpp
 * @brief USG Backend场景测试应用程序主入口
 * 
 * 演示如何使用USG Backend框架在不同渲染后端之间切换，
 * 同时保持相同的场景内容和渲染效果。
 */

#include "SceneTestApp.h"
#include <USG_Backend/BackendFactory.h>
#include <iostream>
#include <cstdlib>

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif

using namespace USG;

/**
 * @brief 设置控制台UTF-8支持
 */
void setupConsole() {
#ifdef _WIN32
    // 设置控制台代码页为UTF-8
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
    
    // 启用ANSI转义序列支持
    HANDLE hOut = GetStdHandle(STD_OUTPUT_HANDLE);
    if (hOut != INVALID_HANDLE_VALUE) {
        DWORD dwMode = 0;
        if (GetConsoleMode(hOut, &dwMode)) {
            dwMode |= ENABLE_VIRTUAL_TERMINAL_PROCESSING;
            SetConsoleMode(hOut, dwMode);
        }
    }
#endif
}

/**
 * @brief 打印应用程序信息
 */
void printAppInfo() {
    std::cout << "========================================" << std::endl;
    std::cout << "USG Backend 场景测试应用程序" << std::endl;
    std::cout << "版本: 1.0.0" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << std::endl;
    
    std::cout << "功能特性:" << std::endl;
    std::cout << "• 支持多种渲染后端 (WebGPU, Vulkan, OpenGL)" << std::endl;
    std::cout << "• 运行时后端切换" << std::endl;
    std::cout << "• VSG场景图集成" << std::endl;
    std::cout << "• 实时性能监控" << std::endl;
    std::cout << "• 交互式场景控制" << std::endl;
    std::cout << std::endl;
    
    std::cout << "控制说明:" << std::endl;
    std::cout << "• 数字键 1-3: 切换渲染后端" << std::endl;
    std::cout << "• 空格键: 暂停/恢复场景动画" << std::endl;
    std::cout << "• F1: 显示/隐藏性能统计" << std::endl;
    std::cout << "• F2: 显示/隐藏后端选择器" << std::endl;
    std::cout << "• ESC: 退出应用程序" << std::endl;
    std::cout << std::endl;
}

/**
 * @brief 检查系统要求
 * @return 是否满足要求
 */
bool checkSystemRequirements() {
    std::cout << "检查系统要求..." << std::endl;
    
    auto& factory = BackendFactory::getInstance();
    
    // 检查可用后端
    auto availableBackends = factory.getAvailableBackends();
    if (availableBackends.empty()) {
        std::cerr << "错误: 没有找到可用的渲染后端" << std::endl;
        return false;
    }
    
    std::cout << "找到 " << availableBackends.size() << " 个可用后端:" << std::endl;
    for (auto backend : availableBackends) {
        std::string name;
        switch (backend) {
            case BackendType::WebGPU: name = "WebGPU"; break;
            case BackendType::Vulkan: name = "Vulkan"; break;
            case BackendType::OpenGL: name = "OpenGL"; break;
            default: name = "Unknown"; break;
        }
        std::cout << "  • " << name << std::endl;
    }
    
    std::cout << "系统要求检查完成" << std::endl;
    std::cout << std::endl;
    
    return true;
}

/**
 * @brief 解析命令行参数
 * @param argc 参数数量
 * @param argv 参数数组
 * @param config 应用程序配置
 */
void parseCommandLine(int argc, char* argv[], SceneTestApp::AppConfig& config) {
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "--help" || arg == "-h") {
            std::cout << "用法: " << argv[0] << " [选项]" << std::endl;
            std::cout << std::endl;
            std::cout << "选项:" << std::endl;
            std::cout << "  --backend <name>    指定默认后端 (webgpu, vulkan, opengl)" << std::endl;
            std::cout << "  --width <pixels>    设置窗口宽度" << std::endl;
            std::cout << "  --height <pixels>   设置窗口高度" << std::endl;
            std::cout << "  --fullscreen        全屏模式" << std::endl;
            std::cout << "  --no-vsync          禁用垂直同步" << std::endl;
            std::cout << "  --no-rotation       禁用场景旋转" << std::endl;
            std::cout << "  --help, -h          显示此帮助信息" << std::endl;
            std::exit(0);
        }
        else if (arg == "--backend" && i + 1 < argc) {
            std::string backendName = argv[++i];
            if (backendName == "webgpu") {
                config.defaultBackend = BackendType::WebGPU;
            } else if (backendName == "vulkan") {
                config.defaultBackend = BackendType::Vulkan;
            } else if (backendName == "opengl") {
                config.defaultBackend = BackendType::OpenGL;
            } else {
                std::cerr << "警告: 未知的后端类型 '" << backendName << "'" << std::endl;
            }
        }
        else if (arg == "--width" && i + 1 < argc) {
            config.windowWidth = std::atoi(argv[++i]);
        }
        else if (arg == "--height" && i + 1 < argc) {
            config.windowHeight = std::atoi(argv[++i]);
        }
        else if (arg == "--fullscreen") {
            config.fullscreen = true;
        }
        else if (arg == "--no-vsync") {
            config.enableVSync = false;
        }
        else if (arg == "--no-rotation") {
            config.enableRotation = false;
        }
        else {
            std::cerr << "警告: 未知的命令行参数 '" << arg << "'" << std::endl;
        }
    }
}

/**
 * @brief 主函数
 * @param argc 命令行参数数量
 * @param argv 命令行参数数组
 * @return 程序退出代码
 */
int main(int argc, char* argv[]) {
    // 设置控制台
    setupConsole();
    
    // 打印应用程序信息
    printAppInfo();
    
    // 检查系统要求
    if (!checkSystemRequirements()) {
        std::cerr << "系统要求检查失败，程序退出" << std::endl;
        return -1;
    }
    
    // 解析命令行参数
    SceneTestApp::AppConfig config;
    parseCommandLine(argc, argv, config);
    
    // 创建应用程序
    SceneTestApp app;
    
    // 设置错误回调
    app.setErrorCallback([](const std::string& error) {
        std::cerr << "应用程序错误: " << error << std::endl;
    });
    
    try {
        // 初始化应用程序
        std::cout << "初始化应用程序..." << std::endl;
        if (!app.initialize(config)) {
            std::cerr << "应用程序初始化失败" << std::endl;
            return -1;
        }
        
        std::cout << "应用程序初始化成功" << std::endl;
        std::cout << "启动主循环..." << std::endl;
        std::cout << std::endl;
        
        // 运行应用程序
        int exitCode = app.run();
        
        std::cout << std::endl;
        std::cout << "应用程序正常退出，退出代码: " << exitCode << std::endl;
        
        return exitCode;
    }
    catch (const std::exception& e) {
        std::cerr << "应用程序异常: " << e.what() << std::endl;
        return -1;
    }
    catch (...) {
        std::cerr << "应用程序发生未知异常" << std::endl;
        return -1;
    }
}
