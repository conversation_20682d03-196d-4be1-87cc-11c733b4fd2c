<?xml version='1.0' encoding='utf-8' ?>
<!DOCTYPE bookinfo PUBLIC "-//OASIS//DTD DocBook XML V4.5//EN" "http://www.oasis-open.org/docbook/xml/4.5/docbookx.dtd" [
<!ENTITY % BOOK_ENTITIES SYSTEM "Wayland.ent">
%BOOK_ENTITIES;
]>
<bookinfo id="book-Wayland-Wayland">
  <title>Wayland</title>
  <subtitle>The Wayland Protocol</subtitle>
  <productname>Documentation</productname>
  <productnumber>0.1</productnumber>
  <edition>1</edition>
  <pubsnumber>0</pubsnumber>
  <abstract>
    <para>
      Wayland is a protocol for a compositor to talk to
      its clients as well as a C library implementation of
      that protocol. The compositor can be a standalone
      display server running on Linux kernel modesetting
      and evdev input devices, an X application, or a
      Wayland client itself. The clients can be
      traditional applications, X servers (rootless or
      fullscreen) or other display servers.
    </para>
  </abstract>
  <corpauthor>
    <inlinemediaobject>
      <imageobject>
	<imagedata fileref="images/wayland.png" format="PNG" />
      </imageobject>
      <textobject>
	<phrase>
	  Wayland logo
	</phrase>
      </textobject>
    </inlinemediaobject>
  </corpauthor>

  <legalnotice lang="en-US">
    <para>
      Copyright <trademark class="copyright"></trademark> &YEAR; &HOLDER;
    </para>

	<para>
	  Permission is hereby granted, free of charge, to any person obtaining a
	  copy of this software and associated documentation files (the "Software"),
	  to deal in the Software without restriction, including without limitation
	  the rights to use, copy, modify, merge, publish, distribute, sublicense,
	  and/or sell copies of the Software, and to permit persons to whom the
	  Software is furnished to do so, subject to the following conditions:
	</para>

	<para>
	  The above copyright notice and this permission notice (including the next
	  paragraph) shall be included in all copies or substantial portions of the
	  Software.
	</para>

	<para>
	  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
	  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
	  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
	  THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
	  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
	  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
	  DEALINGS IN THE SOFTWARE.
	</para>
  </legalnotice>


  <xi:include href="Author_Group.xml" xmlns:xi="http://www.w3.org/2001/XInclude" />
</bookinfo>
