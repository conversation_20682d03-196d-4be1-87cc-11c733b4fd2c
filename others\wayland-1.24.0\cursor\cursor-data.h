/*
* Copyright 1999 SuSE, Inc.
*
* Permission is hereby granted, free of charge, to any person obtaining
* a copy of this software and associated documentation files (the
* "Software"), to deal in the Software without restriction, including
* without limitation the rights to use, copy, modify, merge, publish,
* distribute, sublicense, and/or sell copies of the Software, and to
* permit persons to whom the Software is furnished to do so, subject to
* the following conditions:
*
* The above copyright notice and this permission notice (including the
* next paragraph) shall be included in all copies or substantial
* portions of the Software.
*
* THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
* MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
* NONINFRINGEMENT.  IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
* BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
* ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
* CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
* SOFTWARE.
*
* Author:  <PERSON>, <PERSON><PERSON>, Inc.
*/

#include <stdint.h>

static uint32_t cursor_data[] = {
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0xffffffff, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0xffffffff, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0xff000000,
	0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff,
	0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000,
	0x00000000, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0xffffffff, 0xff000000, 0xffffffff, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0xff000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0x00000000, 0xffffffff,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0x00000000,
	0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000,
	0xffffffff, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff,
	0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000,
	0xffffffff, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0xffffffff, 0x00000000, 0xffffffff, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0xffffffff, 0x00000000, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff,
	0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0xffffffff, 0xffffffff, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000,
	0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xffffffff, 0x00000000, 0x00000000, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0xffffffff, 0xff000000, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xff000000,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xff000000, 0xffffffff, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xff000000, 0xffffffff,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff,
	0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0xffffffff, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0xffffffff, 0xff000000,
	0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xffffffff, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0xffffffff, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff,
	0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xffffffff, 0xff000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0xffffffff, 0xff000000, 0xffffffff,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xff000000,
	0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff,
	0xffffffff, 0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0x00000000, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xffffffff, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000, 0xffffffff,
	0xffffffff, 0xff000000, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0xff000000, 0xffffffff,
	0xffffffff, 0x00000000, 0x00000000, 0xffffffff, 0xffffffff, 0xffffffff,
	0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0xff000000,
	0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0x00000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0xffffffff, 0xff000000, 0xffffffff, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff,
	0xffffffff, 0x00000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0xff000000, 0xffffffff, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0xff000000, 0xffffffff, 0xffffffff, 0xff000000, 0xffffffff, 0xff000000,
	0xffffffff, 0xffffffff, 0xffffffff, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xff000000, 0xffffffff, 0xffffffff,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xff000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xff000000, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xff000000,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xff000000, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff,
	0xff000000, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff,
	0xffffffff, 0xff000000, 0xff000000, 0xffffffff, 0xffffffff, 0xffffffff,
	0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0xffffffff,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0x00000000,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xffffffff, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xffffffff, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0x00000000, 0x00000000, 0xffffffff, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xffffffff,
	0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
	0xffffffff, 0xff000000, 0xff000000, 0xff000000, 0xff000000, 0xff000000,
	0xff000000, 0xff000000, 0xff000000, 0xffffffff, 0x00000000, 0x00000000,
	0x00000000, 0x00000000,
};

static struct cursor_metadata {
	char *name;
	int width, height;
	int hotspot_x, hotspot_y;
	size_t offset;
} cursor_metadata[] = {
	{ "bottom_left_corner", 16, 16, 1, 14, 0 },
	{ "bottom_right_corner", 16, 16, 14, 14, 256 },
	{ "bottom_side", 15, 16, 7, 14, 512 },
	{ "grabbing", 16, 16, 8, 8, 752 },
	{ "left_ptr", 10, 16, 1, 1, 1008 },
	{ "left_side", 16, 15, 1, 7, 1168 },
	{ "right_side", 16, 15, 14, 7, 1408 },
	{ "top_left_corner", 16, 16, 1, 1, 1648 },
	{ "top_right_corner", 16, 16, 14, 1, 1904 },
	{ "top_side", 15, 16, 7, 1, 2160 },
	{ "xterm", 9, 16, 4, 8, 2400 },
	{ "hand1", 13, 16, 12, 0, 2544 },
	{ "watch", 16, 16, 15, 9, 2752 },

	/* https://www.freedesktop.org/wiki/Specifications/cursor-spec/ */
	{ "sw-resize", 16, 16, 1, 14, 0 },
	{ "se-resize", 16, 16, 14, 14, 256 },
	{ "s-resize", 15, 16, 7, 14, 512 },
	{ "all-scroll", 16, 16, 8, 8, 752 },
	{ "default", 10, 16, 1, 1, 1008 },
	{ "w-resize", 16, 15, 1, 7, 1168 },
	{ "e-resize", 16, 15, 14, 7, 1408 },
	{ "nw-resize", 16, 16, 1, 1, 1648 },
	{ "ne-resize", 16, 16, 14, 1, 1904 },
	{ "n-resize", 15, 16, 7, 1, 2160 },
	{ "text", 9, 16, 4, 8, 2400 },
	{ "pointer", 13, 16, 12, 0, 2544 },
	{ "wait", 16, 16, 15, 9, 2752 },
};
