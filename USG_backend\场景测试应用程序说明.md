# USG Backend 场景测试应用程序

## 项目概述

这是一个基于USG Backend框架开发的场景测试应用程序，演示了如何在不同渲染后端（WebGPU、Vulkan、OpenGL）之间进行无缝切换，同时保持相同的场景内容和渲染效果。

## 核心功能

### 1. 多后端支持
- **WebGPU后端**: 现代跨平台图形API，支持桌面和WebAssembly
- **Vulkan后端**: 高性能低级图形API，适合桌面应用
- **OpenGL后端**: 传统图形API，广泛兼容性

### 2. 运行时后端切换
- 无需重启应用程序即可切换渲染后端
- 自动保持场景状态和资源
- 平滑的切换体验

### 3. VSG集成
- 支持VSG（VulkanSceneGraph）场景图
- VSG场景可以在不同后端上渲染
- 统一的场景管理接口

### 4. 实时性能监控
- FPS和帧时间统计
- 渲染统计信息（三角形数量、绘制调用等）
- 后端性能对比

## 项目结构

```
USG_backend/
├── include/
│   ├── USG_Backend/           # 核心后端接口
│   └── VSG_Adapter/           # VSG适配器接口
├── src/
│   ├── Core/                  # 核心实现
│   ├── WebGPU/               # WebGPU后端实现
│   ├── Vulkan/               # Vulkan后端实现
│   └── VSG_Adapter/          # VSG适配器实现
├── examples/
│   └── scene_test/           # 场景测试应用程序
│       ├── main.cpp          # 主程序入口
│       ├── SceneTestApp.*    # 应用程序主类
│       ├── SimpleScene.*     # 简单场景实现
│       └── BackendSwitcher.* # 后端切换器
└── tests/                    # 测试程序
```

## 核心组件

### 1. SceneTestApp
主应用程序类，负责：
- 窗口管理和事件处理
- 后端初始化和切换
- 场景创建和更新
- 用户界面和统计显示

### 2. BackendSwitcher
后端切换器，负责：
- 扫描和管理可用后端
- 执行后端切换操作
- 资源状态保持
- 切换性能监控

### 3. SimpleScene
简单场景实现，负责：
- 基本几何体创建（立方体、球体、平面等）
- 场景对象管理
- 渲染命令生成
- 后端适配

### 4. VSG_SceneRenderer
VSG场景渲染器，负责：
- VSG场景图渲染
- VSG到USG Backend的转换
- 相机和视图管理
- 渲染优化

### 5. VSG_BackendBridge
VSG后端桥接器，负责：
- VSG资源到USG Backend资源的映射
- 渲染命令转换
- 状态管理
- 性能优化

## 编译和运行

### 系统要求
- Windows 10/11 (主要支持平台)
- Visual Studio 2019/2022
- CMake 3.20+
- C++20编译器

### 依赖库
- **VSG**: VulkanSceneGraph库 (位于 F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph)
- **WebGPU**: wgpu-native库 (位于 F:/cmo-dev/my_osgearth_web/LearnWebGPU-Code-step100-next2/webgpu)
- **GLFW**: 窗口管理库 (自动下载)
- **Vulkan SDK**: Vulkan开发库

### 编译步骤
```bash
# 1. 创建构建目录
mkdir build_scene_test
cd build_scene_test

# 2. 配置CMake
cmake .. -DUSG_BUILD_SCENE_TEST=ON -DUSG_BUILD_VSG_ADAPTER=ON -DUSG_BUILD_WEBGPU_BACKEND=ON -DUSG_BUILD_VULKAN_BACKEND=ON

# 3. 编译
cmake --build . --config Release

# 4. 运行
Release/scene_test_app.exe
```

### 命令行参数
```bash
scene_test_app.exe [选项]

选项:
  --backend <name>    指定默认后端 (webgpu, vulkan, opengl)
  --width <pixels>    设置窗口宽度
  --height <pixels>   设置窗口高度
  --fullscreen        全屏模式
  --no-vsync          禁用垂直同步
  --no-rotation       禁用场景旋转
  --help, -h          显示帮助信息
```

## 使用说明

### 控制操作
- **数字键 1-3**: 切换渲染后端
  - 1: WebGPU后端
  - 2: Vulkan后端
  - 3: OpenGL后端
- **空格键**: 暂停/恢复场景动画
- **F1**: 显示/隐藏性能统计
- **F2**: 显示/隐藏后端选择器
- **ESC**: 退出应用程序

### 界面说明
- **性能统计面板**: 显示FPS、帧时间、三角形数量等信息
- **后端选择器**: 显示可用后端列表和切换按钮
- **场景控制面板**: 场景对象管理和渲染设置

## 技术特点

### 1. 统一抽象层
- 为不同图形API提供统一接口
- 隐藏底层API差异
- 简化跨平台开发

### 2. 资源管理
- 自动资源生命周期管理
- 智能指针和RAII模式
- 内存泄漏防护

### 3. 性能优化
- 最小化状态切换
- 批量渲染优化
- 多线程支持准备

### 4. 扩展性设计
- 插件化后端架构
- 易于添加新的图形API支持
- 模块化组件设计

## 开发计划

### 短期目标
- [ ] 完善WebGPU后端实现
- [ ] 添加基本几何体渲染
- [ ] 实现简单的材质系统
- [ ] 添加基础光照支持

### 中期目标
- [ ] 完整的VSG场景图支持
- [ ] 纹理和材质系统
- [ ] 阴影渲染
- [ ] 后处理效果

### 长期目标
- [ ] WebAssembly平台支持
- [ ] 移动平台适配
- [ ] 高级渲染技术
- [ ] 性能分析工具

## 贡献指南

欢迎贡献代码和建议！请遵循以下规范：

1. **代码风格**: 使用2空格缩进，遵循现有代码风格
2. **注释**: 使用中文注释，详细说明功能
3. **测试**: 添加相应的单元测试
4. **文档**: 更新相关文档

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目仓库: [USG Backend](https://github.com/your-repo/usg-backend)
- 邮箱: <EMAIL>

---

**注意**: 这是一个演示项目，主要用于验证USG Backend框架的可行性和展示多后端切换功能。在生产环境中使用前，请进行充分的测试和优化。
