#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OSGEarth WebAssembly 本地服务器
用于运行已编译的WebAssembly版本
"""

import http.server
import socketserver
import os
import sys
import webbrowser
import threading
import time

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """支持CORS的HTTP请求处理器"""
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        # 设置正确的MIME类型
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        super().end_headers()
    
    def guess_type(self, path):
        """设置正确的MIME类型"""
        # 修复Python 3.13兼容性问题
        result = super().guess_type(path)
        if isinstance(result, tuple) and len(result) >= 2:
            mimetype, encoding = result[0], result[1]
        else:
            mimetype = result if not isinstance(result, tuple) else result[0]
            encoding = None
            
        if path.endswith('.wasm'):
            return 'application/wasm'
        elif path.endswith('.js'):
            return 'application/javascript'
        elif path.endswith('.html'):
            return 'text/html'
        return mimetype

def open_browser(url, delay=2):
    """延迟打开浏览器"""
    time.sleep(delay)
    print(f"\n🌍 正在浏览器中打开: {url}")
    webbrowser.open(url)

def main():
    """主函数"""
    # 设置服务器
    PORT = 8080
    
    # 确保在正确的目录中运行
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print("=" * 60)
    print("🚀 OSGEarth WebAssembly 本地服务器")
    print("=" * 60)
    print(f"📁 服务目录: {script_dir}")
    print(f"🌐 端口: {PORT}")
    print()
    

    
    try:
        # 创建服务器
        with socketserver.TCPServer(("", PORT), CORSHTTPRequestHandler) as httpd:
            print(f"🎯 服务器启动成功: http://localhost:{PORT}")
            print("📝 推荐访问: http://localhost:{PORT}/index.html")
            print()
            print("⚡ 控制台输出:")
            print("   - 使用 Ctrl+C 停止服务器")
            print("   - 浏览器会自动打开主页面")
            print()
            
            # 在新线程中延迟打开浏览器
            #browser_thread = threading.Thread(
            #    target=open_browser, 
            #    args=(f"http://localhost:{PORT}/index.html",)
            #)
            #browser_thread.daemon = True
            #browser_thread.start()
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {PORT} 已被占用")
            print("请尝试关闭其他Web服务器或使用不同端口")
        else:
            print(f"❌ 服务器错误: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

if __name__ == "__main__":
    main() 