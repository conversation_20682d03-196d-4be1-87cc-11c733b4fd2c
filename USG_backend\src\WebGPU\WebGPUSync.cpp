#include "WebGPUBackend.h"
#include <chrono>
#include <thread>

namespace USG {

// WebGPUFence实现
WebGPUFence::WebGPUFence() : _signaled(false) {
}

bool WebGPUFence::wait(uint64_t timeoutNs) {
    if (_signaled.load()) {
        return true;
    }
    
    // 简单的轮询实现
    auto startTime = std::chrono::high_resolution_clock::now();
    auto timeoutDuration = std::chrono::nanoseconds(timeoutNs);
    
    while (!_signaled.load()) {
        auto currentTime = std::chrono::high_resolution_clock::now();
        auto elapsed = currentTime - startTime;
        
        if (timeoutNs != UINT64_MAX && elapsed >= timeoutDuration) {
            return false; // 超时
        }
        
        // 短暂休眠避免忙等待
        std::this_thread::sleep_for(std::chrono::microseconds(100));
    }
    
    return true;
}

void WebGPUFence::reset() {
    _signaled.store(false);
}

bool WebGPUFence::isSignaled() {
    return _signaled.load();
}

void WebGPUFence::signal() {
    _signaled.store(true);
}

// WebGPUSemaphore实现
WebGPUSemaphore::WebGPUSemaphore() {
    _fence = std::make_unique<WebGPUFence>();
}

} // namespace USG
