struct Uniforms {
    modelMatrix: mat4x4<f32>,
    viewMatrix: mat4x4<f32>,
    projMatrix: mat4x4<f32>,
    lightDirection: vec3<f32>,
    time: f32,
}

struct VertexInput {
    @location(0) position: vec3<f32>,
    @location(1) texCoord: vec2<f32>,
    @location(2) normal: vec3<f32>,
}

struct VertexOutput {
    @builtin(position) position: vec4<f32>,
    @location(0) texCoord: vec2<f32>,
    @location(1) normal: vec3<f32>,
    @location(2) worldPos: vec3<f32>,
}

@group(0) @binding(0) var<uniform> uniforms: Uniforms;
@group(0) @binding(1) var earthTexture: texture_2d<f32>;
@group(0) @binding(2) var earthSampler: sampler;

@vertex
fn vs_main(input: VertexInput) -> VertexOutput {
    var output: VertexOutput;
    
    // 计算世界坐标
    let worldPos = uniforms.modelMatrix * vec4<f32>(input.position, 1.0);
    output.worldPos = worldPos.xyz;
    
    // 计算屏幕坐标
    let viewPos = uniforms.viewMatrix * worldPos;
    output.position = uniforms.projMatrix * viewPos;
    
    // 传递纹理坐标和法线
    output.texCoord = input.texCoord;
    output.normal = normalize((uniforms.modelMatrix * vec4<f32>(input.normal, 0.0)).xyz);
    
    return output;
}

@fragment
fn fs_main(input: VertexOutput) -> @location(0) vec4<f32> {
    // 采样地球纹理
    let baseColor = textureSample(earthTexture, earthSampler, input.texCoord);
    
    // 计算光照
    let normal = normalize(input.normal);
    let lightDir = normalize(-uniforms.lightDirection);
    let diffuse = max(dot(normal, lightDir), 0.0);
    
    // 环境光
    let ambient = 0.3;
    
    // 大气散射效果（简化）
    let viewDir = normalize(input.worldPos);
    let atmosphere = pow(1.0 - abs(dot(normal, viewDir)), 2.0) * 0.5;
    
    // 最终颜色
    let lighting = ambient + diffuse * 0.7;
    var finalColor = baseColor.rgb * lighting;
    
    // 添加大气效果
    finalColor = mix(finalColor, vec3<f32>(0.5, 0.7, 1.0), atmosphere);
    
    return vec4<f32>(finalColor, baseColor.a);
}

// 简化的大气着色器（可选）
@fragment
fn fs_atmosphere(input: VertexOutput) -> @location(0) vec4<f32> {
    let normal = normalize(input.normal);
    let viewDir = normalize(input.worldPos);
    
    // 大气散射
    let fresnel = 1.0 - abs(dot(normal, viewDir));
    let atmosphere = pow(fresnel, 3.0);
    
    let skyColor = vec3<f32>(0.5, 0.7, 1.0);
    return vec4<f32>(skyColor * atmosphere, atmosphere * 0.8);
}
