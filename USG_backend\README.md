# USG Backend - WebGPU渲染引擎平台

## 项目概述

USG Backend是一个独立的WebGPU渲染引擎框架，专门设计用于替换现有图形引擎(如VSG/OSG)的渲染后端，实现零侵入、透明替换的目标。

## 核心特性

### 🎯 设计目标
- **零侵入**: 无需修改VSG/OSG源码
- **透明替换**: 应用层代码无需修改
- **多后端支持**: 支持WebGPU、Vulkan、OpenGL后端切换
- **跨平台**: 统一支持桌面端和WebAssembly
- **高性能**: 充分利用现代GPU特性

### 🏗️ 架构特点
- **面向接口设计**: 基于抽象接口的插件化架构
- **适配器模式**: 通过适配器实现API转换
- **工厂模式**: 动态创建不同后端实现
- **代理模式**: 透明代理原有API调用

## 项目结构

```
USG_backend/
├── README.md                           # 项目说明
├── CMakeLists.txt                      # 构建系统
├── docs/                               # 设计文档
│   ├── Architecture.md                 # 架构设计
│   ├── VSG_Integration.md              # VSG集成方案
│   └── API_Reference.md                # API参考
├── include/                            # 公共头文件
│   ├── USG_Backend/                    # 主要接口
│   │   ├── RenderBackend.h             # 后端抽象接口
│   │   ├── BackendFactory.h            # 后端工厂
│   │   ├── BackendTypes.h              # 通用类型定义
│   │   └── BackendConfig.h             # 配置管理
│   └── VSG_Adapter/                    # VSG适配器
│       ├── VSG_Device.h                # VSG设备适配
│       ├── VSG_CommandBuffer.h         # VSG命令缓冲区适配
│       └── VSG_Pipeline.h              # VSG管线适配
├── src/                                # 源文件实现
│   ├── Core/                           # 核心实现
│   │   ├── BackendFactory.cpp          # 工厂实现
│   │   ├── BackendManager.cpp          # 后端管理器
│   │   └── TypeConverters.cpp          # 类型转换器
│   ├── WebGPU/                         # WebGPU后端实现
│   │   ├── WebGPUBackend.cpp           # WebGPU后端
│   │   ├── WebGPUDevice.cpp            # WebGPU设备
│   │   ├── WebGPUBuffer.cpp            # WebGPU缓冲区
│   │   ├── WebGPUTexture.cpp           # WebGPU纹理
│   │   └── WebGPUPipeline.cpp          # WebGPU管线
│   ├── Vulkan/                         # Vulkan后端实现(兼容)
│   │   └── VulkanBackend.cpp           # Vulkan后端
│   └── VSG_Adapter/                    # VSG适配器实现
│       ├── VSG_Device.cpp              # VSG设备适配实现
│       ├── VSG_CommandBuffer.cpp       # VSG命令缓冲区适配实现
│       └── VSG_Pipeline.cpp            # VSG管线适配实现
├── examples/                           # 示例代码
│   ├── basic_triangle/                 # 基础三角形示例
│   ├── vsg_earth/                      # VSG地球渲染示例
│   └── performance_test/               # 性能测试
├── tests/                              # 单元测试
│   ├── backend_tests/                  # 后端测试
│   ├── adapter_tests/                  # 适配器测试
│   └── integration_tests/              # 集成测试
└── tools/                              # 工具
    ├── vsg_analyzer/                   # VSG API分析工具
    └── performance_profiler/           # 性能分析工具
```

## 核心设计理念

### 1. 分层架构
```
┌─────────────────────────────────────────────────────────┐
│                VSG应用层 (不变)                          │
│           Scene Graph, Nodes, Visitors                  │
├─────────────────────────────────────────────────────────┤
│                VSG API层 (不变)                          │
│         vsg::Device, vsg::CommandBuffer, etc.           │
├─────────────────────────────────────────────────────────┤
│              USG Backend适配器层 (新增)                  │
│        VSG_Device, VSG_CommandBuffer, etc.              │
├─────────────────────────────────────────────────────────┤
│              USG Backend抽象层 (新增)                    │
│           RenderBackend, BackendDevice, etc.            │
├─────────────────────────────────────────────────────────┤
│              USG Backend实现层 (新增)                    │
│        WebGPUBackend, VulkanBackend, etc.               │
└─────────────────────────────────────────────────────────┘
```

### 2. 接口设计原则
- **最小接口**: 只暴露必要的抽象接口
- **类型安全**: 强类型检查和编译期验证
- **异常安全**: RAII和异常安全保证
- **性能优先**: 零开销抽象和内联优化

### 3. 集成方式
- **动态链接**: 运行时加载USG Backend库
- **静态链接**: 编译时集成USG Backend
- **插件模式**: 通过插件接口动态加载
- **预编译**: 预编译的二进制替换

## 使用方式

### 方式1: 动态替换 (推荐)
```cpp
// 在VSG应用启动时
#include <USG_Backend/BackendManager.h>

int main() {
    // 初始化USG Backend
    USG::BackendManager::initialize(USG::BackendType::WebGPU);
    
    // 原有VSG代码完全不变
    auto device = vsg::Device::create();
    auto scene = vsg::Group::create();
    // ... 正常的VSG代码
    
    return 0;
}
```

### 方式2: 编译时集成
```cpp
// 在CMakeLists.txt中
find_package(USG_Backend REQUIRED)
target_link_libraries(your_app USG_Backend::VSG_Adapter)

// 代码中自动替换，无需修改
```

### 方式3: 预编译替换
```bash
# 使用USG Backend提供的预编译库替换VSG库
cp libUSG_Backend_VSG.so /path/to/vsg/lib/libvsg.so
```

## 性能特性

### 渲染性能
- **零开销抽象**: 编译期优化，运行时无额外开销
- **批处理优化**: 自动合并相似的渲染调用
- **状态缓存**: 智能的渲染状态缓存和去重
- **异步执行**: 支持多线程和异步渲染

### 内存管理
- **智能缓存**: 自动的资源缓存和生命周期管理
- **内存池**: 高效的内存分配和回收
- **延迟加载**: 按需加载和释放GPU资源
- **压缩优化**: 自动的纹理和几何体压缩

## 兼容性

### VSG兼容性
- ✅ VSG 1.0.x 完全兼容
- ✅ VSG 1.1.x 完全兼容
- ✅ 所有VSG核心功能支持
- ✅ VSG扩展功能支持

### 平台支持
- ✅ Windows 10/11 (Desktop + WebAssembly)
- ✅ Linux (Desktop + WebAssembly)
- ✅ macOS (Desktop + WebAssembly)
- ✅ Web浏览器 (Chrome, Firefox, Safari, Edge)

### 后端支持
- ✅ WebGPU (主要目标)
- ✅ Vulkan (兼容模式)
- ✅ OpenGL (兼容模式)
- 🔄 DirectX 12 (计划中)
- 🔄 Metal (计划中)

## 快速开始

### 1. 安装USG Backend
```bash
# 从源码编译
git clone https://github.com/your-org/USG_backend.git
cd USG_backend
mkdir build && cd build
cmake .. -DUSG_BUILD_VSG_ADAPTER=ON
make -j8
sudo make install
```

### 2. 集成到VSG项目
```cmake
# 在你的CMakeLists.txt中添加
find_package(USG_Backend REQUIRED)
target_link_libraries(your_vsg_app USG_Backend::VSG_Adapter)
```

### 3. 运行示例
```bash
# 运行基础示例
cd examples/basic_triangle
mkdir build && cd build
cmake .. && make
./basic_triangle

# 运行VSG地球示例
cd examples/vsg_earth
mkdir build && cd build
cmake .. && make
./vsg_earth
```

## 开发指南

### 添加新后端
1. 继承`RenderBackend`接口
2. 实现所有虚函数
3. 注册到`BackendFactory`
4. 编写单元测试

### 添加新适配器
1. 分析目标API调用模式
2. 设计适配器接口
3. 实现API转换逻辑
4. 集成测试验证

### 性能优化
1. 使用性能分析工具
2. 识别热点代码路径
3. 应用优化策略
4. 基准测试验证

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献指南

欢迎贡献代码、报告问题或提出建议。请参阅CONTRIBUTING.md了解详细信息。

## 联系方式

- 项目主页: https://github.com/your-org/USG_backend
- 问题反馈: https://github.com/your-org/USG_backend/issues
- 邮件联系: <EMAIL>
