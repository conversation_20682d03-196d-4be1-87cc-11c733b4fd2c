桌面版，经过修正代码之后的编译命令
rd /s /q build_desk
cmake . -B build_desk    
cmake --build build_desk --config Release



//准备emscripten工具
emsdk update
emsdk install latest
emsdk activate latest

rd /s /q build_wasm
emcmake cmake -S . -B build_wasm -G Ninja -DCMAKE_BUILD_TYPE=Release -DDEV_MODE=OFF -DWEBGPU_BACKEND=EMDAWNWEBGPU
emcmake cmake -S . -B build_wasm -G "Ninja" -DWEBGPU_BACKEND=EMDAWNWEBGPU  -DWEBGPU_BUILD_FROM_SOURCE=OFF      或者 EMSCRIPTEN
Ninja -C build_wasm -t clean

编译：1
Ninja -C build_wasm -j8
编译：2
cmake --build build_wasm