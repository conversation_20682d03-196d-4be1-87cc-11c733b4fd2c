#pragma once

/**
 * @file USG.h
 * @brief USG (WebGPU Scene Graph) 主头文件
 * 
 * 这个头文件包含了USG库的所有主要组件，提供了一个统一的入口点。
 * 用户只需要包含这个头文件就可以使用USG的所有功能。
 */

// 版本信息
#define USG_VERSION_MAJOR 1
#define USG_VERSION_MINOR 0
#define USG_VERSION_PATCH 0
#define USG_VERSION_STRING "1.0.0"

// 平台检测
#ifdef __EMSCRIPTEN__
    #define USG_PLATFORM_WASM 1
    #define USG_PLATFORM_DESKTOP 0
#else
    #define USG_PLATFORM_WASM 0
    #define USG_PLATFORM_DESKTOP 1
#endif

// 核心组件
#include <USG/Core/Object.h>

// 数学库
#include <USG/Math/Math.h>
#include <USG/Math/BoundingSphere.h>

// 场景图节点
#include <USG/Nodes/Node.h>

// 访问者系统
#include <USG/Visitors/Visitor.h>

// WebGPU封装
#include <USG/WebGPU/WebGPUDevice.h>

// 便利命名空间
namespace usg {

/**
 * @brief 获取USG版本信息
 * @return 版本字符串
 */
inline const char* getVersion() {
    return USG_VERSION_STRING;
}

/**
 * @brief 获取主版本号
 * @return 主版本号
 */
inline int getVersionMajor() {
    return USG_VERSION_MAJOR;
}

/**
 * @brief 获取次版本号
 * @return 次版本号
 */
inline int getVersionMinor() {
    return USG_VERSION_MINOR;
}

/**
 * @brief 获取补丁版本号
 * @return 补丁版本号
 */
inline int getVersionPatch() {
    return USG_VERSION_PATCH;
}

/**
 * @brief 检查是否为WebAssembly平台
 * @return 是否为WebAssembly平台
 */
inline bool isWebAssembly() {
    return USG_PLATFORM_WASM;
}

/**
 * @brief 检查是否为桌面平台
 * @return 是否为桌面平台
 */
inline bool isDesktop() {
    return USG_PLATFORM_DESKTOP;
}

/**
 * @brief 初始化USG库
 * @return 是否成功初始化
 */
bool initialize();

/**
 * @brief 清理USG库
 */
void cleanup();

/**
 * @brief 获取库信息
 * @return 库信息字符串
 */
std::string getLibraryInfo();

} // namespace usg

// 兼容性别名，用于与VSG/OSG的API兼容
namespace vsg = usg;  // VSG兼容性
namespace osg = usg;  // OSG兼容性

/**
 * @brief 使用示例
 * 
 * @code
 * #include <USG/USG.h>
 * 
 * int main() {
 *     // 初始化USG
 *     if (!usg::initialize()) {
 *         return -1;
 *     }
 *     
 *     // 创建WebGPU设备
 *     auto device = usg::WebGPUDevice::create();
 *     if (!device || !device->isValid()) {
 *         return -1;
 *     }
 *     
 *     // 创建场景图
 *     auto root = usg::make_ref<usg::Group>();
 *     auto transform = usg::make_ref<usg::Transform>();
 *     auto geometry = usg::make_ref<usg::Geometry>();
 *     
 *     // 构建场景层次
 *     root->addChild(transform);
 *     transform->addChild(geometry);
 *     
 *     // 创建访问者
 *     usg::CullVisitor cullVisitor;
 *     usg::RenderVisitor renderVisitor(device);
 *     
 *     // 遍历场景图
 *     root->accept(cullVisitor);
 *     root->accept(renderVisitor);
 *     
 *     // 清理
 *     usg::cleanup();
 *     return 0;
 * }
 * @endcode
 */

/**
 * @brief VSG兼容性示例
 * 
 * @code
 * #include <USG/USG.h>
 * 
 * // 使用VSG风格的API
 * int main() {
 *     // 创建场景图（VSG风格）
 *     auto root = vsg::Group::create();
 *     auto transform = vsg::Transform::create();
 *     
 *     // 设置变换
 *     transform->setMatrix(vsg::translate(0.0f, 1.0f, 0.0f));
 *     
 *     // 构建层次
 *     root->addChild(transform);
 *     
 *     return 0;
 * }
 * @endcode
 */

/**
 * @brief OSG兼容性示例
 * 
 * @code
 * #include <USG/USG.h>
 * 
 * // 使用OSG风格的API
 * int main() {
 *     // 创建场景图（OSG风格）
 *     osg::ref_ptr<osg::Group> root = new osg::Group;
 *     osg::ref_ptr<osg::Transform> transform = new osg::Transform;
 *     
 *     // 构建层次
 *     root->addChild(transform);
 *     
 *     return 0;
 * }
 * @endcode
 */
