#include <USG_Backend/BackendFactory.h>
#include <iostream>

namespace USG {

BackendFactory& BackendFactory::getInstance() {
    static BackendFactory instance;
    return instance;
}

bool BackendFactory::registerBackend(BackendType type, BackendCreator creator) {
    if (!creator) {
        std::cerr << "[BackendFactory] Invalid creator for backend type: " << static_cast<int>(type) << std::endl;
        return false;
    }
    
    BackendInfo info;
    info.type = type;
    info.creator = creator;
    info.name = "Backend_" + std::to_string(static_cast<int>(type));
    
    _backendsByType[type] = info;
    _backendsByName[info.name] = info;
    
    std::cout << "[BackendFactory] Registered backend: " << info.name << " (type: " << static_cast<int>(type) << ")" << std::endl;
    return true;
}

bool BackendFactory::registerBackend(const std::string& name, BackendType type, BackendCreator creator) {
    if (!creator || name.empty()) {
        std::cerr << "[BackendFactory] Invalid parameters for backend registration" << std::endl;
        return false;
    }
    
    BackendInfo info;
    info.type = type;
    info.creator = creator;
    info.name = name;
    
    _backendsByType[type] = info;
    _backendsByName[name] = info;
    
    std::cout << "[BackendFactory] Registered backend: " << name << " (type: " << static_cast<int>(type) << ")" << std::endl;
    return true;
}

void BackendFactory::unregisterBackend(BackendType type) {
    auto it = _backendsByType.find(type);
    if (it != _backendsByType.end()) {
        std::string name = it->second.name;
        _backendsByType.erase(it);
        _backendsByName.erase(name);
        std::cout << "[BackendFactory] Unregistered backend type: " << static_cast<int>(type) << std::endl;
    }
}

void BackendFactory::unregisterBackend(const std::string& name) {
    auto it = _backendsByName.find(name);
    if (it != _backendsByName.end()) {
        BackendType type = it->second.type;
        _backendsByName.erase(it);
        _backendsByType.erase(type);
        std::cout << "[BackendFactory] Unregistered backend: " << name << std::endl;
    }
}

std::unique_ptr<RenderBackend> BackendFactory::createBackend(BackendType type) {
    auto it = _backendsByType.find(type);
    if (it == _backendsByType.end()) {
        std::cerr << "[BackendFactory] Backend type not registered: " << static_cast<int>(type) << std::endl;
        return nullptr;
    }
    
    try {
        auto backend = it->second.creator();
        if (backend) {
            std::cout << "[BackendFactory] Created backend: " << it->second.name << std::endl;
        } else {
            std::cerr << "[BackendFactory] Failed to create backend: " << it->second.name << std::endl;
        }
        return backend;
    } catch (const std::exception& e) {
        std::cerr << "[BackendFactory] Exception creating backend: " << e.what() << std::endl;
        return nullptr;
    }
}

std::unique_ptr<RenderBackend> BackendFactory::createBackend(const std::string& name) {
    auto it = _backendsByName.find(name);
    if (it == _backendsByName.end()) {
        std::cerr << "[BackendFactory] Backend not registered: " << name << std::endl;
        return nullptr;
    }
    
    try {
        auto backend = it->second.creator();
        if (backend) {
            std::cout << "[BackendFactory] Created backend: " << name << std::endl;
        } else {
            std::cerr << "[BackendFactory] Failed to create backend: " << name << std::endl;
        }
        return backend;
    } catch (const std::exception& e) {
        std::cerr << "[BackendFactory] Exception creating backend: " << e.what() << std::endl;
        return nullptr;
    }
}

bool BackendFactory::isBackendAvailable(BackendType type) const {
    return _backendsByType.find(type) != _backendsByType.end();
}

bool BackendFactory::isBackendAvailable(const std::string& name) const {
    return _backendsByName.find(name) != _backendsByName.end();
}

std::vector<BackendType> BackendFactory::getAvailableBackends() const {
    std::vector<BackendType> backends;
    for (const auto& pair : _backendsByType) {
        backends.push_back(pair.first);
    }
    return backends;
}

std::vector<std::string> BackendFactory::getAvailableBackendNames() const {
    std::vector<std::string> names;
    for (const auto& pair : _backendsByName) {
        names.push_back(pair.first);
    }
    return names;
}

BackendType BackendFactory::getDefaultBackendType() const {
    return _defaultBackendType;
}

void BackendFactory::setDefaultBackendType(BackendType type) {
    _defaultBackendType = type;
}

BackendType BackendFactory::detectBestBackend() const {
    // 按优先级检测最佳后端
    std::vector<BackendType> priorities = {
        BackendType::WebGPU,
        BackendType::Vulkan,
        BackendType::DirectX12,
        BackendType::OpenGL
    };
    
    for (BackendType type : priorities) {
        if (isBackendAvailable(type)) {
            return type;
        }
    }
    
    return BackendType::Unknown;
}

void BackendFactory::clear() {
    _backendsByType.clear();
    _backendsByName.clear();
    std::cout << "[BackendFactory] Cleared all registered backends" << std::endl;
}

// BackendManager简化实现 - 只提供基础功能
BackendManager& BackendManager::getInstance() {
    static BackendManager instance;
    return instance;
}

bool BackendManager::initialize(BackendType type, const BackendConfig& config) {
    if (_initialized) {
        std::cout << "[BackendManager] Already initialized" << std::endl;
        return true;
    }
    
    std::cout << "[BackendManager] Initializing with backend type: " << static_cast<int>(type) << std::endl;
    
    // 简化版本：只设置状态，不创建实际后端
    _currentBackendType = type;
    _initialized = true;
    
    std::cout << "[BackendManager] Successfully initialized (simplified mode)" << std::endl;
    return true;
}

bool BackendManager::initialize(const std::string& name, const BackendConfig& config) {
    if (_initialized) {
        std::cout << "[BackendManager] Already initialized" << std::endl;
        return true;
    }
    
    std::cout << "[BackendManager] Initializing with backend: " << name << std::endl;
    
    // 简化版本：只设置状态
    _currentBackendType = BackendType::WebGPU; // 默认
    _initialized = true;
    
    std::cout << "[BackendManager] Successfully initialized (simplified mode)" << std::endl;
    return true;
}

void BackendManager::cleanup() {
    if (!_initialized) {
        return;
    }
    
    std::cout << "[BackendManager] Cleaning up..." << std::endl;
    
    _currentBackendType = BackendType::Unknown;
    _initialized = false;
    
    std::cout << "[BackendManager] Cleanup completed" << std::endl;
}

RenderBackend* BackendManager::getCurrentBackend() const {
    return nullptr; // 简化版本返回nullptr
}

bool BackendManager::switchBackend(BackendType type, const BackendConfig& config) {
    std::cout << "[BackendManager] Switching to backend type: " << static_cast<int>(type) << std::endl;
    
    // 清理当前后端
    cleanup();
    
    // 初始化新后端
    return initialize(type, config);
}

bool BackendManager::switchBackend(const std::string& name, const BackendConfig& config) {
    std::cout << "[BackendManager] Switching to backend: " << name << std::endl;
    
    // 清理当前后端
    cleanup();
    
    // 初始化新后端
    return initialize(name, config);
}

bool BackendManager::isInitialized() const {
    return _initialized;
}

BackendType BackendManager::getCurrentBackendType() const {
    return _currentBackendType;
}

std::string BackendManager::getCurrentBackendName() const {
    return backendTypeToString(_currentBackendType);
}

void BackendManager::setErrorCallback(std::function<void(const std::string&)> callback) {
    _errorCallback = callback;
}

void BackendManager::setDebugCallback(std::function<void(const std::string&)> callback) {
    _debugCallback = callback;
}

} // namespace USG
