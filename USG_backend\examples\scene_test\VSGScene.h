#pragma once

#include <memory>
#include <vector>
#include <string>

// USG Backend includes
#include <USG_Backend/RenderBackend.h>
#include <USG_Backend/BackendTypes.h>

// VSG Adapter includes (如果可用)
#ifdef USG_VSG_ADAPTER_AVAILABLE
#include <VSG_Adapter/VSG_SceneRenderer.h>
#include <VSG_Adapter/VSG_Device.h>
#include <VSG_Adapter/VSG_BackendBridge.h>
#endif

namespace USG
{
    /**
     * @brief VSG场景管理器
     *
     * 使用VSG适配器来管理和渲染场景，提供与VSG兼容的API
     */
    class VSGScene
    {
    public:
        struct RenderStats
        {
            uint32_t triangleCount = 0;
            uint32_t drawCalls = 0;
            uint32_t vertexCount = 0;
            double frameTime = 0.0;
        };

        struct SceneObject
        {
            std::string name;
            float transform[16] = {
                1.0f, 0.0f, 0.0f, 0.0f,
                0.0f, 1.0f, 0.0f, 0.0f,
                0.0f, 0.0f, 1.0f, 0.0f,
                0.0f, 0.0f, 0.0f, 1.0f};
            bool visible = true;
            uint32_t vertexCount = 0;
            uint32_t indexCount = 0;

            // 渲染资源
            std::unique_ptr<BackendBuffer> vertexBuffer;
            std::unique_ptr<BackendBuffer> indexBuffer;
        };

    public:
        VSGScene();
        ~VSGScene();

        /**
         * @brief 初始化VSG场景
         * @param backend 渲染后端
         * @return 是否成功
         */
        bool initialize(RenderBackend *backend);

        /**
         * @brief 清理场景
         */
        void cleanup();

        /**
         * @brief 创建测试场景
         */
        void createTestScene();

        /**
         * @brief 渲染场景
         * @param commandList 命令列表
         * @param viewMatrix 视图矩阵
         * @param projMatrix 投影矩阵
         */
        void render(BackendCommandList *commandList, const float *viewMatrix, const float *projMatrix);

        /**
         * @brief 更新场景
         * @param deltaTime 时间增量
         */
        void update(double deltaTime);

        /**
         * @brief 获取渲染统计信息
         */
        const RenderStats &getRenderStats() const { return _renderStats; }

        /**
         * @brief 添加场景对象
         */
        void addSceneObject(SceneObject &&object);

        /**
         * @brief 设置相机参数
         */
        void setCameraPosition(float x, float y, float z);
        void setCameraTarget(float x, float y, float z);

    private:
        void createTriangleGeometry();
        void createCubeGeometry();
        void updateRenderStats();

    private:
        RenderBackend *_backend = nullptr;

#ifdef USG_VSG_ADAPTER_AVAILABLE
        std::unique_ptr<VSG_SceneRenderer> _vsgRenderer;
        std::unique_ptr<VSG_Device> _vsgDevice;
#endif

        // 场景数据
        std::vector<SceneObject> _sceneObjects;

        // 渲染资源
        std::unique_ptr<BackendShader> _vertexShader;
        std::unique_ptr<BackendShader> _fragmentShader;
        std::unique_ptr<BackendPipeline> _pipeline;
        std::unique_ptr<BackendBuffer> _triangleVertexBuffer;
        std::unique_ptr<BackendBuffer> _triangleIndexBuffer;
        std::unique_ptr<BackendBuffer> _cubeVertexBuffer;
        std::unique_ptr<BackendBuffer> _cubeIndexBuffer;

        // 状态
        bool _initialized = false;
        double _totalTime = 0.0;

        // 统计信息
        RenderStats _renderStats;

        // 相机参数
        float _cameraPosition[3] = {0.0f, 0.0f, 5.0f};
        float _cameraTarget[3] = {0.0f, 0.0f, 0.0f};
    };

} // namespace USG
