#pragma once

#include <USG_Backend/BackendTypes.h>
#include <USG_Backend/BackendFactory.h>
#include <memory>
#include <string>
#include <vector>
#include <functional>

#ifdef USG_HAS_VSG
#include <VSG_Adapter/VSG_SceneRenderer.h>
#endif

// GLFW前向声明
struct GLFWwindow;

namespace USG
{

    // 前向声明
    class RenderBackend;
    class SimpleScene;
    class VSGScene;
    class BackendSwitcher;

    /**
     * @brief 场景测试应用程序
     *
     * 演示如何在不同渲染后端之间切换，同时保持相同的场景内容
     */
    class SceneTestApp
    {
    public:
        /**
         * @brief 应用程序配置
         */
        struct AppConfig
        {
            std::string title = "USG Backend Scene Test";
            uint32_t windowWidth = 1200;
            uint32_t windowHeight = 800;
            bool enableVSync = true;
            bool fullscreen = false;

            // 默认后端
            BackendType defaultBackend = BackendType::OpenGL;

            // 场景配置
            bool enableRotation = true;
            float rotationSpeed = 1.0f;
            bool showStats = true;
            bool showUI = true;
        };

        /**
         * @brief 应用程序状态
         */
        struct AppState
        {
            bool running = false;
            bool paused = false;
            double deltaTime = 0.0;
            double totalTime = 0.0;
            uint32_t frameCount = 0;

            // 当前后端
            BackendType currentBackend = BackendType::Unknown;
            std::string currentBackendName;

            // 性能统计
            double fps = 0.0;
            double frameTime = 0.0;
            uint32_t triangleCount = 0;
            uint32_t drawCalls = 0;
        };

    public:
        SceneTestApp();
        ~SceneTestApp();

        /**
         * @brief 初始化应用程序
         * @param config 应用程序配置
         * @return 是否成功
         */
        bool initialize(const AppConfig &config = {});

        /**
         * @brief 运行应用程序主循环
         * @return 退出代码
         */
        int run();

        /**
         * @brief 清理应用程序
         */
        void cleanup();

        /**
         * @brief 切换渲染后端
         * @param backendType 新的后端类型
         * @return 是否成功
         */
        bool switchBackend(BackendType backendType);

        /**
         * @brief 切换渲染后端（通过名称）
         * @param backendName 后端名称
         * @return 是否成功
         */
        bool switchBackend(const std::string &backendName);

        /**
         * @brief 获取可用的后端列表
         * @return 后端类型列表
         */
        std::vector<BackendType> getAvailableBackends() const;

        /**
         * @brief 获取可用的后端名称列表
         * @return 后端名称列表
         */
        std::vector<std::string> getAvailableBackendNames() const;

        /**
         * @brief 获取当前应用程序状态
         * @return 应用程序状态
         */
        const AppState &getAppState() const { return _appState; }

        /**
         * @brief 获取应用程序配置
         * @return 应用程序配置
         */
        const AppConfig &getAppConfig() const { return _appConfig; }

        /**
         * @brief 设置错误回调函数
         * @param callback 错误回调函数
         */
        void setErrorCallback(std::function<void(const std::string &)> callback);

        /**
         * @brief 暂停/恢复应用程序
         * @param paused 是否暂停
         */
        void setPaused(bool paused);

        /**
         * @brief 请求退出应用程序
         */
        void requestExit();

    private:
        // 内部方法
        bool createWindow();
        bool initializeBackend();
        bool createScene();
        bool setupUI();

        void updateFrame();
        void renderFrame();
        void renderUI();
        void handleInput();
        void updateStats();

        // 回调函数
        void onWindowResize(int width, int height);
        void onKeyPress(int key, int action);
        void onMouseButton(int button, int action);
        void onMouseMove(double x, double y);

        // GLFW回调包装
        static void glfwErrorCallback(int error, const char *description);
        static void glfwWindowSizeCallback(GLFWwindow *window, int width, int height);
        static void glfwKeyCallback(GLFWwindow *window, int key, int scancode, int action, int mods);
        static void glfwMouseButtonCallback(GLFWwindow *window, int button, int action, int mods);
        static void glfwCursorPosCallback(GLFWwindow *window, double x, double y);

    private:
        // 配置和状态
        AppConfig _appConfig;
        AppState _appState;

        // 窗口
        GLFWwindow *_window = nullptr;

        // 渲染后端
        std::unique_ptr<RenderBackend> _backend;
        std::unique_ptr<BackendSwitcher> _backendSwitcher;

        // 场景
        std::unique_ptr<SimpleScene> _scene;
        std::unique_ptr<VSGScene> _vsgScene;

#ifdef USG_HAS_VSG
        // VSG渲染器
        std::unique_ptr<VSG_SceneRenderer> _vsgRenderer;
#endif

        // 回调函数
        std::function<void(const std::string &)> _errorCallback;

        // 时间管理
        double _lastFrameTime = 0.0;
        double _fpsUpdateTime = 0.0;
        uint32_t _fpsFrameCount = 0;

        // 输入状态
        bool _keys[1024] = {false};
        double _mouseX = 0.0;
        double _mouseY = 0.0;
        bool _mouseButtons[8] = {false};

        // UI状态
        bool _showBackendSelector = true;
        bool _showPerformanceStats = true;
        bool _showSceneControls = true;
    };

} // namespace USG
