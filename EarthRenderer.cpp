#include "EarthRenderer.h"
#include "ResourceManager.h"
#include <iostream>
#include <cmath>
#include <sstream>
#include <cstring>

#define M_PI 3.14159265358979323846

#ifdef __EMSCRIPTEN__
#include <emscripten.h>
#include <emscripten/fetch.h>
#endif

// 瓦片下载功能使用Emscripten fetch实现

#define M_PI 3.14159265358979323846

#ifdef __EMSCRIPTEN__
// 全局EarthRenderer指针，用于在回调中访问
static EarthRenderer *g_earthRenderer = nullptr;

// 瓦片下载成功回调
void tileDownloadSuccess(emscripten_fetch_t *fetch)
{
    std::cout << "Successfully downloaded tile: " << fetch->url << " (" << fetch->numBytes << " bytes)" << std::endl;

    if (fetch->numBytes > 0 && fetch->data && g_earthRenderer)
    {
        std::cout << "Tile data received, size: " << fetch->numBytes << " bytes" << std::endl;

        // 解析URL获取瓦片坐标
        std::string url = fetch->url;
        int x = 0, y = 0, z = 0;

        // 简单的URL解析（假设Google Maps格式）
        size_t xPos = url.find("&x=");
        size_t yPos = url.find("&y=");
        size_t zPos = url.find("&z=");

        if (xPos != std::string::npos && yPos != std::string::npos && zPos != std::string::npos)
        {
            x = std::stoi(url.substr(xPos + 3, url.find("&", xPos + 3) - xPos - 3));
            y = std::stoi(url.substr(yPos + 3, url.find("&", yPos + 3) - yPos - 3));
            z = std::stoi(url.substr(zPos + 3, url.find("&", zPos + 3) - zPos - 3));

            std::cout << "Parsed tile coordinates: x=" << x << ", y=" << y << ", z=" << z << std::endl;

            // 创建真实的瓦片纹理
            g_earthRenderer->createRealTileTexture(x, y, z, (const uint8_t *)fetch->data, fetch->numBytes);
        }
    }

    emscripten_fetch_close(fetch);
}

// 瓦片下载失败回调
void tileDownloadError(emscripten_fetch_t *fetch)
{
    std::cout << "Failed to download tile: " << fetch->url << " (HTTP " << fetch->status << ")" << std::endl;
    emscripten_fetch_close(fetch);
}
#endif

EarthRenderer::EarthRenderer()
{
#ifdef __EMSCRIPTEN__
    g_earthRenderer = this;
#endif
}

EarthRenderer::~EarthRenderer()
{
#ifdef __EMSCRIPTEN__
    if (g_earthRenderer == this)
    {
        g_earthRenderer = nullptr;
    }
#endif
    cleanup();
}

bool EarthRenderer::initialize(Device device, TextureFormat swapChainFormat)
{
    m_device = device;

    // 生成球体几何
    generateSphere(64);

    // 创建顶点缓冲区
    BufferDescriptor vertexBufferDesc;
    vertexBufferDesc.size = m_vertices.size() * sizeof(EarthVertex);
    vertexBufferDesc.usage = BufferUsage::Vertex | BufferUsage::CopyDst;
    vertexBufferDesc.label = WGPUStringView{"Earth Vertex Buffer", WGPU_STRLEN};
    m_vertexBuffer = m_device.createBuffer(vertexBufferDesc);

    // 创建索引缓冲区
    BufferDescriptor indexBufferDesc;
    indexBufferDesc.size = m_indices.size() * sizeof(uint32_t);
    indexBufferDesc.usage = BufferUsage::Index | BufferUsage::CopyDst;
    indexBufferDesc.label = WGPUStringView{"Earth Index Buffer", WGPU_STRLEN};
    m_indexBuffer = m_device.createBuffer(indexBufferDesc);

    // 上传数据
    m_device.getQueue().writeBuffer(m_vertexBuffer, 0, m_vertices.data(), m_vertices.size() * sizeof(EarthVertex));
    m_device.getQueue().writeBuffer(m_indexBuffer, 0, m_indices.data(), m_indices.size() * sizeof(uint32_t));

    m_indexCount = static_cast<uint32_t>(m_indices.size());

    // 创建统一缓冲区
    BufferDescriptor uniformBufferDesc;
    uniformBufferDesc.size = sizeof(Uniforms);
    uniformBufferDesc.usage = BufferUsage::Uniform | BufferUsage::CopyDst;
    uniformBufferDesc.label = WGPUStringView{"Earth Uniform Buffer", WGPU_STRLEN};
    m_uniformBuffer = m_device.createBuffer(uniformBufferDesc);

    // 创建默认纹理（蓝色地球）
    TextureDescriptor textureDesc;
    textureDesc.size = {512, 512, 1};
    textureDesc.format = TextureFormat::RGBA8Unorm;
    textureDesc.usage = TextureUsage::TextureBinding | TextureUsage::CopyDst;
    textureDesc.label = WGPUStringView{"Default Earth Texture", WGPU_STRLEN};
    textureDesc.mipLevelCount = 1;
    textureDesc.sampleCount = 1;
    textureDesc.dimension = TextureDimension::_2D;
    m_defaultTexture = m_device.createTexture(textureDesc);

    // 填充默认纹理（简单的蓝色）
    std::vector<uint8_t> defaultTextureData(512 * 512 * 4);
    for (size_t i = 0; i < defaultTextureData.size(); i += 4)
    {
        defaultTextureData[i] = 70;      // R
        defaultTextureData[i + 1] = 130; // G
        defaultTextureData[i + 2] = 180; // B
        defaultTextureData[i + 3] = 255; // A
    }

    TexelCopyBufferLayout dataLayout;
    dataLayout.offset = 0;
    dataLayout.bytesPerRow = 512 * 4;
    dataLayout.rowsPerImage = 512;

    TexelCopyTextureInfo destination;
    destination.texture = m_defaultTexture;
    destination.mipLevel = 0;
    destination.origin = {0, 0, 0};
    destination.aspect = TextureAspect::All;

    m_device.getQueue().writeTexture(destination, defaultTextureData.data(),
                                     defaultTextureData.size(), dataLayout, {512, 512, 1});

    // 创建采样器
    SamplerDescriptor samplerDesc;
    samplerDesc.addressModeU = AddressMode::Repeat;
    samplerDesc.addressModeV = AddressMode::Repeat;
    samplerDesc.addressModeW = AddressMode::Repeat;
    samplerDesc.magFilter = FilterMode::Linear;
    samplerDesc.minFilter = FilterMode::Linear;
    samplerDesc.mipmapFilter = MipmapFilterMode::Linear;
    m_sampler = m_device.createSampler(samplerDesc);

    // 创建绑定组布局
    std::vector<BindGroupLayoutEntry> bindingLayoutEntries(3);

    // 统一缓冲区
    bindingLayoutEntries[0].binding = 0;
    bindingLayoutEntries[0].visibility = ShaderStage::Vertex | ShaderStage::Fragment;
    bindingLayoutEntries[0].buffer.type = BufferBindingType::Uniform;

    // 纹理
    bindingLayoutEntries[1].binding = 1;
    bindingLayoutEntries[1].visibility = ShaderStage::Fragment;
    bindingLayoutEntries[1].texture.sampleType = TextureSampleType::Float;
    bindingLayoutEntries[1].texture.viewDimension = TextureViewDimension::_2D;

    // 采样器
    bindingLayoutEntries[2].binding = 2;
    bindingLayoutEntries[2].visibility = ShaderStage::Fragment;
    bindingLayoutEntries[2].sampler.type = SamplerBindingType::Filtering;

    BindGroupLayoutDescriptor bindGroupLayoutDesc;
    bindGroupLayoutDesc.entryCount = bindingLayoutEntries.size();
    bindGroupLayoutDesc.entries = bindingLayoutEntries.data();
    m_bindGroupLayout = m_device.createBindGroupLayout(bindGroupLayoutDesc);

    // 创建绑定组
    std::vector<BindGroupEntry> bindingEntries(3);

    bindingEntries[0].binding = 0;
    bindingEntries[0].buffer = m_uniformBuffer;
    bindingEntries[0].size = sizeof(Uniforms);

    bindingEntries[1].binding = 1;
    bindingEntries[1].textureView = m_defaultTexture.createView();

    bindingEntries[2].binding = 2;
    bindingEntries[2].sampler = m_sampler;

    BindGroupDescriptor bindGroupDesc;
    bindGroupDesc.layout = m_bindGroupLayout;
    bindGroupDesc.entryCount = bindingEntries.size();
    bindGroupDesc.entries = bindingEntries.data();
    m_bindGroup = m_device.createBindGroup(bindGroupDesc);

    // 创建地球着色器
    m_shaderModule = ResourceManager::loadShaderModule("resources/earth_shader.wgsl", m_device);
    if (!m_shaderModule)
    {
        std::cout << "Failed to load earth shader, using default shader" << std::endl;
        // 使用默认着色器
        m_shaderModule = ResourceManager::loadShaderModule("resources/shader.wgsl", m_device);
        if (!m_shaderModule)
        {
            std::cout << "Failed to load default shader" << std::endl;
            return false;
        }
    }

    // 创建渲染管线
    RenderPipelineDescriptor pipelineDesc;
    pipelineDesc.label = WGPUStringView{"Earth Render Pipeline", WGPU_STRLEN};

    // 顶点阶段
    VertexState vertexState;
    vertexState.module = m_shaderModule;
    vertexState.entryPoint = WGPUStringView{"vs_main", WGPU_STRLEN};

    // 顶点属性
    std::vector<VertexAttribute> vertexAttribs(3);

    // Position
    vertexAttribs[0].shaderLocation = 0;
    vertexAttribs[0].format = VertexFormat::Float32x3;
    vertexAttribs[0].offset = offsetof(EarthVertex, position);

    // TexCoord
    vertexAttribs[1].shaderLocation = 1;
    vertexAttribs[1].format = VertexFormat::Float32x2;
    vertexAttribs[1].offset = offsetof(EarthVertex, texCoord);

    // Normal
    vertexAttribs[2].shaderLocation = 2;
    vertexAttribs[2].format = VertexFormat::Float32x3;
    vertexAttribs[2].offset = offsetof(EarthVertex, normal);

    VertexBufferLayout vertexBufferLayout;
    vertexBufferLayout.arrayStride = sizeof(EarthVertex);
    vertexBufferLayout.stepMode = VertexStepMode::Vertex;
    vertexBufferLayout.attributeCount = vertexAttribs.size();
    vertexBufferLayout.attributes = vertexAttribs.data();

    vertexState.bufferCount = 1;
    vertexState.buffers = &vertexBufferLayout;

    pipelineDesc.vertex = vertexState;

    // 片段阶段
    FragmentState fragmentState;
    fragmentState.module = m_shaderModule;
    fragmentState.entryPoint = WGPUStringView{"fs_main", WGPU_STRLEN};

    BlendState blendState;
    blendState.color.srcFactor = BlendFactor::SrcAlpha;
    blendState.color.dstFactor = BlendFactor::OneMinusSrcAlpha;
    blendState.color.operation = BlendOperation::Add;
    blendState.alpha.srcFactor = BlendFactor::Zero;
    blendState.alpha.dstFactor = BlendFactor::One;
    blendState.alpha.operation = BlendOperation::Add;

    ColorTargetState colorTarget;
    colorTarget.format = swapChainFormat;
    colorTarget.blend = &blendState;
    colorTarget.writeMask = ColorWriteMask::All;

    fragmentState.targetCount = 1;
    fragmentState.targets = &colorTarget;
    pipelineDesc.fragment = &fragmentState;

    // 深度模板状态
    DepthStencilState depthStencilState;
    depthStencilState.format = TextureFormat::Depth24Plus;
    depthStencilState.depthWriteEnabled = WGPUOptionalBool_True;
    depthStencilState.depthCompare = CompareFunction::Less;
    pipelineDesc.depthStencil = &depthStencilState;

    // 管线布局
    PipelineLayoutDescriptor layoutDesc;
    layoutDesc.bindGroupLayoutCount = 1;
    layoutDesc.bindGroupLayouts = (const WGPUBindGroupLayout *)&m_bindGroupLayout;
    PipelineLayout pipelineLayout = m_device.createPipelineLayout(layoutDesc);
    pipelineDesc.layout = pipelineLayout;

    // 多重采样
    pipelineDesc.multisample.count = 1;
    pipelineDesc.multisample.mask = ~0u;
    pipelineDesc.multisample.alphaToCoverageEnabled = false;

    // 图元状态
    pipelineDesc.primitive.topology = PrimitiveTopology::TriangleList;
    pipelineDesc.primitive.stripIndexFormat = IndexFormat::Undefined;
    pipelineDesc.primitive.frontFace = FrontFace::CCW;
    pipelineDesc.primitive.cullMode = CullMode::Back;

    m_pipeline = m_device.createRenderPipeline(pipelineDesc);

    std::cout << "EarthRenderer initialized successfully" << std::endl;

    return true;
}

void EarthRenderer::generateSphere(int segments)
{
    m_vertices.clear();
    m_indices.clear();

    // 生成球体顶点
    for (int lat = 0; lat <= segments; ++lat)
    {
        float theta = lat * M_PI / segments;
        float sinTheta = sin(theta);
        float cosTheta = cos(theta);

        for (int lon = 0; lon <= segments; ++lon)
        {
            float phi = lon * 2 * M_PI / segments;
            float sinPhi = sin(phi);
            float cosPhi = cos(phi);

            EarthVertex vertex;
            vertex.position.x = cosPhi * sinTheta;
            vertex.position.y = cosTheta;
            vertex.position.z = sinPhi * sinTheta;

            vertex.normal = vertex.position;

            // Web墨卡托投影的正确纹理坐标映射
            // 经度映射：-180° 到 +180° -> 0 到 1
            vertex.texCoord.x = (float)lon / segments;

            // 纬度映射：Web墨卡托使用特殊的纬度投影
            // 从球面坐标转换为墨卡托投影坐标
            float latitude = M_PI * 0.5f - theta; // 转换为纬度（-π/2 到 π/2）
            float mercatorY = log(tan(M_PI * 0.25f + latitude * 0.5f));

            // 归一化墨卡托Y坐标到[0,1]范围
            // Web墨卡托的Y范围大约是[-π, π]
            vertex.texCoord.y = 0.5f + mercatorY / (2.0f * M_PI);

            // 确保纹理坐标在有效范围内
            vertex.texCoord.y = std::max(0.0f, std::min(1.0f, vertex.texCoord.y));

            m_vertices.push_back(vertex);
        }
    }

    // 生成索引
    for (int lat = 0; lat < segments; ++lat)
    {
        for (int lon = 0; lon < segments; ++lon)
        {
            int first = lat * (segments + 1) + lon;
            int second = first + segments + 1;

            m_indices.push_back(first);
            m_indices.push_back(second);
            m_indices.push_back(first + 1);

            m_indices.push_back(second);
            m_indices.push_back(second + 1);
            m_indices.push_back(first + 1);
        }
    }
}

void EarthRenderer::render(RenderPassEncoder renderPass, const glm::mat4 &viewMatrix, const glm::mat4 &projMatrix)
{
    render(renderPass, viewMatrix, projMatrix, false);
}

void EarthRenderer::render(RenderPassEncoder renderPass, const glm::mat4 &viewMatrix, const glm::mat4 &projMatrix, bool wireframeMode)
{
    // 更新统一缓冲区
    Uniforms uniforms;
    uniforms.modelMatrix = glm::mat4(1.0f);
    uniforms.viewMatrix = viewMatrix;
    uniforms.projMatrix = projMatrix;
    uniforms.lightDirection = glm::normalize(glm::vec3(1.0f, 1.0f, 1.0f));
    uniforms.time = 0.0f;

    m_device.getQueue().writeBuffer(m_uniformBuffer, 0, &uniforms, sizeof(Uniforms));

    // 设置渲染管线和绑定组
    if (m_pipeline)
    {
        renderPass.setPipeline(m_pipeline);
        renderPass.setBindGroup(0, m_bindGroup, 0, nullptr);
        renderPass.setVertexBuffer(0, m_vertexBuffer, 0, m_vertices.size() * sizeof(EarthVertex));
        renderPass.setIndexBuffer(m_indexBuffer, IndexFormat::Uint32, 0, m_indices.size() * sizeof(uint32_t));

        if (wireframeMode)
        {
            // 线框模式：可以尝试绘制线条或使用特殊着色器
            // std::cout << "Earth rendering in wireframe mode" << std::endl;
        }

        renderPass.drawIndexed(m_indexCount, 1, 0, 0, 0);
    }
}

void EarthRenderer::update(float deltaTime)
{
    // 更新地球旋转等
    m_rotation.y += deltaTime * 0.1f; // 慢速旋转
}

std::string EarthRenderer::getTileUrl(int x, int y, int z)
{
    // 使用Google地图HTTP服务（注意：生产环境需要API密钥）
    std::vector<std::string> servers = {
        // Google Maps 卫星图像 (HTTP, JPG格式)
        "http://mt1.google.com/vt/lyrs=s&x=" + std::to_string(x) + "&y=" + std::to_string(y) + "&z=" + std::to_string(z) + "&format=jpg",
        "http://mt2.google.com/vt/lyrs=s&x=" + std::to_string(x) + "&y=" + std::to_string(y) + "&z=" + std::to_string(z) + "&format=jpg",
        "http://mt3.google.com/vt/lyrs=s&x=" + std::to_string(x) + "&y=" + std::to_string(y) + "&z=" + std::to_string(z) + "&format=jpg",
        // Google Maps 地图 (HTTP, JPG格式)
        "http://mt1.google.com/vt/lyrs=m&x=" + std::to_string(x) + "&y=" + std::to_string(y) + "&z=" + std::to_string(z) + "&format=jpg",
        // OpenStreetMap 作为备选 (HTTP)
        "http://tile.openstreetmap.org/" + std::to_string(z) + "/" + std::to_string(x) + "/" + std::to_string(y) + ".png"};

    // 简单的负载均衡，根据坐标选择服务器
    int serverIndex = (x + y + z) % servers.size();
    std::string url = servers[serverIndex];

    std::cout << "Generated Google Maps tile URL: " << url << std::endl;
    return url;
}

void EarthRenderer::loadTile(int x, int y, int z)
{
    std::string key = std::to_string(z) + "_" + std::to_string(x) + "_" + std::to_string(y);

    if (m_tiles.find(key) != m_tiles.end())
    {
        return; // 已经加载或正在加载
    }

    auto tile = std::make_unique<TileInfo>();
    tile->x = x;
    tile->y = y;
    tile->z = z;
    tile->url = getTileUrl(x, y, z);

    // 在移动之前保存URL用于输出
    std::string tileUrl = tile->url;

    m_tiles[key] = std::move(tile);

    std::cout << "Loading tile (" << x << ", " << y << ", " << z << ") from: " << tileUrl << std::endl;

    // 输出下载URL到控制台，便于调试
    std::cout << "Tile download URL: " << tileUrl << std::endl;

#ifdef __EMSCRIPTEN__
    // 使用Emscripten fetch进行真实的HTTP下载
    emscripten_fetch_attr_t attr;
    emscripten_fetch_attr_init(&attr);
    strcpy(attr.requestMethod, "GET");
    attr.attributes = EMSCRIPTEN_FETCH_LOAD_TO_MEMORY;
    attr.onsuccess = tileDownloadSuccess;
    attr.onerror = tileDownloadError;

    std::cout << "Starting HTTP fetch for: " << tileUrl << std::endl;
    emscripten_fetch(&attr, tileUrl.c_str());
#else
    std::cout << "Emscripten fetch not available, using mock texture" << std::endl;
#endif

    // 同时创建模拟纹理作为占位符
    createTileTexture(x, y, z);
}

void EarthRenderer::updateTiles(const glm::vec3 & /* cameraPos */)
{
    // 根据相机位置更新需要加载的瓦片
    // 这里简化处理，加载一些基础瓦片
    static bool firstCall = true;
    if (firstCall)
    {
        std::cout << "First call to updateTiles, loading initial tiles..." << std::endl;
        std::cout << "Current zoom level: " << m_currentZoomLevel << std::endl;
        firstCall = false;
    }

    // 根据zoom level加载合适数量的瓦片
    int tilesPerSide = std::min(4, 1 << m_currentZoomLevel); // 最多4x4瓦片

    for (int x = 0; x < tilesPerSide; ++x)
    {
        for (int y = 0; y < tilesPerSide; ++y)
        {
            loadTile(x, y, m_currentZoomLevel);
        }
    }

    std::cout << "Loading " << tilesPerSide << "x" << tilesPerSide << " tiles for zoom level " << m_currentZoomLevel << std::endl;

    // too many tiles will cause memory overflow
    // std::cout << "updateTiles completed, total tiles: " << m_tiles.size() << std::endl;
}

void EarthRenderer::createTileTexture(int x, int y, int z)
{
    // 创建模拟瓦片纹理，使用不同颜色表示不同瓦片
    const int textureSize = 256;
    std::vector<uint8_t> textureData(textureSize * textureSize * 4);

    // 根据瓦片坐标生成不同颜色和图案
    uint8_t r = static_cast<uint8_t>((x * 100) % 256);
    uint8_t g = static_cast<uint8_t>((y * 150) % 256);
    uint8_t b = static_cast<uint8_t>((z * 200) % 256);

    for (int row = 0; row < textureSize; ++row)
    {
        for (int col = 0; col < textureSize; ++col)
        {
            int i = row * textureSize + col;

            // 创建棋盘格图案来更清楚地显示瓦片边界
            bool checker = ((row / 32) + (col / 32)) % 2 == 0;

            if (checker)
            {
                textureData[i * 4 + 0] = r; // R
                textureData[i * 4 + 1] = g; // G
                textureData[i * 4 + 2] = b; // B
            }
            else
            {
                textureData[i * 4 + 0] = r / 2; // 暗一些的R
                textureData[i * 4 + 1] = g / 2; // 暗一些的G
                textureData[i * 4 + 2] = b / 2; // 暗一些的B
            }
            textureData[i * 4 + 3] = 255; // A
        }
    }

    // 创建纹理
    TextureDescriptor textureDesc;
    textureDesc.size = {textureSize, textureSize, 1};
    textureDesc.format = TextureFormat::RGBA8Unorm;
    textureDesc.usage = TextureUsage::TextureBinding | TextureUsage::CopyDst;
    textureDesc.mipLevelCount = 1;
    textureDesc.sampleCount = 1;
    textureDesc.dimension = TextureDimension::_2D;
    textureDesc.label = WGPUStringView{"Tile Texture", WGPU_STRLEN};

    Texture tileTexture = m_device.createTexture(textureDesc);

    // 上传纹理数据
    TexelCopyBufferLayout dataLayout;
    dataLayout.offset = 0;
    dataLayout.bytesPerRow = textureSize * 4;
    dataLayout.rowsPerImage = textureSize;

    TexelCopyTextureInfo destination;
    destination.texture = tileTexture;
    destination.mipLevel = 0;
    destination.origin = {0, 0, 0};
    destination.aspect = TextureAspect::All;

    m_device.getQueue().writeTexture(destination, textureData.data(),
                                     textureData.size(), dataLayout, {textureSize, textureSize, 1});

    // 缓存纹理
    if (m_tileTextures.size() >= static_cast<size_t>(m_maxTileTextures))
    {
        // 删除最老的纹理
        m_tileTextures.front().release();
        m_tileTextures.erase(m_tileTextures.begin());
    }
    m_tileTextures.push_back(tileTexture);

    // 更新绑定组以使用新的瓦片纹理
    updateBindGroupWithTileTexture(tileTexture);

    std::cout << "Created tile texture for (" << x << ", " << y << ", " << z << ")" << std::endl;
}

void EarthRenderer::createRealTileTexture(int x, int y, int z, const uint8_t *imageData, size_t dataSize)
{
    std::cout << "Creating real tile texture for (" << x << ", " << y << ", " << z << ") with " << dataSize << " bytes" << std::endl;

    // 注意：这里需要JPG解码库来解析图像数据
    // 目前我们创建一个基于下载数据的简化纹理，但要创建完整的地球纹理

    const int earthTextureSize = 1024; // 更大的地球纹理

    // 检查是否已有地球纹理，如果没有则创建
    static std::vector<uint8_t> earthTextureData;
    static bool textureInitialized = false;

    if (!textureInitialized)
    {
        earthTextureData.resize(earthTextureSize * earthTextureSize * 4);
        textureInitialized = true;

        // 使用下载数据的哈希值来生成纹理颜色，表示真实下载
        uint32_t hash = 0;
        for (size_t i = 0; i < std::min(dataSize, size_t(1024)); ++i)
        {
            hash = hash * 31 + imageData[i];
        }

        uint8_t r = static_cast<uint8_t>((hash >> 16) & 0xFF);
        uint8_t g = static_cast<uint8_t>((hash >> 8) & 0xFF);
        uint8_t b = static_cast<uint8_t>(hash & 0xFF);

        // 首先用基础地球颜色填充整个纹理
        for (int i = 0; i < earthTextureSize * earthTextureSize; ++i)
        {
            earthTextureData[i * 4 + 0] = 70; // 深蓝色海洋
            earthTextureData[i * 4 + 1] = 130;
            earthTextureData[i * 4 + 2] = 180;
            earthTextureData[i * 4 + 3] = 255;
        }

        // Web墨卡托坐标到球面纹理坐标的正确转换
        // 对于zoom level z，瓦片总数为 2^z x 2^z
        int tilesPerSide = 1 << z; // 2^z

        // 计算瓦片在纹理中的位置（归一化坐标）
        float tileU = (float)x / tilesPerSide;
        float tileV = (float)y / tilesPerSide;
        float tileWidth = 1.0f / tilesPerSide;
        float tileHeight = 1.0f / tilesPerSide;

        // 转换为纹理像素坐标
        int startX = (int)(tileU * earthTextureSize);
        int startY = (int)(tileV * earthTextureSize);
        int endX = (int)((tileU + tileWidth) * earthTextureSize);
        int endY = (int)((tileV + tileHeight) * earthTextureSize);

        // 确保不超出纹理边界
        startX = std::max(0, std::min(startX, earthTextureSize - 1));
        startY = std::max(0, std::min(startY, earthTextureSize - 1));
        endX = std::max(startX + 1, std::min(endX, earthTextureSize));
        endY = std::max(startY + 1, std::min(endY, earthTextureSize));

        std::cout << "Mapping tile (" << x << "," << y << "," << z << ") to texture region ("
                  << startX << "," << startY << ") - (" << endX << "," << endY << ")" << std::endl;

        if (startX < endX && startY < endY)
        {
            int regionWidth = endX - startX;
            int regionHeight = endY - startY;

            for (int row = 0; row < regionHeight; ++row)
            {
                for (int col = 0; col < regionWidth; ++col)
                {
                    int earthX = startX + col;
                    int earthY = startY + row;

                    // 确保在纹理边界内
                    if (earthX >= 0 && earthX < earthTextureSize && earthY >= 0 && earthY < earthTextureSize)
                    {
                        int earthIndex = earthY * earthTextureSize + earthX;

                        // 创建表示真实瓦片的图案
                        bool border = (row < 2 || row >= regionHeight - 2 || col < 2 || col >= regionWidth - 2);

                        if (border)
                        {
                            // 边框使用亮色表示这是真实下载的瓦片
                            earthTextureData[earthIndex * 4 + 0] = 255; // 亮黄色边框
                            earthTextureData[earthIndex * 4 + 1] = 255;
                            earthTextureData[earthIndex * 4 + 2] = 0;
                        }
                        else
                        {
                            // 内部使用基于真实数据的颜色
                            earthTextureData[earthIndex * 4 + 0] = r;
                            earthTextureData[earthIndex * 4 + 1] = g;
                            earthTextureData[earthIndex * 4 + 2] = b;
                        }
                        earthTextureData[earthIndex * 4 + 3] = 255; // A
                    }
                }
            }
        }

        // 创建WebGPU纹理
        TextureDescriptor textureDesc;
        textureDesc.size = {earthTextureSize, earthTextureSize, 1};
        textureDesc.format = TextureFormat::RGBA8Unorm;
        textureDesc.usage = TextureUsage::TextureBinding | TextureUsage::CopyDst;
        textureDesc.mipLevelCount = 1;
        textureDesc.sampleCount = 1;
        textureDesc.dimension = TextureDimension::_2D;
        textureDesc.label = WGPUStringView{"Real Earth Texture", WGPU_STRLEN};

        Texture realEarthTexture = m_device.createTexture(textureDesc);

        // 上传纹理数据
        TexelCopyBufferLayout dataLayout;
        dataLayout.offset = 0;
        dataLayout.bytesPerRow = earthTextureSize * 4;
        dataLayout.rowsPerImage = earthTextureSize;

        TexelCopyTextureInfo destination;
        destination.texture = realEarthTexture;
        destination.mipLevel = 0;
        destination.origin = {0, 0, 0};
        destination.aspect = TextureAspect::All;

        m_device.getQueue().writeTexture(destination, earthTextureData.data(),
                                         earthTextureData.size(), dataLayout, {earthTextureSize, earthTextureSize, 1});

        // 更新绑定组使用真实地球纹理
        updateBindGroupWithTileTexture(realEarthTexture);

        // 缓存纹理
        if (m_tileTextures.size() >= static_cast<size_t>(m_maxTileTextures))
        {
            // 删除最老的纹理
            m_tileTextures.front().release();
            m_tileTextures.erase(m_tileTextures.begin());
        }
        m_tileTextures.push_back(realEarthTexture);

        std::cout << "Created real tile texture with downloaded data hash: " << std::hex << hash << std::dec << std::endl;
    }
}

void EarthRenderer::updateBindGroupWithTileTexture(const Texture &tileTexture)
{
    // 创建纹理视图
    TextureViewDescriptor textureViewDesc;
    textureViewDesc.aspect = TextureAspect::All;
    textureViewDesc.baseArrayLayer = 0;
    textureViewDesc.arrayLayerCount = 1;
    textureViewDesc.baseMipLevel = 0;
    textureViewDesc.mipLevelCount = 1;
    textureViewDesc.dimension = TextureViewDimension::_2D;
    textureViewDesc.format = TextureFormat::RGBA8Unorm;
    textureViewDesc.label = WGPUStringView{"Tile Texture View", WGPU_STRLEN};

    TextureView tileTextureView = tileTexture.createView(textureViewDesc);

    // 重新创建绑定组
    if (m_bindGroup)
    {
        m_bindGroup.release();
    }

    std::vector<BindGroupEntry> bindingEntries(3);

    // 统一缓冲区
    bindingEntries[0].binding = 0;
    bindingEntries[0].buffer = m_uniformBuffer;
    bindingEntries[0].offset = 0;
    bindingEntries[0].size = sizeof(Uniforms);

    // 瓦片纹理
    bindingEntries[1].binding = 1;
    bindingEntries[1].textureView = tileTextureView;

    // 采样器
    bindingEntries[2].binding = 2;
    bindingEntries[2].sampler = m_sampler;

    BindGroupDescriptor bindGroupDesc;
    bindGroupDesc.layout = m_bindGroupLayout;
    bindGroupDesc.entryCount = bindingEntries.size();
    bindGroupDesc.entries = bindingEntries.data();
    m_bindGroup = m_device.createBindGroup(bindGroupDesc);

    std::cout << "Updated bind group with tile texture" << std::endl;
}

void EarthRenderer::cleanup()
{
    if (m_vertexBuffer)
        m_vertexBuffer.release();
    if (m_indexBuffer)
        m_indexBuffer.release();
    if (m_uniformBuffer)
        m_uniformBuffer.release();
    if (m_bindGroup)
        m_bindGroup.release();
    if (m_bindGroupLayout)
        m_bindGroupLayout.release();
    if (m_sampler)
        m_sampler.release();
    if (m_defaultTexture)
        m_defaultTexture.release();
    if (m_shaderModule)
        m_shaderModule.release();
    if (m_pipeline)
        m_pipeline.release();

    // 清理瓦片纹理
    for (auto &texture : m_tileTextures)
    {
        texture.release();
    }
    m_tileTextures.clear();
}
