#pragma once

#include <USG_Backend/RenderBackend.h>
#include <webgpu/webgpu.h>
#include <memory>
#include <vector>

#ifdef USG_PLATFORM_DESKTOP
#include <GLFW/glfw3.h>
#endif

namespace USG
{

    /**
     * @brief 简化的WebGPU渲染后端实现
     *
     * 专注于桌面端渲染的WebGPU后端
     */
    class WebGPUBackendSimple : public RenderBackend
    {
    public:
        WebGPUBackendSimple();
        ~WebGPUBackendSimple() override;

        // RenderBackend接口实现
        bool initialize(const BackendConfig &config) override;
        void cleanup() override;
        BackendType getBackendType() const override { return BackendType::WebGPU; }
        std::string getBackendName() const override { return "WebGPU_Simple"; }

        // 资源创建
        BackendBuffer *createBuffer(const BufferDesc &desc) override;
        BackendTexture *createTexture(const TextureDesc &desc) override;
        BackendShader *createShader(const ShaderDesc &desc) override;
        BackendPipeline *createPipeline(const PipelineDesc &desc) override;
        BackendDescriptorSet *createDescriptorSet() override;
        BackendCommandList *createCommandList() override;
        BackendFence *createFence() override;
        BackendSemaphore *createSemaphore() override;

        // 命令执行
        void executeCommandList(BackendCommandList *cmdList,
                                const std::vector<BackendSemaphore *> &waitSemaphores = {},
                                const std::vector<BackendSemaphore *> &signalSemaphores = {},
                                BackendFence *fence = nullptr) override;

        // 资源销毁
        void destroyBuffer(BackendBuffer *buffer) override;
        void destroyTexture(BackendTexture *texture) override;
        void destroyShader(BackendShader *shader) override;
        void destroyPipeline(BackendPipeline *pipeline) override;
        void destroyDescriptorSet(BackendDescriptorSet *descriptorSet) override;
        void destroyCommandList(BackendCommandList *cmdList) override;
        void destroyFence(BackendFence *fence) override;
        void destroySemaphore(BackendSemaphore *semaphore) override;

        // 其他抽象方法
        BackendDevice *getDevice() override { return nullptr; }
        void beginFrame() override {}
        void endFrame() override {}
        void present() override {}
        void waitIdle() override {}
        void setErrorCallback(std::function<void(const std::string &)> callback) override {}
        void setDebugCallback(std::function<void(const std::string &)> callback) override {}

        // WebGPU特定方法
        WGPUDevice getWGPUDevice() const { return _device; }
        WGPUQueue getQueue() const { return _queue; }
        WGPUSurface getSurface() const { return _surface; }

    private:
        bool initializeWebGPU();
        bool createSurface(void *nativeWindow);
        bool createDevice();
        bool createSwapchain(uint32_t width, uint32_t height);

        static void onDeviceError(WGPUDevice const *device, WGPUErrorType type, WGPUStringView message, void *userdata1, void *userdata2);
        static void onAdapterRequestEnded(WGPURequestAdapterStatus status, WGPUAdapter adapter, WGPUStringView message, void *userdata1, void *userdata2);
        static void onDeviceRequestEnded(WGPURequestDeviceStatus status, WGPUDevice device, WGPUStringView message, void *userdata1, void *userdata2);

        // 默认着色器代码
        std::string getDefaultVertexShaderWGSL();
        std::string getDefaultFragmentShaderWGSL();

    private:
        // WebGPU核心对象
        WGPUInstance _instance = nullptr;
        WGPUAdapter _adapter = nullptr;
        WGPUDevice _device = nullptr;
        WGPUQueue _queue = nullptr;
        WGPUSurface _surface = nullptr;

        // 配置
        BackendConfig _config;
        bool _initialized = false;

        // 同步对象
        struct RequestResult
        {
            bool completed = false;
            bool success = false;
            WGPUAdapter adapter = nullptr;
            WGPUDevice device = nullptr;
            std::string message;
        };

        static RequestResult _adapterRequest;
        static RequestResult _deviceRequest;
    };

    // 简化的WebGPU资源类
    class WebGPUBufferSimple : public BackendBuffer
    {
    public:
        WebGPUBufferSimple(WGPUBuffer buffer, const BufferDesc &desc);
        ~WebGPUBufferSimple() override;

        uint64_t getSize() const override { return _desc.size; }
        BufferUsage getUsage() const override { return _desc.usage; }
        MemoryProperty getMemoryProperties() const override { return _desc.memoryProperties; }

        WGPUBuffer getBuffer() const { return _buffer; }

    private:
        WGPUBuffer _buffer;
        BufferDesc _desc;
    };

    class WebGPUTextureSimple : public BackendTexture
    {
    public:
        WebGPUTextureSimple(WGPUTexture texture, const TextureDesc &desc);
        ~WebGPUTextureSimple() override;

        uint32_t getWidth() const override { return _desc.width; }
        uint32_t getHeight() const override { return _desc.height; }
        uint32_t getDepth() const override { return _desc.depth; }
        uint32_t getMipLevels() const override { return _desc.mipLevels; }
        uint32_t getArrayLayers() const override { return _desc.arrayLayers; }
        TextureFormat getFormat() const override { return _desc.format; }
        TextureUsage getUsage() const override { return _desc.usage; }

        WGPUTexture getTexture() const { return _texture; }

    private:
        WGPUTexture _texture;
        TextureDesc _desc;
    };

    class WebGPUShaderSimple : public BackendShader
    {
    public:
        WebGPUShaderSimple(WGPUShaderModule module, const ShaderDesc &desc);
        ~WebGPUShaderSimple() override;

        ShaderStage getStage() const override { return _desc.stage; }
        std::string getEntryPoint() const override { return _desc.entryPoint; }

        WGPUShaderModule getModule() const { return _module; }

    private:
        WGPUShaderModule _module;
        ShaderDesc _desc;
    };

    class WebGPUPipelineSimple : public BackendPipeline
    {
    public:
        WebGPUPipelineSimple(WGPURenderPipeline pipeline, const PipelineDesc &desc);
        ~WebGPUPipelineSimple() override;

        bool isComputePipeline() const override { return false; }

        WGPURenderPipeline getPipeline() const { return _pipeline; }

    private:
        WGPURenderPipeline _pipeline;
        PipelineDesc _desc;
    };

    class WebGPUDescriptorSetSimple : public BackendDescriptorSet
    {
    public:
        WebGPUDescriptorSetSimple(WGPUBindGroup bindGroup);
        ~WebGPUDescriptorSetSimple() override;

        void bindBuffer(uint32_t binding, BackendBuffer *buffer, size_t offset = 0, size_t size = 0) override {}
        void bindTexture(uint32_t binding, BackendTexture *texture) override {}
        void update() override {}

        WGPUBindGroup getBindGroup() const { return _bindGroup; }

    private:
        WGPUBindGroup _bindGroup;
    };

    class WebGPUCommandListSimple : public BackendCommandList
    {
    public:
        WebGPUCommandListSimple(WGPUCommandEncoder encoder);
        ~WebGPUCommandListSimple() override;

        void begin() override {}
        void end() override {}
        void reset() override {}
        void beginRenderPass(const RenderPassDesc &desc) override {}
        void endRenderPass() override {}
        void setPipeline(BackendPipeline *pipeline) override {}
        void setVertexBuffer(BackendBuffer *buffer, uint32_t binding = 0, size_t offset = 0) override {}
        void setIndexBuffer(BackendBuffer *buffer, IndexFormat format, uint64_t offset = 0) override {}
        void setDescriptorSet(BackendDescriptorSet *descriptorSet, uint32_t set = 0) override {}
        void draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance) override {}
        void drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t vertexOffset, uint32_t firstInstance) override {}
        void dispatch(uint32_t groupCountX, uint32_t groupCountY, uint32_t groupCountZ) override {}
        void barrier(const BarrierDesc &barrier) override {}
        void pushDebugGroup(const std::string &name) override {}
        void popDebugGroup() override {}
        void insertDebugMarker(const std::string &name) override {}

        WGPUCommandEncoder getEncoder() const { return _encoder; }

    private:
        WGPUCommandEncoder _encoder;
    };

    class WebGPUFenceSimple : public BackendFence
    {
    public:
        WebGPUFenceSimple() = default;
        ~WebGPUFenceSimple() override = default;

        bool wait(uint64_t timeoutNs = UINT64_MAX) override { return true; }
        void reset() override {}
        bool isSignaled() override { return true; }
    };

    class WebGPUSemaphoreSimple : public BackendSemaphore
    {
    public:
        WebGPUSemaphoreSimple() = default;
        ~WebGPUSemaphoreSimple() override = default;
    };

} // namespace USG
