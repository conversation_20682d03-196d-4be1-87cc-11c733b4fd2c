/**
 * @file main.cpp
 * @brief USG基础地球渲染示例
 * 
 * 这个示例展示了如何使用USG创建一个基础的地球渲染应用，
 * 演示了场景图的构建、WebGPU设备的使用以及渲染循环。
 */

#include <USG/USG.h>
#include <iostream>
#include <memory>

#ifdef USG_PLATFORM_DESKTOP
#include <GLFW/glfw3.h>
#include <glfw3webgpu.h>
#endif

/**
 * @brief 地球节点类
 * 
 * 继承自Transform节点，专门用于地球渲染
 */
class EarthNode : public usg::Transform {
public:
    EarthNode() {
        setName("Earth");
        
        // 创建地球几何体
        createEarthGeometry();
        
        // 设置地球材质
        createEarthMaterial();
    }
    
    /**
     * @brief 更新地球自转
     * @param deltaTime 时间增量
     */
    void updateRotation(double deltaTime) {
        static double totalTime = 0.0;
        totalTime += deltaTime;
        
        // 地球自转（24小时一圈，这里加速显示）
        float rotationAngle = static_cast<float>(totalTime * 0.1); // 加速10倍
        usg::mat4 rotation = usg::math::rotate(rotationAngle, usg::vec3(0.0f, 1.0f, 0.0f));
        setMatrix(rotation);
    }
    
    std::string className() const override { 
        return "EarthNode"; 
    }
    
private:
    void createEarthGeometry() {
        // TODO: 创建球体几何体
        // 这里应该创建一个球体网格
    }
    
    void createEarthMaterial() {
        // TODO: 创建地球材质
        // 包括地球纹理、法线贴图等
    }
};

/**
 * @brief 应用程序类
 */
class EarthApplication {
public:
    EarthApplication() = default;
    ~EarthApplication() = cleanup();
    
    /**
     * @brief 初始化应用程序
     * @return 是否成功
     */
    bool initialize() {
        std::cout << "Initializing USG Earth Application..." << std::endl;
        std::cout << "USG Version: " << usg::getVersion() << std::endl;
        std::cout << "Platform: " << (usg::isWebAssembly() ? "WebAssembly" : "Desktop") << std::endl;
        
        // 初始化USG库
        if (!usg::initialize()) {
            std::cerr << "Failed to initialize USG library" << std::endl;
            return false;
        }
        
#ifdef USG_PLATFORM_DESKTOP
        // 初始化GLFW
        if (!initializeGLFW()) {
            return false;
        }
#endif
        
        // 创建WebGPU设备
        if (!createWebGPUDevice()) {
            return false;
        }
        
        // 创建场景图
        if (!createSceneGraph()) {
            return false;
        }
        
        // 初始化渲染器
        if (!initializeRenderer()) {
            return false;
        }
        
        std::cout << "Application initialized successfully!" << std::endl;
        return true;
    }
    
    /**
     * @brief 运行应用程序
     */
    void run() {
        std::cout << "Starting render loop..." << std::endl;
        
#ifdef USG_PLATFORM_DESKTOP
        // 桌面平台的渲染循环
        while (!glfwWindowShouldClose(_window)) {
            glfwPollEvents();
            
            // 计算时间增量
            double currentTime = glfwGetTime();
            double deltaTime = currentTime - _lastFrameTime;
            _lastFrameTime = currentTime;
            
            // 更新和渲染
            update(deltaTime);
            render();
            
            glfwSwapBuffers(_window);
        }
#else
        // WebAssembly平台使用Emscripten的主循环
        emscripten_set_main_loop_arg([](void* userData) {
            auto* app = static_cast<EarthApplication*>(userData);
            
            // 计算时间增量
            double currentTime = emscripten_get_now() / 1000.0;
            double deltaTime = currentTime - app->_lastFrameTime;
            app->_lastFrameTime = currentTime;
            
            // 更新和渲染
            app->update(deltaTime);
            app->render();
        }, this, 0, 1);
#endif
    }
    
private:
#ifdef USG_PLATFORM_DESKTOP
    bool initializeGLFW() {
        if (!glfwInit()) {
            std::cerr << "Failed to initialize GLFW" << std::endl;
            return false;
        }
        
        // 创建窗口
        glfwWindowHint(GLFW_CLIENT_API, GLFW_NO_API);
        glfwWindowHint(GLFW_RESIZABLE, GLFW_TRUE);
        
        _window = glfwCreateWindow(1024, 768, "USG Earth Example", nullptr, nullptr);
        if (!_window) {
            std::cerr << "Failed to create GLFW window" << std::endl;
            glfwTerminate();
            return false;
        }
        
        return true;
    }
#endif
    
    bool createWebGPUDevice() {
        std::cout << "Creating WebGPU device..." << std::endl;
        
#ifdef USG_PLATFORM_DESKTOP
        // 桌面平台：从GLFW窗口创建表面
        usg::Surface surface = glfwGetWGPUSurface(_device->getInstance(), _window);
        _device = usg::WebGPUDevice::create(surface);
#else
        // WebAssembly平台：直接创建设备
        _device = usg::WebGPUDevice::create();
#endif
        
        if (!_device || !_device->isValid()) {
            std::cerr << "Failed to create WebGPU device" << std::endl;
            return false;
        }
        
        std::cout << "WebGPU device created successfully" << std::endl;
        std::cout << _device->getDeviceInfo() << std::endl;
        return true;
    }
    
    bool createSceneGraph() {
        std::cout << "Creating scene graph..." << std::endl;
        
        // 创建根节点
        _root = usg::make_ref<usg::Group>();
        _root->setName("Root");
        
        // 创建地球节点
        _earthNode = usg::make_ref<EarthNode>();
        
        // 构建场景层次
        _root->addChild(_earthNode);
        
        std::cout << "Scene graph created with " << _root->getNumChildren() << " children" << std::endl;
        return true;
    }
    
    bool initializeRenderer() {
        std::cout << "Initializing renderer..." << std::endl;
        
        // 创建访问者
        _updateVisitor = std::make_unique<usg::UpdateVisitor>();
        _cullVisitor = std::make_unique<usg::CullVisitor>();
        
        // 设置相机参数
        usg::vec3 eye(0.0f, 0.0f, 3.0f);
        usg::vec3 center(0.0f, 0.0f, 0.0f);
        usg::vec3 up(0.0f, 1.0f, 0.0f);
        
        usg::mat4 viewMatrix = usg::math::lookAt(eye, center, up);
        usg::mat4 projMatrix = usg::math::perspective(
            usg::math::radians(45.0f), 1024.0f / 768.0f, 0.1f, 100.0f);
        
        _cullVisitor->setViewMatrix(viewMatrix);
        _cullVisitor->setProjectionMatrix(projMatrix);
        
        std::cout << "Renderer initialized successfully" << std::endl;
        return true;
    }
    
    void update(double deltaTime) {
        // 更新地球自转
        if (_earthNode) {
            _earthNode->updateRotation(deltaTime);
        }
        
        // 更新访问者
        _updateVisitor->setFrameTime(_lastFrameTime);
        _updateVisitor->setDeltaTime(deltaTime);
        
        // 遍历场景图进行更新
        if (_root) {
            _root->accept(*_updateVisitor);
        }
    }
    
    void render() {
        // 裁剪遍历
        if (_root && _cullVisitor) {
            _cullVisitor->reset();
            _root->accept(*_cullVisitor);
        }
        
        // TODO: 渲染遍历
        // 这里应该创建RenderVisitor并执行渲染
        
        // 提交渲染命令
        if (_device) {
            _device->tick();
        }
    }
    
    void cleanup() {
        std::cout << "Cleaning up application..." << std::endl;
        
        // 清理场景图
        _earthNode.reset();
        _root.reset();
        
        // 清理访问者
        _updateVisitor.reset();
        _cullVisitor.reset();
        
        // 清理WebGPU设备
        _device.reset();
        
#ifdef USG_PLATFORM_DESKTOP
        // 清理GLFW
        if (_window) {
            glfwDestroyWindow(_window);
            _window = nullptr;
        }
        glfwTerminate();
#endif
        
        // 清理USG库
        usg::cleanup();
        
        std::cout << "Application cleaned up" << std::endl;
    }
    
private:
    // WebGPU设备
    usg::ref_ptr<usg::WebGPUDevice> _device;
    
    // 场景图
    usg::ref_ptr<usg::Group> _root;
    usg::ref_ptr<EarthNode> _earthNode;
    
    // 访问者
    std::unique_ptr<usg::UpdateVisitor> _updateVisitor;
    std::unique_ptr<usg::CullVisitor> _cullVisitor;
    
    // 时间管理
    double _lastFrameTime = 0.0;
    
#ifdef USG_PLATFORM_DESKTOP
    GLFWwindow* _window = nullptr;
#endif
};

/**
 * @brief 主函数
 */
int main() {
    try {
        EarthApplication app;
        
        if (!app.initialize()) {
            std::cerr << "Failed to initialize application" << std::endl;
            return -1;
        }
        
        app.run();
        
    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
