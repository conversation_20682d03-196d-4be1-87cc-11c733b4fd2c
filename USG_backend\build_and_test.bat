@echo off
setlocal enabledelayedexpansion

:: USG Backend 自动化编译和测试脚本
echo ========================================
echo USG Backend 自动化编译和测试
echo ========================================

:: 设置变量
set "BUILD_TYPE=Release"
set "PROJECT_ROOT=%~dp0"
set "BUILD_DIR=%PROJECT_ROOT%build_test"

echo 项目根目录: %PROJECT_ROOT%
echo 构建目录: %BUILD_DIR%
echo 构建类型: %BUILD_TYPE%
echo.

:: 检查CMake
echo 检查构建工具...
cmake --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到 CMake，请安装 CMake
    pause
    exit /b 1
)
echo ✅ 找到 CMake

:: 创建构建目录
if not exist "%BUILD_DIR%" (
    echo 创建构建目录...
    mkdir "%BUILD_DIR%"
    echo ✅ 构建目录已创建
)

:: 进入构建目录
cd /d "%BUILD_DIR%"

:: 配置项目
echo.
echo 配置 CMake 项目...
cmake .. -DCMAKE_BUILD_TYPE=%BUILD_TYPE% -DUSG_BUILD_SCENE_TEST=ON -DUSG_BUILD_WEBGPU_BACKEND=ON -DUSG_BUILD_VULKAN_BACKEND=ON
if errorlevel 1 (
    echo ❌ CMake 配置失败
    pause
    exit /b 1
)
echo ✅ CMake 配置完成

:: 编译项目
echo.
echo 编译项目...
cmake --build . --config %BUILD_TYPE% --target scene_test_app
if errorlevel 1 (
    echo ❌ 编译失败
    pause
    exit /b 1
)
echo ✅ 编译完成

:: 检查生成的文件
set "EXE_PATH=%BUILD_DIR%\%BUILD_TYPE%\scene_test_app.exe"
if exist "%EXE_PATH%" (
    echo ✅ 找到可执行文件: %EXE_PATH%
) else (
    echo ❌ 未找到可执行文件: %EXE_PATH%
    pause
    exit /b 1
)

:: 检查DLL文件
set "DLL_PATH=%BUILD_DIR%\%BUILD_TYPE%\wgpu_native.dll"
if exist "%DLL_PATH%" (
    echo ✅ 找到 wgpu_native.dll: %DLL_PATH%
) else (
    echo ⚠️  未找到 wgpu_native.dll，WebGPU 后端可能无法工作
)

:: 显示控制说明
echo.
echo ========================================
echo 程序控制说明:
echo   • 数字键 1: 切换到 WebGPU 后端
echo   • 数字键 2: 切换到 Vulkan 后端
echo   • 数字键 3: 切换到 OpenGL 后端
echo   • ESC: 退出程序
echo ========================================
echo.
echo 按任意键启动测试程序...
pause >nul

:: 启动程序
echo 启动测试程序...
"%EXE_PATH%"

if errorlevel 1 (
    echo ❌ 程序运行出错
) else (
    echo ✅ 程序正常退出
)

:: 返回原目录
cd /d "%PROJECT_ROOT%"

echo.
echo ✅ 构建和测试完成！
pause
