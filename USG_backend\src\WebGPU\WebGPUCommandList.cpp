#include "WebGPUBackend.h"

namespace USG
{

    // WebGPUCommandList实现
    WebGPUCommandList::WebGPUCommandList(WGPUDevice device, WebGPUBackend *backend)
        : _device(device), _backend(backend), _encoder(nullptr), _renderPassEncoder(nullptr),
          _computePassEncoder(nullptr), _commandBuffer(nullptr),
          _inRenderPass(false), _inComputePass(false), _recording(false)
    {
    }

    WebGPUCommandList::~WebGPUCommandList()
    {
        if (_commandBuffer)
        {
            wgpuCommandBufferRelease(_commandBuffer);
        }
        if (_renderPassEncoder)
        {
            wgpuRenderPassEncoderRelease(_renderPassEncoder);
        }
        if (_computePassEncoder)
        {
            wgpuComputePassEncoderRelease(_computePassEncoder);
        }
        if (_encoder)
        {
            wgpuCommandEncoderRelease(_encoder);
        }
    }

    void WebGPUCommandList::begin()
    {
        if (_recording)
        {
            return; // 已经在记录中
        }

        WGPUCommandEncoderDescriptor encoderDesc = {};
        encoderDesc.label = "USG Command Encoder";

        _encoder = wgpuDeviceCreateCommandEncoder(_device, &encoderDesc);
        _recording = true;
    }

    void WebGPUCommandList::end()
    {
        if (!_recording)
        {
            return;
        }

        // 结束当前的渲染通道或计算通道
        if (_inRenderPass)
        {
            endRenderPass();
        }
        if (_inComputePass)
        {
            // endComputePass(); // 需要实现
        }

        // 完成命令缓冲区
        if (_encoder)
        {
            WGPUCommandBufferDescriptor cmdBufferDesc = {};
            cmdBufferDesc.label = "USG Command Buffer";

            _commandBuffer = wgpuCommandEncoderFinish(_encoder, &cmdBufferDesc);

            wgpuCommandEncoderRelease(_encoder);
            _encoder = nullptr;
        }

        _recording = false;
    }

    void WebGPUCommandList::reset()
    {
        // 清理现有资源
        if (_commandBuffer)
        {
            wgpuCommandBufferRelease(_commandBuffer);
            _commandBuffer = nullptr;
        }
        if (_renderPassEncoder)
        {
            wgpuRenderPassEncoderRelease(_renderPassEncoder);
            _renderPassEncoder = nullptr;
        }
        if (_computePassEncoder)
        {
            wgpuComputePassEncoderRelease(_computePassEncoder);
            _computePassEncoder = nullptr;
        }
        if (_encoder)
        {
            wgpuCommandEncoderRelease(_encoder);
            _encoder = nullptr;
        }

        _inRenderPass = false;
        _inComputePass = false;
        _recording = false;
    }

    void WebGPUCommandList::beginRenderPass(const RenderPassDesc &desc)
    {
        if (!_encoder || _inRenderPass)
        {
            return;
        }

        WGPURenderPassDescriptor renderPassDesc = {};
        renderPassDesc.label = desc.label.empty() ? "USG Render Pass" : desc.label.c_str();

        // 设置颜色附件
        std::vector<WGPURenderPassColorAttachment> colorAttachments;
        for (const auto &colorAttachment : desc.colorAttachments)
        {
            WGPURenderPassColorAttachment attachment = {};

            if (colorAttachment.texture)
            {
                auto *webgpuTexture = static_cast<WebGPUTexture *>(colorAttachment.texture);
                attachment.view = webgpuTexture->getWGPUTextureView();
            }
            else
            {
                // 如果没有指定纹理，使用当前交换链纹理视图
                attachment.view = _backend->getCurrentTextureView();
                if (!attachment.view)
                {
                    std::cerr << "[WebGPUCommandList] Failed to get current texture view" << std::endl;
                    return;
                }
            }

            attachment.loadOp = colorAttachment.loadClear ? WGPULoadOp_Clear : WGPULoadOp_Load;
            attachment.storeOp = colorAttachment.storeResult ? WGPUStoreOp_Store : WGPUStoreOp_Discard;
            attachment.clearValue.r = colorAttachment.clearColor[0];
            attachment.clearValue.g = colorAttachment.clearColor[1];
            attachment.clearValue.b = colorAttachment.clearColor[2];
            attachment.clearValue.a = colorAttachment.clearColor[3];

            colorAttachments.push_back(attachment);

            std::cout << "[WebGPUCommandList] Added color attachment with clear color: ("
                      << colorAttachment.clearColor[0] << ", " << colorAttachment.clearColor[1]
                      << ", " << colorAttachment.clearColor[2] << ", " << colorAttachment.clearColor[3] << ")" << std::endl;
        }

        renderPassDesc.colorAttachmentCount = colorAttachments.size();
        renderPassDesc.colorAttachments = colorAttachments.data();

        // 设置深度模板附件
        WGPURenderPassDepthStencilAttachment depthStencilAttachment = {};
        if (desc.depthStencilAttachment.texture)
        {
            auto *webgpuTexture = static_cast<WebGPUTexture *>(desc.depthStencilAttachment.texture);
            depthStencilAttachment.view = webgpuTexture->getWGPUTextureView();
            depthStencilAttachment.depthLoadOp = desc.depthStencilAttachment.loadClearDepth ? WGPULoadOp_Clear : WGPULoadOp_Load;
            depthStencilAttachment.depthStoreOp = desc.depthStencilAttachment.storeDepth ? WGPUStoreOp_Store : WGPUStoreOp_Discard;
            depthStencilAttachment.depthClearValue = desc.depthStencilAttachment.clearDepth;
            depthStencilAttachment.stencilLoadOp = WGPULoadOp_Clear;
            depthStencilAttachment.stencilStoreOp = WGPUStoreOp_Discard;
            depthStencilAttachment.stencilClearValue = desc.depthStencilAttachment.clearStencil;

            renderPassDesc.depthStencilAttachment = &depthStencilAttachment;
        }

        _renderPassEncoder = wgpuCommandEncoderBeginRenderPass(_encoder, &renderPassDesc);
        _inRenderPass = true;
    }

    void WebGPUCommandList::endRenderPass()
    {
        if (!_inRenderPass || !_renderPassEncoder)
        {
            return;
        }

        wgpuRenderPassEncoderEnd(_renderPassEncoder);
        wgpuRenderPassEncoderRelease(_renderPassEncoder);
        _renderPassEncoder = nullptr;
        _inRenderPass = false;
    }

    void WebGPUCommandList::setPipeline(BackendPipeline *pipeline)
    {
        if (!pipeline)
        {
            return;
        }

        auto *webgpuPipeline = static_cast<WebGPUPipeline *>(pipeline);

        if (webgpuPipeline->isComputePipeline())
        {
            if (_computePassEncoder)
            {
                wgpuComputePassEncoderSetPipeline(_computePassEncoder, webgpuPipeline->getComputePipeline());
            }
        }
        else
        {
            if (_renderPassEncoder)
            {
                wgpuRenderPassEncoderSetPipeline(_renderPassEncoder, webgpuPipeline->getRenderPipeline());
            }
        }
    }

    void WebGPUCommandList::setVertexBuffer(BackendBuffer *buffer, uint32_t slot, size_t offset)
    {
        if (!buffer || !_renderPassEncoder)
        {
            return;
        }

        auto *webgpuBuffer = static_cast<WebGPUBuffer *>(buffer);
        wgpuRenderPassEncoderSetVertexBuffer(_renderPassEncoder, slot, webgpuBuffer->getWGPUBuffer(), offset, webgpuBuffer->getSize() - offset);
    }

    void WebGPUCommandList::setIndexBuffer(BackendBuffer *buffer, IndexFormat format, size_t offset)
    {
        if (!buffer || !_renderPassEncoder)
        {
            return;
        }

        auto *webgpuBuffer = static_cast<WebGPUBuffer *>(buffer);
        WGPUIndexFormat wgpuFormat = (format == IndexFormat::UInt16) ? WGPUIndexFormat_Uint16 : WGPUIndexFormat_Uint32;

        wgpuRenderPassEncoderSetIndexBuffer(_renderPassEncoder, webgpuBuffer->getWGPUBuffer(), wgpuFormat, offset, webgpuBuffer->getSize() - offset);
    }

    void WebGPUCommandList::setDescriptorSet(BackendDescriptorSet *descriptorSet, uint32_t slot)
    {
        if (!descriptorSet)
        {
            return;
        }

        auto *webgpuDescriptorSet = static_cast<WebGPUDescriptorSet *>(descriptorSet);
        WGPUBindGroup bindGroup = webgpuDescriptorSet->getWGPUBindGroup();

        if (bindGroup)
        {
            if (_renderPassEncoder)
            {
                wgpuRenderPassEncoderSetBindGroup(_renderPassEncoder, slot, bindGroup, 0, nullptr);
            }
            else if (_computePassEncoder)
            {
                wgpuComputePassEncoderSetBindGroup(_computePassEncoder, slot, bindGroup, 0, nullptr);
            }
        }
    }

    void WebGPUCommandList::draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance)
    {
        if (!_renderPassEncoder)
        {
            return;
        }

        wgpuRenderPassEncoderDraw(_renderPassEncoder, vertexCount, instanceCount, firstVertex, firstInstance);
    }

    void WebGPUCommandList::drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t vertexOffset, uint32_t firstInstance)
    {
        if (!_renderPassEncoder)
        {
            return;
        }

        wgpuRenderPassEncoderDrawIndexed(_renderPassEncoder, indexCount, instanceCount, firstIndex, vertexOffset, firstInstance);
    }

    void WebGPUCommandList::dispatch(uint32_t groupCountX, uint32_t groupCountY, uint32_t groupCountZ)
    {
        if (!_computePassEncoder)
        {
            return;
        }

        wgpuComputePassEncoderDispatchWorkgroups(_computePassEncoder, groupCountX, groupCountY, groupCountZ);
    }

    void WebGPUCommandList::barrier(const BarrierDesc &barrier)
    {
        // WebGPU自动处理大部分屏障，这里可以是空实现
        // 或者插入调试标记
    }

    void WebGPUCommandList::pushDebugGroup(const std::string &name)
    {
        if (_renderPassEncoder)
        {
            wgpuRenderPassEncoderPushDebugGroup(_renderPassEncoder, name.c_str());
        }
        else if (_computePassEncoder)
        {
            wgpuComputePassEncoderPushDebugGroup(_computePassEncoder, name.c_str());
        }
        else if (_encoder)
        {
            wgpuCommandEncoderPushDebugGroup(_encoder, name.c_str());
        }
    }

    void WebGPUCommandList::popDebugGroup()
    {
        if (_renderPassEncoder)
        {
            wgpuRenderPassEncoderPopDebugGroup(_renderPassEncoder);
        }
        else if (_computePassEncoder)
        {
            wgpuComputePassEncoderPopDebugGroup(_computePassEncoder);
        }
        else if (_encoder)
        {
            wgpuCommandEncoderPopDebugGroup(_encoder);
        }
    }

    void WebGPUCommandList::insertDebugMarker(const std::string &name)
    {
        if (_renderPassEncoder)
        {
            wgpuRenderPassEncoderInsertDebugMarker(_renderPassEncoder, name.c_str());
        }
        else if (_computePassEncoder)
        {
            wgpuComputePassEncoderInsertDebugMarker(_computePassEncoder, name.c_str());
        }
        else if (_encoder)
        {
            wgpuCommandEncoderInsertDebugMarker(_encoder, name.c_str());
        }
    }

} // namespace USG
