#pragma once

/**
 * @file WebGPUAdapter.h
 * @brief WebGPU库适配器，支持不同的WebGPU实现
 *
 * 根据编译时配置，自动选择合适的WebGPU头文件和实现：
 * - wgpu-native: 原生Rust实现
 * - Dawn: Google的C++实现
 * - EmDawn: WebAssembly优化版本
 * - Emscripten: 浏览器内置WebGPU
 */

// 根据编译配置选择WebGPU头文件
#if defined(USG_USE_WGPU_NATIVE)
// 使用wgpu-native
#include <webgpu/webgpu.h>
#define USG_WEBGPU_IMPL "wgpu-native"

#elif defined(USG_USE_DAWN)
// 使用Dawn
#include <dawn/webgpu.h>
#include <dawn/dawn_proc.h>
#define USG_WEBGPU_IMPL "Dawn"

#elif defined(USG_USE_EMDAWN)
// 使用EmDawn (WebAssembly)
#include <webgpu/webgpu.hpp>
#define USG_WEBGPU_IMPL "EmDawn"

#elif defined(USG_USE_EMSCRIPTEN_WEBGPU)
// 使用Emscripten内置WebGPU
#include <emscripten/html5_webgpu.h>
#define USG_WEBGPU_IMPL "Emscripten"

#elif defined(USG_USE_WEBGPU_DISTRIBUTION)
// 使用WebGPU-distribution
#include <webgpu/webgpu.hpp>
#define USG_WEBGPU_IMPL "WebGPU-distribution"

#else
#error "No WebGPU implementation selected"
#endif

#include <string>
#include <functional>

namespace USG
{

    /**
     * @brief WebGPU适配器类
     *
     * 提供统一的WebGPU接口，隐藏不同实现之间的差异
     */
    class WebGPUAdapter
    {
    public:
        /**
         * @brief 获取WebGPU实现名称
         * @return 实现名称字符串
         */
        static std::string getImplementationName()
        {
            return USG_WEBGPU_IMPL;
        }

        /**
         * @brief 初始化WebGPU
         * @return 是否成功
         */
        static bool initialize();

        /**
         * @brief 清理WebGPU
         */
        static void cleanup();

        /**
         * @brief 创建WebGPU实例
         * @return WebGPU实例
         */
        static WGPUInstance createInstance();

        /**
         * @brief 请求适配器
         * @param instance WebGPU实例
         * @param options 适配器选项
         * @param callback 回调函数
         */
        static void requestAdapter(WGPUInstance instance,
                                   const WGPURequestAdapterOptions *options,
                                   std::function<void(WGPUAdapter)> callback);

        /**
         * @brief 请求设备
         * @param adapter 适配器
         * @param descriptor 设备描述符
         * @param callback 回调函数
         */
        static void requestDevice(WGPUAdapter adapter,
                                  const WGPUDeviceDescriptor *descriptor,
                                  std::function<void(WGPUDevice)> callback);

        /**
         * @brief 设置错误回调
         * @param callback 错误回调函数
         */
        static void setErrorCallback(std::function<void(WGPUErrorType, const char *)> callback);

        /**
         * @brief 处理事件（主要用于异步操作）
         */
        static void processEvents();

        /**
         * @brief 检查WebGPU是否可用
         * @return 是否可用
         */
        static bool isAvailable();

        /**
         * @brief 获取支持的特性
         * @return 特性列表
         */
        static std::vector<std::string> getSupportedFeatures();

    private:
        static bool _initialized;
        static std::function<void(WGPUErrorType, const char *)> _errorCallback;

        // 不同实现的特定初始化
        static bool initializeWGPUNative();
        static bool initializeDawn();
        static bool initializeEmDawn();
        static bool initializeEmscripten();
        static bool initializeWebGPUDistribution();
    };

// 便利宏定义
#define USG_WEBGPU_CHECK_RESULT(result)                                                          \
    do                                                                                           \
    {                                                                                            \
        if (!(result))                                                                           \
        {                                                                                        \
            if (WebGPUAdapter::_errorCallback)                                                   \
            {                                                                                    \
                WebGPUAdapter::_errorCallback(WGPUErrorType_Unknown, "WebGPU operation failed"); \
            }                                                                                    \
        }                                                                                        \
    } while (0)

    // 平台特定的辅助函数
    namespace WebGPUPlatform
    {

#if defined(USG_PLATFORM_DESKTOP)
        /**
         * @brief 从GLFW窗口创建表面
         * @param instance WebGPU实例
         * @param window GLFW窗口
         * @return WebGPU表面
         */
        WGPUSurface createSurfaceFromGLFW(WGPUInstance instance, void *window);

#elif defined(USG_PLATFORM_WASM)
        /**
         * @brief 从Canvas创建表面
         * @param instance WebGPU实例
         * @param canvasId Canvas元素ID
         * @return WebGPU表面
         */
        WGPUSurface createSurfaceFromCanvas(WGPUInstance instance, const char *canvasId);
#endif

        /**
         * @brief 获取首选的交换链格式
         * @param surface 表面
         * @return 首选格式
         */
        WGPUTextureFormat getPreferredSwapChainFormat(WGPUSurface surface);

        /**
         * @brief 获取表面能力
         * @param surface 表面
         * @param adapter 适配器
         * @return 表面能力
         */
        WGPUSurfaceCapabilities getSurfaceCapabilities(WGPUSurface surface, WGPUAdapter adapter);
    }

    // 调试辅助函数
    namespace WebGPUDebug
    {
        /**
         * @brief 错误类型转字符串
         * @param errorType 错误类型
         * @return 错误字符串
         */
        const char *errorTypeToString(WGPUErrorType errorType);

        /**
         * @brief 设备丢失原因转字符串
         * @param reason 丢失原因
         * @return 原因字符串
         */
        const char *deviceLostReasonToString(WGPUDeviceLostReason reason);

        /**
         * @brief 缓冲区映射状态转字符串
         * @param status 映射状态
         * @return 状态字符串
         */
        const char *bufferMapAsyncStatusToString(WGPUMapAsyncStatus status);

        /**
         * @brief 启用详细日志
         * @param enabled 是否启用
         */
        void setVerboseLogging(bool enabled);

        /**
         * @brief 打印设备信息
         * @param device WebGPU设备
         */
        void printDeviceInfo(WGPUDevice device);

        /**
         * @brief 打印适配器信息
         * @param adapter WebGPU适配器
         */
        void printAdapterInfo(WGPUAdapter adapter);
    }

    // 性能分析辅助
    namespace WebGPUProfiler
    {
        /**
         * @brief 开始性能分析
         */
        void beginProfiling();

        /**
         * @brief 结束性能分析
         */
        void endProfiling();

        /**
         * @brief 标记事件
         * @param name 事件名称
         */
        void markEvent(const char *name);

        /**
         * @brief 获取性能统计
         * @return 统计信息
         */
        std::string getPerformanceStats();
    }

} // namespace USG

// 版本兼容性检查
#if defined(USG_USE_WGPU_NATIVE)
  // wgpu-native特定的版本检查
#ifndef WGPU_NATIVE_VERSION
// wgpu-native version not detected, compatibility may vary
#endif

#elif defined(USG_USE_DAWN)
  // Dawn特定的版本检查
#ifndef DAWN_VERSION_MAJOR
// Dawn version not detected, compatibility may vary
#endif
#endif

// 编译时特性检查
namespace USG
{
    namespace WebGPUFeatures
    {
        // 检查是否支持计算着色器
        constexpr bool hasComputeShaders()
        {
#if defined(USG_USE_WGPU_NATIVE) || defined(USG_USE_DAWN)
            return true;
#else
            return false; // 某些WebAssembly实现可能不支持
#endif
        }

        // 检查是否支持时间戳查询
        constexpr bool hasTimestampQueries()
        {
#if defined(USG_USE_WGPU_NATIVE) || defined(USG_USE_DAWN)
            return true;
#else
            return false;
#endif
        }

        // 检查是否支持多重采样
        constexpr bool hasMultisampling()
        {
            return true; // 所有实现都应该支持
        }

        // 检查是否支持纹理压缩
        constexpr bool hasTextureCompression()
        {
#if defined(USG_USE_WGPU_NATIVE) || defined(USG_USE_DAWN)
            return true;
#else
            return false; // WebAssembly可能有限制
#endif
        }
    }
}

// 使用示例注释
/*
使用示例:

// 初始化
if (!USG::WebGPUAdapter::initialize()) {
    // 处理初始化失败
}

// 创建实例
WGPUInstance instance = USG::WebGPUAdapter::createInstance();

// 请求适配器
USG::WebGPUAdapter::requestAdapter(instance, nullptr, [](WGPUAdapter adapter) {
    // 请求设备
    USG::WebGPUAdapter::requestDevice(adapter, nullptr, [](WGPUDevice device) {
        // 使用设备进行渲染
    });
});

// 清理
USG::WebGPUAdapter::cleanup();
*/
