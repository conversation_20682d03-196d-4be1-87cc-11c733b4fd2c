#!/usr/bin/env python3
"""
将SPIR-V二进制文件转换为C++数组的工具
"""

import sys
import os

def convert_spirv_to_cpp_array(spirv_file, array_name):
    """将SPIR-V文件转换为C++数组"""
    try:
        with open(spirv_file, 'rb') as f:
            data = f.read()
        
        # 确保数据长度是4的倍数（SPIR-V是32位对齐的）
        if len(data) % 4 != 0:
            print(f"警告: {spirv_file} 的大小不是4的倍数")
            return None
        
        # 转换为uint32_t数组
        uint32_data = []
        for i in range(0, len(data), 4):
            # 小端序读取uint32_t
            value = int.from_bytes(data[i:i+4], byteorder='little')
            uint32_data.append(value)
        
        # 生成C++代码
        cpp_code = f"static const uint32_t {array_name}[] = {{\n"
        
        # 每行8个元素
        for i in range(0, len(uint32_data), 8):
            line_data = uint32_data[i:i+8]
            hex_values = [f"0x{value:08x}" for value in line_data]
            cpp_code += "    " + ", ".join(hex_values)
            if i + 8 < len(uint32_data):
                cpp_code += ","
            cpp_code += "\n"
        
        cpp_code += "};\n"
        
        return cpp_code, len(uint32_data)
        
    except Exception as e:
        print(f"错误: 无法处理文件 {spirv_file}: {e}")
        return None

def main():
    # 转换顶点着色器
    vert_result = convert_spirv_to_cpp_array("vert.spv", "vertexShaderSPIRV")
    if vert_result:
        vert_code, vert_size = vert_result
        print("// 顶点着色器SPIR-V代码")
        print(vert_code)
        print(f"// 大小: {vert_size} uint32_t 元素\n")
    
    # 转换片段着色器
    frag_result = convert_spirv_to_cpp_array("frag.spv", "fragmentShaderSPIRV")
    if frag_result:
        frag_code, frag_size = frag_result
        print("// 片段着色器SPIR-V代码")
        print(frag_code)
        print(f"// 大小: {frag_size} uint32_t 元素")

if __name__ == "__main__":
    main()
