#pragma once

#include <USG_Backend/RenderBackend.h>
#include <memory>
#include <iostream>

namespace USG
{

    /**
     * @brief 模拟渲染后端
     *
     * 用于演示后端切换功能的模拟实现
     */
    class MockBackend : public RenderBackend
    {
    public:
        MockBackend(BackendType type, const std::string &name);
        ~MockBackend() override;

        // RenderBackend接口实现
        bool initialize(const BackendConfig &config) override;
        void cleanup() override;
        BackendType getBackendType() const override { return _backendType; }
        std::string getBackendName() const override { return _backendName; }

        // 资源创建
        BackendBuffer *createBuffer(const BufferDesc &desc) override;
        BackendTexture *createTexture(const TextureDesc &desc) override;
        BackendShader *createShader(const ShaderDesc &desc) override;
        BackendPipeline *createPipeline(const PipelineDesc &desc) override;
        BackendDescriptorSet *createDescriptorSet() override;
        BackendCommandList *createCommandList() override;
        BackendFence *createFence() override;
        BackendSemaphore *createSemaphore() override;

        // 命令执行
        void executeCommandList(BackendCommandList *cmdList,
                                const std::vector<BackendSemaphore *> &waitSemaphores = {},
                                const std::vector<BackendSemaphore *> &signalSemaphores = {},
                                BackendFence *fence = nullptr) override;

        // 资源销毁
        void destroyBuffer(BackendBuffer *buffer) override;
        void destroyTexture(BackendTexture *texture) override;
        void destroyShader(BackendShader *shader) override;
        void destroyPipeline(BackendPipeline *pipeline) override;
        void destroyDescriptorSet(BackendDescriptorSet *descriptorSet) override;
        void destroyCommandList(BackendCommandList *cmdList) override;
        void destroyFence(BackendFence *fence) override;
        void destroySemaphore(BackendSemaphore *semaphore) override;

        // 其他抽象方法
        BackendDevice *getDevice() override { return nullptr; }
        void beginFrame() override {}
        void endFrame() override {}
        void present() override {}
        void waitIdle() override {}
        void setErrorCallback(std::function<void(const std::string &)> callback) override {}
        void setDebugCallback(std::function<void(const std::string &)> callback) override {}

    private:
        BackendType _backendType;
        std::string _backendName;
        bool _initialized = false;
        uint32_t _resourceCounter = 0;
    };

    // 简化的模拟资源类
    class MockResource
    {
    public:
        MockResource(uint32_t id) : _id(id) {}
        uint32_t getId() const { return _id; }

    private:
        uint32_t _id;
    };

    class MockBuffer : public BackendBuffer, public MockResource
    {
    public:
        MockBuffer(uint32_t id, const BufferDesc &desc) : MockResource(id), _desc(desc) {}
        uint64_t getSize() const override { return _desc.size; }
        BufferUsage getUsage() const override { return _desc.usage; }
        MemoryProperty getMemoryProperties() const override { return _desc.memoryProperties; }

    private:
        BufferDesc _desc;
    };

    class MockTexture : public BackendTexture, public MockResource
    {
    public:
        MockTexture(uint32_t id, const TextureDesc &desc) : MockResource(id), _desc(desc) {}
        uint32_t getWidth() const override { return _desc.width; }
        uint32_t getHeight() const override { return _desc.height; }
        uint32_t getDepth() const override { return _desc.depth; }
        uint32_t getMipLevels() const override { return _desc.mipLevels; }
        uint32_t getArrayLayers() const override { return _desc.arrayLayers; }
        TextureFormat getFormat() const override { return _desc.format; }
        TextureUsage getUsage() const override { return _desc.usage; }

    private:
        TextureDesc _desc;
    };

    class MockShader : public BackendShader, public MockResource
    {
    public:
        MockShader(uint32_t id, const ShaderDesc &desc) : MockResource(id), _desc(desc) {}
        ShaderStage getStage() const override { return _desc.stage; }
        std::string getEntryPoint() const override { return _desc.entryPoint; }

    private:
        ShaderDesc _desc;
    };

    class MockPipeline : public BackendPipeline, public MockResource
    {
    public:
        MockPipeline(uint32_t id, const PipelineDesc &desc) : MockResource(id), _desc(desc) {}
        bool isComputePipeline() const override { return _desc.isComputePipeline; }

    private:
        PipelineDesc _desc;
    };

    class MockDescriptorSet : public BackendDescriptorSet, public MockResource
    {
    public:
        MockDescriptorSet(uint32_t id) : MockResource(id) {}
        void bindBuffer(uint32_t binding, BackendBuffer *buffer, size_t offset = 0, size_t size = 0) override {}
        void bindTexture(uint32_t binding, BackendTexture *texture) override {}
        void update() override {}
    };

    class MockCommandList : public BackendCommandList, public MockResource
    {
    public:
        MockCommandList(uint32_t id) : MockResource(id) {}

        void begin() override {}
        void end() override {}
        void reset() override {}
        void beginRenderPass(const RenderPassDesc &desc) override {}
        void endRenderPass() override {}
        void setPipeline(BackendPipeline *pipeline) override {}
        void setVertexBuffer(BackendBuffer *buffer, uint32_t binding = 0, size_t offset = 0) override {}
        void setIndexBuffer(BackendBuffer *buffer, IndexFormat format, uint64_t offset = 0) override {}
        void setDescriptorSet(BackendDescriptorSet *descriptorSet, uint32_t set = 0) override {}
        void draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance) override {}
        void drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t vertexOffset, uint32_t firstInstance) override {}
        void dispatch(uint32_t groupCountX, uint32_t groupCountY, uint32_t groupCountZ) override {}
        void barrier(const BarrierDesc &barrier) override {}
        void pushDebugGroup(const std::string &name) override {}
        void popDebugGroup() override {}
        void insertDebugMarker(const std::string &name) override {}
    };

    class MockFence : public BackendFence, public MockResource
    {
    public:
        MockFence(uint32_t id) : MockResource(id) {}
        bool wait(uint64_t timeoutNs = UINT64_MAX) override { return true; }
        void reset() override {}
        bool isSignaled() override { return true; }
    };

    class MockSemaphore : public BackendSemaphore, public MockResource
    {
    public:
        MockSemaphore(uint32_t id) : MockResource(id) {}
    };

} // namespace USG
