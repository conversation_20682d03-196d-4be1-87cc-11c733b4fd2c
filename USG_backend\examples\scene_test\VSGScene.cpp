#include "VSGScene.h"
#include <iostream>
#include <cmath>

namespace USG
{

    VSGScene::VSGScene()
    {
    }

    VSGScene::~VSGScene()
    {
        cleanup();
    }

    bool VSGScene::initialize(RenderBackend *backend)
    {
        if (!backend)
        {
            std::cerr << "[VSGScene] Invalid backend" << std::endl;
            return false;
        }

        _backend = backend;

        std::cout << "[VSGScene] Initializing VSG scene..." << std::endl;

#ifdef USG_VSG_ADAPTER_AVAILABLE
        // 创建VSG设备适配器
        _vsgDevice = std::make_unique<VSG_Device>();
        if (!_vsgDevice->initialize())
        {
            std::cerr << "[VSGScene] Failed to initialize VSG device" << std::endl;
            return false;
        }

        // 创建VSG场景渲染器
        _vsgRenderer = std::make_unique<VSG_SceneRenderer>();
        if (!_vsgRenderer->initialize(backend))
        {
            std::cerr << "[VSGScene] Failed to initialize VSG renderer" << std::endl;
            return false;
        }

        std::cout << "[VSGScene] VSG adapters initialized successfully" << std::endl;
#else
        std::cout << "[VSGScene] VSG adapter not available, using fallback implementation" << std::endl;
#endif

        // 创建着色器
        ShaderDesc vertexShaderDesc;
        vertexShaderDesc.stage = ShaderStage::Vertex;
        vertexShaderDesc.entryPoint = "main";
        vertexShaderDesc.label = "VSG Vertex Shader";
        // 在实际应用中，这里应该是编译后的着色器字节码
        vertexShaderDesc.bytecode = {}; // 空字节码，由后端处理

        // 临时注释掉source字段
        /*vertexShaderDesc.source = R"(
        #version 450
        layout(location = 0) in vec3 position;
        layout(location = 1) in vec3 color;

        layout(location = 0) out vec3 fragColor;

        void main() {
            gl_Position = vec4(position, 1.0);
            fragColor = color;
        }
    )"; */

        _vertexShader.reset(_backend->createShader(vertexShaderDesc));
        if (!_vertexShader)
        {
            std::cerr << "[VSGScene] Failed to create vertex shader" << std::endl;
            return false;
        }

        ShaderDesc fragmentShaderDesc;
        fragmentShaderDesc.stage = ShaderStage::Fragment;
        fragmentShaderDesc.entryPoint = "main";
        fragmentShaderDesc.label = "VSG Fragment Shader";
        // 在实际应用中，这里应该是编译后的着色器字节码
        fragmentShaderDesc.bytecode = {}; // 空字节码，由后端处理

        // 临时注释掉source字段
        /*fragmentShaderDesc.source = R"(
        #version 450
        layout(location = 0) in vec3 fragColor;
        layout(location = 0) out vec4 outColor;

        void main() {
            outColor = vec4(fragColor, 1.0);
        }
    )"; */

        _fragmentShader.reset(_backend->createShader(fragmentShaderDesc));
        if (!_fragmentShader)
        {
            std::cerr << "[VSGScene] Failed to create fragment shader" << std::endl;
            return false;
        }

        std::cout << "[VSGScene] Shaders created successfully" << std::endl;

        // 创建渲染管线
        PipelineDesc pipelineDesc;
        pipelineDesc.vertexShader = _vertexShader.get();
        pipelineDesc.fragmentShader = _fragmentShader.get();
        pipelineDesc.isComputePipeline = false;
        pipelineDesc.label = "VSGScene Pipeline";

        _pipeline.reset(_backend->createPipeline(pipelineDesc));
        if (!_pipeline)
        {
            std::cerr << "[VSGScene] Failed to create pipeline" << std::endl;
            return false;
        }

        std::cout << "[VSGScene] Pipeline created successfully" << std::endl;

        // 创建几何体
        createTriangleGeometry();
        createCubeGeometry();

        // 创建测试场景
        createTestScene();

        _initialized = true;
        std::cout << "[VSGScene] VSG scene initialized successfully" << std::endl;

        return true;
    }

    void VSGScene::cleanup()
    {
        if (!_initialized)
            return;

        std::cout << "[VSGScene] Cleaning up VSG scene..." << std::endl;

        // 清理渲染资源
        if (_triangleVertexBuffer && _backend)
        {
            _backend->destroyBuffer(_triangleVertexBuffer.release());
        }
        if (_triangleIndexBuffer && _backend)
        {
            _backend->destroyBuffer(_triangleIndexBuffer.release());
        }
        if (_cubeVertexBuffer && _backend)
        {
            _backend->destroyBuffer(_cubeVertexBuffer.release());
        }
        if (_cubeIndexBuffer && _backend)
        {
            _backend->destroyBuffer(_cubeIndexBuffer.release());
        }
        if (_pipeline && _backend)
        {
            _backend->destroyPipeline(_pipeline.release());
        }
        if (_vertexShader && _backend)
        {
            _backend->destroyShader(_vertexShader.release());
        }
        if (_fragmentShader && _backend)
        {
            _backend->destroyShader(_fragmentShader.release());
        }

#ifdef USG_VSG_ADAPTER_AVAILABLE
        // 清理VSG适配器
        _vsgRenderer.reset();
        _vsgDevice.reset();
#endif

        _sceneObjects.clear();
        _initialized = false;

        std::cout << "[VSGScene] VSG scene cleaned up" << std::endl;
    }

    void VSGScene::createTestScene()
    {
        std::cout << "[VSGScene] Creating test scene with VSG objects..." << std::endl;

        // 添加三角形对象
        SceneObject triangle;
        triangle.name = "VSG Triangle";
        triangle.vertexCount = 3;
        triangle.indexCount = 3;
        triangle.visible = true;
        // 设置变换矩阵（向左移动）
        triangle.transform[12] = -1.0f; // X偏移
        addSceneObject(triangle);

        // 添加立方体对象
        SceneObject cube;
        cube.name = "VSG Cube";
        cube.vertexCount = 8;
        cube.indexCount = 36;
        cube.visible = true;
        // 设置变换矩阵（向右移动）
        cube.transform[12] = 1.0f; // X偏移
        addSceneObject(cube);

        std::cout << "[VSGScene] Created " << _sceneObjects.size() << " VSG scene objects" << std::endl;
    }

    void VSGScene::render(BackendCommandList *commandList, const float *viewMatrix, const float *projMatrix)
    {
        if (!_initialized || !commandList || !_pipeline)
            return;

        // 开始命令缓冲区记录
        commandList->begin();

#ifdef USG_VSG_ADAPTER_AVAILABLE
        // 使用VSG渲染器
        if (_vsgRenderer)
        {
            _vsgRenderer->renderFrame();
        }
#endif

        // 开始渲染通道
        RenderPassDesc renderPassDesc;
        renderPassDesc.clearColor[0] = 0.2f;
        renderPassDesc.clearColor[1] = 0.3f;
        renderPassDesc.clearColor[2] = 0.4f;
        renderPassDesc.clearColor[3] = 1.0f;
        renderPassDesc.hasDepthStencil = true;
        renderPassDesc.clearDepth = 1.0f;

        commandList->beginRenderPass(renderPassDesc);

        // 设置管线
        commandList->setPipeline(_pipeline.get());

        // 渲染所有场景对象
        for (const auto &object : _sceneObjects)
        {
            if (!object.visible)
                continue;

            std::cout << "[VSGScene] Rendering VSG object: " << object.name << std::endl;

            // 根据对象类型选择缓冲区
            if (object.name.find("Triangle") != std::string::npos && _triangleVertexBuffer)
            {
                commandList->setVertexBuffer(_triangleVertexBuffer.get(), 0, 0);
                if (_triangleIndexBuffer && object.indexCount > 0)
                {
                    commandList->setIndexBuffer(_triangleIndexBuffer.get(), IndexFormat::UInt32, 0);
                    commandList->drawIndexed(object.indexCount, 1, 0, 0, 0);
                }
                else
                {
                    commandList->draw(object.vertexCount, 1, 0, 0);
                }
            }
            else if (object.name.find("Cube") != std::string::npos && _cubeVertexBuffer)
            {
                commandList->setVertexBuffer(_cubeVertexBuffer.get(), 0, 0);
                if (_cubeIndexBuffer && object.indexCount > 0)
                {
                    commandList->setIndexBuffer(_cubeIndexBuffer.get(), IndexFormat::UInt32, 0);
                    commandList->drawIndexed(object.indexCount, 1, 0, 0, 0);
                }
                else
                {
                    commandList->draw(object.vertexCount, 1, 0, 0);
                }
            }
        }

        commandList->endRenderPass();

        // 结束命令缓冲区记录
        commandList->end();

        updateRenderStats();
        std::cout << "[VSGScene] Rendered " << _sceneObjects.size() << " VSG objects" << std::endl;
    }

    void VSGScene::update(double deltaTime)
    {
        _totalTime += deltaTime;

        // 简单的动画：旋转对象
        for (auto &object : _sceneObjects)
        {
            if (object.name.find("Cube") != std::string::npos)
            {
                float angle = static_cast<float>(_totalTime);
                float cosA = std::cos(angle);
                float sinA = std::sin(angle);

                // 绕Y轴旋转
                object.transform[0] = cosA;
                object.transform[2] = sinA;
                object.transform[8] = -sinA;
                object.transform[10] = cosA;
            }
        }
    }

    void VSGScene::addSceneObject(const SceneObject &object)
    {
        _sceneObjects.push_back(object);
        std::cout << "[VSGScene] Added VSG scene object: " << object.name << std::endl;
    }

    void VSGScene::setCameraPosition(float x, float y, float z)
    {
        _cameraPosition[0] = x;
        _cameraPosition[1] = y;
        _cameraPosition[2] = z;
    }

    void VSGScene::setCameraTarget(float x, float y, float z)
    {
        _cameraTarget[0] = x;
        _cameraTarget[1] = y;
        _cameraTarget[2] = z;
    }

    void VSGScene::createTriangleGeometry()
    {
        // 三角形顶点数据 (位置 + 颜色)
        float triangleVertices[] = {
            // 位置          // 颜色
            0.0f, 0.5f, 0.0f, 1.0f, 0.0f, 0.0f,   // 顶部 - 红色
            -0.5f, -0.5f, 0.0f, 0.0f, 1.0f, 0.0f, // 左下 - 绿色
            0.5f, -0.5f, 0.0f, 0.0f, 0.0f, 1.0f   // 右下 - 蓝色
        };

        uint32_t triangleIndices[] = {0, 1, 2};

        // 创建顶点缓冲区
        BufferDesc vertexBufferDesc;
        vertexBufferDesc.size = sizeof(triangleVertices);
        vertexBufferDesc.usage = BufferUsage::Vertex;
        vertexBufferDesc.memoryProperties = MemoryProperty::HostVisible;

        _triangleVertexBuffer.reset(_backend->createBuffer(vertexBufferDesc));

        // 创建索引缓冲区
        BufferDesc indexBufferDesc;
        indexBufferDesc.size = sizeof(triangleIndices);
        indexBufferDesc.usage = BufferUsage::Index;
        indexBufferDesc.memoryProperties = MemoryProperty::HostVisible;

        _triangleIndexBuffer.reset(_backend->createBuffer(indexBufferDesc));

        std::cout << "[VSGScene] Created VSG triangle geometry: 3 vertices, 3 indices" << std::endl;
    }

    void VSGScene::createCubeGeometry()
    {
        // 立方体顶点数据 (位置 + 颜色)
        float cubeVertices[] = {
            // 前面
            -0.3f, -0.3f, 0.3f, 1.0f, 0.0f, 0.0f,
            0.3f, -0.3f, 0.3f, 0.0f, 1.0f, 0.0f,
            0.3f, 0.3f, 0.3f, 0.0f, 0.0f, 1.0f,
            -0.3f, 0.3f, 0.3f, 1.0f, 1.0f, 0.0f,
            // 后面
            -0.3f, -0.3f, -0.3f, 1.0f, 0.0f, 1.0f,
            0.3f, -0.3f, -0.3f, 0.0f, 1.0f, 1.0f,
            0.3f, 0.3f, -0.3f, 1.0f, 1.0f, 1.0f,
            -0.3f, 0.3f, -0.3f, 0.5f, 0.5f, 0.5f};

        uint32_t cubeIndices[] = {
            // 前面
            0, 1, 2, 2, 3, 0,
            // 后面
            4, 5, 6, 6, 7, 4,
            // 左面
            7, 3, 0, 0, 4, 7,
            // 右面
            1, 5, 6, 6, 2, 1,
            // 上面
            3, 2, 6, 6, 7, 3,
            // 下面
            0, 1, 5, 5, 4, 0};

        // 创建顶点缓冲区
        BufferDesc vertexBufferDesc;
        vertexBufferDesc.size = sizeof(cubeVertices);
        vertexBufferDesc.usage = BufferUsage::Vertex;
        vertexBufferDesc.memoryProperties = MemoryProperty::HostVisible;

        _cubeVertexBuffer.reset(_backend->createBuffer(vertexBufferDesc));

        // 创建索引缓冲区
        BufferDesc indexBufferDesc;
        indexBufferDesc.size = sizeof(cubeIndices);
        indexBufferDesc.usage = BufferUsage::Index;
        indexBufferDesc.memoryProperties = MemoryProperty::HostVisible;

        _cubeIndexBuffer.reset(_backend->createBuffer(indexBufferDesc));

        std::cout << "[VSGScene] Created VSG cube geometry: 8 vertices, 36 indices" << std::endl;
    }

    void VSGScene::updateRenderStats()
    {
        _renderStats.drawCalls = 0;
        _renderStats.triangleCount = 0;
        _renderStats.vertexCount = 0;

        for (const auto &object : _sceneObjects)
        {
            if (object.visible)
            {
                _renderStats.drawCalls++;
                _renderStats.vertexCount += object.vertexCount;
                if (object.indexCount > 0)
                {
                    _renderStats.triangleCount += object.indexCount / 3;
                }
                else
                {
                    _renderStats.triangleCount += object.vertexCount / 3;
                }
            }
        }
    }

} // namespace USG
