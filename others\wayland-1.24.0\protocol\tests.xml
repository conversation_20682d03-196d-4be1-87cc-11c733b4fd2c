<?xml version="1.0" encoding="UTF-8"?>
<protocol name="build_time_wayland_tests">

  <copyright>
    Copyright © 2017 Samsung Electronics Co., Ltd

    Permission is hereby granted, free of charge, to any person
    obtaining a copy of this software and associated documentation files
    (the "Software"), to deal in the Software without restriction,
    including without limitation the rights to use, copy, modify, merge,
    publish, distribute, sublicense, and/or sell copies of the Software,
    and to permit persons to whom the Software is furnished to do so,
    subject to the following conditions:

    The above copyright notice and this permission notice (including the
    next paragraph) shall be included in all copies or substantial
    portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
    NONINFRINGEMENT.  IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
    BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
    ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
    CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
    SOFTWARE.
  </copyright>

  <interface name="fd_passer" version="2">
    <description summary="Sends an event with an fd">
      A trivial interface for fd passing tests.
    </description>

    <request name="destroy" type="destructor"/>

    <event name="pre_fd"/>

    <event name="fd">
      <description summary="passes a file descriptor"/>
      <arg name="fd" type="fd" summary="file descriptor"/>
    </event>

    <!-- Version 2 additions -->
    <request name="conjoin" since="2">
      <description summary="register another fd passer with this one">
	Tells this fd passer object about another one to send events
	to for more complicated fd leak tests.
      </description>
      <arg name="passer" type="object" interface="fd_passer"/>
    </request>
  </interface>
</protocol>
