#pragma once

#include <USG_Backend/BackendTypes.h>
#include <USG_Backend/RenderBackend.h>
#include <memory>
#include <vector>
#include <functional>

// 前向声明VSG类型（避免直接依赖VSG头文件）
namespace vsg {
    class Node;
    class Camera;
    class Group;
    class MatrixTransform;
    class Geometry;
    class StateGroup;
    class CommandGraph;
    class RenderGraph;
    class View;
    class Window;
    class Viewer;
    class CompileTraversal;
    class RecordTraversal;
}

namespace USG {

    // 前向声明
    class RenderBackend;
    class VSG_BackendBridge;

    /**
     * @brief VSG场景渲染器
     * 
     * 负责将VSG场景图渲染到不同的后端（WebGPU、Vulkan等）
     */
    class VSG_SceneRenderer {
    public:
        /**
         * @brief 场景节点信息
         */
        struct SceneNode {
            std::shared_ptr<vsg::Node> node;
            std::string name;
            bool visible = true;
        };

        /**
         * @brief 相机配置
         */
        struct CameraConfig {
            float fov = 45.0f;
            float nearPlane = 0.1f;
            float farPlane = 1000.0f;
            float aspectRatio = 16.0f / 9.0f;
            
            // 相机位置和方向
            float position[3] = {0.0f, 0.0f, 5.0f};
            float target[3] = {0.0f, 0.0f, 0.0f};
            float up[3] = {0.0f, 1.0f, 0.0f};
        };

        /**
         * @brief 渲染配置
         */
        struct RenderConfig {
            uint32_t windowWidth = 800;
            uint32_t windowHeight = 600;
            bool enableVSync = true;
            bool enableMSAA = false;
            uint32_t msaaSamples = 4;
            float clearColor[4] = {0.2f, 0.3f, 0.4f, 1.0f};
        };

    public:
        VSG_SceneRenderer();
        ~VSG_SceneRenderer();

        /**
         * @brief 初始化渲染器
         * @param backend 渲染后端
         * @param config 渲染配置
         * @return 是否成功
         */
        bool initialize(RenderBackend* backend, const RenderConfig& config = {});

        /**
         * @brief 清理渲染器
         */
        void cleanup();

        /**
         * @brief 设置场景根节点
         * @param root 场景根节点
         */
        void setSceneRoot(std::shared_ptr<vsg::Node> root);

        /**
         * @brief 添加场景节点
         * @param node 场景节点
         * @param name 节点名称
         */
        void addSceneNode(std::shared_ptr<vsg::Node> node, const std::string& name = "");

        /**
         * @brief 移除场景节点
         * @param name 节点名称
         */
        void removeSceneNode(const std::string& name);

        /**
         * @brief 设置相机配置
         * @param config 相机配置
         */
        void setCameraConfig(const CameraConfig& config);

        /**
         * @brief 设置渲染配置
         * @param config 渲染配置
         */
        void setRenderConfig(const RenderConfig& config);

        /**
         * @brief 渲染一帧
         * @return 是否成功
         */
        bool renderFrame();

        /**
         * @brief 调整窗口大小
         * @param width 新宽度
         * @param height 新高度
         */
        void resize(uint32_t width, uint32_t height);

        /**
         * @brief 获取当前后端类型
         * @return 后端类型
         */
        BackendType getCurrentBackendType() const;

        /**
         * @brief 切换渲染后端
         * @param newBackend 新的渲染后端
         * @return 是否成功
         */
        bool switchBackend(RenderBackend* newBackend);

        /**
         * @brief 获取场景节点列表
         * @return 场景节点列表
         */
        const std::vector<SceneNode>& getSceneNodes() const { return _sceneNodes; }

        /**
         * @brief 获取相机配置
         * @return 相机配置
         */
        const CameraConfig& getCameraConfig() const { return _cameraConfig; }

        /**
         * @brief 获取渲染配置
         * @return 渲染配置
         */
        const RenderConfig& getRenderConfig() const { return _renderConfig; }

        /**
         * @brief 设置帧回调函数
         * @param callback 回调函数
         */
        void setFrameCallback(std::function<void(double deltaTime)> callback);

        /**
         * @brief 获取渲染统计信息
         */
        struct RenderStats {
            uint32_t frameCount = 0;
            double frameTime = 0.0;
            double fps = 0.0;
            uint32_t triangleCount = 0;
            uint32_t drawCalls = 0;
        };

        /**
         * @brief 获取渲染统计信息
         * @return 渲染统计信息
         */
        const RenderStats& getRenderStats() const { return _renderStats; }

    private:
        // 内部方法
        bool createVSGObjects();
        bool setupCamera();
        bool setupRenderGraph();
        bool compileScene();
        void updateStats(double deltaTime);
        void rebuildScene();

    private:
        // 渲染后端
        RenderBackend* _backend = nullptr;
        std::unique_ptr<VSG_BackendBridge> _bridge;

        // VSG对象
        std::shared_ptr<vsg::Group> _sceneRoot;
        std::shared_ptr<vsg::Camera> _camera;
        std::shared_ptr<vsg::RenderGraph> _renderGraph;
        std::shared_ptr<vsg::CommandGraph> _commandGraph;
        std::shared_ptr<vsg::View> _view;
        std::shared_ptr<vsg::CompileTraversal> _compile;
        std::shared_ptr<vsg::RecordTraversal> _record;

        // 场景数据
        std::vector<SceneNode> _sceneNodes;
        CameraConfig _cameraConfig;
        RenderConfig _renderConfig;

        // 状态
        bool _initialized = false;
        bool _sceneChanged = false;

        // 统计信息
        RenderStats _renderStats;
        double _lastFrameTime = 0.0;
        std::function<void(double)> _frameCallback;
    };

} // namespace USG
