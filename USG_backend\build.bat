@echo off
setlocal enabledelayedexpansion

echo ========================================
echo USG Backend 编译脚本
echo ========================================

:: 设置编译参数
set BUILD_TYPE=Release
set BUILD_DIR=build_desk
set INSTALL_DIR=redist_desk
set WEBGPU_IMPL=wgpu-native

:: 解析命令行参数
:parse_args
if "%~1"=="" goto :start_build
if "%~1"=="--debug" (
    set BUILD_TYPE=Debug
    shift
    goto :parse_args
)
if "%~1"=="--build-dir" (
    set BUILD_DIR=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--webgpu" (
    set WEBGPU_IMPL=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    echo 用法: build.bat [选项]
    echo 选项:
    echo   --debug              编译Debug版本 (默认: Release)
    echo   --build-dir DIR      指定编译目录 (默认: build_desk)
    echo   --webgpu IMPL        指定WebGPU实现 (wgpu-native/dawn/distribution)
    echo   --help               显示此帮助信息
    exit /b 0
)
shift
goto :parse_args

:start_build
echo 编译配置:
echo   编译类型: %BUILD_TYPE%
echo   编译目录: %BUILD_DIR%
echo   安装目录: %INSTALL_DIR%
echo   WebGPU实现: %WEBGPU_IMPL%
echo.

:: 检查必要工具
echo 检查编译环境...
where cmake >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到CMake，请确保CMake已安装并在PATH中
    exit /b 1
)

where cl >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到MSVC编译器，请运行vcvarsall.bat或在Visual Studio命令提示符中执行
    exit /b 1
)

:: 检查vcpkg
if not exist "C:\dev\vcpkg\vcpkg.exe" (
    echo 警告: 未找到vcpkg，某些依赖可能无法自动安装
)

:: 创建编译目录
if not exist "%BUILD_DIR%" (
    echo 创建编译目录: %BUILD_DIR%
    mkdir "%BUILD_DIR%"
)

:: 进入编译目录
cd "%BUILD_DIR%"

:: 设置WebGPU选项
set CMAKE_WEBGPU_OPTIONS=
if "%WEBGPU_IMPL%"=="wgpu-native" (
    set CMAKE_WEBGPU_OPTIONS=-DUSG_WEBGPU_USE_WGPU_NATIVE=ON -DUSG_WEBGPU_USE_DAWN=OFF -DUSG_WEBGPU_USE_EMDAWN=OFF
) else if "%WEBGPU_IMPL%"=="dawn" (
    set CMAKE_WEBGPU_OPTIONS=-DUSG_WEBGPU_USE_WGPU_NATIVE=OFF -DUSG_WEBGPU_USE_DAWN=ON -DUSG_WEBGPU_USE_EMDAWN=OFF
) else if "%WEBGPU_IMPL%"=="distribution" (
    set CMAKE_WEBGPU_OPTIONS=-DUSG_WEBGPU_USE_WGPU_NATIVE=OFF -DUSG_WEBGPU_USE_DAWN=OFF -DUSG_WEBGPU_USE_EMDAWN=OFF
) else (
    echo 错误: 不支持的WebGPU实现: %WEBGPU_IMPL%
    echo 支持的实现: wgpu-native, dawn, distribution
    exit /b 1
)

:: 配置CMake
echo.
echo 配置CMake...
cmake .. ^
    -G "Visual Studio 17 2022" ^
    -A x64 ^
    -DCMAKE_BUILD_TYPE=%BUILD_TYPE% ^
    -DCMAKE_INSTALL_PREFIX=../%INSTALL_DIR% ^
    -DUSG_BUILD_WEBGPU_BACKEND=ON ^
    -DUSG_BUILD_VULKAN_BACKEND=OFF ^
    -DUSG_BUILD_OPENGL_BACKEND=OFF ^
    -DUSG_BUILD_VSG_ADAPTER=ON ^
    -DUSG_BUILD_OSG_ADAPTER=OFF ^
    -DUSG_BUILD_EXAMPLES=ON ^
    -DUSG_BUILD_TESTS=ON ^
    -DUSG_BUILD_DOCS=OFF ^
    -DUSG_ENABLE_VALIDATION=ON ^
    -DUSG_ENABLE_PROFILING=OFF ^
    %CMAKE_WEBGPU_OPTIONS%

if errorlevel 1 (
    echo 错误: CMake配置失败
    exit /b 1
)

:: 编译项目
echo.
echo 开始编译...
cmake --build . --config %BUILD_TYPE% --parallel

if errorlevel 1 (
    echo 错误: 编译失败
    exit /b 1
)

:: 安装/复制文件到发布目录
echo.
echo 安装文件到发布目录...
cmake --install . --config %BUILD_TYPE%

if errorlevel 1 (
    echo 警告: 安装过程出现问题，但编译成功
)

:: 复制运行时依赖
echo.
echo 复制运行时依赖...

:: 创建发布目录结构
if not exist "../%INSTALL_DIR%/bin" mkdir "../%INSTALL_DIR%/bin"
if not exist "../%INSTALL_DIR%/lib" mkdir "../%INSTALL_DIR%/lib"
if not exist "../%INSTALL_DIR%/include" mkdir "../%INSTALL_DIR%/include"

:: 复制编译生成的文件
if exist "%BUILD_TYPE%\*.exe" (
    copy "%BUILD_TYPE%\*.exe" "../%INSTALL_DIR%/bin/" >nul 2>&1
)
if exist "%BUILD_TYPE%\*.dll" (
    copy "%BUILD_TYPE%\*.dll" "../%INSTALL_DIR%/bin/" >nul 2>&1
)
if exist "%BUILD_TYPE%\*.lib" (
    copy "%BUILD_TYPE%\*.lib" "../%INSTALL_DIR%/lib/" >nul 2>&1
)

:: 复制测试程序
if exist "tests\%BUILD_TYPE%\*.exe" (
    copy "tests\%BUILD_TYPE%\*.exe" "../%INSTALL_DIR%/bin/" >nul 2>&1
)

:: 复制示例程序
if exist "examples\*\%BUILD_TYPE%\*.exe" (
    for /d %%i in (examples\*) do (
        if exist "%%i\%BUILD_TYPE%\*.exe" (
            copy "%%i\%BUILD_TYPE%\*.exe" "../%INSTALL_DIR%/bin/" >nul 2>&1
        )
    )
)

:: 复制wgpu-native库（如果使用）
if "%WEBGPU_IMPL%"=="wgpu-native" (
    if exist "..\third_party\wgpu-native\lib\wgpu_native.dll" (
        copy "..\third_party\wgpu-native\lib\wgpu_native.dll" "../%INSTALL_DIR%/bin/" >nul 2>&1
        echo 已复制wgpu-native运行时库
    )
)

:: 返回原目录
cd ..

:: 显示编译结果
echo.
echo ========================================
echo 编译完成!
echo ========================================
echo 编译类型: %BUILD_TYPE%
echo 编译目录: %BUILD_DIR%
echo 发布目录: %INSTALL_DIR%
echo WebGPU实现: %WEBGPU_IMPL%
echo.

:: 列出生成的文件
echo 生成的文件:
if exist "%INSTALL_DIR%\bin" (
    echo 可执行文件:
    dir /b "%INSTALL_DIR%\bin\*.exe" 2>nul
)
if exist "%INSTALL_DIR%\lib" (
    echo 库文件:
    dir /b "%INSTALL_DIR%\lib\*.lib" 2>nul
)

echo.
echo 运行测试:
echo   cd %INSTALL_DIR%\bin
echo   test_webgpu_backend.exe
echo.
echo 运行示例:
echo   cd %INSTALL_DIR%\bin
echo   vsg_earth.exe
echo.

:: 询问是否运行测试
set /p RUN_TEST="是否运行测试程序? (y/n): "
if /i "%RUN_TEST%"=="y" (
    if exist "%INSTALL_DIR%\bin\test_webgpu_backend.exe" (
        echo.
        echo 运行测试程序...
        cd "%INSTALL_DIR%\bin"
        test_webgpu_backend.exe
        cd ..\..
    ) else (
        echo 测试程序不存在: %INSTALL_DIR%\bin\test_webgpu_backend.exe
    )
)

echo.
echo 编译脚本执行完成!
pause
