# USG 架构设计文档

## 1. 整体架构概览

USG采用分层架构设计，从底层WebGPU API到高层场景图API，提供清晰的抽象层次。

```mermaid
graph TB
    subgraph "应用层 (Application Layer)"
        App[Application]
        Viewer[Viewer]
        Scene[Scene Manager]
    end
    
    subgraph "场景图层 (Scene Graph Layer)"
        Root[Root Node]
        Group[Group Node]
        Transform[Transform Node]
        Geometry[Geometry Node]
        StateSet[StateSet]
    end
    
    subgraph "访问者系统 (Visitor System)"
        CullVisitor[Cull Visitor]
        RenderVisitor[Render Visitor]
        UpdateVisitor[Update Visitor]
        ComputeVisitor[Compute Visitor]
    end
    
    subgraph "渲染抽象层 (Render Abstraction)"
        RenderGraph[Render Graph]
        CommandBuffer[Command Buffer]
        RenderPass[Render Pass]
        Pipeline[Pipeline State]
    end
    
    subgraph "WebGPU封装层 (WebGPU Wrapper)"
        Device[Device Manager]
        Buffer[Buffer Manager]
        Texture[Texture Manager]
        Shader[Shader Manager]
    end
    
    subgraph "资源管理 (Resource Management)"
        ResourceCache[Resource Cache]
        AssetLoader[Asset Loader]
        MemoryPool[Memory Pool]
    end
    
    App --> Viewer
    Viewer --> Root
    Root --> Group
    Group --> Transform
    Transform --> Geometry
    
    CullVisitor --> RenderVisitor
    RenderVisitor --> UpdateVisitor
    
    RenderGraph --> CommandBuffer
    CommandBuffer --> RenderPass
    RenderPass --> Pipeline
    
    Device --> Buffer
    Device --> Texture
    Device --> Shader
```

## 2. 核心设计原则

### 2.1 现代C++设计
- **RAII**: 资源获取即初始化，自动管理GPU资源
- **智能指针**: 使用ref_ptr实现引用计数和自动内存管理
- **移动语义**: 充分利用C++11/14/17的移动语义优化性能
- **模板元编程**: 编译期优化和类型安全

### 2.2 接口设计原则
- **最小接口**: 每个类只暴露必要的公共接口
- **组合优于继承**: 优先使用组合而非深层继承
- **依赖注入**: 通过构造函数或setter注入依赖
- **不可变性**: 尽可能设计不可变对象

### 2.3 性能设计原则
- **零拷贝**: 尽可能避免不必要的数据拷贝
- **批处理**: 合并相似的渲染操作
- **异步处理**: 资源加载和计算的异步化
- **缓存友好**: 数据结构设计考虑CPU缓存效率

## 3. 核心组件设计

### 3.1 对象模型 (Object Model)

```cpp
namespace usg {

// 基础对象类，所有USG对象的根类
class Object {
public:
    Object() = default;
    virtual ~Object() = default;
    
    // 类型信息
    virtual std::string className() const = 0;
    virtual bool isSameKindAs(const Object* obj) const;
    
    // 序列化支持
    virtual void serialize(Serializer& serializer) const {}
    virtual void deserialize(Deserializer& deserializer) {}
    
    // 引用计数
    void ref() const { ++_refCount; }
    void unref() const { if (--_refCount == 0) delete this; }
    int referenceCount() const { return _refCount; }
    
protected:
    mutable std::atomic<int> _refCount{0};
};

// 智能指针类型
template<typename T>
class ref_ptr {
public:
    ref_ptr() : _ptr(nullptr) {}
    ref_ptr(T* ptr) : _ptr(ptr) { if (_ptr) _ptr->ref(); }
    ref_ptr(const ref_ptr& rp) : _ptr(rp._ptr) { if (_ptr) _ptr->ref(); }
    ref_ptr(ref_ptr&& rp) noexcept : _ptr(rp._ptr) { rp._ptr = nullptr; }
    
    ~ref_ptr() { if (_ptr) _ptr->unref(); }
    
    ref_ptr& operator=(const ref_ptr& rp);
    ref_ptr& operator=(ref_ptr&& rp) noexcept;
    
    T* operator->() const { return _ptr; }
    T& operator*() const { return *_ptr; }
    T* get() const { return _ptr; }
    
    explicit operator bool() const { return _ptr != nullptr; }
    
private:
    T* _ptr;
};

} // namespace usg
```

### 3.2 场景图节点 (Scene Graph Nodes)

```cpp
namespace usg {

// 场景图节点基类
class Node : public Object {
public:
    Node() = default;
    virtual ~Node() = default;
    
    // 访问者模式
    virtual void accept(Visitor& visitor) = 0;
    
    // 包围盒计算
    virtual BoundingSphere computeBoundingSphere() const = 0;
    
    // 状态管理
    void setStateSet(ref_ptr<StateSet> stateSet) { _stateSet = stateSet; }
    ref_ptr<StateSet> getStateSet() const { return _stateSet; }
    
    // 父节点管理
    void addParent(Node* parent);
    void removeParent(Node* parent);
    const std::vector<Node*>& getParents() const { return _parents; }
    
    std::string className() const override { return "Node"; }
    
protected:
    ref_ptr<StateSet> _stateSet;
    std::vector<Node*> _parents;
    mutable BoundingSphere _boundingSphere;
    mutable bool _boundingSphereComputed = false;
};

// 组节点，可以包含子节点
class Group : public Node {
public:
    Group() = default;
    
    void accept(Visitor& visitor) override;
    BoundingSphere computeBoundingSphere() const override;
    
    // 子节点管理
    void addChild(ref_ptr<Node> child);
    void removeChild(ref_ptr<Node> child);
    void removeChild(size_t index);
    void removeChildren(size_t start, size_t count);
    
    size_t getNumChildren() const { return _children.size(); }
    ref_ptr<Node> getChild(size_t index) const { return _children[index]; }
    const std::vector<ref_ptr<Node>>& getChildren() const { return _children; }
    
    std::string className() const override { return "Group"; }
    
protected:
    std::vector<ref_ptr<Node>> _children;
};

// 变换节点
class Transform : public Group {
public:
    Transform() = default;
    Transform(const mat4& matrix) : _matrix(matrix) {}
    
    void accept(Visitor& visitor) override;
    BoundingSphere computeBoundingSphere() const override;
    
    // 变换矩阵
    void setMatrix(const mat4& matrix) { _matrix = matrix; }
    const mat4& getMatrix() const { return _matrix; }
    
    // 便利方法
    void setTranslation(const vec3& translation);
    void setRotation(const quat& rotation);
    void setScale(const vec3& scale);
    
    vec3 getTranslation() const;
    quat getRotation() const;
    vec3 getScale() const;
    
    std::string className() const override { return "Transform"; }
    
protected:
    mat4 _matrix = mat4(1.0f);
};

} // namespace usg
```

### 3.3 访问者系统 (Visitor System)

```cpp
namespace usg {

// 访问者基类
class Visitor {
public:
    virtual ~Visitor() = default;
    
    // 节点访问接口
    virtual void visit(Node& node) {}
    virtual void visit(Group& group) { traverse(group); }
    virtual void visit(Transform& transform) { traverse(transform); }
    virtual void visit(Geometry& geometry) {}
    
    // 遍历控制
    enum TraversalMode {
        TRAVERSE_NONE,
        TRAVERSE_PARENTS,
        TRAVERSE_ALL_CHILDREN,
        TRAVERSE_ACTIVE_CHILDREN
    };
    
    void setTraversalMode(TraversalMode mode) { _traversalMode = mode; }
    TraversalMode getTraversalMode() const { return _traversalMode; }
    
protected:
    void traverse(Group& group);
    
    TraversalMode _traversalMode = TRAVERSE_ALL_CHILDREN;
};

// 裁剪访问者
class CullVisitor : public Visitor {
public:
    CullVisitor(ref_ptr<WebGPUDevice> device);
    
    void visit(Transform& transform) override;
    void visit(Geometry& geometry) override;
    
    // 视锥体裁剪
    void setViewMatrix(const mat4& view) { _viewMatrix = view; }
    void setProjectionMatrix(const mat4& proj) { _projectionMatrix = proj; }
    
    // 渲染列表
    const std::vector<RenderLeaf>& getRenderLeaves() const { return _renderLeaves; }
    
private:
    ref_ptr<WebGPUDevice> _device;
    mat4 _viewMatrix;
    mat4 _projectionMatrix;
    mat4 _modelViewMatrix;
    std::vector<RenderLeaf> _renderLeaves;
    std::stack<ref_ptr<StateSet>> _stateStack;
};

} // namespace usg
```

## 4. WebGPU封装层设计

### 4.1 设备管理
- **WebGPUDevice**: WebGPU设备的高级封装
- **WebGPUContext**: 渲染上下文管理
- **WebGPUQueue**: 命令队列管理

### 4.2 资源管理
- **BufferManager**: 缓冲区资源管理
- **TextureManager**: 纹理资源管理
- **ShaderManager**: 着色器资源管理

### 4.3 渲染管线
- **RenderPipeline**: 渲染管线状态
- **ComputePipeline**: 计算管线状态
- **BindGroup**: 资源绑定组

## 5. 性能优化策略

### 5.1 渲染优化
- **状态排序**: 按渲染状态对绘制调用排序
- **实例化渲染**: 相同几何体的批量渲染
- **视锥体裁剪**: 高效的视锥体裁剪算法
- **LOD系统**: 距离相关的细节层次

### 5.2 内存优化
- **对象池**: 重用频繁创建的对象
- **内存对齐**: GPU友好的数据布局
- **延迟加载**: 按需加载资源
- **压缩纹理**: 使用压缩纹理格式

### 5.3 并发优化
- **多线程渲染**: 渲染命令的并行生成
- **异步加载**: 资源的异步加载
- **计算着色器**: GPU并行计算
- **流水线并行**: 渲染管线的并行化

## 6. 扩展性设计

### 6.1 插件系统
- **节点插件**: 自定义场景图节点
- **访问者插件**: 自定义遍历逻辑
- **渲染器插件**: 自定义渲染器
- **资源插件**: 自定义资源加载器

### 6.2 脚本支持
- **JavaScript绑定**: WebAssembly环境的脚本支持
- **Lua绑定**: 桌面环境的脚本支持
- **Python绑定**: 工具和原型开发

### 6.3 工具链
- **场景编辑器**: 可视化场景编辑工具
- **性能分析器**: 渲染性能分析工具
- **资源转换器**: 资源格式转换工具
- **调试器**: 渲染调试和验证工具
