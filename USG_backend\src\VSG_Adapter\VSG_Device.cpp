#include <VSG_Adapter/VSG_Device.h>
#include <USG_Backend/BackendFactory.h>
#include <iostream>
#include <cassert>

namespace USG {

VSG_Device::VSG_Device(BackendType backendType)
    : _backendType(backendType) {
    
    std::cout << "[VSG_Device] Creating VSG Device adapter with backend: " 
              << static_cast<int>(backendType) << std::endl;
}

VSG_Device::~VSG_Device() {
    cleanup();
}

bool VSG_Device::initialize(const BackendConfig& config) {
    if (_initialized) {
        return true;
    }
    
    std::cout << "[VSG_Device] Initializing VSG Device adapter..." << std::endl;
    
    // 创建后端实例
    _backend = BackendFactory::getInstance().createBackend(_backendType);
    if (!_backend) {
        std::cerr << "[VSG_Device] Failed to create backend" << std::endl;
        return false;
    }
    
    // 初始化后端
    if (!_backend->initialize(config)) {
        std::cerr << "[VSG_Device] Failed to initialize backend" << std::endl;
        _backend.reset();
        return false;
    }
    
    // 创建类型转换器
    _converter = std::make_unique<TypeConverter>();
    
    // 创建资源管理器
    _resourceManager = std::make_unique<ResourceManager>();
    
    _initialized = true;
    
    std::cout << "[VSG_Device] VSG Device adapter initialized successfully" << std::endl;
    std::cout << "[VSG_Device] Backend: " << _backend->getBackendName() << std::endl;
    
    return true;
}

void VSG_Device::cleanup() {
    if (!_initialized) {
        return;
    }
    
    std::cout << "[VSG_Device] Cleaning up VSG Device adapter..." << std::endl;
    
    // 清理所有映射的资源
    for (auto& [handle, buffer] : _bufferMap) {
        if (buffer) {
            _backend->destroyBuffer(buffer);
        }
    }
    _bufferMap.clear();
    
    for (auto& [handle, texture] : _imageMap) {
        if (texture) {
            _backend->destroyTexture(texture);
        }
    }
    _imageMap.clear();
    
    for (auto& [handle, pipeline] : _pipelineMap) {
        if (pipeline) {
            _backend->destroyPipeline(pipeline);
        }
    }
    _pipelineMap.clear();
    
    for (auto& [handle, shader] : _shaderMap) {
        if (shader) {
            _backend->destroyShader(shader);
        }
    }
    _shaderMap.clear();
    
    for (auto& [handle, descriptorSet] : _descriptorSetMap) {
        if (descriptorSet) {
            _backend->destroyDescriptorSet(descriptorSet);
        }
    }
    _descriptorSetMap.clear();
    
    for (auto& [handle, fence] : _fenceMap) {
        if (fence) {
            _backend->destroyFence(fence);
        }
    }
    _fenceMap.clear();
    
    for (auto& [handle, semaphore] : _semaphoreMap) {
        if (semaphore) {
            _backend->destroySemaphore(semaphore);
        }
    }
    _semaphoreMap.clear();
    
    // 清理组件
    _resourceManager.reset();
    _converter.reset();
    
    // 清理后端
    if (_backend) {
        _backend->cleanup();
        _backend.reset();
    }
    
    _initialized = false;
    std::cout << "[VSG_Device] VSG Device adapter cleaned up" << std::endl;
}

BackendType VSG_Device::getBackendType() const {
    return _backendType;
}

// VSG兼容的Vulkan API实现
VkResult VSG_Device::createBuffer(const VkBufferCreateInfo* pCreateInfo, VkBuffer* pBuffer) {
    if (!_initialized || !pCreateInfo || !pBuffer) {
        logError("createBuffer", "Invalid parameters");
        return VK_ERROR_INITIALIZATION_FAILED;
    }
    
    logCall("createBuffer", "size: " + std::to_string(pCreateInfo->size));
    
    // 转换Vulkan缓冲区创建信息到USG格式
    BufferDesc desc = _converter->convertVkBufferCreateInfo(pCreateInfo);
    
    // 创建后端缓冲区
    BackendBuffer* backendBuffer = _backend->createBuffer(desc);
    if (!backendBuffer) {
        logError("createBuffer", "Failed to create backend buffer");
        return VK_ERROR_OUT_OF_DEVICE_MEMORY;
    }
    
    // 创建Vulkan句柄并注册映射
    VkBuffer handle = createVkHandle();
    registerBuffer(handle, backendBuffer);
    
    *pBuffer = handle;
    return VK_SUCCESS;
}

void VSG_Device::destroyBuffer(VkBuffer buffer) {
    if (!_initialized || buffer == VK_NULL_HANDLE) {
        return;
    }
    
    logCall("destroyBuffer");
    
    BackendBuffer* backendBuffer = getBackendBuffer(buffer);
    if (backendBuffer) {
        _backend->destroyBuffer(backendBuffer);
        unregisterBuffer(buffer);
    }
}

VkResult VSG_Device::createImage(const VkImageCreateInfo* pCreateInfo, VkImage* pImage) {
    if (!_initialized || !pCreateInfo || !pImage) {
        logError("createImage", "Invalid parameters");
        return VK_ERROR_INITIALIZATION_FAILED;
    }
    
    logCall("createImage", std::to_string(pCreateInfo->extent.width) + "x" + 
                          std::to_string(pCreateInfo->extent.height));
    
    // 转换Vulkan图像创建信息到USG格式
    TextureDesc desc = _converter->convertVkImageCreateInfo(pCreateInfo);
    
    // 创建后端纹理
    BackendTexture* backendTexture = _backend->createTexture(desc);
    if (!backendTexture) {
        logError("createImage", "Failed to create backend texture");
        return VK_ERROR_OUT_OF_DEVICE_MEMORY;
    }
    
    // 创建Vulkan句柄并注册映射
    VkImage handle = createVkImageHandle();
    registerImage(handle, backendTexture);
    
    *pImage = handle;
    return VK_SUCCESS;
}

void VSG_Device::destroyImage(VkImage image) {
    if (!_initialized || image == VK_NULL_HANDLE) {
        return;
    }
    
    logCall("destroyImage");
    
    BackendTexture* backendTexture = getBackendTexture(image);
    if (backendTexture) {
        _backend->destroyTexture(backendTexture);
        unregisterImage(image);
    }
}

VkResult VSG_Device::allocateMemory(const VkMemoryAllocateInfo* pAllocateInfo, VkDeviceMemory* pMemory) {
    if (!_initialized || !pAllocateInfo || !pMemory) {
        logError("allocateMemory", "Invalid parameters");
        return VK_ERROR_INITIALIZATION_FAILED;
    }
    
    logCall("allocateMemory", "size: " + std::to_string(pAllocateInfo->allocationSize));
    
    // WebGPU不需要显式的内存分配，创建一个虚拟句柄
    *pMemory = reinterpret_cast<VkDeviceMemory>(static_cast<uintptr_t>(0x1000 + _memoryHandleCounter++));
    
    return VK_SUCCESS;
}

void VSG_Device::freeMemory(VkDeviceMemory memory) {
    if (!_initialized || memory == VK_NULL_HANDLE) {
        return;
    }
    
    logCall("freeMemory");
    
    // WebGPU不需要显式的内存释放
}

VkResult VSG_Device::bindBufferMemory(VkBuffer buffer, VkDeviceMemory memory, VkDeviceSize memoryOffset) {
    if (!_initialized || buffer == VK_NULL_HANDLE || memory == VK_NULL_HANDLE) {
        logError("bindBufferMemory", "Invalid parameters");
        return VK_ERROR_INITIALIZATION_FAILED;
    }
    
    logCall("bindBufferMemory");
    
    // WebGPU缓冲区创建时就已经分配了内存，这里只是记录绑定关系
    return VK_SUCCESS;
}

VkResult VSG_Device::bindImageMemory(VkImage image, VkDeviceMemory memory, VkDeviceSize memoryOffset) {
    if (!_initialized || image == VK_NULL_HANDLE || memory == VK_NULL_HANDLE) {
        logError("bindImageMemory", "Invalid parameters");
        return VK_ERROR_INITIALIZATION_FAILED;
    }
    
    logCall("bindImageMemory");
    
    // WebGPU纹理创建时就已经分配了内存，这里只是记录绑定关系
    return VK_SUCCESS;
}

VkResult VSG_Device::mapMemory(VkDeviceMemory memory, VkDeviceSize offset, VkDeviceSize size, void** ppData) {
    if (!_initialized || memory == VK_NULL_HANDLE || !ppData) {
        logError("mapMemory", "Invalid parameters");
        return VK_ERROR_INITIALIZATION_FAILED;
    }
    
    logCall("mapMemory", "offset: " + std::to_string(offset) + ", size: " + std::to_string(size));
    
    // 这里需要找到对应的缓冲区并映射
    // 简化实现：返回一个临时指针
    static uint8_t tempBuffer[1024 * 1024]; // 1MB临时缓冲区
    *ppData = tempBuffer;
    
    return VK_SUCCESS;
}

void VSG_Device::unmapMemory(VkDeviceMemory memory) {
    if (!_initialized || memory == VK_NULL_HANDLE) {
        return;
    }
    
    logCall("unmapMemory");
    
    // WebGPU的取消映射在缓冲区级别处理
}

VkResult VSG_Device::deviceWaitIdle() {
    if (!_initialized) {
        return VK_ERROR_DEVICE_LOST;
    }
    
    logCall("deviceWaitIdle");
    
    _backend->waitIdle();
    return VK_SUCCESS;
}

// 内部辅助方法
VkBuffer VSG_Device::createVkHandle() {
    return reinterpret_cast<VkBuffer>(static_cast<uintptr_t>(0x1000 + _handleCounter++));
}

VkImage VSG_Device::createVkImageHandle() {
    return reinterpret_cast<VkImage>(static_cast<uintptr_t>(0x2000 + _handleCounter++));
}

void VSG_Device::registerBuffer(VkBuffer handle, BackendBuffer* buffer) {
    _bufferMap[handle] = buffer;
}

void VSG_Device::registerImage(VkImage handle, BackendTexture* texture) {
    _imageMap[handle] = texture;
}

BackendBuffer* VSG_Device::getBackendBuffer(VkBuffer handle) {
    auto it = _bufferMap.find(handle);
    return (it != _bufferMap.end()) ? it->second : nullptr;
}

BackendTexture* VSG_Device::getBackendTexture(VkImage handle) {
    auto it = _imageMap.find(handle);
    return (it != _imageMap.end()) ? it->second : nullptr;
}

void VSG_Device::unregisterBuffer(VkBuffer handle) {
    _bufferMap.erase(handle);
}

void VSG_Device::unregisterImage(VkImage handle) {
    _imageMap.erase(handle);
}

void VSG_Device::logCall(const std::string& function, const std::string& details) const {
    if (_debugEnabled) {
        std::string msg = "[VSG_Device::" + function + "]";
        if (!details.empty()) {
            msg += " " + details;
        }
        std::cout << msg << std::endl;
    }
}

void VSG_Device::logError(const std::string& function, const std::string& error) const {
    std::string msg = "[VSG_Device::" + function + "] ERROR: " + error;
    std::cerr << msg << std::endl;
}

} // namespace USG
