#pragma once

#include <USG_Backend/BackendTypes.h>
#include <USG_Backend/BackendFactory.h>
#include <USG_Backend/RenderBackend.h>
#include <memory>
#include <vector>
#include <string>
#include <functional>

namespace USG {

    /**
     * @brief 后端切换器
     * 
     * 负责管理不同渲染后端之间的切换，保持场景状态的一致性
     */
    class BackendSwitcher {
    public:
        /**
         * @brief 后端信息
         */
        struct BackendInfo {
            BackendType type;
            std::string name;
            std::string description;
            bool available = false;
            bool initialized = false;
            std::unique_ptr<RenderBackend> instance;
        };

        /**
         * @brief 切换配置
         */
        struct SwitchConfig {
            bool preserveResources = true;     // 保持资源状态
            bool preserveState = true;         // 保持渲染状态
            bool validateAfterSwitch = true;   // 切换后验证
            uint32_t timeoutMs = 5000;         // 切换超时时间
        };

        /**
         * @brief 切换结果
         */
        struct SwitchResult {
            bool success = false;
            BackendType fromBackend = BackendType::Unknown;
            BackendType toBackend = BackendType::Unknown;
            double switchTimeMs = 0.0;
            std::string errorMessage;
        };

    public:
        BackendSwitcher();
        ~BackendSwitcher();

        /**
         * @brief 初始化切换器
         * @param config 后端配置
         * @return 是否成功
         */
        bool initialize(const BackendConfig& config = {});

        /**
         * @brief 清理切换器
         */
        void cleanup();

        /**
         * @brief 扫描可用后端
         * @return 可用后端数量
         */
        int scanAvailableBackends();

        /**
         * @brief 获取可用后端列表
         * @return 后端信息列表
         */
        const std::vector<BackendInfo>& getAvailableBackends() const { return _backends; }

        /**
         * @brief 获取当前后端
         * @return 当前后端指针
         */
        RenderBackend* getCurrentBackend() const;

        /**
         * @brief 获取当前后端类型
         * @return 当前后端类型
         */
        BackendType getCurrentBackendType() const { return _currentBackendType; }

        /**
         * @brief 获取当前后端名称
         * @return 当前后端名称
         */
        std::string getCurrentBackendName() const;

        /**
         * @brief 切换到指定后端
         * @param backendType 目标后端类型
         * @param config 切换配置
         * @return 切换结果
         */
        SwitchResult switchTo(BackendType backendType, const SwitchConfig& config = {});

        /**
         * @brief 切换到指定后端（通过名称）
         * @param backendName 目标后端名称
         * @param config 切换配置
         * @return 切换结果
         */
        SwitchResult switchTo(const std::string& backendName, const SwitchConfig& config = {});

        /**
         * @brief 检查后端是否可用
         * @param backendType 后端类型
         * @return 是否可用
         */
        bool isBackendAvailable(BackendType backendType) const;

        /**
         * @brief 检查后端是否已初始化
         * @param backendType 后端类型
         * @return 是否已初始化
         */
        bool isBackendInitialized(BackendType backendType) const;

        /**
         * @brief 预初始化后端
         * @param backendType 后端类型
         * @return 是否成功
         */
        bool preInitializeBackend(BackendType backendType);

        /**
         * @brief 预初始化所有可用后端
         * @return 成功初始化的后端数量
         */
        int preInitializeAllBackends();

        /**
         * @brief 获取后端性能信息
         * @param backendType 后端类型
         * @return 性能信息字符串
         */
        std::string getBackendPerformanceInfo(BackendType backendType) const;

        /**
         * @brief 设置切换回调函数
         * @param callback 回调函数
         */
        void setSwitchCallback(std::function<void(const SwitchResult&)> callback);

        /**
         * @brief 设置错误回调函数
         * @param callback 错误回调函数
         */
        void setErrorCallback(std::function<void(const std::string&)> callback);

        /**
         * @brief 获取最后的切换结果
         * @return 切换结果
         */
        const SwitchResult& getLastSwitchResult() const { return _lastSwitchResult; }

        /**
         * @brief 验证当前后端状态
         * @return 是否正常
         */
        bool validateCurrentBackend() const;

        /**
         * @brief 获取推荐的后端
         * @return 推荐的后端类型
         */
        BackendType getRecommendedBackend() const;

        /**
         * @brief 获取切换历史
         * @return 切换历史列表
         */
        const std::vector<SwitchResult>& getSwitchHistory() const { return _switchHistory; }

    private:
        // 内部方法
        BackendInfo* findBackendInfo(BackendType type);
        BackendInfo* findBackendInfo(const std::string& name);
        bool initializeBackend(BackendInfo& info);
        bool cleanupBackend(BackendInfo& info);
        bool transferResources(RenderBackend* from, RenderBackend* to);
        bool validateBackend(RenderBackend* backend) const;
        double getCurrentTimeMs() const;

        // 后端创建方法
        std::unique_ptr<RenderBackend> createWebGPUBackend();
        std::unique_ptr<RenderBackend> createVulkanBackend();
        std::unique_ptr<RenderBackend> createOpenGLBackend();

    private:
        // 后端列表
        std::vector<BackendInfo> _backends;
        BackendType _currentBackendType = BackendType::Unknown;
        int _currentBackendIndex = -1;

        // 配置
        BackendConfig _backendConfig;
        SwitchConfig _defaultSwitchConfig;

        // 状态
        bool _initialized = false;

        // 切换历史和结果
        SwitchResult _lastSwitchResult;
        std::vector<SwitchResult> _switchHistory;

        // 回调函数
        std::function<void(const SwitchResult&)> _switchCallback;
        std::function<void(const std::string&)> _errorCallback;

        // 工厂实例
        BackendFactory& _factory;
    };

} // namespace USG
