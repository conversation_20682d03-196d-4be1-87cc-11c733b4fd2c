#pragma once

#include <USG/Core/Object.h>
#include <webgpu/webgpu.hpp>
#include <memory>
#include <string>
#include <vector>
#include <functional>

namespace usg {

// WebGPU类型别名
using Device = wgpu::Device;
using Queue = wgpu::Queue;
using Surface = wgpu::Surface;
using Adapter = wgpu::Adapter;
using Instance = wgpu::Instance;
using Buffer = wgpu::Buffer;
using Texture = wgpu::Texture;
using TextureView = wgpu::TextureView;
using Sampler = wgpu::Sampler;
using ShaderModule = wgpu::ShaderModule;
using RenderPipeline = wgpu::RenderPipeline;
using ComputePipeline = wgpu::ComputePipeline;
using BindGroup = wgpu::BindGroup;
using BindGroupLayout = wgpu::BindGroupLayout;
using PipelineLayout = wgpu::PipelineLayout;
using CommandEncoder = wgpu::CommandEncoder;
using CommandBuffer = wgpu::CommandBuffer;
using RenderPassEncoder = wgpu::RenderPassEncoder;
using ComputePassEncoder = wgpu::ComputePassEncoder;

/**
 * @brief WebGPU设备封装类
 * 
 * 提供WebGPU设备的高级封装，包括：
 * - 设备初始化和管理
 * - 资源创建和管理
 * - 错误处理和调试
 * - 性能监控
 */
class WebGPUDevice : public Object {
public:
    /**
     * @brief 设备特性
     */
    struct Features {
        bool textureCompressionBC = false;
        bool textureCompressionETC2 = false;
        bool textureCompressionASTC = false;
        bool timestampQuery = false;
        bool pipelineStatisticsQuery = false;
        bool multiDrawIndirect = false;
        bool depthClipControl = false;
        bool depth32FloatStencil8 = false;
    };
    
    /**
     * @brief 设备限制
     */
    struct Limits {
        uint32_t maxTextureDimension1D = 8192;
        uint32_t maxTextureDimension2D = 8192;
        uint32_t maxTextureDimension3D = 2048;
        uint32_t maxTextureArrayLayers = 256;
        uint32_t maxBindGroups = 4;
        uint32_t maxBindingsPerBindGroup = 1000;
        uint32_t maxDynamicUniformBuffersPerPipelineLayout = 8;
        uint32_t maxDynamicStorageBuffersPerPipelineLayout = 4;
        uint32_t maxSampledTexturesPerShaderStage = 16;
        uint32_t maxSamplersPerShaderStage = 16;
        uint32_t maxStorageBuffersPerShaderStage = 8;
        uint32_t maxStorageTexturesPerShaderStage = 4;
        uint32_t maxUniformBuffersPerShaderStage = 12;
        uint64_t maxUniformBufferBindingSize = 65536;
        uint64_t maxStorageBufferBindingSize = 134217728;
        uint32_t minUniformBufferOffsetAlignment = 256;
        uint32_t minStorageBufferOffsetAlignment = 256;
        uint32_t maxVertexBuffers = 8;
        uint64_t maxBufferSize = 268435456;
        uint32_t maxVertexAttributes = 16;
        uint32_t maxVertexBufferArrayStride = 2048;
        uint32_t maxInterStageShaderComponents = 60;
        uint32_t maxComputeWorkgroupStorageSize = 16384;
        uint32_t maxComputeInvocationsPerWorkgroup = 256;
        uint32_t maxComputeWorkgroupSizeX = 256;
        uint32_t maxComputeWorkgroupSizeY = 256;
        uint32_t maxComputeWorkgroupSizeZ = 64;
        uint32_t maxComputeWorkgroupsPerDimension = 65535;
    };
    
    /**
     * @brief 创建WebGPU设备
     * @param surface 表面（可选）
     * @return WebGPU设备
     */
    static ref_ptr<WebGPUDevice> create(Surface surface = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~WebGPUDevice() override = default;
    
    /**
     * @brief 初始化设备
     * @param surface 表面
     * @return 是否成功
     */
    bool initialize(Surface surface = nullptr);
    
    /**
     * @brief 获取WebGPU设备
     * @return WebGPU设备
     */
    Device getDevice() const { return _device; }
    
    /**
     * @brief 获取队列
     * @return 队列
     */
    Queue getQueue() const { return _queue; }
    
    /**
     * @brief 获取表面
     * @return 表面
     */
    Surface getSurface() const { return _surface; }
    
    /**
     * @brief 获取适配器
     * @return 适配器
     */
    Adapter getAdapter() const { return _adapter; }
    
    /**
     * @brief 获取实例
     * @return 实例
     */
    Instance getInstance() const { return _instance; }
    
    /**
     * @brief 获取设备特性
     * @return 设备特性
     */
    const Features& getFeatures() const { return _features; }
    
    /**
     * @brief 获取设备限制
     * @return 设备限制
     */
    const Limits& getLimits() const { return _limits; }
    
    /**
     * @brief 检查是否支持指定特性
     * @param feature 特性名称
     * @return 是否支持
     */
    bool hasFeature(const std::string& feature) const;
    
    /**
     * @brief 创建缓冲区
     * @param size 大小
     * @param usage 用途
     * @param mappedAtCreation 创建时是否映射
     * @return 缓冲区
     */
    Buffer createBuffer(uint64_t size, wgpu::BufferUsage usage, bool mappedAtCreation = false);
    
    /**
     * @brief 创建纹理
     * @param descriptor 纹理描述符
     * @return 纹理
     */
    Texture createTexture(const wgpu::TextureDescriptor& descriptor);
    
    /**
     * @brief 创建采样器
     * @param descriptor 采样器描述符
     * @return 采样器
     */
    Sampler createSampler(const wgpu::SamplerDescriptor& descriptor);
    
    /**
     * @brief 创建着色器模块
     * @param source 着色器源码
     * @param label 标签
     * @return 着色器模块
     */
    ShaderModule createShaderModule(const std::string& source, const std::string& label = "");
    
    /**
     * @brief 创建渲染管线
     * @param descriptor 管线描述符
     * @return 渲染管线
     */
    RenderPipeline createRenderPipeline(const wgpu::RenderPipelineDescriptor& descriptor);
    
    /**
     * @brief 创建计算管线
     * @param descriptor 管线描述符
     * @return 计算管线
     */
    ComputePipeline createComputePipeline(const wgpu::ComputePipelineDescriptor& descriptor);
    
    /**
     * @brief 创建绑定组布局
     * @param descriptor 布局描述符
     * @return 绑定组布局
     */
    BindGroupLayout createBindGroupLayout(const wgpu::BindGroupLayoutDescriptor& descriptor);
    
    /**
     * @brief 创建管线布局
     * @param descriptor 布局描述符
     * @return 管线布局
     */
    PipelineLayout createPipelineLayout(const wgpu::PipelineLayoutDescriptor& descriptor);
    
    /**
     * @brief 创建绑定组
     * @param descriptor 绑定组描述符
     * @return 绑定组
     */
    BindGroup createBindGroup(const wgpu::BindGroupDescriptor& descriptor);
    
    /**
     * @brief 创建命令编码器
     * @param label 标签
     * @return 命令编码器
     */
    CommandEncoder createCommandEncoder(const std::string& label = "");
    
    /**
     * @brief 提交命令缓冲区
     * @param commandBuffers 命令缓冲区列表
     */
    void submit(const std::vector<CommandBuffer>& commandBuffers);
    
    /**
     * @brief 提交单个命令缓冲区
     * @param commandBuffer 命令缓冲区
     */
    void submit(CommandBuffer commandBuffer);
    
    /**
     * @brief 等待设备空闲
     */
    void waitIdle();
    
    /**
     * @brief 处理设备事件
     */
    void tick();
    
    /**
     * @brief 设置错误回调
     * @param callback 错误回调函数
     */
    void setErrorCallback(std::function<void(wgpu::ErrorType, const char*)> callback);
    
    /**
     * @brief 设置设备丢失回调
     * @param callback 设备丢失回调函数
     */
    void setDeviceLostCallback(std::function<void(wgpu::DeviceLostReason, const char*)> callback);
    
    /**
     * @brief 获取设备信息
     * @return 设备信息字符串
     */
    std::string getDeviceInfo() const;
    
    /**
     * @brief 检查设备是否有效
     * @return 是否有效
     */
    bool isValid() const { return _device != nullptr; }
    
    std::string className() const override { return "WebGPUDevice"; }
    
private:
    WebGPUDevice() = default;
    
    bool createInstance();
    bool requestAdapter();
    bool requestDevice();
    void queryFeatures();
    void queryLimits();
    
    Instance _instance;
    Adapter _adapter;
    Device _device;
    Queue _queue;
    Surface _surface;
    
    Features _features;
    Limits _limits;
    
    std::function<void(wgpu::ErrorType, const char*)> _errorCallback;
    std::function<void(wgpu::DeviceLostReason, const char*)> _deviceLostCallback;
};

} // namespace usg
