# Vulkan黑屏问题分析报告

## 问题描述
USG Backend的Vulkan后端出现黑屏问题，程序能够正常初始化Vulkan，但无法正确渲染内容。

## 问题定位过程

### 1. 初始症状
- Vulkan后端初始化成功
- 窗口创建成功，但显示黑屏
- 程序在运行时切换到Vulkan后端时崩溃

### 2. 深入调试发现
通过添加详细的日志输出，我们发现：

1. **✅ Vulkan初始化完全成功**：
   - 实例创建成功
   - 物理设备选择成功
   - 逻辑设备创建成功
   - 交换链创建成功
   - 渲染通道创建成功
   - 帧缓冲创建成功
   - 命令池创建成功
   - 同步对象创建成功

2. **✅ 着色器模块创建成功**：
   - 顶点着色器模块创建成功
   - 片段着色器模块创建成功

3. **❌ 图形管线创建失败**：
   - `vkCreateGraphicsPipelines()` 返回 VkResult: -13
   - 错误代码-13对应 `VK_ERROR_INVALID_SHADER_NV` 或相关的着色器错误

### 3. 根本原因
问题的根本原因是：**SPIR-V着色器字节码无效或不兼容**

具体表现：
- 着色器模块创建成功（vkCreateShaderModule成功）
- 但在创建图形管线时，Vulkan驱动程序检测到SPIR-V代码有问题
- 导致 `vkCreateGraphicsPipelines()` 失败

## 尝试的解决方案

### 1. 修复后端切换时机
- **问题**：VSGScene在OpenGL后端下创建，然后切换到Vulkan
- **解决**：修改默认后端为Vulkan，确保场景在Vulkan后端下创建
- **结果**：问题依然存在，但获得了更清晰的错误信息

### 2. 添加管线状态配置
- **问题**：管线配置可能不完整
- **解决**：添加深度测试配置
- **结果**：问题依然存在

### 3. 修改SPIR-V着色器代码
- **问题**：SPIR-V字节码可能有错误
- **解决**：尝试了多个不同的SPIR-V着色器代码
- **结果**：所有尝试都失败，VkResult始终为-13

## 当前状态
- Vulkan后端的所有基础设施都正常工作
- 问题集中在SPIR-V着色器代码的有效性上
- 需要使用正确的SPIR-V编译工具生成有效的字节码

## 下一步解决方案

### 1. 使用glslc编译器
使用Vulkan SDK提供的glslc编译器从GLSL源代码生成正确的SPIR-V字节码：

```bash
glslc shader.vert -o vert.spv
glslc shader.frag -o frag.spv
```

### 2. 验证SPIR-V代码
使用spirv-val工具验证生成的SPIR-V代码：

```bash
spirv-val vert.spv
spirv-val frag.spv
```

### 3. 集成到构建系统
将SPIR-V编译集成到CMake构建系统中，确保着色器代码的正确性。

## 技术细节

### 错误代码分析
VkResult -13 通常对应以下错误之一：
- `VK_ERROR_INVALID_SHADER_NV` (NVIDIA特定)
- `VK_PIPELINE_COMPILE_REQUIRED_EXT`
- 其他着色器相关错误

### 管线创建参数
当前管线创建使用的参数：
- 着色器阶段：顶点着色器 + 片段着色器
- 顶点输入：无属性（硬编码顶点）
- 输入装配：三角形列表
- 视口：1200x800
- 光栅化：填充模式，背面剔除
- 多重采样：1x采样
- 深度测试：禁用
- 颜色混合：禁用

## 结论
Vulkan后端的架构和实现是正确的，问题仅在于SPIR-V着色器字节码的有效性。
一旦解决了着色器代码问题，Vulkan后端应该能够正常工作。
