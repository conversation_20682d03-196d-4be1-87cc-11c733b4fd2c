#pragma once

#include "BackendTypes.h"
#include <memory>
#include <functional>
#include <unordered_map>
#include <string>

namespace USG
{

    // 前向声明
    class RenderBackend;

    /**
     * @brief 后端工厂类
     *
     * 负责创建和管理不同类型的渲染后端实例。
     * 支持动态注册新的后端类型，实现插件化架构。
     */
    class BackendFactory
    {
    public:
        /**
         * @brief 后端创建函数类型
         */
        using BackendCreator = std::function<std::unique_ptr<RenderBackend>()>;

        /**
         * @brief 获取工厂单例实例
         * @return 工厂实例引用
         */
        static BackendFactory &getInstance();

        /**
         * @brief 注册后端创建函数
         * @param type 后端类型
         * @param creator 创建函数
         * @return 是否注册成功
         */
        bool registerBackend(BackendType type, BackendCreator creator);

        /**
         * @brief 注册后端创建函数（通过名称）
         * @param name 后端名称
         * @param type 后端类型
         * @param creator 创建函数
         * @return 是否注册成功
         */
        bool registerBackend(const std::string &name, BackendType type, BackendCreator creator);

        /**
         * @brief 取消注册后端
         * @param type 后端类型
         */
        void unregisterBackend(BackendType type);

        /**
         * @brief 取消注册后端（通过名称）
         * @param name 后端名称
         */
        void unregisterBackend(const std::string &name);

        /**
         * @brief 创建后端实例
         * @param type 后端类型
         * @return 后端实例智能指针
         */
        std::unique_ptr<RenderBackend> createBackend(BackendType type);

        /**
         * @brief 创建后端实例（通过名称）
         * @param name 后端名称
         * @return 后端实例智能指针
         */
        std::unique_ptr<RenderBackend> createBackend(const std::string &name);

        /**
         * @brief 检查后端是否可用
         * @param type 后端类型
         * @return 是否可用
         */
        bool isBackendAvailable(BackendType type) const;

        /**
         * @brief 检查后端是否可用（通过名称）
         * @param name 后端名称
         * @return 是否可用
         */
        bool isBackendAvailable(const std::string &name) const;

        /**
         * @brief 获取所有可用的后端类型
         * @return 后端类型列表
         */
        std::vector<BackendType> getAvailableBackends() const;

        /**
         * @brief 获取所有可用的后端名称
         * @return 后端名称列表
         */
        std::vector<std::string> getAvailableBackendNames() const;

        /**
         * @brief 获取默认后端类型
         * @return 默认后端类型
         */
        BackendType getDefaultBackendType() const;

        /**
         * @brief 设置默认后端类型
         * @param type 默认后端类型
         */
        void setDefaultBackendType(BackendType type);

        /**
         * @brief 自动检测最佳后端
         * @return 最佳后端类型
         */
        BackendType detectBestBackend() const;

        /**
         * @brief 清除所有注册的后端
         */
        void clear();

    private:
        BackendFactory() = default;
        ~BackendFactory() = default;

        /**
         * @brief 初始化默认后端
         */
        void initializeDefaultBackends();

        // 禁止拷贝和赋值
        BackendFactory(const BackendFactory &) = delete;
        BackendFactory &operator=(const BackendFactory &) = delete;

        struct BackendInfo
        {
            BackendType type;
            BackendCreator creator;
            std::string name;
        };

        std::unordered_map<BackendType, BackendInfo> _backendsByType;
        std::unordered_map<std::string, BackendInfo> _backendsByName;
        BackendType _defaultBackendType = BackendType::WebGPU;
    };

    /**
     * @brief 后端管理器类
     *
     * 提供全局的后端管理功能，包括初始化、切换和清理。
     */
    class BackendManager
    {
    public:
        /**
         * @brief 获取管理器单例实例
         * @return 管理器实例引用
         */
        static BackendManager &getInstance();

        /**
         * @brief 初始化后端管理器
         * @param type 默认后端类型
         * @param config 后端配置
         * @return 是否初始化成功
         */
        bool initialize(BackendType type = BackendType::WebGPU, const BackendConfig &config = {});

        /**
         * @brief 初始化后端管理器（通过名称）
         * @param name 后端名称
         * @param config 后端配置
         * @return 是否初始化成功
         */
        bool initialize(const std::string &name, const BackendConfig &config = {});

        /**
         * @brief 清理后端管理器
         */
        void cleanup();

        /**
         * @brief 获取当前后端
         * @return 当前后端指针
         */
        RenderBackend *getCurrentBackend() const;

        /**
         * @brief 切换后端
         * @param type 新的后端类型
         * @param config 后端配置
         * @return 是否切换成功
         */
        bool switchBackend(BackendType type, const BackendConfig &config = {});

        /**
         * @brief 切换后端（通过名称）
         * @param name 后端名称
         * @param config 后端配置
         * @return 是否切换成功
         */
        bool switchBackend(const std::string &name, const BackendConfig &config = {});

        /**
         * @brief 检查是否已初始化
         * @return 是否已初始化
         */
        bool isInitialized() const;

        /**
         * @brief 获取当前后端类型
         * @return 当前后端类型
         */
        BackendType getCurrentBackendType() const;

        /**
         * @brief 获取当前后端名称
         * @return 当前后端名称
         */
        std::string getCurrentBackendName() const;

        /**
         * @brief 设置错误回调函数
         * @param callback 错误回调函数
         */
        void setErrorCallback(std::function<void(const std::string &)> callback);

        /**
         * @brief 设置调试回调函数
         * @param callback 调试回调函数
         */
        void setDebugCallback(std::function<void(const std::string &)> callback);

    private:
        BackendManager() = default;
        ~BackendManager() = default;

        // 禁止拷贝和赋值
        BackendManager(const BackendManager &) = delete;
        BackendManager &operator=(const BackendManager &) = delete;

        std::unique_ptr<RenderBackend> _currentBackend;
        BackendType _currentBackendType = BackendType::Unknown;
        bool _initialized = false;

        std::function<void(const std::string &)> _errorCallback;
        std::function<void(const std::string &)> _debugCallback;
    };

/**
 * @brief 自动注册后端的辅助宏
 *
 * 在后端实现文件中使用此宏自动注册后端到工厂。
 */
#define USG_REGISTER_BACKEND(Type, BackendClass)                                                               \
    namespace                                                                                                  \
    {                                                                                                          \
        struct BackendRegistrar_##BackendClass                                                                 \
        {                                                                                                      \
            BackendRegistrar_##BackendClass()                                                                  \
            {                                                                                                  \
                USG::BackendFactory::getInstance().registerBackend(                                            \
                    USG::BackendType::Type,                                                                    \
                    []() -> std::unique_ptr<USG::RenderBackend> { return std::make_unique<BackendClass>(); }); \
            }                                                                                                  \
        };                                                                                                     \
        static BackendRegistrar_##BackendClass g_registrar_##BackendClass;                                     \
    }

/**
 * @brief 自动注册命名后端的辅助宏
 */
#define USG_REGISTER_NAMED_BACKEND(Name, Type, BackendClass)                                                   \
    namespace                                                                                                  \
    {                                                                                                          \
        struct BackendRegistrar_##BackendClass                                                                 \
        {                                                                                                      \
            BackendRegistrar_##BackendClass()                                                                  \
            {                                                                                                  \
                USG::BackendFactory::getInstance().registerBackend(                                            \
                    Name,                                                                                      \
                    USG::BackendType::Type,                                                                    \
                    []() -> std::unique_ptr<USG::RenderBackend> { return std::make_unique<BackendClass>(); }); \
            }                                                                                                  \
        };                                                                                                     \
        static BackendRegistrar_##BackendClass g_registrar_##BackendClass;                                     \
    }

    // 便利函数
    namespace Backend
    {
        /**
         * @brief 初始化USG Backend系统
         * @param type 后端类型
         * @param config 配置
         * @return 是否成功
         */
        inline bool initialize(BackendType type = BackendType::WebGPU, const BackendConfig &config = {})
        {
            return BackendManager::getInstance().initialize(type, config);
        }

        /**
         * @brief 清理USG Backend系统
         */
        inline void cleanup()
        {
            BackendManager::getInstance().cleanup();
        }

        /**
         * @brief 获取当前后端
         * @return 当前后端指针
         */
        inline RenderBackend *getCurrent()
        {
            return BackendManager::getInstance().getCurrentBackend();
        }

        /**
         * @brief 切换后端
         * @param type 后端类型
         * @param config 配置
         * @return 是否成功
         */
        inline bool switchTo(BackendType type, const BackendConfig &config = {})
        {
            return BackendManager::getInstance().switchBackend(type, config);
        }

        /**
         * @brief 检查后端是否可用
         * @param type 后端类型
         * @return 是否可用
         */
        inline bool isAvailable(BackendType type)
        {
            return BackendFactory::getInstance().isBackendAvailable(type);
        }

        /**
         * @brief 获取所有可用后端
         * @return 后端类型列表
         */
        inline std::vector<BackendType> getAvailable()
        {
            return BackendFactory::getInstance().getAvailableBackends();
        }

        /**
         * @brief 自动检测最佳后端
         * @return 最佳后端类型
         */
        inline BackendType detectBest()
        {
            return BackendFactory::getInstance().detectBestBackend();
        }
    }

} // namespace USG
